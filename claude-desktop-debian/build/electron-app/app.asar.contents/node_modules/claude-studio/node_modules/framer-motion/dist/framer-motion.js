!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),f=e.createContext({}),g=e.createContext(null),y="undefined"!=typeof document,v=y?e.useLayoutEffect:e.useEffect,x=e.createContext({strict:!1}),P=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),w="data-"+P("framerAppearId"),T={skipAnimations:!1,useManualTiming:!1};class S{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}const b=["read","resolveKeyframes","update","preRender","render","postRender"];function A(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=b.reduce((t,e)=>(t[e]=function(t){let e=new S,n=new S,i=0,s=!1,o=!1;const r=new WeakSet,a={schedule:(t,o=!1,a=!1)=>{const l=a&&s,u=l?e:n;return o&&r.add(t),u.add(t)&&l&&s&&(i=e.order.length),t},cancel:t=>{n.remove(t),r.delete(t)},process:l=>{if(s)o=!0;else{if(s=!0,[e,n]=[n,e],n.clear(),i=e.order.length,i)for(let n=0;n<i;n++){const i=e.order[n];r.has(i)&&(a.schedule(i),t()),i(l)}s=!1,o&&(o=!1,a.process(l))}}};return a}(()=>n=!0),t),{}),r=t=>{o[t].process(s)},a=()=>{const o=T.useManualTiming?s.timestamp:performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,b.forEach(r),s.isProcessing=!1,n&&e&&(i=!1,t(a))};return{schedule:b.reduce((e,r)=>{const l=o[r];return e[r]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(a)),l.schedule(e,o,r)),e},{}),cancel:t=>b.forEach(e=>o[e].cancel(t)),state:s,steps:o}}const{schedule:E,cancel:C}=A(queueMicrotask,!1);function V(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function M(t,n,i){return e.useCallback(e=>{e&&t.mount&&t.mount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):V(i)&&(i.current=e))},[n])}function R(t){return"string"==typeof t||Array.isArray(t)}function D(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const k=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],L=["initial",...k];function B(t){return D(t.animate)||L.some(e=>R(t[e]))}function F(t){return Boolean(B(t)||t.variants)}function j(t){const{initial:n,animate:i}=function(t,e){if(B(t)){const{initial:e,animate:n}=t;return{initial:!1===e||R(e)?e:void 0,animate:R(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(f));return e.useMemo(()=>({initial:n,animate:i}),[O(n),O(i)])}function O(t){return Array.isArray(t)?t.join(" "):t}const I={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},U={};for(const t in I)U[t]={isEnabled:e=>I[t].some(t=>!!e[t])};function W(t){for(const e in t)U[e]={...U[e],...t[e]}}const N=e.createContext({}),z=e.createContext({}),H=Symbol.for("motionComponentSymbol");function $({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){t&&W(t);const r=e.forwardRef((function(r,a){let l;const u={...e.useContext(m),...r,layoutId:Y(r)},{isStatic:c}=u,h=j(r),P=s(r,c);if(!c&&y){h.visualElement=function(t,n,i,s){const{visualElement:o}=e.useContext(f),r=e.useContext(x),a=e.useContext(g),l=e.useContext(m).reducedMotion,u=e.useRef();s=s||r.renderer,!u.current&&s&&(u.current=s(t,{visualState:n,parent:o,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));const c=u.current;e.useInsertionEffect(()=>{c&&c.update(i,a)});const h=e.useRef(Boolean(i[w]&&!window.HandoffComplete));return v(()=>{c&&(E.render(c.render),h.current&&c.animationState&&c.animationState.animateChanges())}),e.useEffect(()=>{c&&(c.updateFeatures(),!h.current&&c.animationState&&c.animationState.animateChanges(),h.current&&(h.current=!1,window.HandoffComplete=!0))}),c}(o,P,u,n);const i=e.useContext(z),s=e.useContext(x).strict;h.visualElement&&(l=h.visualElement.loadFeatures(u,s,t,i))}return p(f.Provider,{value:h,children:[l&&h.visualElement?d(l,{visualElement:h.visualElement,...u}):null,i(o,r,M(P,h.visualElement,a),P,c,h.visualElement)]})}));return r[H]=o,r}function Y({layoutId:t}){const n=e.useContext(N).id;return n&&void 0!==t?n+"-"+t:t}function X(t){function e(e,n={}){return $(t(e,n))}if("undefined"==typeof Proxy)return e;const n=new Map;return new Proxy(e,{get:(t,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const K=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function G(t){return"string"==typeof t&&!t.includes("-")&&!!(K.indexOf(t)>-1||/[A-Z]/u.test(t))}const _={};function q(t){Object.assign(_,t)}const Z=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],J=new Set(Z);function Q(t,{layout:e,layoutId:n}){return J.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!_[t]||"opacity"===t)}const tt=t=>Boolean(t&&t.getVelocity),et={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nt=Z.length;function it(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},i,s){let o="";for(let e=0;e<nt;e++){const n=Z[e];if(void 0!==t[n]){o+=`${et[n]||n}(${t[n]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),s?o=s(t,i?"":o):n&&i&&(o="none"),o}const st=t=>e=>"string"==typeof e&&e.startsWith(t),ot=st("--"),rt=st("var(--"),at=t=>!!rt(t)&&lt.test(t.split("/*")[0].trim()),lt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ut=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ct=(t,e,n)=>n>e?e:n<t?t:n,ht={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},dt={...ht,transform:t=>ct(0,1,t)},pt={...ht,default:1},mt=t=>Math.round(1e5*t)/1e5,ft=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,gt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,yt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu;function vt(t){return"string"==typeof t}const xt=t=>({test:e=>vt(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Pt=xt("deg"),wt=xt("%"),Tt=xt("px"),St=xt("vh"),bt=xt("vw"),At={...wt,parse:t=>wt.parse(t)/100,transform:t=>wt.transform(100*t)},Et={...ht,transform:Math.round},Ct={borderWidth:Tt,borderTopWidth:Tt,borderRightWidth:Tt,borderBottomWidth:Tt,borderLeftWidth:Tt,borderRadius:Tt,radius:Tt,borderTopLeftRadius:Tt,borderTopRightRadius:Tt,borderBottomRightRadius:Tt,borderBottomLeftRadius:Tt,width:Tt,maxWidth:Tt,height:Tt,maxHeight:Tt,size:Tt,top:Tt,right:Tt,bottom:Tt,left:Tt,padding:Tt,paddingTop:Tt,paddingRight:Tt,paddingBottom:Tt,paddingLeft:Tt,margin:Tt,marginTop:Tt,marginRight:Tt,marginBottom:Tt,marginLeft:Tt,rotate:Pt,rotateX:Pt,rotateY:Pt,rotateZ:Pt,scale:pt,scaleX:pt,scaleY:pt,scaleZ:pt,skew:Pt,skewX:Pt,skewY:Pt,distance:Tt,translateX:Tt,translateY:Tt,translateZ:Tt,x:Tt,y:Tt,z:Tt,perspective:Tt,transformPerspective:Tt,opacity:dt,originX:At,originY:At,originZ:Tt,zIndex:Et,backgroundPositionX:Tt,backgroundPositionY:Tt,fillOpacity:dt,strokeOpacity:dt,numOctaves:Et};function Vt(t,e,n,i){const{style:s,vars:o,transform:r,transformOrigin:a}=t;let l=!1,u=!1,c=!0;for(const t in e){const n=e[t];if(ot(t)){o[t]=n;continue}const i=Ct[t],h=ut(n,i);if(J.has(t)){if(l=!0,r[t]=h,!c)continue;n!==(i.default||0)&&(c=!1)}else t.startsWith("origin")?(u=!0,a[t]=h):s[t]=h}if(e.transform||(l||i?s.transform=it(t.transform,n,c,i):s.transform&&(s.transform="none")),u){const{originX:t="50%",originY:e="50%",originZ:n=0}=a;s.transformOrigin=`${t} ${e} ${n}`}}const Mt=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Rt(t,e,n){for(const i in e)tt(e[i])||Q(i,n)||(t[i]=e[i])}function Dt(t,n,i){const s={};return Rt(s,t.style||{},t),Object.assign(s,function({transformTemplate:t},n,i){return e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Vt(e,n,{enableHardwareAcceleration:!i},t),Object.assign({},e.vars,e.style)},[n])}(t,n,i)),s}function kt(t,e,n){const i={},s=Dt(t,e,n);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i}const Lt=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Bt(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Lt.has(t)}let Ft=t=>!Bt(t);function jt(t){t&&(Ft=e=>e.startsWith("on")?!Bt(e):t(e))}try{jt(require("@emotion/is-prop-valid").default)}catch(t){}function Ot(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(Ft(s)||!0===n&&Bt(s)||!e&&!Bt(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}function It(t,e,n){return"string"==typeof t?t:Tt.transform(e+n*t)}const Ut={offset:"stroke-dashoffset",array:"stroke-dasharray"},Wt={offset:"strokeDashoffset",array:"strokeDasharray"};function Nt(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h,d){if(Vt(t,u,c,d),h)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==s||void 0!==o||m.transform)&&(m.transformOrigin=function(t,e,n){return`${It(e,t.x,t.width)} ${It(n,t.y,t.height)}`}(f,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(p.x=e),void 0!==n&&(p.y=n),void 0!==i&&(p.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Ut:Wt;t[o.offset]=Tt.transform(-i);const r=Tt.transform(e),a=Tt.transform(n);t[o.array]=`${r} ${a}`}(p,r,a,l,!1)}const zt=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Ht=t=>"string"==typeof t&&"svg"===t.toLowerCase();function $t(t,n,i,s){const o=e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Nt(e,n,{enableHardwareAcceleration:!1},Ht(s),t.transformTemplate),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const e={};Rt(e,t.style,t),o.style={...e,...o.style}}return o}function Yt(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(G(n)?$t:kt)(i,o,r,n),l=Ot(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo(()=>tt(c)?c.get():c,[c]);return e.createElement(n,{...u,children:h})}}function Xt(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}const Kt=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Gt(t,e,n,i){Xt(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(Kt.has(n)?n:P(n),e.attrs[n])}function _t(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(tt(s[r])||e.style&&tt(e.style[r])||Q(r,t)||void 0!==(null===(i=null==n?void 0:n.getValue(r))||void 0===i?void 0:i.liveStyle))&&(o[r]=s[r]);return o}function qt(t,e,n){const i=_t(t,e,n);for(const n in t)if(tt(t[n])||tt(e[n])){i[-1!==Z.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}function Zt(t){const e=[{},{}];return null==t||t.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function Jt(t,e,n,i){if("function"==typeof e){const[s,o]=Zt(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=Zt(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function Qt(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const te=t=>Array.isArray(t);function ee(t){const e=tt(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const ne=t=>(n,i)=>{const s=e.useContext(f),o=e.useContext(g),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},i,s,o){const r={latestValues:ie(i,s,o,t),renderState:e()};return n&&(r.mount=t=>n(i,t,r)),r}(t,n,s,o);return i?r():Qt(r)};function ie(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=ee(o[t]);let{initial:r,animate:a}=t;const l=B(t),u=F(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!D(h)){(Array.isArray(h)?h:[h]).forEach(e=>{const n=Jt(t,e);if(!n)return;const{transitionEnd:i,transition:o,...r}=n;for(const t in r){let e=r[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const t in i)s[t]=i[t]})}return s}const se=t=>t,{schedule:oe,cancel:re,state:ae,steps:le}=A("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:se,!0),ue={useVisualState:ne({scrapeMotionValuesFromProps:qt,createRenderState:zt,onMount:(t,e,{renderState:n,latestValues:i})=>{oe.read(()=>{try{n.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){n.dimensions={x:0,y:0,width:0,height:0}}}),oe.render(()=>{Nt(n,i,{enableHardwareAcceleration:!1},Ht(e.tagName),t.transformTemplate),Gt(e,n)})}})},ce={useVisualState:ne({scrapeMotionValuesFromProps:_t,createRenderState:Mt})};function he(t,{forwardMotionProps:e=!1},n,i){return{...G(t)?ue:ce,preloadedFeatures:n,useRender:Yt(e),createVisualElement:i,Component:t}}function de(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const pe=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function me(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const fe=t=>e=>pe(e)&&t(e,me(e));function ge(t,e,n,i){return de(t,e,fe(n),i)}const ye=(t,e)=>n=>e(t(n)),ve=(...t)=>t.reduce(ye);function xe(t){let e=null;return()=>{const n=()=>{e=null};return null===e&&(e=t,n)}}const Pe=xe("dragHorizontal"),we=xe("dragVertical");function Te(t){let e=!1;if("y"===t)e=we();else if("x"===t)e=Pe();else{const t=Pe(),n=we();t&&n?e=()=>{t(),n()}:(t&&t(),n&&n())}return e}function Se(){const t=Te(!0);return!t||(t(),!1)}class be{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Ae(t,e){const n=e?"pointerenter":"pointerleave",i=e?"onHoverStart":"onHoverEnd";return ge(t.current,n,(n,s)=>{if("touch"===n.pointerType||Se())return;const o=t.getProps();t.animationState&&o.whileHover&&t.animationState.setActive("whileHover",e);const r=o[i];r&&oe.postRender(()=>r(n,s))},{passive:!t.getProps()[i]})}const Ee=(t,e)=>!!e&&(t===e||Ee(t,e.parentElement));function Ce(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,me(n))}const Ve=new WeakMap,Me=new WeakMap,Re=t=>{const e=Ve.get(t.target);e&&e(t)},De=t=>{t.forEach(Re)};function ke(t,e,n){const i=function({root:t,...e}){const n=t||document;Me.has(n)||Me.set(n,{});const i=Me.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(De,{root:t,...e})),i[s]}(e);return Ve.set(t,n),i.observe(t),()=>{Ve.delete(t),i.unobserve(t)}}const Le={some:0,all:1};const Be={inView:{Feature:class extends be{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Le[i]};return ke(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends be{constructor(){super(...arguments),this.removeStartListeners=se,this.removeEndListeners=se,this.removeAccessibleListeners=se,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),i=ge(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:i,globalTapTarget:s}=this.node.getProps(),o=s||Ee(this.node.current,t.target)?n:i;o&&oe.update(()=>o(t,e))},{passive:!(n.onTap||n.onPointerUp)}),s=ge(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=ve(i,s),this.startPress(t,e)},this.startAccessiblePress=()=>{const t=de(this.node.current,"keydown",t=>{if("Enter"!==t.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=de(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&Ce("up",(t,e)=>{const{onTap:n}=this.node.getProps();n&&oe.postRender(()=>n(t,e))})}),Ce("down",(t,e)=>{this.startPress(t,e)})}),e=de(this.node.current,"blur",()=>{this.isPressing&&Ce("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=ve(t,e)}}startPress(t,e){this.isPressing=!0;const{onTapStart:n,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&oe.postRender(()=>n(t,e))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Se()}cancelPress(t,e){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&oe.postRender(()=>n(t,e))}mount(){const t=this.node.getProps(),e=ge(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),n=de(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=ve(e,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends be{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ve(de(this.node.current,"focus",()=>this.onFocus()),de(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends be{mount(){this.unmount=ve(Ae(this.node,!0),Ae(this.node,!1))}unmount(){}}}};function Fe(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function je(t,e,n){const i=t.getProps();return Jt(i,e,void 0!==n?n:i.custom,t)}const Oe=t=>1e3*t,Ie=t=>t/1e3,Ue={type:"spring",stiffness:500,damping:25,restSpeed:10},We={type:"keyframes",duration:.8},Ne={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ze=(t,{keyframes:e})=>e.length>2?We:J.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Ue:Ne;function He(t,e){return t[e]||t.default||t}const $e={current:!1},Ye=t=>null!==t;function Xe(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(Ye),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}let Ke;function Ge(){Ke=void 0}const _e={now:()=>(void 0===Ke&&_e.set(ae.isProcessing||T.useManualTiming?ae.timestamp:performance.now()),Ke),set:t=>{Ke=t,queueMicrotask(Ge)}},qe=t=>/^0[^.\s]+$/u.test(t);let Ze=se,Je=se;const Qe=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),tn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function en(t,e,n=1){const[i,s]=function(t){const e=tn.exec(t);if(!e)return[,];const[,n,i,s]=e;return["--"+(null!=n?n:i),s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return Qe(t)?parseFloat(t):t}return at(s)?en(s,e,n+1):s}const nn=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),sn=t=>t===ht||t===Tt,on=(t,e)=>parseFloat(t.split(", ")[e]),rn=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/u);if(s)return on(s[1],e);{const e=i.match(/^matrix\((.+)\)$/u);return e?on(e[1],t):0}},an=new Set(["x","y","z"]),ln=Z.filter(t=>!an.has(t));const un={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:rn(4,13),y:rn(5,14)};un.translateX=un.x,un.translateY=un.y;const cn=t=>e=>e.test(t),hn=[ht,Tt,wt,Pt,bt,St,{test:t=>"auto"===t,parse:t=>t}],dn=t=>hn.find(cn(t)),pn=new Set;let mn=!1,fn=!1;function gn(){if(fn){const t=Array.from(pn).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return ln.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{var i;null===(i=t.getValue(e))||void 0===i||i.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}fn=!1,mn=!1,pn.forEach(t=>t.complete()),pn.clear()}function yn(){pn.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(fn=!0)})}class vn{constructor(t,e,n,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(pn.add(this),mn||(mn=!0,oe.read(yn),oe.resolveKeyframes(gn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),pn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,pn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const xn=(t,e)=>n=>Boolean(vt(n)&&yt.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Pn=(t,e,n)=>i=>{if(!vt(i))return i;const[s,o,r,a]=i.match(ft);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},wn={...ht,transform:t=>Math.round((t=>ct(0,255,t))(t))},Tn={test:xn("rgb","red"),parse:Pn("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+wn.transform(t)+", "+wn.transform(e)+", "+wn.transform(n)+", "+mt(dt.transform(i))+")"};const Sn={test:xn("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Tn.transform},bn={test:xn("hsl","hue"),parse:Pn("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+wt.transform(mt(e))+", "+wt.transform(mt(n))+", "+mt(dt.transform(i))+")"},An={test:t=>Tn.test(t)||Sn.test(t)||bn.test(t),parse:t=>Tn.test(t)?Tn.parse(t):bn.test(t)?bn.parse(t):Sn.parse(t),transform:t=>vt(t)?t:t.hasOwnProperty("red")?Tn.transform(t):bn.transform(t)};const En=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Cn(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(En,t=>(An.test(t)?(i.color.push(o),s.push("color"),n.push(An.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push("number"),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function Vn(t){return Cn(t).values}function Mn(t){const{split:e,types:n}=Cn(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+="number"===e?mt(t[o]):"color"===e?An.transform(t[o]):t[o]}return s}}const Rn=t=>"number"==typeof t?0:t;const Dn={test:function(t){var e,n;return isNaN(t)&&vt(t)&&((null===(e=t.match(ft))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(gt))||void 0===n?void 0:n.length)||0)>0},parse:Vn,createTransformer:Mn,getAnimatableNone:function(t){const e=Vn(t);return Mn(t)(e.map(Rn))}},kn=new Set(["brightness","contrast","saturate","opacity"]);function Ln(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(ft)||[];if(!i)return t;const s=n.replace(i,"");let o=kn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Bn=/\b([a-z-]*)\(.*?\)/gu,Fn={...Dn,getAnimatableNone:t=>{const e=t.match(Bn);return e?e.map(Ln).join(" "):t}},jn={...Ct,color:An,backgroundColor:An,outlineColor:An,fill:An,stroke:An,borderColor:An,borderTopColor:An,borderRightColor:An,borderBottomColor:An,borderLeftColor:An,filter:Fn,WebkitFilter:Fn},On=t=>jn[t];function In(t,e){let n=On(t);return n!==Fn&&(n=Dn),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Un=new Set(["auto","none","0"]);class Wn extends vn{constructor(t,e,n,i){super(t,e,n,i,null==i?void 0:i.owner,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),at(i))){const s=en(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!nn.has(n)||2!==t.length)return;const[i,s]=t,o=dn(i),r=dn(s);if(o!==r)if(sn(o)&&sn(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||qe(i))&&n.push(e);var i;n.length&&function(t,e,n){let i=0,s=void 0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Un.has(e)&&Cn(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=In(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=un[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=un[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}function Nn(t){let e;return()=>(void 0===e&&(e=t()),e)}const zn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Dn.test(t)&&"0"!==t||t.startsWith("url(")));class Hn{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...r}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.options={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,...r},this.updateFinishedPromise()}get resolved(){return this._resolved||this.hasAttemptedResolve||(yn(),gn()),this._resolved}onKeyframesResolved(t,e){this.hasAttemptedResolve=!0;const{name:n,type:i,velocity:s,delay:o,onComplete:r,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=zn(s,e),a=zn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||"spring"===n&&i)}(t,n,i,s)){if($e.current||!o)return null==a||a(Xe(t,this.options,e)),null==r||r(),void this.resolveFinishedPromise();this.options.duration=0}const u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function $n(t,e){return e?t*(1e3/e):0}function Yn(t,e,n){const i=Math.max(e-5,0);return $n(n-t(i),e-i)}function Xn({duration:t=800,bounce:e=.25,velocity:n=0,mass:i=1}){let s,o,r=1-e;r=ct(.05,1,r),t=ct(.01,10,Ie(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/Kn(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=Kn(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<12;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=Oe(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}function Kn(t,e){return t*Math.sqrt(1-e*e)}const Gn=["duration","bounce"],_n=["stiffness","damping","mass"];function qn(t,e){return e.some(e=>void 0!==t[e])}function Zn({keyframes:t,restDelta:e,restSpeed:n,...i}){const s=t[0],o=t[t.length-1],r={done:!1,value:s},{stiffness:a,damping:l,mass:u,duration:c,velocity:h,isResolvedFromDuration:d}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!qn(t,_n)&&qn(t,Gn)){const n=Xn(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}({...i,velocity:-Ie(i.velocity||0)}),p=h||0,m=l/(2*Math.sqrt(a*u)),f=o-s,g=Ie(Math.sqrt(a/u)),y=Math.abs(f)<5;let v;if(n||(n=y?.01:2),e||(e=y?.005:.5),m<1){const t=Kn(g,m);v=e=>{const n=Math.exp(-m*g*e);return o-n*((p+m*g*f)/t*Math.sin(t*e)+f*Math.cos(t*e))}}else if(1===m)v=t=>o-Math.exp(-g*t)*(f+(p+g*f)*t);else{const t=g*Math.sqrt(m*m-1);v=e=>{const n=Math.exp(-m*g*e),i=Math.min(t*e,300);return o-n*((p+m*g*f)*Math.sinh(i)+t*f*Math.cosh(i))/t}}return{calculatedDuration:d&&c||null,next:t=>{const i=v(t);if(d)r.done=t>=c;else{let s=p;0!==t&&(s=m<1?Yn(v,t,i):0);const a=Math.abs(s)<=n,l=Math.abs(o-i)<=e;r.done=a&&l}return r.value=r.done?o:i,r}}}function Jn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let P,w;const T=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(P=t,w=Zn({keyframes:[d.value,p(d.value)],velocity:Yn(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==P||(e=!0,x(t),T(t)),void 0!==P&&t>=P?w.next(t-P):(!e&&x(t),d)}}}const Qn=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function ti(t,e,n,i){if(t===e&&n===i)return se;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=Qn(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:Qn(s(t),e,i)}const ei=ti(.42,0,1,1),ni=ti(0,0,.58,1),ii=ti(.42,0,.58,1),si=t=>Array.isArray(t)&&"number"!=typeof t[0],oi=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ri=t=>e=>1-t(1-e),ai=t=>1-Math.sin(Math.acos(t)),li=ri(ai),ui=oi(ai),ci=ti(.33,1.53,.69,.99),hi=ri(ci),di=oi(hi),pi=t=>(t*=2)<1?.5*hi(t):.5*(2-Math.pow(2,-10*(t-1))),mi={linear:se,easeIn:ei,easeInOut:ii,easeOut:ni,circIn:ai,circInOut:ui,circOut:li,backIn:hi,backInOut:di,backOut:ci,anticipate:pi},fi=t=>{if(Array.isArray(t)){Je(4===t.length);const[e,n,i,s]=t;return ti(e,n,i,s)}return"string"==typeof t?mi[t]:t},gi=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},yi=(t,e,n)=>t+(e-t)*n;function vi(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function xi(t,e){return n=>n>0?e:t}const Pi=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},wi=[Sn,Tn,bn];function Ti(t){const e=(n=t,wi.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===bn&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=vi(a,i,t+1/3),o=vi(a,i,t),r=vi(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Si=(t,e)=>{const n=Ti(t),i=Ti(e);if(!n||!i)return xi(t,e);const s={...n};return t=>(s.red=Pi(n.red,i.red,t),s.green=Pi(n.green,i.green,t),s.blue=Pi(n.blue,i.blue,t),s.alpha=yi(n.alpha,i.alpha,t),Tn.transform(s))},bi=new Set(["none","hidden"]);function Ai(t,e){return n=>yi(t,e,n)}function Ei(t){return"number"==typeof t?Ai:"string"==typeof t?at(t)?xi:An.test(t)?Si:Mi:Array.isArray(t)?Ci:"object"==typeof t?An.test(t)?Si:Vi:xi}function Ci(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>Ei(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Vi(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=Ei(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Mi=(t,e)=>{const n=Dn.createTransformer(e),i=Cn(t),s=Cn(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?bi.has(t)&&!s.values.length||bi.has(e)&&!i.values.length?function(t,e){return bi.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):ve(Ci(function(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=null!==(n=t.values[a])&&void 0!==n?n:0;i[o]=l,s[r]++}return i}(i,s),s.values),n):xi(t,e)};function Ri(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return yi(t,e,n);return Ei(t)(t,e)}function Di(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(Je(o===e.length),1===o)return()=>e[0];if(2===o&&t[0]===t[1])return()=>e[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=function(t,e,n){const i=[],s=n||Ri,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||se:e;o=ve(t,o)}i.push(o)}return i}(e,i,s),a=r.length,l=e=>{let n=0;if(a>1)for(;n<t.length-2&&!(e<t[n+1]);n++);const i=gi(t[n],t[n+1],e);return r[n](i)};return n?e=>l(ct(t[0],t[o-1],e)):l}function ki(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=gi(0,e,i);t.push(yi(n,1,s))}}function Li(t){const e=[0];return ki(e,t.length-1),e}function Bi({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=si(i)?i.map(fi):fi(i),o={done:!1,value:e[0]},r=Di(function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Li(e),t),e,{ease:Array.isArray(s)?s:(a=e,l=s,a.map(()=>l||ii).splice(0,a.length-1))});var a,l;return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}function Fi(t){let e=0;let n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}const ji=t=>{const e=({timestamp:e})=>t(e);return{start:()=>oe.update(e,!0),stop:()=>re(e),now:()=>ae.isProcessing?ae.timestamp:_e.now()}},Oi={decay:Jn,inertia:Jn,tween:Bi,keyframes:Bi,spring:Zn},Ii=t=>t/100;class Ui extends Hn{constructor({KeyframeResolver:t=vn,...e}){super(e),this.holdTime=null,this.startTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:n,motionValue:i,keyframes:s}=this.options,o=(t,e)=>this.onKeyframesResolved(t,e);n&&i&&i.owner?this.resolver=i.owner.resolveKeyframes(s,o,n,i):this.resolver=new t(s,o,n,i),this.resolver.scheduleResolve()}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,r=Oi[e]||Bi;let a,l;r!==Bi&&"number"!=typeof t[0]&&(a=ve(Ii,Ri(t[0],t[1])),t=[0,100]);const u=r({...this.options,keyframes:t});"mirror"===s&&(l=r({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=Fi(u));const{calculatedDuration:c}=u,h=c+i;return{generator:u,mirroredGenerator:l,mapPercentToKeyframes:a,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(n+1)-i}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:r,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return s.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,m&&(n-=m/c)):"mirror"===p&&(x=o)),v=ct(0,1,n)*c}const P=y?{done:!1,value:a[0]}:x.next(v);r&&(P.value=r(P.value));let{done:w}=P;y||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==i&&(P.value=Xe(a,this.options,i)),f&&f(P.value),T&&this.finish(),P}get duration(){const{resolved:t}=this;return t?Ie(t.calculatedDuration):0}get time(){return Ie(this.currentTime)}set time(t){t=Oe(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=Ie(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=ji,onPlay:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();const n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime&&"finished"!==this.state||(this.startTime=n),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;this._resolved?(this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function Wi(t){return new Ui(t)}const Ni=t=>Array.isArray(t)&&"number"==typeof t[0];function zi(t){return Boolean(!t||"string"==typeof t&&t in $i||Ni(t)||Array.isArray(t)&&t.every(zi))}const Hi=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,$i={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Hi([0,.65,.55,1]),circOut:Hi([.55,0,1,.45]),backIn:Hi([.31,.01,.66,-.59]),backOut:Hi([.33,1.53,.69,.99])};function Yi(t){return Xi(t)||$i.easeOut}function Xi(t){return t?Ni(t)?Hi(t):Array.isArray(t)?t.map(Yi):$i[t]:void 0}function Ki(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a,times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=Xi(a);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}const Gi=Nn(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),_i=new Set(["opacity","clipPath","filter","transform"]);class qi extends Hn{constructor(t){super(t);const{name:e,motionValue:n,keyframes:i}=this.options;this.resolver=new Wn(i,(t,e)=>this.onKeyframesResolved(t,e),e,n),this.resolver.scheduleResolve()}initPlayback(t,e){var n;let{duration:i=300,times:s,ease:o,type:r,motionValue:a,name:l}=this.options;if(!(null===(n=a.owner)||void 0===n?void 0:n.current))return!1;if("spring"===(u=this.options).type||"backgroundColor"===u.name||!zi(u.ease)){const{onComplete:e,onUpdate:n,motionValue:a,...l}=this.options,u=function(t,e){const n=new Ui({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const s=[];let o=0;for(;!i.done&&o<2e4;)i=n.sample(o),s.push(i.value),o+=10;return{times:void 0,keyframes:s,duration:o-10,ease:"linear"}}(t,l);1===(t=u.keyframes).length&&(t[1]=t[0]),i=u.duration,s=u.times,o=u.ease,r="keyframes"}var u;const c=Ki(a.owner.current,l,t,{...this.options,duration:i,times:s,ease:o});return c.startTime=_e.now(),this.pendingTimeline?(c.timeline=this.pendingTimeline,this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:n}=this.options;a.set(Xe(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:i,times:s,type:r,ease:o,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return Ie(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return Ie(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=Oe(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return se;const{animation:n}=e;n.timeline=t,n.onfinish=null}else this.pendingTimeline=t;return se}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:i,type:s,ease:o,times:r}=t;if("idle"!==e.playState&&"finished"!==e.playState){if(this.time){const{motionValue:t,onUpdate:e,onComplete:a,...l}=this.options,u=new Ui({...l,keyframes:n,duration:i,type:s,ease:o,times:r,isGenerator:!0}),c=Oe(this.time);t.setWithVelocity(u.sample(c-10).value,u.sample(c).value,10)}this.cancel()}}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;return Gi()&&n&&_i.has(n)&&e&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}}function Zi(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return oe.update(i,!0),()=>re(i)}const Ji=Nn(()=>void 0!==window.ScrollTimeline);class Qi{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,e){return Promise.all(this.animations).then(t).catch(e)}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>{if(!Ji()||!e.attachTimeline)return e.pause(),Zi(t=>{e.time=e.duration*t},t);e.attachTimeline(t)});return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}const ts=(t,e,n,i={},s,o)=>r=>{const a=He(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=Oe(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||(c={...c,...ze(t,c)}),c.duration&&(c.duration=Oe(c.duration)),c.repeatDelay&&(c.repeatDelay=Oe(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),($e.current||T.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),h&&!o&&void 0!==e.get()){const t=Xe(c.keyframes,a);if(void 0!==t)return oe.update(()=>{c.onUpdate(t),c.onComplete()}),new Qi([])}return!o&&qi.supports(c)?new qi(c):new Ui(c)};function es(t){return Boolean(tt(t)&&t.add)}function ns(t,e){-1===t.indexOf(e)&&t.push(e)}function is(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class ss{constructor(){this.subscriptions=[]}add(t){return ns(this.subscriptions,t),()=>is(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const os={current:void 0};class rs{constructor(t,e={}){this.version="11.2.10",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=_e.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=_e.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ss);const n=this.events[t].add(e);return"change"===t?()=>{n(),oe.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return os.current&&os.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=_e.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return $n(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function as(t,e){return new rs(t,e)}function ls(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,as(n))}function us(t,e){const n=je(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){ls(t,e,(r=o[e],te(r)?r[r.length-1]||0:r))}var r}function cs(t){return t.getProps()[w]}function hs({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function ds(t,e,{delay:n=0,transitionOverride:i,type:s}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;const u=t.getValue("willChange");i&&(r=i);const c=[],h=s&&t.animationState&&t.animationState.getState()[s];for(const e in l){const i=t.getValue(e,null!==(o=t.latestValues[e])&&void 0!==o?o:null),s=l[e];if(void 0===s||h&&hs(h,e))continue;const a={delay:n,elapsed:0,...He(r||{},e)};let d=!1;if(window.HandoffAppearAnimations){const n=cs(t);if(n){const t=window.HandoffAppearAnimations(n,e,i,oe);null!==t&&(a.elapsed=t,d=!0)}}i.start(ts(e,i,s,t.shouldReduceMotion&&J.has(e)?{type:!1}:a,t,d));const p=i.animation;p&&(es(u)&&(u.add(e),p.then(()=>u.remove(e))),c.push(p))}return a&&Promise.all(c).then(()=>{oe.update(()=>{a&&us(t,a)})}),c}function ps(t,e,n={}){var i;const s=je(t,e,"exit"===n.type?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(ds(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(ms).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(ps(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}return Promise.all([r(),a(n.delay)])}function ms(t,e){return t.sortNodePosition(e)}function fs(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>ps(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=ps(t,e,n);else{const s="function"==typeof e?je(t,e,n.custom):e;i=Promise.all(ds(t,s,n))}return i.then(()=>{oe.postRender(()=>{t.notify("AnimationComplete",e)})})}const gs=[...k].reverse(),ys=k.length;function vs(t){let e=function(t){return e=>Promise.all(e.map(({animation:e,options:n})=>fs(t,e,n)))}(t);const n={animate:Ps(!0),whileInView:Ps(),whileHover:Ps(),whileTap:Ps(),whileDrag:Ps(),whileFocus:Ps(),exit:Ps()};let i=!0;const s=e=>(n,i)=>{var s;const o=je(t,i,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const r=t.getProps(),a=t.getVariantContext(!0)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<ys;e++){const d=gs[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=R(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||D(m)||"boolean"==typeof m)continue;let v=xs(p.prevProp,m)||d===o&&p.isActive&&!y&&f||e>h&&f,x=!1;const P=Array.isArray(m)?m:[m];let w=P.reduce(s(d),{});!1===g&&(w={});const{prevResolvedValues:T={}}=p,S={...T,...w},b=e=>{v=!0,u.has(e)&&(x=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in S){const e=w[t],n=T[t];if(c.hasOwnProperty(t))continue;let i=!1;i=te(e)&&te(n)?!Fe(e,n):e!==n,i?null!=e?b(t):u.add(t):void 0!==e&&u.has(t)?b(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=w,p.isActive&&(c={...c,...w}),i&&t.blockInitialAnimation&&(v=!1),!v||y&&!x||l.push(...P.map(t=>({animation:t,options:{type:d}})))}if(u.size){const e={};u.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=null!=i?i:null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)}),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n}}function xs(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Fe(e,t)}function Ps(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let ws=0;const Ts={animation:{Feature:class extends be{constructor(t){super(t),t.animationState||(t.animationState=vs(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),D(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends be{constructor(){super(...arguments),this.id=ws++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>e(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}},Ss=(t,e)=>Math.abs(t-e);function bs(t,e){const n=Ss(t.x,e.x),i=Ss(t.y,e.y);return Math.sqrt(n**2+i**2)}class As{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Vs(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=bs(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=ae;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Es(e,this.transformPagePoint),oe.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Vs("pointercancel"===t.type?this.lastMoveEventInfo:Es(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!pe(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Es(me(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=ae;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Vs(o,this.history)),this.removeListeners=ve(ge(this.contextWindow,"pointermove",this.handlePointerMove),ge(this.contextWindow,"pointerup",this.handlePointerUp),ge(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),re(this.updatePoint)}}function Es(t,e){return e?{point:e(t.point)}:t}function Cs(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Vs({point:t},e){return{point:t,delta:Cs(t,Rs(e)),offset:Cs(t,Ms(e)),velocity:Ds(e,.1)}}function Ms(t){return t[0]}function Rs(t){return t[t.length-1]}function Ds(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Rs(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>Oe(e)));)n--;if(!i)return{x:0,y:0};const o=Ie(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function ks(t){return t.max-t.min}function Ls(t,e=0,n=.01){return Math.abs(t-e)<=n}function Bs(t,e,n,i=.5){t.origin=i,t.originPoint=yi(e.min,e.max,t.origin),t.scale=ks(n)/ks(e),(Ls(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=yi(n.min,n.max,t.origin)-t.originPoint,(Ls(t.translate)||isNaN(t.translate))&&(t.translate=0)}function Fs(t,e,n,i){Bs(t.x,e.x,n.x,i?i.originX:void 0),Bs(t.y,e.y,n.y,i?i.originY:void 0)}function js(t,e,n){t.min=n.min+e.min,t.max=t.min+ks(e)}function Os(t,e,n){t.min=e.min-n.min,t.max=t.min+ks(e)}function Is(t,e,n){Os(t.x,e.x,n.x),Os(t.y,e.y,n.y)}function Us(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Ws(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Ns=.35;function zs(t,e,n){return{min:Hs(t,e),max:Hs(t,n)}}function Hs(t,e){return"number"==typeof t?t:t[e]||0}const $s=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Ys(t){return[t("x"),t("y")]}function Xs({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Ks(t){return void 0===t||1===t}function Gs({scale:t,scaleX:e,scaleY:n}){return!Ks(t)||!Ks(e)||!Ks(n)}function _s(t){return Gs(t)||qs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function qs(t){return Zs(t.x)||Zs(t.y)}function Zs(t){return t&&"0%"!==t}function Js(t,e,n){return n+e*(t-n)}function Qs(t,e,n,i,s){return void 0!==s&&(t=Js(t,s,i)),Js(t,n,i)+e}function to(t,e=0,n=1,i,s){t.min=Qs(t.min,e,n,i,s),t.max=Qs(t.max,e,n,i,s)}function eo(t,{x:e,y:n}){to(t.x,e.translate,e.scale,e.originPoint),to(t.y,n.translate,n.scale,n.originPoint)}function no(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function io(t,e){t.min=t.min+e,t.max=t.max+e}function so(t,e,[n,i,s]){const o=void 0!==e[s]?e[s]:.5,r=yi(t.min,t.max,o);to(t,e[n],e[i],r,e.scale)}const oo=["x","scaleX","originX"],ro=["y","scaleY","originY"];function ao(t,e){so(t.x,e,oo),so(t.y,e,ro)}function lo(t,e){return Xs(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const uo=({current:t})=>t?t.ownerDocument.defaultView:null,co=new WeakMap;class ho{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new As(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(me(t,"page").point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Te(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ys(t=>{let e=this.getAxisMotionValue(t).get()||0;if(wt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=ks(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&oe.postRender(()=>s(t,e));const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Ys(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:uo(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&oe.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!po(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?yi(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?yi(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&V(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Us(t.x,n,s),y:Us(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Ns){return!1===t?t=0:!0===t&&(t=Ns),{x:zs(t,"left","right"),y:zs(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ys(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!V(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=lo(t,n),{scroll:s}=e;return s&&(io(i.x,s.offset.x),io(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Ws(t.x,e.x),y:Ws(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Xs(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Ys(r=>{if(!po(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return n.start(ts(t,n,0,e,this.visualElement))}stopAnimation(){Ys(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ys(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e="_drag"+t.toUpperCase(),n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Ys(e=>{const{drag:n}=this.getProps();if(!po(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-yi(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!V(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ys(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=ks(t),s=ks(e);return s>i?n=gi(e.min,e.max-i,t.min):i>s&&(n=gi(t.min,t.max-s,e.min)),ct(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Ys(e=>{if(!po(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(yi(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;co.set(this.visualElement,this);const t=ge(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();V(t)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),e();const s=de(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Ys(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Ns,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function po(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const mo=t=>(e,n)=>{t&&oe.postRender(()=>t(e,n))};const fo=["TopLeft","TopRight","BottomLeft","BottomRight"],go=fo.length,yo=t=>"string"==typeof t?parseFloat(t):t,vo=t=>"number"==typeof t||Tt.test(t);function xo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Po=To(0,.5,li),wo=To(.5,.95,se);function To(t,e,n){return i=>i<t?0:i>e?1:n(gi(t,e,i))}function So(t,e){t.min=e.min,t.max=e.max}function bo(t,e){So(t.x,e.x),So(t.y,e.y)}function Ao(t,e,n,i,s){return t=Js(t-=e,1/n,i),void 0!==s&&(t=Js(t,1/s,i)),t}function Eo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){if(wt.test(e)){e=parseFloat(e);e=yi(r.min,r.max,e/100)-r.min}if("number"!=typeof e)return;let a=yi(o.min,o.max,i);t===o&&(a-=e),t.min=Ao(t.min,e,n,a,s),t.max=Ao(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Co=["x","scaleX","originX"],Vo=["y","scaleY","originY"];function Mo(t,e,n,i){Eo(t.x,e,Co,n?n.x:void 0,i?i.x:void 0),Eo(t.y,e,Vo,n?n.y:void 0,i?i.y:void 0)}function Ro(t){return 0===t.translate&&1===t.scale}function Do(t){return Ro(t.x)&&Ro(t.y)}function ko(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Lo(t){return ks(t.x)/ks(t.y)}class Bo{constructor(){this.members=[]}add(t){ns(this.members,t),t.scheduleRender()}remove(t){if(is(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Fo(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}const jo=(t,e)=>t.depth-e.depth;class Oo{constructor(){this.children=[],this.isDirty=!1}add(t){ns(this.children,t),this.isDirty=!0}remove(t){is(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(jo),this.isDirty=!1,this.children.forEach(t)}}const Io={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Uo(t,e){const n=_e.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(re(i),t(o-e))};return oe.read(i,!0),()=>re(i)}function Wo(t,e,n){var i;if("string"==typeof t){let s=document;e&&(Je(Boolean(e.current)),s=e.current),n?(null!==(i=n[t])&&void 0!==i||(n[t]=s.querySelectorAll(t)),t=n[t]):t=s.querySelectorAll(t)}else t instanceof Element&&(t=[t]);return Array.from(t||[])}const No=new WeakMap;function zo(t){return t instanceof SVGElement&&"svg"!==t.tagName}const Ho={current:null},$o={current:!1};function Yo(){if($o.current=!0,y)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ho.current=t.matches;t.addListener(e),e()}else Ho.current=!1}const Xo=[...hn,An,Dn],Ko=Object.keys(U),Go=Ko.length,_o=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],qo=L.length;class Zo{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.resolveKeyframes=(t,e,n,i)=>new this.KeyframeResolver(t,e,n,i,this),this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=vn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>oe.render(this.render,!1,!0);const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=B(e),this.isVariantNode=F(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&tt(e)&&(e.set(a[t],!1),es(u)&&u.add(t))}}mount(t){this.current=t,No.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),$o.current||Yo(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ho.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){var t;No.delete(this.current),this.projection&&this.projection.unmount(),re(this.notifyUpdate),re(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const e in this.features)null===(t=this.features[e])||void 0===t||t.unmount();this.current=null}bindToMotionValue(t,e){const n=J.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&oe.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},n,i,s){let o,r;for(let t=0;t<Go;t++){const n=Ko[t],{isEnabled:i,Feature:s,ProjectionNode:a,MeasureLayout:l}=U[n];a&&(o=a),i(e)&&(!this.features[n]&&s&&(this.features[n]=new s(this)),l&&(r=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){const{layoutId:t,layout:n,drag:i,dragConstraints:r,layoutScroll:a,layoutRoot:l}=e;this.projection=new o(this.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(this.parent)),this.projection.setOptions({layoutId:t,layout:n,alwaysMeasureLayout:Boolean(i)||r&&V(r),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,layoutScroll:a,layoutRoot:l})}return r}updateFeatures(){for(const t in this.features){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<_o.length;e++){const n=_o[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){const{willChange:i}=e;for(const s in e){const o=e[s],r=n[s];if(tt(o))t.addValue(s,o),es(i)&&i.add(s);else if(tt(r))t.addValue(s,as(o,{owner:t})),es(i)&&i.remove(s);else if(r!==o)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(o):e.hasAnimated||e.set(o)}else{const e=t.getStaticValue(s);t.addValue(s,as(void 0!==e?e:o,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}const e={};for(let t=0;t<qo;t++){const n=L[t],i=this.props[n];(R(i)||!1===i)&&(e[n]=i)}return e}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=as(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){var n;let i=void 0===this.latestValues[t]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,t))&&void 0!==n?n:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&(Qe(i)||qe(i))?i=parseFloat(i):(s=i,!Xo.find(cn(s))&&Dn.test(e)&&(i=In(t,e))),this.setBaseTarget(t,tt(i)?i.get():i)),tt(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=Jt(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||tt(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new ss),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Jo extends Zo{constructor(){super(...arguments),this.KeyframeResolver=Wn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}}class Qo extends Jo{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(J.has(e)){const t=On(e);return t&&t.default||0}return e=Kt.has(e)?e:P(e),t.getAttribute(e)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(t,e,n){return qt(t,e,n)}build(t,e,n,i){Nt(t,e,n,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,n,i){Gt(t,e,0,i)}mount(t){this.isSVGTag=Ht(t.tagName),super.mount(t)}}class tr extends Jo{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(J.has(e)){const t=On(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=(ot(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return lo(t,e)}build(t,e,n,i){Vt(t,e,n,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return _t(t,e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;tt(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=""+t)}))}renderInstance(t,e,n,i){Xt(t,e,n,i)}}function er(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=zo(t)?new Qo(e,{enableHardwareAcceleration:!1}):new tr(e,{enableHardwareAcceleration:!0});n.mount(t),No.set(t,n)}function nr(t,e,n){const i=tt(t)?t:as(t);return i.start(ts("",i,e,n)),i.animation}function ir(t,e=100){const n=Zn({keyframes:[0,e],...t}),i=Math.min(Fi(n),2e4);return{type:"keyframes",ease:t=>n.next(i*t).value/e,duration:Ie(i)}}function sr(t,e,n,i){var s;return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(s=i.get(e))&&void 0!==s?s:t}const or=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t};function rr(t,e){return si(t)?t[or(0,t.length,e)]:t}function ar(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(is(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:yi(s,o,i[r]),easing:rr(n,r)})}function lr(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function ur(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function cr(t,e){return e[t]||(e[t]=[]),e[t]}function hr(t){return Array.isArray(t)?t:[t]}function dr(t,e){return t[e]?{...t,...t[e]}:{...t}}const pr=t=>"number"==typeof t,mr=t=>t.every(pr);function fr(t,e,n,i){const s=Wo(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t];No.has(i)||er(i);const a=No.get(i),l={...n};"function"==typeof l.delay&&(l.delay=l.delay(t,o)),r.push(...ds(a,{...e,transition:l},{}))}return new Qi(r)}function gr(t,e,n){const i=[];return function(t,{defaultTransition:e={},...n}={},i){const s=e.duration||.3,o=new Map,r=new Map,a={},l=new Map;let u=0,c=0,h=0;for(let n=0;n<t.length;n++){const o=t[n];if("string"==typeof o){l.set(o,c);continue}if(!Array.isArray(o)){l.set(o.name,sr(c,o.at,u,l));continue}let[d,p,m={}]=o;void 0!==m.at&&(c=sr(c,m.at,u,l));let f=0;const g=(t,n,i,o=0,r=0)=>{const a=hr(t),{delay:l=0,times:u=Li(a),type:d="keyframes",...p}=n;let{ease:m=e.ease||"easeOut",duration:g}=n;const y="function"==typeof l?l(o,r):l,v=a.length;if(v<=2&&"spring"===d){let t=100;if(2===v&&mr(a)){const e=a[1]-a[0];t=Math.abs(e)}const e={...p};void 0!==g&&(e.duration=Oe(g));const n=ir(e,t);m=n.ease,g=n.duration}null!=g||(g=s);const x=c+y,P=x+g;1===u.length&&0===u[0]&&(u[1]=1);const w=u.length-a.length;w>0&&ki(u,w),1===a.length&&a.unshift(null),ar(i,a,m,u,x,P),f=Math.max(y+g,f),h=Math.max(P,h)};if(tt(d)){g(p,m,cr("default",ur(d,r)))}else{const t=Wo(d,i,a),e=t.length;for(let n=0;n<e;n++){p=p,m=m;const i=ur(t[n],r);for(const t in p)g(p[t],dr(m,t),cr(t,i),n,e)}}u=c,c+=f}return r.forEach((t,i)=>{for(const s in t){const r=t[s];r.sort(lr);const a=[],l=[],u=[];for(let t=0;t<r.length;t++){const{at:e,value:n,easing:i}=r[t];a.push(n),l.push(gi(0,h,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),o.has(i)||o.set(i,{keyframes:{},transition:{}});const c=o.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:h,ease:u,times:l,...n}}}),o}(t,e,n).forEach(({keyframes:t,transition:e},n)=>{let s;s=tt(n)?nr(n,t.default,e.default):fr(n,t,e),i.push(s)}),new Qi(i)}const yr=t=>function(e,n,i){let s;var o;return o=e,s=Array.isArray(o)&&Array.isArray(o[0])?gr(e,n,t):function(t){return"object"==typeof t&&!Array.isArray(t)}(n)?fr(e,n,i,t):nr(e,n,i),t&&t.animations.push(s),s},vr=yr(),xr=new WeakMap;let Pr;function wr({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=xr.get(t))||void 0===i||i.forEach(i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})})}function Tr(t){t.forEach(wr)}function Sr(t,e){Pr||"undefined"!=typeof ResizeObserver&&(Pr=new ResizeObserver(Tr));const n=Wo(t);return n.forEach(t=>{let n=xr.get(t);n||(n=new Set,xr.set(t,n)),n.add(e),null==Pr||Pr.observe(t)}),()=>{n.forEach(t=>{const n=xr.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Pr||Pr.unobserve(t)})}}const br=new Set;let Ar;function Er(t){return br.add(t),Ar||(Ar=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};br.forEach(t=>t(e))},window.addEventListener("resize",Ar)),()=>{br.delete(t),!br.size&&Ar&&(Ar=void 0)}}const Cr={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Vr(t,e,n,i){const s=n[e],{length:o,position:r}=Cr[e],a=s.current,l=n.time;s.current=t["scroll"+r],s.scrollLength=t["scroll"+o]-t["client"+o],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=gi(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:$n(s.current-a,u)}const Mr={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Rr={start:0,center:.5,end:1};function Dr(t,e,n=0){let i=0;if(t in Rr&&(t=Rr[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const kr=[0,0];function Lr(t,e,n,i){let s=Array.isArray(t)?t:kr,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,Rr[t]?t:"0"]),o=Dr(s[0],n,i),r=Dr(s[1],e),o-r}const Br={x:0,y:0};function Fr(t,e,n){const{offset:i=Mr.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):Br,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=Lr(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=Di(e[o].offset,Li(i)),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=e[o].interpolate(e[o].current)}function jr(t,e,n,i={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){Vr(t,"x",e,n),Vr(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&Fr(t,n,i)},notify:()=>e(n)}}const Or=new WeakMap,Ir=new WeakMap,Ur=new WeakMap,Wr=t=>t===document.documentElement?window:t;function Nr(t,{container:e=document.documentElement,...n}={}){let i=Ur.get(e);i||(i=new Set,Ur.set(e,i));const s=jr(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!Or.has(e)){const t=()=>{for(const t of i)t.measure()},n=()=>{for(const t of i)t.update(ae.timestamp)},s=()=>{for(const t of i)t.notify()},a=()=>{oe.read(t,!1,!0),oe.read(n,!1,!0),oe.update(s,!1,!0)};Or.set(e,a);const l=Wr(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&Ir.set(e,(r=a,"function"==typeof(o=e)?Er(o):Sr(o,r))),l.addEventListener("scroll",a,{passive:!0})}var o,r;const a=Or.get(e);return oe.read(a,!1,!0),()=>{var t;re(a);const n=Ur.get(e);if(!n)return;if(n.delete(s),n.size)return;const i=Or.get(e);Or.delete(e),i&&(Wr(e).removeEventListener("scroll",i),null===(t=Ir.get(e))||void 0===t||t(),window.removeEventListener("resize",i))}}const zr=new Map;function Hr({source:t=document.documentElement,axis:e="y"}={}){zr.has(t)||zr.set(t,{});const n=zr.get(t);return n[e]||(n[e]=Ji()?new ScrollTimeline({source:t,axis:e}):function({source:t,axis:e="y"}){const n={value:0},i=Nr(t=>{n.value=100*t[e].progress},{container:t,axis:e});return{currentTime:n,cancel:i}}({source:t,axis:e})),n[e]}const $r={some:0,all:1};function Yr(t,e,{root:n,margin:i,amount:s="some"}={}){const o=Wo(t),r=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else n&&(n(t),r.delete(t.target))})},{root:n,rootMargin:i,threshold:"number"==typeof s?s:$r[s]});return o.forEach(t=>a.observe(t)),()=>a.disconnect()}function Xr(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=t[1+n],o=t[2+n],r=t[3+n],a=Di(s,o,{mixer:(l=o[0],(t=>t&&"object"==typeof t&&t.mix)(l)?l.mix:void 0),...r});var l;return e?a(i):a}const Kr=oe,Gr=b.reduce((t,e)=>(t[e]=t=>re(t),t),{}),_r=["","X","Y","Z"],qr={visibility:"hidden"};let Zr=0;const Jr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Qr(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function ta({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=Zr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var t;this.projectionUpdateScheduled=!1,Jr.totalNodes=Jr.resolvedTargetDeltas=Jr.recalculatedProjection=0,this.nodes.forEach(ia),this.nodes.forEach(ca),this.nodes.forEach(ha),this.nodes.forEach(sa),t=Jr,window.MotionDebug&&window.MotionDebug.record(t)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Oo)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ss),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=zo(e),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=Uo(i,250),Io.hasAnimatedSinceResize&&(Io.hasAnimatedSinceResize=!1,this.nodes.forEach(ua))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||ya,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!ko(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...He(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ua(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,re(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(da),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.HandoffCancelAllAnimations&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return!1;const{visualElement:n}=e.options;return!!n&&(!!cs(n)||!(!e.parent||e.parent.hasCheckedOptimisedAppear)&&t(e.parent))}(this)&&window.HandoffCancelAllAnimations(),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(ra);this.isUpdating||this.nodes.forEach(aa),this.isUpdating=!1,this.nodes.forEach(la),this.nodes.forEach(ea),this.nodes.forEach(na),this.clearAllSnapshots();const t=_e.now();ae.delta=ct(0,1e3/60,t-ae.timestamp),ae.timestamp=t,ae.isProcessing=!0,le.update.process(ae),le.preRender.process(ae),le.render.process(ae),ae.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,E.read(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(oa),this.sharedNodes.forEach(pa)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,oe.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){oe.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:n(this.instance)})}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!Do(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||_s(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Pa((i=n).x),Pa(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox(),{scroll:n}=this.root;return n&&(io(e.x,n.offset.x),io(e.y,n.offset.y)),e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};bo(e,t);for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;if(i!==this.root&&s&&o.layoutScroll){if(s.isRoot){bo(e,t);const{scroll:n}=this.root;n&&(io(e.x,-n.offset.x),io(e.y,-n.offset.y))}io(e.x,s.offset.x),io(e.y,s.offset.y)}}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};bo(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ao(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),_s(i.latestValues)&&ao(n,i.latestValues)}return _s(this.latestValues)&&ao(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};bo(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!_s(n.latestValues))continue;Gs(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};bo(i,n.measurePageBox()),Mo(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return _s(this.latestValues)&&Mo(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ae.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=ae.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Is(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),bo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,js(r.x,a.x,l.x),js(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):bo(this.target,this.layout.layoutBox),eo(this.target,this.targetDelta)):bo(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Is(this.relativeTargetOrigin,this.target,t.target),bo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Jr.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Gs(this.parent.latestValues)&&!qs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===ae.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;bo(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const s=o.instance;s&&s.style&&"contents"===s.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ao(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,eo(t,r)),i&&_s(o.latestValues)&&ao(t,o.latestValues))}e.x=no(e.x),e.y=no(e.y)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;Fs(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=Fo(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===r&&this.treeScale.y===a||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Jr.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(ga));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;ma(o.x,t.x,n),ma(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Is(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){fa(t.x,e.x,n.x,i),fa(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,l.x.min===d.x.min&&l.x.max===d.x.max&&l.y.min===d.y.min&&l.y.max===d.y.max)&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),bo(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=yi(0,void 0!==n.opacity?n.opacity:1,Po(i)),t.opacityExit=yi(void 0!==e.opacity?e.opacity:1,0,wo(i))):o&&(t.opacity=yi(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let s=0;s<go;s++){const o=`border${fo[s]}Radius`;let r=xo(e,o),a=xo(n,o);if(void 0===r&&void 0===a)continue;r||(r=0),a||(a=0);0===r||0===a||vo(r)===vo(a)?(t[o]=Math.max(yi(yo(r),yo(a),i),0),(wt.test(a)||wt.test(r))&&(t[o]+="%")):t[o]=a}(e.rotate||n.rotate)&&(t.rotate=yi(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(re(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=oe.update(()=>{Io.hasAnimatedSinceResize=!0,this.currentAnimation=nr(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&wa(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=ks(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=ks(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}bo(e,n),ao(e,s),Fs(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Bo);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&Qr("z",t,i,this.animationValues);for(let e=0;e<_r.length;e++)Qr("rotate"+_r[e],t,i,this.animationValues),Qr("skew"+_r[e],t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return qr;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=ee(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=ee(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!_s(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=Fo(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const t in _){if(void 0===r[t])continue;const{correct:e,applyTo:n}=_[t],s="none"===i.transform?r[t]:e(r[t],o);if(n){const t=n.length;for(let e=0;e<t;e++)i[n[e]]=s}else i[t]=s}return this.options.layoutId&&(i.pointerEvents=o===this?ee(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(ra),this.root.sharedNodes.clear()}}}function ea(t){t.updateLayout()}function na(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?Ys(t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=ks(i);i.min=e[t].min,i.max=i.min+s}):wa(s,n.layoutBox,e)&&Ys(i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=ks(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Fs(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Fs(a,t.applyTransform(i,!0),n.measuredBox):Fs(a,e,n.layoutBox);const l=!Do(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Is(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Is(a,e,o.layoutBox),ko(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function ia(t){Jr.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sa(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function oa(t){t.clearSnapshot()}function ra(t){t.clearMeasurements()}function aa(t){t.isLayoutDirty=!1}function la(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ua(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ca(t){t.resolveTargetDelta()}function ha(t){t.calcProjection()}function da(t){t.resetSkewAndRotation()}function pa(t){t.removeLeadSnapshot()}function ma(t,e,n){t.translate=yi(e.translate,0,n),t.scale=yi(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function fa(t,e,n,i){t.min=yi(e.min,n.min,i),t.max=yi(e.max,n.max,i)}function ga(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const ya={duration:.45,ease:[.4,0,.1,1]},va=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),xa=va("applewebkit/")&&!va("chrome/")?Math.round:se;function Pa(t){t.min=xa(t.min),t.max=xa(t.max)}function wa(t,e,n){return"position"===t||"preserve-aspect"===t&&!Ls(Lo(e),Lo(n),.2)}const Ta=ta({attachResizeListener:(t,e)=>de(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Sa={current:void 0},ba=ta({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Sa.current){const t=new Ta({});t.mount(window),t.setOptions({layoutScroll:!0}),Sa.current=t}return Sa.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Aa=t=>!t.isLayoutDirty&&t.willUpdate(!1);function Ea(){const t=new Set,e=new WeakMap,n=()=>t.forEach(Aa);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}function Ca(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Va={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Tt.test(t))return t;t=parseFloat(t)}return`${Ca(t,e.target.x)}% ${Ca(t,e.target.y)}%`}},Ma={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Dn.parse(t);if(s.length>5)return i;const o=Dn.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=yi(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function Ra(){const t=e.useContext(g);if(null===t)return[!0,null];const{isPresent:n,onExitComplete:i,register:s}=t,o=e.useId();e.useEffect(()=>s(o),[]);return!n&&i?[!1,()=>i&&i(o)]:[!0]}class Da extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;q(La),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Io.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||oe.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),E.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ka(t){const[n,i]=Ra(),s=e.useContext(N);return d(Da,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(z),isPresent:n,safeToRemove:i})}const La={borderRadius:{...Va,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Va,borderTopRightRadius:Va,borderBottomLeftRadius:Va,borderBottomRightRadius:Va,boxShadow:Ma},Ba={pan:{Feature:class extends be{constructor(){super(...arguments),this.removePointerDownListener=se}onPointerDown(t){this.session=new As(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:uo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:mo(t),onStart:mo(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&oe.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=ge(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends be{constructor(t){super(t),this.removeGroupControls=se,this.removeListeners=se,this.controls=new ho(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||se}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:ba,MeasureLayout:ka}},Fa=(t,n)=>G(t)?new Qo(n,{enableHardwareAcceleration:!1}):new tr(n,{allowProjection:t!==e.Fragment,enableHardwareAcceleration:!0}),ja={layout:{ProjectionNode:ba,MeasureLayout:ka}},Oa={...Ts,...Be,...Ba,...ja},Ia=X((t,e)=>he(t,e,Oa,Fa));const Ua=X(he);function Wa(){const t=e.useRef(!1);return v(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function Na(){const t=Wa(),[n,i]=e.useState(0),s=e.useCallback(()=>{t.current&&i(n+1)},[n]);return[e.useCallback(()=>oe.postRender(s),[s]),n]}class za extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Ha({children:t,isPresent:n}){const s=e.useId(),o=e.useRef(null),r=e.useRef({width:0,height:0,top:0,left:0}),{nonce:a}=e.useContext(m);return e.useInsertionEffect(()=>{const{width:t,height:e,top:i,left:l}=r.current;if(n||!o.current||!t||!e)return;o.current.dataset.motionPopId=s;const u=document.createElement("style");return a&&(u.nonce=a),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`\n          [data-motion-pop-id="${s}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            top: ${i}px !important;\n            left: ${l}px !important;\n          }\n        `),()=>{document.head.removeChild(u)}},[n]),d(za,{isPresent:n,childRef:o,sizeRef:r,children:i.cloneElement(t,{ref:o})})}const $a=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l})=>{const u=Qt(Ya),c=e.useId(),h=e.useMemo(()=>({id:c,initial:n,isPresent:s,custom:r,onExitComplete:t=>{u.set(t,!0);for(const t of u.values())if(!t)return;o&&o()},register:t=>(u.set(t,!1),()=>u.delete(t))}),a?[Math.random()]:[s]);return e.useMemo(()=>{u.forEach((t,e)=>u.set(e,!1))},[s]),i.useEffect(()=>{!s&&!u.size&&o&&o()},[s]),"popLayout"===l&&(t=d(Ha,{isPresent:s,children:t})),d(g.Provider,{value:h,children:t})};function Ya(){return new Map}function Xa(t){return e.useEffect(()=>()=>t(),[])}const Ka=t=>t.key||"";function Ga(t){return"function"==typeof t}const _a=e.createContext(null),qa=t=>!0===t,Za=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(N),o=e.useContext(_a),[r,a]=Na(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>qa(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:qa(i)&&s.group||Ea()});const c=e.useMemo(()=>({...l.current,forceRender:r}),[a]);return d(N.Provider,{value:c,children:t})},Ja=e.createContext(null);function Qa(t){return t.value}function tl(t,e){return t.layout.min-e.layout.min}function el(t){const n=Qt(()=>as(t)),{isStatic:i}=e.useContext(m);if(i){const[,i]=e.useState(t);e.useEffect(()=>n.on("change",i),[])}return n}function nl(t,e){const n=el(e()),i=()=>n.set(e());return i(),v(()=>{const e=()=>oe.preRender(i,!1,!0),n=t.map(t=>t.on("change",e));return()=>{n.forEach(t=>t()),re(i)}}),n}function il(t,e,n,i){if("function"==typeof t)return function(t){os.current=[],t();const e=nl(os.current,t);return os.current=void 0,e}(t);const s="function"==typeof e?e:Xr(e,n,i);return Array.isArray(t)?sl(t,s):sl([t],([t])=>s(t))}function sl(t,e){const n=Qt(()=>[]);return nl(t,()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)})}function ol(t,e=0){return tt(t)?t:el(e)}const rl={Group:e.forwardRef((function({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=Qt(()=>Ia(n)),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex(e=>t===e.value);-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(tl)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex(t=>t.value===e);if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=yi(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?function([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(Qa).filter(t=>-1!==o.indexOf(t))))}};return e.useEffect(()=>{c.current=!1}),d(l,{...r,ref:a,ignoreStrict:!0,children:d(Ja.Provider,{value:h,children:t})})})),Item:e.forwardRef((function({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=Qt(()=>Ia(s)),c=e.useContext(Ja),h={x:ol(n.x),y:ol(n.y)},p=il([h.x,h.y],([t,e])=>t||e?1:"unset"),{axis:m,registerItem:f,updateOrder:g}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&g(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>f(i,t),ref:l,ignoreStrict:!0,children:t})}))},al={renderer:Fa,...Ts,...Be},ll={...al,...Ba,...ja};function ul(t,n,i){e.useInsertionEffect(()=>t.on(n,i),[t,n,i])}function cl(t,e){Ze(Boolean(!e||e.current))}const hl=()=>({scrollX:as(0),scrollY:as(0),scrollXProgress:as(0),scrollYProgress:as(0)});function dl({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=Qt(hl);return(i?v:e.useEffect)(()=>(cl(0,n),cl(0,t),Nr(({x:t,y:e})=>{o.scrollX.set(t.current),o.scrollXProgress.set(t.progress),o.scrollY.set(e.current),o.scrollYProgress.set(e.progress)},{...s,container:(null==t?void 0:t.current)||void 0,target:(null==n?void 0:n.current)||void 0})),[t,n,JSON.stringify(s.offset)]),o}function pl(t){const n=e.useRef(0),{isStatic:i}=e.useContext(m);e.useEffect(()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return oe.update(e,!0),()=>re(e)},[t])}class ml extends rs{constructor(){super(...arguments),this.members=[],this.transforms=new Set}add(t){let e;J.has(t)?(this.transforms.add(t),e="transform"):t.startsWith("origin")||ot(t)||"willChange"===t||(e=P(t)),e&&(ns(this.members,e),this.update())}remove(t){J.has(t)?(this.transforms.delete(t),this.transforms.size||is(this.members,"transform")):is(this.members,P(t)),this.update()}update(){this.set(this.members.length?this.members.join(", "):"auto")}}function fl(){!$o.current&&Yo();const[t]=e.useState(Ho.current);return t}function gl(t,e){[...e].reverse().forEach(n=>{const i=t.getVariant(n);i&&us(t,i),t.variantChildren&&t.variantChildren.forEach(t=>{gl(t,e)})})}function yl(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach(t=>{i.push(fs(t,e,{transitionOverride:n}))}),Promise.all(i)},set:e=>t.forEach(t=>{!function(t,e){Array.isArray(e)?gl(t,e):"string"==typeof e?gl(t,[e]):us(t,e)}(t,e)}),stop(){t.forEach(t=>{!function(t){t.values.forEach(t=>t.stop())}(t)})},mount:()=>()=>{e.stop()}};return e}function vl(){const t=Qt(yl);return v(t.mount,[]),t}const xl=vl;class Pl{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}const wl=()=>new Pl;function Tl(t){return null!==t&&"object"==typeof t&&H in t}function Sl(){return bl}function bl(t){Sa.current&&(Sa.current.isUpdating=!1,Sa.current.blockUpdate(),t&&t())}const Al=(t,e)=>`${t}: ${e}`,El=new Map;let Cl,Vl,Ml;function Rl(t,e,n,i){const s=J.has(e)?"transform":e,o=Al(t,s),r=El.get(o);if(!r)return null;const{animation:a,startTime:l}=r;return null===l||window.HandoffComplete?((()=>{if(El.delete(o),i)i.render(()=>i.render(()=>{try{a.cancel()}catch(t){}}));else try{a.cancel()}catch(t){}})(),null):(void 0===Cl&&(Cl=performance.now()),Cl-l||0)}const Dl=()=>({});class kl extends Zo{build(){}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const Ll=ne({scrapeMotionValuesFromProps:Dl,createRenderState:Dl});const Bl=t=>t>.001?1/t:1e5;let Fl=0;t.AcceleratedAnimation=qi,t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,exitBeforeEnter:o,presenceAffectsLayout:r=!0,mode:a="sync"})=>{const l=e.useContext(N).forceRender||Na()[0],u=Wa(),c=function(t){const n=[];return e.Children.forEach(t,t=>{e.isValidElement(t)&&n.push(t)}),n}(t);let p=c;const m=e.useRef(new Map).current,f=e.useRef(p),g=e.useRef(new Map).current,y=e.useRef(!0);if(v(()=>{y.current=!1,function(t,e){t.forEach(t=>{const n=Ka(t);e.set(n,t)})}(c,g),f.current=p}),Xa(()=>{y.current=!0,g.clear(),m.clear()}),y.current)return d(h,{children:p.map(t=>d($a,{isPresent:!0,initial:!!i&&void 0,presenceAffectsLayout:r,mode:a,children:t},Ka(t)))});p=[...p];const x=f.current.map(Ka),P=c.map(Ka),w=x.length;for(let t=0;t<w;t++){const e=x[t];-1!==P.indexOf(e)||m.has(e)||m.set(e,void 0)}return"wait"===a&&m.size&&(p=[]),m.forEach((t,e)=>{if(-1!==P.indexOf(e))return;const i=g.get(e);if(!i)return;const o=x.indexOf(e);let h=t;if(!h){h=d($a,{isPresent:!1,onExitComplete:()=>{m.delete(e);const t=Array.from(g.keys()).filter(t=>!P.includes(t));if(t.forEach(t=>g.delete(t)),f.current=c.filter(n=>{const i=Ka(n);return i===e||t.includes(i)}),!m.size){if(!1===u.current)return;l(),s&&s()}},custom:n,presenceAffectsLayout:r,mode:a,children:i},Ka(i)),m.set(e,h)}p.splice(o,0,h)}),p=p.map(t=>{const e=t.key;return m.has(e)?t:d($a,{isPresent:!0,presenceAffectsLayout:r,mode:a,children:t},Ka(t))}),d(h,{children:m.size?p:p.map(t=>e.cloneElement(t))})},t.AnimateSharedLayout=({children:t})=>(i.useEffect(()=>{},[]),d(Za,{id:Qt(()=>"asl-"+Fl++),children:t})),t.DeprecatedLayoutGroupContext=_a,t.DragControls=Pl,t.FlatTree=Oo,t.LayoutGroup=Za,t.LayoutGroupContext=N,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!Ga(n)),o=e.useRef(void 0);if(!Ga(n)){const{renderer:t,...e}=n;o.current=t,W(e)}return e.useEffect(()=>{Ga(n)&&n().then(({renderer:t,...e})=>{W(e),o.current=t,s(!0)})},[]),d(x.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&jt(n),(i={...e.useContext(m),...i}).isStatic=Qt(()=>i.isStatic);const s=e.useMemo(()=>i,[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(m.Provider,{value:s,children:t})},t.MotionConfigContext=m,t.MotionContext=f,t.MotionGlobalConfig=T,t.MotionValue=rs,t.PresenceContext=g,t.Reorder=rl,t.SwitchLayoutGroupContext=z,t.VisualElement=Zo,t.addPointerEvent=ge,t.addPointerInfo=fe,t.addScaleCorrector=q,t.animate=vr,t.animateValue=Wi,t.animateVisualElement=fs,t.animationControls=yl,t.animations=Ts,t.anticipate=pi,t.backIn=hi,t.backInOut=di,t.backOut=ci,t.buildTransform=it,t.calcLength=ks,t.cancelFrame=re,t.cancelSync=Gr,t.circIn=ai,t.circInOut=ui,t.circOut=li,t.clamp=ct,t.color=An,t.complex=Dn,t.createBox=$s,t.createDomMotionComponent=function(t){return $(he(t,{forwardMotionProps:!1},Oa,Fa))},t.createMotionComponent=$,t.createScopedAnimate=yr,t.cubicBezier=ti,t.delay=Uo,t.disableInstantTransitions=function(){$e.current=!1},t.distance=Ss,t.distance2D=bs,t.domAnimation=al,t.domMax=ll,t.easeIn=ei,t.easeInOut=ii,t.easeOut=ni,t.filterProps=Ot,t.frame=oe,t.frameData=ae,t.inView=Yr,t.interpolate=Di,t.invariant=Je,t.isBrowser=y,t.isDragActive=Se,t.isMotionComponent=Tl,t.isMotionValue=tt,t.isValidMotionProp=Bt,t.m=Ua,t.makeUseVisualState=ne,t.mirrorEasing=oi,t.mix=Ri,t.motion=Ia,t.motionValue=as,t.optimizedAppearDataAttribute=w,t.pipe=ve,t.progress=gi,t.px=Tt,t.resolveMotionValue=ee,t.reverseEasing=ri,t.scroll=function(t,e){const n=Hr(e);return"function"==typeof t?Zi(t,n):t.attachTimeline(n)},t.scrollInfo=Nr,t.spring=Zn,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=fi(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.HandoffComplete)return void(window.HandoffAppearAnimations=void 0);const o=t.dataset.framerAppearId;if(!o)return;window.HandoffAppearAnimations=Rl;const r=Al(o,e);Ml||(Ml=Ki(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),El.set(r,{animation:Ml,startTime:null}),window.HandoffCancelAllAnimations||(window.HandoffCancelAllAnimations=()=>{El.forEach(({animation:t})=>{t.cancel()}),El.clear(),window.HandoffCancelAllAnimations=void 0}));const a=()=>{Ml.cancel();const o=Ki(t,e,n,i);void 0===Vl&&(Vl=performance.now()),o.startTime=Vl,El.set(r,{animation:o,startTime:Vl}),s&&s(o)};Ml.ready?Ml.ready.then(a).catch(se):a()},t.steps=le,t.sync=Kr,t.transform=Xr,t.unwrapMotionComponent=function(t){if(Tl(t))return t[H]},t.useAnimate=function(){const t=Qt(()=>({current:null,animations:[]})),e=Qt(()=>yr(t));return Xa(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimation=xl,t.useAnimationControls=vl,t.useAnimationFrame=pl,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]);return[i,e.useCallback(e=>{n.current="number"!=typeof e?or(0,t.length,n.current+1):e,s(t[n.current])},[t.length,...t])]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=Ll({},!1),o=Qt(()=>new kl({props:{},visualState:s,presenceContext:null},{initialState:t}));return e.useEffect(()=>(o.mount({}),()=>o.unmount()),[o]),e.useEffect(()=>{o.update({onUpdate:t=>{i({...t})}},null)},[i,o]),[n,Qt(()=>t=>fs(o,t))]},t.useDeprecatedInvertedScale=function(t){let n=el(1),i=el(1);const{visualElement:s}=e.useContext(f);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:il(n,Bl),scaleY:il(i,Bl)}},t.useDomEvent=function(t,n,i,s){e.useEffect(()=>{const e=t.current;if(i&&e)return de(e,n,i,s)},[t,n,i,s])},t.useDragControls=function(){return Qt(wl)},t.useElementScroll=function(t){return dl({container:t})},t.useForceUpdate=Na,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1}={}){const[r,a]=e.useState(!1);return e.useEffect(()=>{if(!t.current||o&&r)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return Yr(t.current,()=>(a(!0),o?void 0:()=>a(!1)),e)},[n,t,i,o,s]),r},t.useInstantLayoutTransition=Sl,t.useInstantTransition=function(){const[t,n]=Na(),i=Sl(),s=e.useRef();return e.useEffect(()=>{oe.postRender(()=>oe.postRender(()=>{n===s.current&&($e.current=!1)}))},[n]),e=>{i(()=>{$e.current=!0,t(),e(),s.current=n+1})}},t.useIsPresent=function(){return null===(t=e.useContext(g))||t.isPresent;var t},t.useIsomorphicLayoutEffect=v,t.useMotionTemplate=function(t,...e){const n=t.length;return nl(e.filter(tt),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=tt(n)?n.get():n)}return i}))},t.useMotionValue=el,t.useMotionValueEvent=ul,t.usePresence=Ra,t.useReducedMotion=fl,t.useReducedMotionConfig=function(){const t=fl(),{reducedMotion:n}=e.useContext(m);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback(()=>{const t=Sa.current;t&&t.resetTree()},[])},t.useScroll=dl,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(m),s=e.useRef(null),o=el(tt(t)?t.get():t),r=e.useRef(o.get()),a=e.useRef(()=>{}),l=()=>{const t=s.current;t&&0===t.time&&t.sample(ae.delta),u(),s.current=Wi({keyframes:[o.get(),r.current],velocity:o.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:a.current})},u=()=>{s.current&&s.current.stop()};return e.useInsertionEffect(()=>o.attach((t,e)=>i?e(t):(r.current=t,a.current=e,oe.update(l),o.get()),u),[JSON.stringify(n)]),v(()=>{if(tt(t))return t.on("change",t=>o.set(parseFloat(t)))},[o]),o},t.useTime=function(){const t=el(0);return pl(e=>t.set(e)),t},t.useTransform=il,t.useUnmountEffect=Xa,t.useVelocity=function(t){const e=el(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&oe.update(n)};return ul(t,"change",()=>{oe.update(n,!1,!0)}),e},t.useViewportScroll=function(){return dl()},t.useWillChange=function(){return Qt(()=>new ml("auto"))},t.visualElementStore=No,t.warning=Ze,t.wrap=or}));
