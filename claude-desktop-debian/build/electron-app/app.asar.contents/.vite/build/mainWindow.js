"use strict";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new t.Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="04880a6b-8cdd-458e-aab1-27beb2af7ff4",t._sentryDebugIdIdentifier="sentry-dbid-04880a6b-8cdd-458e-aab1-27beb2af7ff4")}catch{}})();const k=require("electron"),Jr=Object.prototype.toString;function jn(t){switch(Jr.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return we(t,Error)}}function Ge(t,e){return Jr.call(t)===`[object ${e}]`}function Kr(t){return Ge(t,"ErrorEvent")}function nr(t){return Ge(t,"DOMError")}function mi(t){return Ge(t,"DOMException")}function ie(t){return Ge(t,"String")}function Ln(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function Fn(t){return t===null||Ln(t)||typeof t!="object"&&typeof t!="function"}function Ue(t){return Ge(t,"Object")}function Wt(t){return typeof Event<"u"&&we(t,Event)}function gi(t){return typeof Element<"u"&&we(t,Element)}function yi(t){return Ge(t,"RegExp")}function Gt(t){return!!(t&&t.then&&typeof t.then=="function")}function _i(t){return Ue(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function we(t,e){try{return t instanceof e}catch{return!1}}function Xr(t){return!!(typeof t=="object"&&t!==null&&(t.__isVue||t._isVue))}function je(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function rr(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const s=t[r];try{Xr(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function vi(t,e,n=!1){return ie(t)?yi(e)?e.test(t):ie(e)?n?t===e:t.includes(e):!1:!1}function zt(t,e=[],n=!1){return e.some(r=>vi(t,r,n))}function bi(t,e,n=250,r,s,i,o){if(!i.exception||!i.exception.values||!o||!we(o.originalException,Error))return;const a=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;a&&(i.exception.values=Ei(ln(t,e,s,o.originalException,r,i.exception.values,a,0),n))}function ln(t,e,n,r,s,i,o,a){if(i.length>=n+1)return i;let c=[...i];if(we(r[s],Error)){sr(o,a);const u=t(e,r[s]),d=c.length;ir(u,s,d,a),c=ln(t,e,n,r[s],s,[u,...c],u,d)}return Array.isArray(r.errors)&&r.errors.forEach((u,d)=>{if(we(u,Error)){sr(o,a);const l=t(e,u),f=c.length;ir(l,`errors[${d}]`,f,a),c=ln(t,e,n,u,s,[l,...c],l,f)}}),c}function sr(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function ir(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function Ei(t,e){return t.map(n=>(n.value&&(n.value=je(n.value,e)),n))}function Qr(t){if(t!==void 0)return t>=400&&t<500?"warning":t>=500?"error":void 0}const be="8.33.1",C=globalThis;function Yt(t,e,n){const r=n||C,s=r.__SENTRY__=r.__SENTRY__||{},i=s[be]=s[be]||{};return i[t]||(i[t]=e())}const Un=C,Si=80;function es(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let i=0,o=0;const a=" > ",c=a.length;let u;const d=Array.isArray(e)?e:e.keyAttrs,l=!Array.isArray(e)&&e.maxStringLength||Si;for(;n&&i++<r&&(u=wi(n,d),!(u==="html"||i>1&&o+s.length*c+u.length>=l));)s.push(u),o+=u.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function wi(t,e){const n=t,r=[];if(!n||!n.tagName)return"";if(Un.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e&&e.length?e.filter(o=>n.getAttribute(o)).map(o=>[o,n.getAttribute(o)]):null;if(s&&s.length)s.forEach(o=>{r.push(`[${o[0]}="${o[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const o=n.className;if(o&&ie(o)){const a=o.split(/\s+/);for(const c of a)r.push(`.${c}`)}}const i=["aria-label","type","name","title","alt"];for(const o of i){const a=n.getAttribute(o);a&&r.push(`[${o}="${a}"]`)}return r.join("")}function xi(){try{return Un.document.location.href}catch{return""}}function ki(t){if(!Un.HTMLElement)return null;let e=t;const n=5;for(let r=0;r<n;r++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}const ht=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ti="Sentry Logger ",fn=["debug","info","warn","error","log","assert","trace"],At={};function mt(t){if(!("console"in C))return t();const e=C.console,n={},r=Object.keys(At);r.forEach(s=>{const i=At[s];n[s]=e[s],e[s]=i});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}function Ii(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return ht?fn.forEach(n=>{e[n]=(...r)=>{t&&mt(()=>{C.console[n](`${Ti}[${n}]:`,...r)})}}):fn.forEach(n=>{e[n]=()=>{}}),e}const E=Yt("logger",Ii),Ci=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Ri(t){return t==="http"||t==="https"}function Jt(t,e=!1){const{host:n,path:r,pass:s,port:i,projectId:o,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&s?`:${s}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${o}`}function Oi(t){const e=Ci.exec(t);if(!e){mt(()=>{console.error(`Invalid Sentry Dsn: ${t}`)});return}const[n,r,s="",i="",o="",a=""]=e.slice(1);let c="",u=a;const d=u.split("/");if(d.length>1&&(c=d.slice(0,-1).join("/"),u=d.pop()),u){const l=u.match(/^\d+/);l&&(u=l[0])}return ts({host:i,pass:s,path:c,projectId:u,port:o,protocol:n,publicKey:r})}function ts(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function Ni(t){if(!ht)return!0;const{port:e,projectId:n,protocol:r}=t;return["protocol","publicKey","host","projectId"].find(o=>t[o]?!1:(E.error(`Invalid Sentry Dsn: ${o} missing`),!0))?!1:n.match(/^\d+$/)?Ri(r)?e&&isNaN(parseInt(e,10))?(E.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):!0:(E.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(E.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function Ai(t){const e=typeof t=="string"?Oi(t):ts(t);if(!(!e||!Ni(e)))return e}class K extends Error{constructor(e,n="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}function F(t,e,n){if(!(e in t))return;const r=t[e],s=n(r);typeof s=="function"&&ns(s,r),t[e]=s}function xe(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{ht&&E.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function ns(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,xe(t,"__sentry_original__",e)}catch{}}function Zn(t){return t.__sentry_original__}function Mi(t){return Object.keys(t).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`).join("&")}function rs(t){if(jn(t))return{message:t.message,name:t.name,stack:t.stack,...ar(t)};if(Wt(t)){const e={type:t.type,target:or(t.target),currentTarget:or(t.currentTarget),...ar(t)};return typeof CustomEvent<"u"&&we(t,CustomEvent)&&(e.detail=t.detail),e}else return t}function or(t){try{return gi(t)?es(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}}function ar(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}else return{}}function Pi(t,e=40){const n=Object.keys(rs(t));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=e)return je(r,e);for(let s=n.length;s>0;s--){const i=n.slice(0,s).join(", ");if(!(i.length>e))return s===n.length?i:je(i,e)}return""}function U(t){return pn(t,new Map)}function pn(t,e){if(Di(t)){const n=e.get(t);if(n!==void 0)return n;const r={};e.set(t,r);for(const s of Object.getOwnPropertyNames(t))typeof t[s]<"u"&&(r[s]=pn(t[s],e));return r}if(Array.isArray(t)){const n=e.get(t);if(n!==void 0)return n;const r=[];return e.set(t,r),t.forEach(s=>{r.push(pn(s,e))}),r}return t}function Di(t){if(!Ue(t))return!1;try{const e=Object.getPrototypeOf(t).constructor.name;return!e||e==="Object"}catch{return!0}}const ss=50,fe="?",cr=/\(error: (.*)\)/,ur=/captureMessage|captureException/;function is(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const i=[],o=n.split(`
`);for(let a=r;a<o.length;a++){const c=o[a];if(c.length>1024)continue;const u=cr.test(c)?c.replace(cr,"$1"):c;if(!u.match(/\S*Error: /)){for(const d of e){const l=d(u);if(l){i.push(l);break}}if(i.length>=ss+s)break}}return os(i.slice(s))}}function $i(t){return Array.isArray(t)?is(...t):t}function os(t){if(!t.length)return[];const e=Array.from(t);return/sentryWrapped/.test(St(e).function||"")&&e.pop(),e.reverse(),ur.test(St(e).function||"")&&(e.pop(),ur.test(St(e).function||"")&&e.pop()),e.slice(0,ss).map(n=>({...n,filename:n.filename||St(e).filename,function:n.function||fe}))}function St(t){return t[t.length-1]||{}}const tn="<anonymous>";function pe(t){try{return!t||typeof t!="function"?tn:t.name||tn}catch{return tn}}function dr(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Ot={},lr={};function Oe(t,e){Ot[t]=Ot[t]||[],Ot[t].push(e)}function Ne(t,e){lr[t]||(e(),lr[t]=!0)}function z(t,e){const n=t&&Ot[t];if(n)for(const r of n)try{r(e)}catch(s){ht&&E.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${pe(r)}
Error:`,s)}}function ji(t){const e="console";Oe(e,t),Ne(e,Li)}function Li(){"console"in C&&fn.forEach(function(t){t in C.console&&F(C.console,t,function(e){return At[t]=e,function(...n){z("console",{args:n,level:t});const s=At[t];s&&s.apply(C.console,n)}})})}const hn=C;function as(){if(!("fetch"in hn))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function mn(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Fi(){if(typeof EdgeRuntime=="string")return!0;if(!as())return!1;if(mn(hn.fetch))return!0;let t=!1;const e=hn.document;if(e&&typeof e.createElement=="function")try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=mn(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){ht&&E.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return t}const cs=1e3;function gt(){return Date.now()/cs}function Ui(){const{performance:t}=C;if(!t||!t.now)return gt;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/cs}const oe=Ui();(()=>{const{performance:t}=C;if(!t||!t.now)return;const e=3600*1e3,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,i=s<e,o=t.timing&&t.timing.navigationStart,c=typeof o=="number"?Math.abs(o+n-r):e,u=c<e;return i||u?s<=c?t.timeOrigin:o:r})();function Zi(t,e){const n="fetch";Oe(n,t),Ne(n,()=>Bi(void 0,e))}function Bi(t,e=!1){e&&!Fi()||F(C,"fetch",function(n){return function(...r){const{method:s,url:i}=Hi(r),o={args:r,fetchData:{method:s,url:i},startTimestamp:oe()*1e3};z("fetch",{...o});const a=new Error().stack;return n.apply(C,r).then(async c=>(z("fetch",{...o,endTimestamp:oe()*1e3,response:c}),c),c=>{throw z("fetch",{...o,endTimestamp:oe()*1e3,error:c}),jn(c)&&c.stack===void 0&&(c.stack=a,xe(c,"framesToPop",1)),c})}})}function gn(t,e){return!!t&&typeof t=="object"&&!!t[e]}function fr(t){return typeof t=="string"?t:t?gn(t,"url")?t.url:t.toString?t.toString():"":""}function Hi(t){if(t.length===0)return{method:"GET",url:""};if(t.length===2){const[n,r]=t;return{url:fr(n),method:gn(r,"method")?String(r.method).toUpperCase():"GET"}}const e=t[0];return{url:fr(e),method:gn(e,"method")?String(e.method).toUpperCase():"GET"}}let wt=null;function qi(t){const e="error";Oe(e,t),Ne(e,Vi)}function Vi(){wt=C.onerror,C.onerror=function(t,e,n,r,s){return z("error",{column:r,error:s,line:n,msg:t,url:e}),wt&&!wt.__SENTRY_LOADER__?wt.apply(this,arguments):!1},C.onerror.__SENTRY_INSTRUMENTED__=!0}let xt=null;function Wi(t){const e="unhandledrejection";Oe(e,t),Ne(e,Gi)}function Gi(){xt=C.onunhandledrejection,C.onunhandledrejection=function(t){return z("unhandledrejection",t),xt&&!xt.__SENTRY_LOADER__?xt.apply(this,arguments):!0},C.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function zi(){return"npm"}function Yi(){const t=typeof WeakSet=="function",e=t?new WeakSet:[];function n(s){if(t)return e.has(s)?!0:(e.add(s),!1);for(let i=0;i<e.length;i++)if(e[i]===s)return!0;return e.push(s),!1}function r(s){if(t)e.delete(s);else for(let i=0;i<e.length;i++)if(e[i]===s){e.splice(i,1);break}}return[n,r]}function Z(){const t=C,e=t.crypto||t.msCrypto;let n=()=>Math.random()*16;try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>{const r=new Uint8Array(1);return e.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function us(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function ue(t){const{message:e,event_id:n}=t;if(e)return e;const r=us(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function yn(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],i=s[0]=s[0]||{};i.value||(i.value=e||""),i.type||(i.type="Error")}function Xe(t,e){const n=us(t);if(!n)return;const r={type:"generic",handled:!0},s=n.mechanism;if(n.mechanism={...r,...s,...e},e&&"data"in e){const i={...s&&s.data,...e.data};n.mechanism.data=i}}function pr(t){if(t&&t.__sentry_captured__)return!0;try{xe(t,"__sentry_captured__",!0)}catch{}return!1}function ds(t){return Array.isArray(t)?t:[t]}function se(t,e=100,n=1/0){try{return _n("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function ls(t,e=3,n=100*1024){const r=se(t,e);return Qi(r)>n?ls(t,e-1,n):r}function _n(t,e,n=1/0,r=1/0,s=Yi()){const[i,o]=s;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const a=Ji(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;const c=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(c===0)return a.replace("object ","");if(i(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{const p=u.toJSON();return _n("",p,c-1,r,s)}catch{}const d=Array.isArray(e)?[]:{};let l=0;const f=rs(e);for(const p in f){if(!Object.prototype.hasOwnProperty.call(f,p))continue;if(l>=r){d[p]="[MaxProperties ~]";break}const y=f[p];d[p]=_n(p,y,c-1,r,s),l++}return o(e),d}function Ji(t,e){try{if(t==="domain"&&e&&typeof e=="object"&&e._events)return"[Domain]";if(t==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&e===global)return"[Global]";if(typeof window<"u"&&e===window)return"[Window]";if(typeof document<"u"&&e===document)return"[Document]";if(Xr(e))return"[VueViewModel]";if(_i(e))return"[SyntheticEvent]";if(typeof e=="number"&&!Number.isFinite(e))return`[${e}]`;if(typeof e=="function")return`[Function: ${pe(e)}]`;if(typeof e=="symbol")return`[${String(e)}]`;if(typeof e=="bigint")return`[BigInt: ${String(e)}]`;const n=Ki(e);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function Ki(t){const e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}function Xi(t){return~-encodeURI(t).split(/%..|./).length}function Qi(t){return Xi(JSON.stringify(t))}var re;(function(t){t[t.PENDING=0]="PENDING";const n=1;t[t.RESOLVED=n]="RESOLVED";const r=2;t[t.REJECTED=r]="REJECTED"})(re||(re={}));function ke(t){return new q(e=>{e(t)})}function Mt(t){return new q((e,n)=>{n(t)})}class q{constructor(e){q.prototype.__init.call(this),q.prototype.__init2.call(this),q.prototype.__init3.call(this),q.prototype.__init4.call(this),this._state=re.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(n){this._reject(n)}}then(e,n){return new q((r,s)=>{this._handlers.push([!1,i=>{if(!e)r(i);else try{r(e(i))}catch(o){s(o)}},i=>{if(!n)s(i);else try{r(n(i))}catch(o){s(o)}}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new q((n,r)=>{let s,i;return this.then(o=>{i=!1,s=o,e&&e()},o=>{i=!0,s=o,e&&e()}).then(()=>{if(i){r(s);return}n(s)})})}__init(){this._resolve=e=>{this._setResult(re.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(re.REJECTED,e)}}__init3(){this._setResult=(e,n)=>{if(this._state===re.PENDING){if(Gt(n)){n.then(this._resolve,this._reject);return}this._state=e,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===re.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===re.RESOLVED&&n[1](this._value),this._state===re.REJECTED&&n[2](this._value),n[0]=!0)})}}}function eo(t){const e=[];function n(){return t===void 0||e.length<t}function r(o){return e.splice(e.indexOf(o),1)[0]||Promise.resolve(void 0)}function s(o){if(!n())return Mt(new K("Not adding Promise because buffer limit was reached."));const a=o();return e.indexOf(a)===-1&&e.push(a),a.then(()=>r(a)).then(null,()=>r(a).then(null,()=>{})),a}function i(o){return new q((a,c)=>{let u=e.length;if(!u)return a(!0);const d=setTimeout(()=>{o&&o>0&&a(!1)},o);e.forEach(l=>{ke(l).then(()=>{--u||(clearTimeout(d),a(!0))},c)})})}return{$:e,add:s,drain:i}}function nn(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}const to=["fatal","error","warning","log","info","debug"];function no(t){return t==="warn"?"warning":to.includes(t)?t:"log"}function ro(t,e=!1){return!(e||t&&!t.startsWith("/")&&!t.match(/^[A-Z]:/)&&!t.startsWith(".")&&!t.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&t!==void 0&&!t.includes("node_modules/")}function so(t){const e=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{const s=r.match(n);if(s){let i,o,a,c,u;if(s[1]){a=s[1];let f=a.lastIndexOf(".");if(a[f-1]==="."&&f--,f>0){i=a.slice(0,f),o=a.slice(f+1);const p=i.indexOf(".Module");p>0&&(a=a.slice(p+1),i=i.slice(0,p))}c=void 0}o&&(c=i,u=o),o==="<anonymous>"&&(u=void 0,a=void 0),a===void 0&&(u=u||fe,a=c?`${c}.${u}`:u);let d=s[2]&&s[2].startsWith("file://")?s[2].slice(7):s[2];const l=s[5]==="native";return d&&d.match(/\/[A-Z]:/)&&(d=d.slice(1)),!d&&s[5]&&!l&&(d=s[5]),{filename:d,module:void 0,function:a,lineno:hr(s[3]),colno:hr(s[4]),in_app:ro(d||"",l)}}if(r.match(e))return{filename:r}}}function io(t){return[90,so()]}function hr(t){return parseInt(t||"",10)||void 0}const oo="sentry-",ao=/^sentry-/;function co(t){const e=uo(t);if(!e)return;const n=Object.entries(e).reduce((r,[s,i])=>{if(s.match(ao)){const o=s.slice(oo.length);r[o]=i}return r},{});if(Object.keys(n).length>0)return n}function uo(t){if(!(!t||!ie(t)&&!Array.isArray(t)))return Array.isArray(t)?t.reduce((e,n)=>{const r=mr(n);return Object.entries(r).forEach(([s,i])=>{e[s]=i}),e},{}):mr(t)}function mr(t){return t.split(",").map(e=>e.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}function yt(t,e=[]){return[t,e]}function lo(t,e){const[n,r]=t;return[n,[...r,e]]}function gr(t,e){const n=t[1];for(const r of n){const s=r[0].type;if(e(r,s))return!0}return!1}function vn(t){return C.__SENTRY__&&C.__SENTRY__.encodePolyfill?C.__SENTRY__.encodePolyfill(t):new TextEncoder().encode(t)}function fo(t){const[e,n]=t;let r=JSON.stringify(e);function s(i){typeof r=="string"?r=typeof i=="string"?r+i:[vn(r),i]:r.push(typeof i=="string"?vn(i):i)}for(const i of n){const[o,a]=i;if(s(`
${JSON.stringify(o)}
`),typeof a=="string"||a instanceof Uint8Array)s(a);else{let c;try{c=JSON.stringify(a)}catch{c=JSON.stringify(se(a))}s(c)}}return typeof r=="string"?r:po(r)}function po(t){const e=t.reduce((s,i)=>s+i.length,0),n=new Uint8Array(e);let r=0;for(const s of t)n.set(s,r),r+=s.length;return n}function ho(t){const e=typeof t.data=="string"?vn(t.data):t.data;return[U({type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),e]}const mo={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function yr(t){return mo[t]}function fs(t){if(!t||!t.sdk)return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function go(t,e,n,r){const s=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:new Date().toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:Jt(r)},...s&&{trace:U({...s})}}}function yo(t,e,n){const r=[{type:"client_report"},{timestamp:gt(),discarded_events:t}];return yt(e?{dsn:e}:{},[r])}const _o=60*1e3;function vo(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return n*1e3;const r=Date.parse(`${t}`);return isNaN(r)?_o:r-e}function bo(t,e){return t[e]||t.all||0}function Eo(t,e,n=Date.now()){return bo(t,e)>n}function So(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},i=n&&n["x-sentry-rate-limits"],o=n&&n["retry-after"];if(i)for(const a of i.trim().split(",")){const[c,u,,,d]=a.split(":",5),l=parseInt(c,10),f=(isNaN(l)?60:l)*1e3;if(!u)s.all=r+f;else for(const p of u.split(";"))p==="metric_bucket"?(!d||d.split(";").includes("custom"))&&(s[p]=r+f):s[p]=r+f}else o?s.all=r+vo(o,r):e===429&&(s.all=r+60*1e3);return s}function _r(){return{traceId:Z(),spanId:Z().substring(16)}}const kt=C;function wo(){const t=kt.chrome,e=t&&t.app&&t.app.runtime,n="history"in kt&&!!kt.history.pushState&&!!kt.history.replaceState;return!e&&n}const R=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Kt(){return Bn(C),C}function Bn(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||be,e[be]=e[be]||{}}function xo(t){const e=oe(),n={sid:Z(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>To(n)};return t&&Ze(n,t),n}function Ze(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),!t.did&&!e.did&&(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||oe(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:Z()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function ko(t,e){let n={};t.status==="ok"&&(n={status:"exited"}),Ze(t,n)}function To(t){return U({sid:`${t.sid}`,init:t.init,started:new Date(t.started*1e3).toISOString(),timestamp:new Date(t.timestamp*1e3).toISOString(),status:t.status,errors:t.errors,did:typeof t.did=="number"||typeof t.did=="string"?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}const bn="_sentrySpan";function vr(t,e){e?xe(t,bn,e):delete t[bn]}function br(t){return t[bn]}const Io=100;class Hn{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=_r()}clone(){const e=new Hn;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,vr(e,br(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&Ze(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,[r,s]=n instanceof Te?[n.getScopeData(),n.getRequestSession()]:Ue(n)?[e,e.requestSession]:[],{tags:i,extra:o,user:a,contexts:c,level:u,fingerprint:d=[],propagationContext:l}=r||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...o},this._contexts={...this._contexts,...c},a&&Object.keys(a).length&&(this._user=a),u&&(this._level=u),d.length&&(this._fingerprint=d),l&&(this._propagationContext=l),s&&(this._requestSession=s),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,vr(this,void 0),this._attachments=[],this._propagationContext=_r(),this._notifyScopeListeners(),this}addBreadcrumb(e,n){const r=typeof n=="number"?n:Io;if(r<=0)return this;const s={timestamp:gt(),...e},i=this._breadcrumbs;return i.push(s),this._breadcrumbs=i.length>r?i.slice(-r):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:br(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...e},this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=n&&n.event_id?n.event_id:Z();if(!this._client)return E.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=r&&r.event_id?r.event_id:Z();if(!this._client)return E.warn("No client configured on scope - will not capture message!"),s;const i=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:i,...r,event_id:s},this),s}captureEvent(e,n){const r=n&&n.event_id?n.event_id:Z();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(E.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}const Te=Hn;function Co(){return Yt("defaultCurrentScope",()=>new Te)}function Ro(){return Yt("defaultIsolationScope",()=>new Te)}class Oo{constructor(e,n){let r;e?r=e:r=new Te;let s;n?s=n:s=new Te,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return Gt(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function Be(){const t=Kt(),e=Bn(t);return e.stack=e.stack||new Oo(Co(),Ro())}function No(t){return Be().withScope(t)}function Ao(t,e){const n=Be();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function Er(t){return Be().withScope(()=>t(Be().getIsolationScope()))}function Mo(){return{withIsolationScope:Er,withScope:No,withSetScope:Ao,withSetIsolationScope:(t,e)=>Er(e),getCurrentScope:()=>Be().getScope(),getIsolationScope:()=>Be().getIsolationScope()}}function qn(t){const e=Bn(t);return e.acs?e.acs:Mo()}function te(){const t=Kt();return qn(t).getCurrentScope()}function Ae(){const t=Kt();return qn(t).getIsolationScope()}function Po(){return Yt("globalScope",()=>new Te)}function Do(...t){const e=Kt(),n=qn(e);if(t.length===2){const[r,s]=t;return r?n.withSetScope(r,s):n.withScope(s)}return n.withScope(t[0])}function j(){return te().getClient()}const $o="_sentryMetrics";function jo(t){const e=t[$o];if(!e)return;const n={};for(const[,[r,s]]of e)(n[r]||(n[r]=[])).push(U(s));return n}const Lo="sentry.source",Fo="sentry.sample_rate",Uo="sentry.op",Zo="sentry.origin",Bo=0,Ho=1,qo=1;function Vo(t){const{spanId:e,traceId:n}=t.spanContext(),{parent_span_id:r}=Pt(t);return U({parent_span_id:r,span_id:e,trace_id:n})}function Sr(t){return typeof t=="number"?wr(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?wr(t.getTime()):oe()}function wr(t){return t>9999999999?t/1e3:t}function Pt(t){if(Go(t))return t.getSpanJSON();try{const{spanId:e,traceId:n}=t.spanContext();if(Wo(t)){const{attributes:r,startTime:s,name:i,endTime:o,parentSpanId:a,status:c}=t;return U({span_id:e,trace_id:n,data:r,description:i,parent_span_id:a,start_timestamp:Sr(s),timestamp:Sr(o)||void 0,status:Yo(c),op:r[Uo],origin:r[Zo],_metrics_summary:jo(t)})}return{span_id:e,trace_id:n}}catch{return{}}}function Wo(t){const e=t;return!!e.attributes&&!!e.startTime&&!!e.name&&!!e.endTime&&!!e.status}function Go(t){return typeof t.getSpanJSON=="function"}function zo(t){const{traceFlags:e}=t.spanContext();return e===qo}function Yo(t){if(!(!t||t.code===Bo))return t.code===Ho?"ok":t.message||"unknown_error"}const Jo="_sentryRootSpan";function ps(t){return t[Jo]||t}function Ko(t){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const e=j(),n=e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const Vn="production",Xo="_frozenDsc";function hs(t,e){const n=e.getOptions(),{publicKey:r}=e.getDsn()||{},s=U({environment:n.environment||Vn,release:n.release,public_key:r,trace_id:t});return e.emit("createDsc",s),s}function Qo(t){const e=j();if(!e)return{};const n=hs(Pt(t).trace_id||"",e),r=ps(t),s=r[Xo];if(s)return s;const i=r.spanContext().traceState,o=i&&i.get("sentry.dsc"),a=o&&co(o);if(a)return a;const c=Pt(r),u=c.data||{},d=u[Fo];d!=null&&(n.sample_rate=`${d}`);const l=u[Lo],f=c.description;return l!=="url"&&f&&(n.transaction=f),Ko()&&(n.sampled=String(zo(r))),e.emit("createDsc",n,r),n}function ea(t){if(typeof t=="boolean")return Number(t);const e=typeof t=="string"?parseFloat(t):t;if(typeof e!="number"||isNaN(e)||e<0||e>1){R&&E.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`);return}return e}function ta(t,e){return e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]]),t}function na(t,e,n,r){const s=fs(n),i={sent_at:new Date().toISOString(),...s&&{sdk:s},...!!r&&e&&{dsn:Jt(e)}},o="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return yt(i,[o])}function ra(t,e,n,r){const s=fs(n),i=t.type&&t.type!=="replay_event"?t.type:"event";ta(t,n&&n.sdk);const o=go(t,s,r,e);return delete t.sdkProcessingMetadata,yt(o,[[{type:i},t]])}function En(t,e,n,r=0){return new q((s,i)=>{const o=t[r];if(e===null||typeof o!="function")s(e);else{const a=o({...e},n);R&&o.id&&a===null&&E.log(`Event processor "${o.id}" dropped event`),Gt(a)?a.then(c=>En(t,c,n,r+1).then(s)).then(null,i):En(t,a,n,r+1).then(s).then(null,i)}})}function sa(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:i}=e;ia(t,e),r&&ca(t,r),ua(t,n),oa(t,s),aa(t,i)}function Sn(t,e){const{extra:n,tags:r,user:s,contexts:i,level:o,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:d,attachments:l,propagationContext:f,transactionName:p,span:y}=e;ze(t,"extra",n),ze(t,"tags",r),ze(t,"user",s),ze(t,"contexts",i),ze(t,"sdkProcessingMetadata",a),o&&(t.level=o),p&&(t.transactionName=p),y&&(t.span=y),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),d.length&&(t.eventProcessors=[...t.eventProcessors,...d]),l.length&&(t.attachments=[...t.attachments,...l]),t.propagationContext={...t.propagationContext,...f}}function ze(t,e,n){if(n&&Object.keys(n).length){t[e]={...t[e]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[e][r]=n[r])}}function ia(t,e){const{extra:n,tags:r,user:s,contexts:i,level:o,transactionName:a}=e,c=U(n);c&&Object.keys(c).length&&(t.extra={...c,...t.extra});const u=U(r);u&&Object.keys(u).length&&(t.tags={...u,...t.tags});const d=U(s);d&&Object.keys(d).length&&(t.user={...d,...t.user});const l=U(i);l&&Object.keys(l).length&&(t.contexts={...l,...t.contexts}),o&&(t.level=o),a&&t.type!=="transaction"&&(t.transaction=a)}function oa(t,e){const n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}function aa(t,e){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...e}}function ca(t,e){t.contexts={trace:Vo(e),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:Qo(e),...t.sdkProcessingMetadata};const n=ps(e),r=Pt(n).description;r&&!t.transaction&&t.type==="transaction"&&(t.transaction=r)}function ua(t,e){t.fingerprint=t.fingerprint?ds(t.fingerprint):[],e&&(t.fingerprint=t.fingerprint.concat(e)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}function da(t,e,n,r,s,i){const{normalizeDepth:o=3,normalizeMaxBreadth:a=1e3}=t,c={...e,event_id:e.event_id||n.event_id||Z(),timestamp:e.timestamp||gt()},u=n.integrations||t.integrations.map(I=>I.name);la(c,t),ha(c,u),s&&s.emit("applyFrameMetadata",e),e.type===void 0&&fa(c,t.stackParser);const d=ga(r,n.captureContext);n.mechanism&&Xe(c,n.mechanism);const l=s?s.getEventProcessors():[],f=Po().getScopeData();if(i){const I=i.getScopeData();Sn(f,I)}if(d){const I=d.getScopeData();Sn(f,I)}const p=[...n.attachments||[],...f.attachments];p.length&&(n.attachments=p),sa(c,f);const y=[...l,...f.eventProcessors];return En(y,c,n).then(I=>(I&&pa(I),typeof o=="number"&&o>0?ma(I,o,a):I))}function la(t,e){const{environment:n,release:r,dist:s,maxValueLength:i=250}=e;"environment"in t||(t.environment="environment"in e?n:Vn),t.release===void 0&&r!==void 0&&(t.release=r),t.dist===void 0&&s!==void 0&&(t.dist=s),t.message&&(t.message=je(t.message,i));const o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=je(o.value,i));const a=t.request;a&&a.url&&(a.url=je(a.url,i))}const xr=new WeakMap;function fa(t,e){const n=C._sentryDebugIds;if(!n)return;let r;const s=xr.get(e);s?r=s:(r=new Map,xr.set(e,r));const i=Object.entries(n).reduce((o,[a,c])=>{let u;const d=r.get(a);d?u=d:(u=e(a),r.set(a,u));for(let l=u.length-1;l>=0;l--){const f=u[l];if(f.filename){o[f.filename]=c;break}}return o},{});try{t.exception.values.forEach(o=>{o.stacktrace.frames.forEach(a=>{a.filename&&(a.debug_id=i[a.filename])})})}catch{}}function pa(t){const e={};try{t.exception.values.forEach(r=>{r.stacktrace.frames.forEach(s=>{s.debug_id&&(s.abs_path?e[s.abs_path]=s.debug_id:s.filename&&(e[s.filename]=s.debug_id),delete s.debug_id)})})}catch{}if(Object.keys(e).length===0)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const n=t.debug_meta.images;Object.entries(e).forEach(([r,s])=>{n.push({type:"sourcemap",code_file:r,debug_id:s})})}function ha(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}function ma(t,e,n){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(s=>({...s,...s.data&&{data:se(s.data,e,n)}}))},...t.user&&{user:se(t.user,e,n)},...t.contexts&&{contexts:se(t.contexts,e,n)},...t.extra&&{extra:se(t.extra,e,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=se(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map(s=>({...s,...s.data&&{data:se(s.data,e,n)}}))),r}function ga(t,e){if(!e)return t;const n=t?t.clone():new Te;return n.update(e),n}function ya(t,e){return te().captureException(t,void 0)}function ms(t,e){return te().captureEvent(t,e)}function kr(t){const e=j(),n=Ae(),r=te(),{release:s,environment:i=Vn}=e&&e.getOptions()||{},{userAgent:o}=C.navigator||{},a=xo({release:s,environment:i,user:r.getUser()||n.getUser(),...o&&{userAgent:o},...t}),c=n.getSession();return c&&c.status==="ok"&&Ze(c,{status:"exited"}),gs(),n.setSession(a),r.setSession(a),a}function gs(){const t=Ae(),e=te(),n=e.getSession()||t.getSession();n&&ko(n),ys(),t.setSession(),e.setSession()}function ys(){const t=Ae(),e=te(),n=j(),r=e.getSession()||t.getSession();r&&n&&n.captureSession(r)}function Tr(t=!1){if(t){gs();return}ys()}const _a="7";function va(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function ba(t){return`${va(t)}${t.projectId}/envelope/`}function Ea(t,e){return Mi({sentry_key:t.publicKey,sentry_version:_a,...e&&{sentry_client:`${e.name}/${e.version}`}})}function Sa(t,e,n){return e||`${ba(t)}?${Ea(t,n)}`}const Ir=[];function wa(t){const e={};return t.forEach(n=>{const{name:r}=n,s=e[r];s&&!s.isDefaultInstance&&n.isDefaultInstance||(e[r]=n)}),Object.values(e)}function xa(t){const e=t.defaultIntegrations||[],n=t.integrations;e.forEach(o=>{o.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...e,...n]:typeof n=="function"?r=ds(n(e)):r=e;const s=wa(r),i=s.findIndex(o=>o.name==="Debug");if(i>-1){const[o]=s.splice(i,1);s.push(o)}return s}function ka(t,e){const n={};return e.forEach(r=>{r&&_s(t,r,n)}),n}function Cr(t,e){for(const n of e)n&&n.afterAllSetup&&n.afterAllSetup(t)}function _s(t,e,n){if(n[e.name]){R&&E.log(`Integration skipped because it was already installed: ${e.name}`);return}if(n[e.name]=e,Ir.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),Ir.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,i)=>r(s,i,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),s=Object.assign((i,o)=>r(i,o,t),{id:e.name});t.addEventProcessor(s)}R&&E.log(`Integration installed: ${e.name}`)}const Rr="Not capturing exception because it's already been captured.";class Ta{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=Ai(e.dsn):R&&E.warn("No DSN provided, client will not send events."),this._dsn){const n=Sa(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const s=Z();if(pr(e))return R&&E.log(Rr),s;const i={event_id:s,...n};return this._process(this.eventFromException(e,i).then(o=>this._captureEvent(o,i,r))),i.event_id}captureMessage(e,n,r,s){const i={event_id:Z(),...r},o=Ln(e)?e:String(e),a=Fn(e)?this.eventFromMessage(o,n,i):this.eventFromException(e,i);return this._process(a.then(c=>this._captureEvent(c,i,s))),i.event_id}captureEvent(e,n,r){const s=Z();if(n&&n.originalException&&pr(n.originalException))return R&&E.log(Rr),s;const i={event_id:s,...n},a=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(e,i,a||r)),i.event_id}captureSession(e){typeof e.release!="string"?R&&E.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),Ze(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s))):ke(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];_s(this,e,this._integrations),n||Cr(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=ra(e,this._dsn,this._options._metadata,this._options.tunnel);for(const i of n.attachments||[])r=lo(r,ho(i));const s=this.sendEnvelope(r);s&&s.then(i=>this.emit("afterSendEvent",e,i),null)}sendSession(e){const n=na(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(e,n,r){if(this._options.sendClientReports){const s=typeof r=="number"?r:1,i=`${e}:${n}`;R&&E.log(`Recording outcome: "${i}"${s>1?` (${s} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+s}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(s=>s(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>(R&&E.error("Error while sending event:",n),n)):(R&&E.error("Transport disabled"),ke({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=ka(this,e),Cr(this,e)}_updateSessionFromEvent(e,n){let r=!1,s=!1;const i=n.exception&&n.exception.values;if(i){s=!0;for(const c of i){const u=c.mechanism;if(u&&u.handled===!1){r=!0;break}}}const o=e.status==="ok";(o&&e.errors===0||o&&r)&&(Ze(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new q(n=>{let r=0;const s=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=s,e&&r>=e&&(clearInterval(i),n(!1)))},s)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,s=Ae()){const i=this.getOptions(),o=Object.keys(this._integrations);return!n.integrations&&o.length>0&&(n.integrations=o),this.emit("preprocessEvent",e,n),e.type||s.setLastEventId(e.event_id||n.event_id),da(i,e,n,r,this,s).then(a=>{if(a===null)return a;const c={...s.getPropagationContext(),...r?r.getPropagationContext():void 0};if(!(a.contexts&&a.contexts.trace)&&c){const{traceId:d,spanId:l,parentSpanId:f,dsc:p}=c;a.contexts={trace:U({trace_id:d,span_id:l,parent_span_id:f}),...a.contexts};const y=p||hs(d,this);a.sdkProcessingMetadata={dynamicSamplingContext:y,...a.sdkProcessingMetadata}}return a})}_captureEvent(e,n={},r){return this._processEvent(e,n,r).then(s=>s.event_id,s=>{if(R){const i=s;i.logLevel==="log"?E.log(i.message):E.warn(i)}})}_processEvent(e,n,r){const s=this.getOptions(),{sampleRate:i}=s,o=bs(e),a=vs(e),c=e.type||"error",u=`before send for type \`${c}\``,d=typeof i>"u"?void 0:ea(i);if(a&&typeof d=="number"&&Math.random()>d)return this.recordDroppedEvent("sample_rate","error",e),Mt(new K(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));const l=c==="replay_event"?"replay":c,p=(e.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(e,n,r,p).then(y=>{if(y===null)throw this.recordDroppedEvent("event_processor",l,e),new K("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return y;const I=Ca(this,s,y,n);return Ia(I,u)}).then(y=>{if(y===null){if(this.recordDroppedEvent("before_send",l,e),o){const H=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",H)}throw new K(`${u} returned \`null\`, will not send event.`,"log")}const w=r&&r.getSession();if(!o&&w&&this._updateSessionFromEvent(w,y),o){const P=y.sdkProcessingMetadata&&y.sdkProcessingMetadata.spanCountBeforeProcessing||0,H=y.spans?y.spans.length:0,bt=P-H;bt>0&&this.recordDroppedEvent("before_send","span",bt)}const I=y.transaction_info;if(o&&I&&y.transaction!==e.transaction){const P="custom";y.transaction_info={...I,source:P}}return this.sendEvent(y,n),y}).then(null,y=>{throw y instanceof K?y:(this.captureException(y,{data:{__sentry__:!0},originalException:y}),new K(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${y}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[s,i]=n.split(":");return{reason:s,category:i,quantity:r}})}_flushOutcomes(){R&&E.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0){R&&E.log("No outcomes to send");return}if(!this._dsn){R&&E.log("No dsn provided, will not send outcomes");return}R&&E.log("Sending outcomes:",e);const n=yo(e,this._options.tunnel&&Jt(this._dsn));this.sendEnvelope(n)}}function Ia(t,e){const n=`${e} must return \`null\` or a valid event.`;if(Gt(t))return t.then(r=>{if(!Ue(r)&&r!==null)throw new K(n);return r},r=>{throw new K(`${e} rejected with ${r}`)});if(!Ue(t)&&t!==null)throw new K(n);return t}function Ca(t,e,n,r){const{beforeSend:s,beforeSendTransaction:i,beforeSendSpan:o}=e;if(vs(n)&&s)return s(n,r);if(bs(n)){if(n.spans&&o){const a=[];for(const c of n.spans){const u=o(c);u?a.push(u):t.recordDroppedEvent("before_send","span")}n.spans=a}if(i){if(n.spans){const a=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:a}}return i(n,r)}}return n}function vs(t){return t.type===void 0}function bs(t){return t.type==="transaction"}function Ra(t,e){e.debug===!0&&(R?E.enable():mt(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),te().update(e.initialScope);const r=new t(e);return Oa(r),r.init(),r}function Oa(t){te().setClient(t)}const Na=64;function Es(t,e,n=eo(t.bufferSize||Na)){let r={};const s=o=>n.drain(o);function i(o){const a=[];if(gr(o,(l,f)=>{const p=yr(f);if(Eo(r,p)){const y=Or(l,f);t.recordDroppedEvent("ratelimit_backoff",p,y)}else a.push(l)}),a.length===0)return ke({});const c=yt(o[0],a),u=l=>{gr(c,(f,p)=>{const y=Or(f,p);t.recordDroppedEvent(l,yr(p),y)})},d=()=>e({body:fo(c)}).then(l=>(l.statusCode!==void 0&&(l.statusCode<200||l.statusCode>=300)&&R&&E.warn(`Sentry responded with status code ${l.statusCode} to sent event.`),r=So(r,l),l),l=>{throw u("network_error"),l});return n.add(d).then(l=>l,l=>{if(l instanceof K)return R&&E.error("Skipped sending event because buffer is full."),u("queue_overflow"),ke({});throw l})}return{send:i,flush:s}}function Or(t,e){if(!(e!=="event"&&e!=="transaction"))return Array.isArray(t)?t[1]:void 0}function Aa(t,e,n=[e],r="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map(i=>({name:`${r}:@sentry/${i}`,version:be})),version:be}),t._metadata=s}const Ma=100;function Ie(t,e){const n=j(),r=Ae();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:i=Ma}=n.getOptions();if(i<=0)return;const a={timestamp:gt(),...t},c=s?mt(()=>s(a,e)):a;c!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",c,e),r.addBreadcrumb(c,i))}let Nr;const Pa="FunctionToString",Ar=new WeakMap,Da=()=>({name:Pa,setupOnce(){Nr=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=Zn(this),n=Ar.has(j())&&e!==void 0?e:this;return Nr.apply(n,t)}}catch{}},setup(t){Ar.set(t,!0)}}),$a=Da,ja=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"],La="InboundFilters",Fa=(t={})=>({name:La,processEvent(e,n,r){const s=r.getOptions(),i=Za(t,s);return Ba(e,i)?null:e}}),Ua=Fa;function Za(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:ja],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]],ignoreInternal:t.ignoreInternal!==void 0?t.ignoreInternal:!0}}function Ba(t,e){return e.ignoreInternal&&za(t)?(R&&E.warn(`Event dropped due to being internal Sentry Error.
Event: ${ue(t)}`),!0):Ha(t,e.ignoreErrors)?(R&&E.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${ue(t)}`),!0):Ja(t)?(R&&E.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${ue(t)}`),!0):qa(t,e.ignoreTransactions)?(R&&E.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${ue(t)}`),!0):Va(t,e.denyUrls)?(R&&E.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${ue(t)}.
Url: ${Dt(t)}`),!0):Wa(t,e.allowUrls)?!1:(R&&E.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${ue(t)}.
Url: ${Dt(t)}`),!0)}function Ha(t,e){return t.type||!e||!e.length?!1:Ga(t).some(n=>zt(n,e))}function qa(t,e){if(t.type!=="transaction"||!e||!e.length)return!1;const n=t.transaction;return n?zt(n,e):!1}function Va(t,e){if(!e||!e.length)return!1;const n=Dt(t);return n?zt(n,e):!1}function Wa(t,e){if(!e||!e.length)return!0;const n=Dt(t);return n?zt(n,e):!0}function Ga(t){const e=[];t.message&&e.push(t.message);let n;try{n=t.exception.values[t.exception.values.length-1]}catch{}return n&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`)),e}function za(t){try{return t.exception.values[0].type==="SentryError"}catch{}return!1}function Ya(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Dt(t){try{let e;try{e=t.exception.values[0].stacktrace.frames}catch{}return e?Ya(e):null}catch{return R&&E.error(`Cannot extract url for event ${ue(t)}`),null}}function Ja(t){return t.type||!t.exception||!t.exception.values||t.exception.values.length===0?!1:!t.message&&!t.exception.values.some(e=>e.stacktrace||e.type&&e.type!=="Error"||e.value)}const Ka="Dedupe",Xa=()=>{let t;return{name:Ka,processEvent(e){if(e.type)return e;try{if(ec(e,t))return R&&E.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}},Qa=Xa;function ec(t,e){return e?!!(tc(t,e)||nc(t,e)):!1}function tc(t,e){const n=t.message,r=e.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!ws(t,e)||!Ss(t,e))}function nc(t,e){const n=Mr(e),r=Mr(t);return!(!n||!r||n.type!==r.type||n.value!==r.value||!ws(t,e)||!Ss(t,e))}function Ss(t,e){let n=dr(t),r=dr(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let s=0;s<r.length;s++){const i=r[s],o=n[s];if(i.filename!==o.filename||i.lineno!==o.lineno||i.colno!==o.colno||i.function!==o.function)return!1}return!0}function ws(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function Mr(t){return t.exception&&t.exception.values&&t.exception.values[0]}const O=C;let wn=0;function xs(){return wn>0}function rc(){wn++,setTimeout(()=>{wn--})}function He(t,e={},n){if(typeof t!="function")return t;try{const s=t.__sentry_wrapped__;if(s)return typeof s=="function"?s:t;if(Zn(t))return t}catch{return t}const r=function(){const s=Array.prototype.slice.call(arguments);try{const i=s.map(o=>He(o,e));return t.apply(this,i)}catch(i){throw rc(),Do(o=>{o.addEventProcessor(a=>(e.mechanism&&(yn(a,void 0),Xe(a,e.mechanism)),a.extra={...a.extra,arguments:s},a)),ya(i)}),i}};try{for(const s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=t[s])}catch{}ns(r,t),xe(t,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return t.name}})}catch{}return r}const _t=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Wn(t,e){const n=Gn(t,e),r={type:e&&e.name,value:cc(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function sc(t,e,n,r){const s=j(),i=s&&s.getOptions().normalizeDepth,o=pc(e),a={__serialized__:ls(e,i)};if(o)return{exception:{values:[Wn(t,o)]},extra:a};const c={exception:{values:[{type:Wt(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:lc(e,{isUnhandledRejection:r})}]},extra:a};if(n){const u=Gn(t,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function rn(t,e){return{exception:{values:[Wn(t,e)]}}}function Gn(t,e){const n=e.stacktrace||e.stack||"",r=oc(e),s=ac(e);try{return t(n,r,s)}catch{}return[]}const ic=/Minified React error #\d+;/i;function oc(t){return t&&ic.test(t.message)?1:0}function ac(t){return typeof t.framesToPop=="number"?t.framesToPop:0}function cc(t){const e=t&&t.message;return e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function uc(t,e,n,r){const s=n&&n.syntheticException||void 0,i=zn(t,e,s,r);return Xe(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),ke(i)}function dc(t,e,n="info",r,s){const i=r&&r.syntheticException||void 0,o=xn(t,e,i,s);return o.level=n,r&&r.event_id&&(o.event_id=r.event_id),ke(o)}function zn(t,e,n,r,s){let i;if(Kr(e)&&e.error)return rn(t,e.error);if(nr(e)||mi(e)){const o=e;if("stack"in e)i=rn(t,e);else{const a=o.name||(nr(o)?"DOMError":"DOMException"),c=o.message?`${a}: ${o.message}`:a;i=xn(t,c,n,r),yn(i,c)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return jn(e)?rn(t,e):Ue(e)||Wt(e)?(i=sc(t,e,n,s),Xe(i,{synthetic:!0}),i):(i=xn(t,e,n,r),yn(i,`${e}`),Xe(i,{synthetic:!0}),i)}function xn(t,e,n,r){const s={};if(r&&n){const i=Gn(t,n);i.length&&(s.exception={values:[{value:e,stacktrace:{frames:i}}]})}if(Ln(e)){const{__sentry_template_string__:i,__sentry_template_values__:o}=e;return s.logentry={message:i,params:o},s}return s.message=e,s}function lc(t,{isUnhandledRejection:e}){const n=Pi(t),r=e?"promise rejection":"exception";return Kr(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:Wt(t)?`Event \`${fc(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function fc(t){try{const e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch{}}function pc(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const n=t[e];if(n instanceof Error)return n}}function hc(t,{metadata:e,tunnel:n,dsn:r}){const s={event_id:t.event_id,sent_at:new Date().toISOString(),...e&&e.sdk&&{sdk:{name:e.sdk.name,version:e.sdk.version}},...!!n&&!!r&&{dsn:Jt(r)}},i=mc(t);return yt(s,[i])}function mc(t){return[{type:"user_report"},t]}class gc extends Ta{constructor(e){const n={parentSpanIsAlwaysRootSpan:!0,...e},r=O.SENTRY_SDK_SOURCE||zi();Aa(n,"browser",["browser"],r),super(n),n.sendClientReports&&O.document&&O.document.addEventListener("visibilitychange",()=>{O.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(e,n){return uc(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return dc(this._options.stackParser,e,n,r,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled()){_t&&E.warn("SDK not enabled, will not capture user feedback.");return}const n=hc(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}_prepareEvent(e,n,r){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r)}}const yc=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,D=C,_c=1e3;let Pr,kn,Tn;function vc(t){const e="dom";Oe(e,t),Ne(e,bc)}function bc(){if(!D.document)return;const t=z.bind(null,"dom"),e=Dr(t,!0);D.document.addEventListener("click",e,!1),D.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{const r=D[n]&&D[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(F(r,"addEventListener",function(s){return function(i,o,a){if(i==="click"||i=="keypress")try{const c=this,u=c.__sentry_instrumentation_handlers__=c.__sentry_instrumentation_handlers__||{},d=u[i]=u[i]||{refCount:0};if(!d.handler){const l=Dr(t);d.handler=l,s.call(this,i,l,a)}d.refCount++}catch{}return s.call(this,i,o,a)}}),F(r,"removeEventListener",function(s){return function(i,o,a){if(i==="click"||i=="keypress")try{const c=this,u=c.__sentry_instrumentation_handlers__||{},d=u[i];d&&(d.refCount--,d.refCount<=0&&(s.call(this,i,d.handler,a),d.handler=void 0,delete u[i]),Object.keys(u).length===0&&delete c.__sentry_instrumentation_handlers__)}catch{}return s.call(this,i,o,a)}}))})}function Ec(t){if(t.type!==kn)return!1;try{if(!t.target||t.target._sentryId!==Tn)return!1}catch{}return!0}function Sc(t,e){return t!=="keypress"?!1:!e||!e.tagName?!0:!(e.tagName==="INPUT"||e.tagName==="TEXTAREA"||e.isContentEditable)}function Dr(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=wc(n);if(Sc(n.type,r))return;xe(n,"_sentryCaptured",!0),r&&!r._sentryId&&xe(r,"_sentryId",Z());const s=n.type==="keypress"?"input":n.type;Ec(n)||(t({event:n,name:s,global:e}),kn=n.type,Tn=r?r._sentryId:void 0),clearTimeout(Pr),Pr=D.setTimeout(()=>{Tn=void 0,kn=void 0},_c)}}function wc(t){try{return t.target}catch{return null}}let Tt;function ks(t){const e="history";Oe(e,t),Ne(e,xc)}function xc(){if(!wo())return;const t=D.onpopstate;D.onpopstate=function(...n){const r=D.location.href,s=Tt;if(Tt=r,z("history",{from:s,to:r}),t)try{return t.apply(this,n)}catch{}};function e(n){return function(...r){const s=r.length>2?r[2]:void 0;if(s){const i=Tt,o=String(s);Tt=o,z("history",{from:i,to:o})}return n.apply(this,r)}}F(D.history,"pushState",e),F(D.history,"replaceState",e)}const Nt={};function kc(t){const e=Nt[t];if(e)return e;let n=D[t];if(mn(n))return Nt[t]=n.bind(D);const r=D.document;if(r&&typeof r.createElement=="function")try{const s=r.createElement("iframe");s.hidden=!0,r.head.appendChild(s);const i=s.contentWindow;i&&i[t]&&(n=i[t]),r.head.removeChild(s)}catch(s){yc&&E.warn(`Could not create sandbox iframe for ${t} check, bailing to window.${t}: `,s)}return n&&(Nt[t]=n.bind(D))}function $r(t){Nt[t]=void 0}const Ye="__sentry_xhr_v3__";function Tc(t){const e="xhr";Oe(e,t),Ne(e,Ic)}function Ic(){if(!D.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const s=oe()*1e3,i=ie(r[0])?r[0].toUpperCase():void 0,o=Cc(r[1]);if(!i||!o)return e.apply(n,r);n[Ye]={method:i,url:o,request_headers:{}},i==="POST"&&o.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const a=()=>{const c=n[Ye];if(c&&n.readyState===4){try{c.status_code=n.status}catch{}const u={endTimestamp:oe()*1e3,startTimestamp:s,xhr:n};z("xhr",u)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(c,u,d){return a(),c.apply(u,d)}}):n.addEventListener("readystatechange",a),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(c,u,d){const[l,f]=d,p=u[Ye];return p&&ie(l)&&ie(f)&&(p.request_headers[l.toLowerCase()]=f),c.apply(u,d)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const s=n[Ye];if(!s)return e.apply(n,r);r[0]!==void 0&&(s.body=r[0]);const i={startTimestamp:oe()*1e3,xhr:n};return z("xhr",i),e.apply(n,r)}})}function Cc(t){if(ie(t))return t;try{return t.toString()}catch{}}function Rc(t,e=kc("fetch")){let n=0,r=0;function s(i){const o=i.body.length;n+=o,r++;const a={body:i.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return $r("fetch"),Mt("No fetch implementation available");try{return e(t.url,a).then(c=>(n-=o,r--,{statusCode:c.status,headers:{"x-sentry-rate-limits":c.headers.get("X-Sentry-Rate-Limits"),"retry-after":c.headers.get("Retry-After")}}))}catch(c){return $r("fetch"),n-=o,r--,Mt(c)}}return Es(t,s)}const Oc=30,Nc=50;function In(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?fe:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const Ac=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Mc=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Pc=/\((\S*)(?::(\d+))(?::(\d+))\)/,Dc=t=>{const e=Ac.exec(t);if(e){const[,r,s,i]=e;return In(r,fe,+s,+i)}const n=Mc.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=Pc.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[s,i]=Is(n[1]||fe,n[2]);return In(i,s,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},Ts=[Oc,Dc],$c=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,jc=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Lc=t=>{const e=$c.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const i=jc.exec(e[3]);i&&(e[1]=e[1]||"eval",e[3]=i[1],e[4]=i[2],e[5]="")}let r=e[3],s=e[1]||fe;return[s,r]=Is(s,r),In(r,s,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}},Fc=[Nc,Lc],Uc=[Ts,Fc],Zc=is(...Uc),Is=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:fe,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},It=1024,Bc="Breadcrumbs",Hc=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:Bc,setup(n){e.console&&ji(Gc(n)),e.dom&&vc(Wc(n,e.dom)),e.xhr&&Tc(zc(n)),e.fetch&&Zi(Yc(n)),e.history&&ks(Jc(n)),e.sentry&&n.on("beforeSendEvent",Vc(n))}}},qc=Hc;function Vc(t){return function(n){j()===t&&Ie({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:ue(n)},{event:n})}}function Wc(t,e){return function(r){if(j()!==t)return;let s,i,o=typeof e=="object"?e.serializeAttribute:void 0,a=typeof e=="object"&&typeof e.maxStringLength=="number"?e.maxStringLength:void 0;a&&a>It&&(_t&&E.warn(`\`dom.maxStringLength\` cannot exceed ${It}, but a value of ${a} was configured. Sentry will use ${It} instead.`),a=It),typeof o=="string"&&(o=[o]);try{const u=r.event,d=Kc(u)?u.target:u;s=es(d,{keyAttrs:o,maxStringLength:a}),i=ki(d)}catch{s="<unknown>"}if(s.length===0)return;const c={category:`ui.${r.name}`,message:s};i&&(c.data={"ui.component_name":i}),Ie(c,{event:r.event,name:r.name,global:r.global})}}function Gc(t){return function(n){if(j()!==t)return;const r={category:"console",data:{arguments:n.args,logger:"console"},level:no(n.level),message:rr(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)r.message=`Assertion failed: ${rr(n.args.slice(1)," ")||"console.assert"}`,r.data.arguments=n.args.slice(1);else return;Ie(r,{input:n.args,level:n.level})}}function zc(t){return function(n){if(j()!==t)return;const{startTimestamp:r,endTimestamp:s}=n,i=n.xhr[Ye];if(!r||!s||!i)return;const{method:o,url:a,status_code:c,body:u}=i,d={method:o,url:a,status_code:c},l={xhr:n.xhr,input:u,startTimestamp:r,endTimestamp:s},f=Qr(c);Ie({category:"xhr",data:d,type:"http",level:f},l)}}function Yc(t){return function(n){if(j()!==t)return;const{startTimestamp:r,endTimestamp:s}=n;if(s&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.error){const i=n.fetchData,o={data:n.error,input:n.args,startTimestamp:r,endTimestamp:s};Ie({category:"fetch",data:i,level:"error",type:"http"},o)}else{const i=n.response,o={...n.fetchData,status_code:i&&i.status},a={input:n.args,response:i,startTimestamp:r,endTimestamp:s},c=Qr(o.status_code);Ie({category:"fetch",data:o,type:"http",level:c},a)}}}function Jc(t){return function(n){if(j()!==t)return;let r=n.from,s=n.to;const i=nn(O.location.href);let o=r?nn(r):void 0;const a=nn(s);(!o||!o.path)&&(o=i),i.protocol===a.protocol&&i.host===a.host&&(s=a.relative),i.protocol===o.protocol&&i.host===o.host&&(r=o.relative),Ie({category:"navigation",data:{from:r,to:s}})}}function Kc(t){return!!t&&!!t.target}const Xc=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Qc="BrowserApiErrors",eu=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:Qc,setupOnce(){e.setTimeout&&F(O,"setTimeout",jr),e.setInterval&&F(O,"setInterval",jr),e.requestAnimationFrame&&F(O,"requestAnimationFrame",nu),e.XMLHttpRequest&&"XMLHttpRequest"in O&&F(XMLHttpRequest.prototype,"send",ru);const n=e.eventTarget;n&&(Array.isArray(n)?n:Xc).forEach(su)}}},tu=eu;function jr(t){return function(...e){const n=e[0];return e[0]=He(n,{mechanism:{data:{function:pe(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function nu(t){return function(e){return t.apply(this,[He(e,{mechanism:{data:{function:"requestAnimationFrame",handler:pe(t)},handled:!1,type:"instrument"}})])}}function ru(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(s=>{s in n&&typeof n[s]=="function"&&F(n,s,function(i){const o={mechanism:{data:{function:s,handler:pe(i)},handled:!1,type:"instrument"}},a=Zn(i);return a&&(o.mechanism.data.handler=pe(a)),He(i,o)})}),t.apply(this,e)}}function su(t){const e=O,n=e[t]&&e[t].prototype;!n||!n.hasOwnProperty||!n.hasOwnProperty("addEventListener")||(F(n,"addEventListener",function(r){return function(s,i,o){try{typeof i.handleEvent=="function"&&(i.handleEvent=He(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:pe(i),target:t},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[s,He(i,{mechanism:{data:{function:"addEventListener",handler:pe(i),target:t},handled:!1,type:"instrument"}}),o])}}),F(n,"removeEventListener",function(r){return function(s,i,o){const a=i;try{const c=a&&a.__sentry_wrapped__;c&&r.call(this,s,c,o)}catch{}return r.call(this,s,a,o)}}))}const iu="GlobalHandlers",ou=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:iu,setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(cu(n),Lr("onerror")),e.onunhandledrejection&&(uu(n),Lr("onunhandledrejection"))}}},au=ou;function cu(t){qi(e=>{const{stackParser:n,attachStacktrace:r}=Cs();if(j()!==t||xs())return;const{msg:s,url:i,line:o,column:a,error:c}=e,u=fu(zn(n,c||s,void 0,r,!1),i,o,a);u.level="error",ms(u,{originalException:c,mechanism:{handled:!1,type:"onerror"}})})}function uu(t){Wi(e=>{const{stackParser:n,attachStacktrace:r}=Cs();if(j()!==t||xs())return;const s=du(e),i=Fn(s)?lu(s):zn(n,s,void 0,r,!0);i.level="error",ms(i,{originalException:s,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function du(t){if(Fn(t))return t;try{if("reason"in t)return t.reason;if("detail"in t&&"reason"in t.detail)return t.detail.reason}catch{}return t}function lu(t){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(t)}`}]}}}function fu(t,e,n,r){const s=t.exception=t.exception||{},i=s.values=s.values||[],o=i[0]=i[0]||{},a=o.stacktrace=o.stacktrace||{},c=a.frames=a.frames||[],u=isNaN(parseInt(r,10))?void 0:r,d=isNaN(parseInt(n,10))?void 0:n,l=ie(e)&&e.length>0?e:xi();return c.length===0&&c.push({colno:u,filename:l,function:fe,in_app:!0,lineno:d}),t}function Lr(t){_t&&E.log(`Global Handler attached: ${t}`)}function Cs(){const t=j();return t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const pu=()=>({name:"HttpContext",preprocessEvent(t){if(!O.navigator&&!O.location&&!O.document)return;const e=t.request&&t.request.url||O.location&&O.location.href,{referrer:n}=O.document||{},{userAgent:r}=O.navigator||{},s={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},i={...t.request,...e&&{url:e},headers:s};t.request=i}}),hu="cause",mu=5,gu="LinkedErrors",yu=(t={})=>{const e=t.limit||mu,n=t.key||hu;return{name:gu,preprocessEvent(r,s,i){const o=i.getOptions();bi(Wn,o.stackParser,o.maxValueLength,n,e,r,s)}}},_u=yu;function Rs(t){return[Ua(),$a(),tu(),qc(),au(),_u(),Qa(),pu()]}function vu(t={}){const e={defaultIntegrations:Rs(),release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:O.SENTRY_RELEASE&&O.SENTRY_RELEASE.id?O.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return t.defaultIntegrations==null&&delete t.defaultIntegrations,{...e,...t}}function bu(){const t=typeof O.window<"u"&&O;if(!t)return!1;const e=t.chrome?"chrome":"browser",n=t[e],r=n&&n.runtime&&n.runtime.id,s=O.location&&O.location.href||"",i=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],o=!!r&&O===O.top&&i.some(c=>s.startsWith(`${c}//`)),a=typeof t.nw<"u";return!!r&&!o&&!a}function Eu(t={}){const e=vu(t);if(bu()){mt(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});return}_t&&(as()||E.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...e,stackParser:$i(e.stackParser||Zc),integrations:xa(e),transport:e.transport||Rc},r=Ra(gc,n);return e.autoSessionTracking&&Su(),r}function Su(){if(typeof O.document>"u"){_t&&E.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}kr({ignoreDuration:!0}),Tr(),ks(({from:t,to:e})=>{t!==void 0&&t!==e&&(kr({ignoreDuration:!0}),Tr())})}function Fr(){const t=Ae().getScopeData();return Sn(t,te().getScopeData()),t.eventProcessors=[],t}function wu(t){Ae().addScopeListener(e=>{const n=Fr();t(n,e)}),te().addScopeListener(e=>{const n=Fr();t(n,e)})}var Ur;(function(t){t[t.Classic=1]="Classic",t[t.Protocol=2]="Protocol",t[t.Both=3]="Both"})(Ur||(Ur={}));const xu="sentry-ipc";var de;(function(t){t.RENDERER_START="sentry-electron.renderer-start",t.EVENT="sentry-electron.event",t.SCOPE="sentry-electron.scope",t.ENVELOPE="sentry-electron.envelope",t.STATUS="sentry-electron.status",t.ADD_METRIC="sentry-electron.add-metric"})(de||(de={}));const ku="sentry-electron-renderer-id";function Pe(t){return`${xu}://${t}/sentry_key`}function Tu(){if(window.__SENTRY_IPC__)return window.__SENTRY_IPC__;{E.log("IPC was not configured in preload script, falling back to custom protocol and fetch");const t=window.__SENTRY_RENDERER_ID__=Z(),e={[ku]:t};return{sendRendererStart:()=>{fetch(Pe(de.RENDERER_START),{method:"POST",body:"",headers:e}).catch(()=>{console.error(`Sentry SDK failed to establish connection with the Electron main process.
  - Ensure you have initialized the SDK in the main process
  - If your renderers use custom sessions, be sure to set 'getSessions' in the main process options
  - If you are bundling your main process code and using Electron < v5, you'll need to manually configure a preload script`)})},sendScope:n=>{fetch(Pe(de.SCOPE),{method:"POST",body:n,headers:e}).catch(()=>{})},sendEvent:n=>{fetch(Pe(de.EVENT),{method:"POST",body:n,headers:e}).catch(()=>{})},sendEnvelope:n=>{fetch(Pe(de.ENVELOPE),{method:"POST",body:n,headers:e}).catch(()=>{})},sendStatus:n=>{fetch(Pe(de.STATUS),{method:"POST",body:JSON.stringify({status:n}),headers:e}).catch(()=>{})},sendAddMetric:n=>{fetch(Pe(de.ADD_METRIC),{method:"POST",body:JSON.stringify(n),headers:e}).catch(()=>{})}}}}let Ct;function Yn(){return Ct||(Ct=Tu(),Ct.sendRendererStart()),Ct}const Iu=()=>({name:"ScopeToMain",setup(){const t=Yn();wu((e,n)=>{t.sendScope(JSON.stringify(se(e,20,2e3))),n.clearBreadcrumbs(),n.clearAttachments()})}});function Cu(t){const e=Yn();return Es(t,async n=>(e.sendEnvelope(n.body),{statusCode:200}))}function Ru(t){const e={pollInterval:1e3,anrThreshold:5e3,captureStackTrace:!1,...t},n=Yn();document.addEventListener("visibilitychange",()=>{n.sendStatus({status:document.visibilityState,config:e})}),n.sendStatus({status:document.visibilityState,config:e}),setInterval(()=>{n.sendStatus({status:"alive",config:e})},e.pollInterval)}const Ou=50,[,Nu]=Ts,[,Au]=io(),Mu=(t,e=0)=>{const n=[];for(const r of t.split(`
`).slice(e)){const s=Nu(r),i=Au(r);if(s&&(i==null?void 0:i.in_app)!==!1?n.push(s):i&&n.push(U(i)),n.length>=Ou)break}return os(n)};function Pu(t){return[...Rs(),Iu()]}function Du(t={},e=Eu){if(window!=null&&window.__SENTRY__RENDERER_INIT__){E.warn(`The browser SDK has already been initialized.
If init has been called in the preload and contextIsolation is disabled, is not required to call init in the renderer`);return}window.__SENTRY__RENDERER_INIT__=!0,t.autoSessionTracking===void 0&&(t.autoSessionTracking=!1),t.sendClientReports=!1,t.defaultIntegrations===void 0&&(t.defaultIntegrations=Pu()),t.stackParser===void 0&&(t.stackParser=Mu),t.dsn===void 0&&(t.dsn="https://<EMAIL>/12345"),t.transport===void 0&&(t.transport=Cu),t.anrDetection&&Ru(t.anrDetection===!0?{}:t.anrDetection),delete t.initialScope,e(t)}var $u=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};$u.SENTRY_RELEASE={id:"27cc6f763724a1af75b35c386a6b8d014eedc334"};var $t=(t=>(t.GetGlobalShortcut="GetGlobalShortcut",t.UpdateTitleBar="UpdateTitleBar",t.UpdateDarkMode="UpdateDarkMode",t))($t||{}),A=(t=>(t.RequestSetGlobalShortcut="request-set-global-shortcut",t.RequestSetMcpConfig="request-set-mcp-config",t.GetMcpConfig="get-mcp-config",t.McpConfigChanged="mcp-config-changed",t.McpStatusChanged="mcp-status-changed",t.ListMcpServers="list-mcp-servers",t.ConnectToMcpServer="connect-to-mcp-server",t.RequestMainMenuPopup="request-main-menu",t.McpServerConnected="mcp-server-connected",t.RequestOpenMcpSettings="request-open-mcp-settings",t.RequestOpenMcpInstaller="request-open-mcp-installer",t.RequestOpenWebSettings="request-open-web-settings",t.RequestOpenFeedback="request-open-feedback",t.RevealMcpServerSettings="reveal-mcp-server-settings",t.RevealMcpConfig="reveal-mcp-config",t.RevealMcpLogs="reveal-mcp-logs",t.TitleBarReady="title-bar-ready",t.ShowLoadErrorState="show-load-error-state",t.HideLoadErrorState="hide-load-error-state",t.RequestReloadWebview="request-reload-webview",t.GetBuildProps="get-build-props",t))(A||{}),Os=(t=>(t.QuickWindow="QuickWindow",t.Find="Find",t.StartupSettings="StartupSettings",t.Filesystem="Filesystem",t.Intl="Intl",t.IntlSync="IntlSync",t.AboutWindow="AboutWindow",t.WindowControl="WindowControl",t))(Os||{});const ju={getBuildProps:()=>k.ipcRenderer.invoke(A.GetBuildProps)};var T;(function(t){t.assertEqual=s=>s;function e(s){}t.assertIs=e;function n(s){throw new Error}t.assertNever=n,t.arrayToEnum=s=>{const i={};for(const o of s)i[o]=o;return i},t.getValidEnumValues=s=>{const i=t.objectKeys(s).filter(a=>typeof s[s[a]]!="number"),o={};for(const a of i)o[a]=s[a];return t.objectValues(o)},t.objectValues=s=>t.objectKeys(s).map(function(i){return s[i]}),t.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const i=[];for(const o in s)Object.prototype.hasOwnProperty.call(s,o)&&i.push(o);return i},t.find=(s,i)=>{for(const o of s)if(i(o))return o},t.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&isFinite(s)&&Math.floor(s)===s;function r(s,i=" | "){return s.map(o=>typeof o=="string"?`'${o}'`:o).join(i)}t.joinValues=r,t.jsonStringifyReplacer=(s,i)=>typeof i=="bigint"?i.toString():i})(T||(T={}));var Cn;(function(t){t.mergeShapes=(e,n)=>({...e,...n})})(Cn||(Cn={}));const g=T.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),le=t=>{switch(typeof t){case"undefined":return g.undefined;case"string":return g.string;case"number":return isNaN(t)?g.nan:g.number;case"boolean":return g.boolean;case"function":return g.function;case"bigint":return g.bigint;case"symbol":return g.symbol;case"object":return Array.isArray(t)?g.array:t===null?g.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?g.promise:typeof Map<"u"&&t instanceof Map?g.map:typeof Set<"u"&&t instanceof Set?g.set:typeof Date<"u"&&t instanceof Date?g.date:g.object;default:return g.unknown}},h=T.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),Lu=t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:");class B extends Error{constructor(e){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const n=e||function(i){return i.message},r={_errors:[]},s=i=>{for(const o of i.issues)if(o.code==="invalid_union")o.unionErrors.map(s);else if(o.code==="invalid_return_type")s(o.returnTypeError);else if(o.code==="invalid_arguments")s(o.argumentsError);else if(o.path.length===0)r._errors.push(n(o));else{let a=r,c=0;for(;c<o.path.length;){const u=o.path[c];c===o.path.length-1?(a[u]=a[u]||{_errors:[]},a[u]._errors.push(n(o))):a[u]=a[u]||{_errors:[]},a=a[u],c++}}};return s(this),r}static assert(e){if(!(e instanceof B))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,T.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=n=>n.message){const n={},r=[];for(const s of this.issues)s.path.length>0?(n[s.path[0]]=n[s.path[0]]||[],n[s.path[0]].push(e(s))):r.push(e(s));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}B.create=t=>new B(t);const qe=(t,e)=>{let n;switch(t.code){case h.invalid_type:t.received===g.undefined?n="Required":n=`Expected ${t.expected}, received ${t.received}`;break;case h.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(t.expected,T.jsonStringifyReplacer)}`;break;case h.unrecognized_keys:n=`Unrecognized key(s) in object: ${T.joinValues(t.keys,", ")}`;break;case h.invalid_union:n="Invalid input";break;case h.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${T.joinValues(t.options)}`;break;case h.invalid_enum_value:n=`Invalid enum value. Expected ${T.joinValues(t.options)}, received '${t.received}'`;break;case h.invalid_arguments:n="Invalid function arguments";break;case h.invalid_return_type:n="Invalid function return type";break;case h.invalid_date:n="Invalid date";break;case h.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(n=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?n=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?n=`Invalid input: must end with "${t.validation.endsWith}"`:T.assertNever(t.validation):t.validation!=="regex"?n=`Invalid ${t.validation}`:n="Invalid";break;case h.too_small:t.type==="array"?n=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?n=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?n=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?n=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:n="Invalid input";break;case h.too_big:t.type==="array"?n=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?n=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?n=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?n=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?n=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:n="Invalid input";break;case h.custom:n="Invalid input";break;case h.invalid_intersection_types:n="Intersection results could not be merged";break;case h.not_multiple_of:n=`Number must be a multiple of ${t.multipleOf}`;break;case h.not_finite:n="Number must be finite";break;default:n=e.defaultError,T.assertNever(t)}return{message:n}};let Ns=qe;function Fu(t){Ns=t}function jt(){return Ns}const Lt=t=>{const{data:e,path:n,errorMaps:r,issueData:s}=t,i=[...n,...s.path||[]],o={...s,path:i};if(s.message!==void 0)return{...s,path:i,message:s.message};let a="";const c=r.filter(u=>!!u).slice().reverse();for(const u of c)a=u(o,{data:e,defaultError:a}).message;return{...s,path:i,message:a}},Uu=[];function m(t,e){const n=jt(),r=Lt({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,n,n===qe?void 0:qe].filter(s=>!!s)});t.common.issues.push(r)}class ${constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,n){const r=[];for(const s of n){if(s.status==="aborted")return b;s.status==="dirty"&&e.dirty(),r.push(s.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,n){const r=[];for(const s of n){const i=await s.key,o=await s.value;r.push({key:i,value:o})}return $.mergeObjectSync(e,r)}static mergeObjectSync(e,n){const r={};for(const s of n){const{key:i,value:o}=s;if(i.status==="aborted"||o.status==="aborted")return b;i.status==="dirty"&&e.dirty(),o.status==="dirty"&&e.dirty(),i.value!=="__proto__"&&(typeof o.value<"u"||s.alwaysSet)&&(r[i.value]=o.value)}return{status:e.value,value:r}}}const b=Object.freeze({status:"aborted"}),$e=t=>({status:"dirty",value:t}),L=t=>({status:"valid",value:t}),Rn=t=>t.status==="aborted",On=t=>t.status==="dirty",Qe=t=>t.status==="valid",et=t=>typeof Promise<"u"&&t instanceof Promise;function Ft(t,e,n,r){if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(t)}function As(t,e,n,r,s){if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,n),n}var _;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(_||(_={}));var Je,Ke;class Q{constructor(e,n,r,s){this._cachedPath=[],this.parent=e,this.data=n,this._path=r,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Zr=(t,e)=>{if(Qe(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new B(t.common.issues);return this._error=n,this._error}}};function S(t){if(!t)return{};const{errorMap:e,invalid_type_error:n,required_error:r,description:s}=t;if(e&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:s}:{errorMap:(o,a)=>{var c,u;const{message:d}=t;return o.code==="invalid_enum_value"?{message:d??a.defaultError}:typeof a.data>"u"?{message:(c=d??r)!==null&&c!==void 0?c:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(u=d??n)!==null&&u!==void 0?u:a.defaultError}},description:s}}class x{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return le(e.data)}_getOrReturnCtx(e,n){return n||{common:e.parent.common,data:e.data,parsedType:le(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new $,ctx:{common:e.parent.common,data:e.data,parsedType:le(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const n=this._parse(e);if(et(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(e){const n=this._parse(e);return Promise.resolve(n)}parse(e,n){const r=this.safeParse(e,n);if(r.success)return r.data;throw r.error}safeParse(e,n){var r;const s={common:{issues:[],async:(r=n==null?void 0:n.async)!==null&&r!==void 0?r:!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:le(e)},i=this._parseSync({data:e,path:s.path,parent:s});return Zr(s,i)}async parseAsync(e,n){const r=await this.safeParseAsync(e,n);if(r.success)return r.data;throw r.error}async safeParseAsync(e,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:le(e)},s=this._parse({data:e,path:r.path,parent:r}),i=await(et(s)?s:Promise.resolve(s));return Zr(r,i)}refine(e,n){const r=s=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,i)=>{const o=e(s),a=()=>i.addIssue({code:h.custom,...r(s)});return typeof Promise<"u"&&o instanceof Promise?o.then(c=>c?!0:(a(),!1)):o?!0:(a(),!1)})}refinement(e,n){return this._refinement((r,s)=>e(r)?!0:(s.addIssue(typeof n=="function"?n(r,s):n),!1))}_refinement(e){return new J({schema:this,typeName:v.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return X.create(this,this._def)}nullable(){return ye.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Y.create(this,this._def)}promise(){return We.create(this,this._def)}or(e){return st.create([this,e],this._def)}and(e){return it.create(this,e,this._def)}transform(e){return new J({...S(this._def),schema:this,typeName:v.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const n=typeof e=="function"?e:()=>e;return new dt({...S(this._def),innerType:this,defaultValue:n,typeName:v.ZodDefault})}brand(){return new Jn({typeName:v.ZodBranded,type:this,...S(this._def)})}catch(e){const n=typeof e=="function"?e:()=>e;return new lt({...S(this._def),innerType:this,catchValue:n,typeName:v.ZodCatch})}describe(e){const n=this.constructor;return new n({...this._def,description:e})}pipe(e){return vt.create(this,e)}readonly(){return ft.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Zu=/^c[^\s-]{8,}$/i,Bu=/^[0-9a-z]+$/,Hu=/^[0-9A-HJKMNP-TV-Z]{26}$/,qu=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Vu=/^[a-z0-9_-]{21}$/i,Wu=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Gu=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,zu="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let sn;const Yu=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ju=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,Ku=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ms="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Xu=new RegExp(`^${Ms}$`);function Ps(t){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Qu(t){return new RegExp(`^${Ps(t)}$`)}function Ds(t){let e=`${Ms}T${Ps(t)}`;const n=[];return n.push(t.local?"Z?":"Z"),t.offset&&n.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${n.join("|")})`,new RegExp(`^${e}$`)}function ed(t,e){return!!((e==="v4"||!e)&&Yu.test(t)||(e==="v6"||!e)&&Ju.test(t))}class G extends x{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==g.string){const i=this._getOrReturnCtx(e);return m(i,{code:h.invalid_type,expected:g.string,received:i.parsedType}),b}const r=new $;let s;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),m(s,{code:h.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),m(s,{code:h.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,a=e.data.length<i.value;(o||a)&&(s=this._getOrReturnCtx(e,s),o?m(s,{code:h.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&m(s,{code:h.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),r.dirty())}else if(i.kind==="email")Gu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"email",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="emoji")sn||(sn=new RegExp(zu,"u")),sn.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"emoji",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="uuid")qu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"uuid",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="nanoid")Vu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"nanoid",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid")Zu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"cuid",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid2")Bu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"cuid2",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="ulid")Hu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"ulid",code:h.invalid_string,message:i.message}),r.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),m(s,{validation:"url",code:h.invalid_string,message:i.message}),r.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"regex",code:h.invalid_string,message:i.message}),r.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),m(s,{code:h.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),r.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),m(s,{code:h.invalid_string,validation:{startsWith:i.value},message:i.message}),r.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),m(s,{code:h.invalid_string,validation:{endsWith:i.value},message:i.message}),r.dirty()):i.kind==="datetime"?Ds(i).test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{code:h.invalid_string,validation:"datetime",message:i.message}),r.dirty()):i.kind==="date"?Xu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{code:h.invalid_string,validation:"date",message:i.message}),r.dirty()):i.kind==="time"?Qu(i).test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{code:h.invalid_string,validation:"time",message:i.message}),r.dirty()):i.kind==="duration"?Wu.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"duration",code:h.invalid_string,message:i.message}),r.dirty()):i.kind==="ip"?ed(e.data,i.version)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"ip",code:h.invalid_string,message:i.message}),r.dirty()):i.kind==="base64"?Ku.test(e.data)||(s=this._getOrReturnCtx(e,s),m(s,{validation:"base64",code:h.invalid_string,message:i.message}),r.dirty()):T.assertNever(i);return{status:r.value,value:e.data}}_regex(e,n,r){return this.refinement(s=>e.test(s),{validation:n,code:h.invalid_string,..._.errToObj(r)})}_addCheck(e){return new G({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",..._.errToObj(e)})}url(e){return this._addCheck({kind:"url",..._.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",..._.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",..._.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",..._.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",..._.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",..._.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",..._.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",..._.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",..._.errToObj(e)})}datetime(e){var n,r;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(n=e==null?void 0:e.offset)!==null&&n!==void 0?n:!1,local:(r=e==null?void 0:e.local)!==null&&r!==void 0?r:!1,..._.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,..._.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",..._.errToObj(e)})}regex(e,n){return this._addCheck({kind:"regex",regex:e,..._.errToObj(n)})}includes(e,n){return this._addCheck({kind:"includes",value:e,position:n==null?void 0:n.position,..._.errToObj(n==null?void 0:n.message)})}startsWith(e,n){return this._addCheck({kind:"startsWith",value:e,..._.errToObj(n)})}endsWith(e,n){return this._addCheck({kind:"endsWith",value:e,..._.errToObj(n)})}min(e,n){return this._addCheck({kind:"min",value:e,..._.errToObj(n)})}max(e,n){return this._addCheck({kind:"max",value:e,..._.errToObj(n)})}length(e,n){return this._addCheck({kind:"length",value:e,..._.errToObj(n)})}nonempty(e){return this.min(1,_.errToObj(e))}trim(){return new G({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new G({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new G({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get minLength(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e}get maxLength(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e}}G.create=t=>{var e;return new G({checks:[],typeName:v.ZodString,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...S(t)})};function td(t,e){const n=(t.toString().split(".")[1]||"").length,r=(e.toString().split(".")[1]||"").length,s=n>r?n:r,i=parseInt(t.toFixed(s).replace(".","")),o=parseInt(e.toFixed(s).replace(".",""));return i%o/Math.pow(10,s)}class he extends x{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==g.number){const i=this._getOrReturnCtx(e);return m(i,{code:h.invalid_type,expected:g.number,received:i.parsedType}),b}let r;const s=new $;for(const i of this._def.checks)i.kind==="int"?T.isInteger(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:h.invalid_type,expected:"integer",received:"float",message:i.message}),s.dirty()):i.kind==="min"?(i.inclusive?e.data<i.value:e.data<=i.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:h.too_small,minimum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?e.data>i.value:e.data>=i.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:h.too_big,maximum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="multipleOf"?td(e.data,i.value)!==0&&(r=this._getOrReturnCtx(e,r),m(r,{code:h.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):i.kind==="finite"?Number.isFinite(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:h.not_finite,message:i.message}),s.dirty()):T.assertNever(i);return{status:s.value,value:e.data}}gte(e,n){return this.setLimit("min",e,!0,_.toString(n))}gt(e,n){return this.setLimit("min",e,!1,_.toString(n))}lte(e,n){return this.setLimit("max",e,!0,_.toString(n))}lt(e,n){return this.setLimit("max",e,!1,_.toString(n))}setLimit(e,n,r,s){return new he({...this._def,checks:[...this._def.checks,{kind:e,value:n,inclusive:r,message:_.toString(s)}]})}_addCheck(e){return new he({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:_.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:_.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:_.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:_.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:_.toString(e)})}multipleOf(e,n){return this._addCheck({kind:"multipleOf",value:e,message:_.toString(n)})}finite(e){return this._addCheck({kind:"finite",message:_.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:_.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:_.toString(e)})}get minValue(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e}get maxValue(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&T.isInteger(e.value))}get isFinite(){let e=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(e===null||r.value<e)&&(e=r.value)}return Number.isFinite(n)&&Number.isFinite(e)}}he.create=t=>new he({checks:[],typeName:v.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...S(t)});class me extends x{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce&&(e.data=BigInt(e.data)),this._getType(e)!==g.bigint){const i=this._getOrReturnCtx(e);return m(i,{code:h.invalid_type,expected:g.bigint,received:i.parsedType}),b}let r;const s=new $;for(const i of this._def.checks)i.kind==="min"?(i.inclusive?e.data<i.value:e.data<=i.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:h.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?e.data>i.value:e.data>=i.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:h.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="multipleOf"?e.data%i.value!==BigInt(0)&&(r=this._getOrReturnCtx(e,r),m(r,{code:h.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):T.assertNever(i);return{status:s.value,value:e.data}}gte(e,n){return this.setLimit("min",e,!0,_.toString(n))}gt(e,n){return this.setLimit("min",e,!1,_.toString(n))}lte(e,n){return this.setLimit("max",e,!0,_.toString(n))}lt(e,n){return this.setLimit("max",e,!1,_.toString(n))}setLimit(e,n,r,s){return new me({...this._def,checks:[...this._def.checks,{kind:e,value:n,inclusive:r,message:_.toString(s)}]})}_addCheck(e){return new me({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:_.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:_.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:_.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:_.toString(e)})}multipleOf(e,n){return this._addCheck({kind:"multipleOf",value:e,message:_.toString(n)})}get minValue(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e}get maxValue(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e}}me.create=t=>{var e;return new me({checks:[],typeName:v.ZodBigInt,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...S(t)})};class tt extends x{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==g.boolean){const r=this._getOrReturnCtx(e);return m(r,{code:h.invalid_type,expected:g.boolean,received:r.parsedType}),b}return L(e.data)}}tt.create=t=>new tt({typeName:v.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...S(t)});class Ce extends x{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==g.date){const i=this._getOrReturnCtx(e);return m(i,{code:h.invalid_type,expected:g.date,received:i.parsedType}),b}if(isNaN(e.data.getTime())){const i=this._getOrReturnCtx(e);return m(i,{code:h.invalid_date}),b}const r=new $;let s;for(const i of this._def.checks)i.kind==="min"?e.data.getTime()<i.value&&(s=this._getOrReturnCtx(e,s),m(s,{code:h.too_small,message:i.message,inclusive:!0,exact:!1,minimum:i.value,type:"date"}),r.dirty()):i.kind==="max"?e.data.getTime()>i.value&&(s=this._getOrReturnCtx(e,s),m(s,{code:h.too_big,message:i.message,inclusive:!0,exact:!1,maximum:i.value,type:"date"}),r.dirty()):T.assertNever(i);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Ce({...this._def,checks:[...this._def.checks,e]})}min(e,n){return this._addCheck({kind:"min",value:e.getTime(),message:_.toString(n)})}max(e,n){return this._addCheck({kind:"max",value:e.getTime(),message:_.toString(n)})}get minDate(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e!=null?new Date(e):null}}Ce.create=t=>new Ce({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:v.ZodDate,...S(t)});class Ut extends x{_parse(e){if(this._getType(e)!==g.symbol){const r=this._getOrReturnCtx(e);return m(r,{code:h.invalid_type,expected:g.symbol,received:r.parsedType}),b}return L(e.data)}}Ut.create=t=>new Ut({typeName:v.ZodSymbol,...S(t)});class nt extends x{_parse(e){if(this._getType(e)!==g.undefined){const r=this._getOrReturnCtx(e);return m(r,{code:h.invalid_type,expected:g.undefined,received:r.parsedType}),b}return L(e.data)}}nt.create=t=>new nt({typeName:v.ZodUndefined,...S(t)});class rt extends x{_parse(e){if(this._getType(e)!==g.null){const r=this._getOrReturnCtx(e);return m(r,{code:h.invalid_type,expected:g.null,received:r.parsedType}),b}return L(e.data)}}rt.create=t=>new rt({typeName:v.ZodNull,...S(t)});class Ve extends x{constructor(){super(...arguments),this._any=!0}_parse(e){return L(e.data)}}Ve.create=t=>new Ve({typeName:v.ZodAny,...S(t)});class Ee extends x{constructor(){super(...arguments),this._unknown=!0}_parse(e){return L(e.data)}}Ee.create=t=>new Ee({typeName:v.ZodUnknown,...S(t)});class ae extends x{_parse(e){const n=this._getOrReturnCtx(e);return m(n,{code:h.invalid_type,expected:g.never,received:n.parsedType}),b}}ae.create=t=>new ae({typeName:v.ZodNever,...S(t)});class Zt extends x{_parse(e){if(this._getType(e)!==g.undefined){const r=this._getOrReturnCtx(e);return m(r,{code:h.invalid_type,expected:g.void,received:r.parsedType}),b}return L(e.data)}}Zt.create=t=>new Zt({typeName:v.ZodVoid,...S(t)});class Y extends x{_parse(e){const{ctx:n,status:r}=this._processInputParams(e),s=this._def;if(n.parsedType!==g.array)return m(n,{code:h.invalid_type,expected:g.array,received:n.parsedType}),b;if(s.exactLength!==null){const o=n.data.length>s.exactLength.value,a=n.data.length<s.exactLength.value;(o||a)&&(m(n,{code:o?h.too_big:h.too_small,minimum:a?s.exactLength.value:void 0,maximum:o?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&(m(n,{code:h.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&(m(n,{code:h.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((o,a)=>s.type._parseAsync(new Q(n,o,n.path,a)))).then(o=>$.mergeArray(r,o));const i=[...n.data].map((o,a)=>s.type._parseSync(new Q(n,o,n.path,a)));return $.mergeArray(r,i)}get element(){return this._def.type}min(e,n){return new Y({...this._def,minLength:{value:e,message:_.toString(n)}})}max(e,n){return new Y({...this._def,maxLength:{value:e,message:_.toString(n)}})}length(e,n){return new Y({...this._def,exactLength:{value:e,message:_.toString(n)}})}nonempty(e){return this.min(1,e)}}Y.create=(t,e)=>new Y({type:t,minLength:null,maxLength:null,exactLength:null,typeName:v.ZodArray,...S(e)});function De(t){if(t instanceof N){const e={};for(const n in t.shape){const r=t.shape[n];e[n]=X.create(De(r))}return new N({...t._def,shape:()=>e})}else return t instanceof Y?new Y({...t._def,type:De(t.element)}):t instanceof X?X.create(De(t.unwrap())):t instanceof ye?ye.create(De(t.unwrap())):t instanceof ee?ee.create(t.items.map(e=>De(e))):t}class N extends x{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),n=T.objectKeys(e);return this._cached={shape:e,keys:n}}_parse(e){if(this._getType(e)!==g.object){const u=this._getOrReturnCtx(e);return m(u,{code:h.invalid_type,expected:g.object,received:u.parsedType}),b}const{status:r,ctx:s}=this._processInputParams(e),{shape:i,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof ae&&this._def.unknownKeys==="strip"))for(const u in s.data)o.includes(u)||a.push(u);const c=[];for(const u of o){const d=i[u],l=s.data[u];c.push({key:{status:"valid",value:u},value:d._parse(new Q(s,l,s.path,u)),alwaysSet:u in s.data})}if(this._def.catchall instanceof ae){const u=this._def.unknownKeys;if(u==="passthrough")for(const d of a)c.push({key:{status:"valid",value:d},value:{status:"valid",value:s.data[d]}});else if(u==="strict")a.length>0&&(m(s,{code:h.unrecognized_keys,keys:a}),r.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const d of a){const l=s.data[d];c.push({key:{status:"valid",value:d},value:u._parse(new Q(s,l,s.path,d)),alwaysSet:d in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const u=[];for(const d of c){const l=await d.key,f=await d.value;u.push({key:l,value:f,alwaysSet:d.alwaysSet})}return u}).then(u=>$.mergeObjectSync(r,u)):$.mergeObjectSync(r,c)}get shape(){return this._def.shape()}strict(e){return _.errToObj,new N({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(n,r)=>{var s,i,o,a;const c=(o=(i=(s=this._def).errorMap)===null||i===void 0?void 0:i.call(s,n,r).message)!==null&&o!==void 0?o:r.defaultError;return n.code==="unrecognized_keys"?{message:(a=_.errToObj(e).message)!==null&&a!==void 0?a:c}:{message:c}}}:{}})}strip(){return new N({...this._def,unknownKeys:"strip"})}passthrough(){return new N({...this._def,unknownKeys:"passthrough"})}extend(e){return new N({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new N({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:v.ZodObject})}setKey(e,n){return this.augment({[e]:n})}catchall(e){return new N({...this._def,catchall:e})}pick(e){const n={};return T.objectKeys(e).forEach(r=>{e[r]&&this.shape[r]&&(n[r]=this.shape[r])}),new N({...this._def,shape:()=>n})}omit(e){const n={};return T.objectKeys(this.shape).forEach(r=>{e[r]||(n[r]=this.shape[r])}),new N({...this._def,shape:()=>n})}deepPartial(){return De(this)}partial(e){const n={};return T.objectKeys(this.shape).forEach(r=>{const s=this.shape[r];e&&!e[r]?n[r]=s:n[r]=s.optional()}),new N({...this._def,shape:()=>n})}required(e){const n={};return T.objectKeys(this.shape).forEach(r=>{if(e&&!e[r])n[r]=this.shape[r];else{let i=this.shape[r];for(;i instanceof X;)i=i._def.innerType;n[r]=i}}),new N({...this._def,shape:()=>n})}keyof(){return $s(T.objectKeys(this.shape))}}N.create=(t,e)=>new N({shape:()=>t,unknownKeys:"strip",catchall:ae.create(),typeName:v.ZodObject,...S(e)});N.strictCreate=(t,e)=>new N({shape:()=>t,unknownKeys:"strict",catchall:ae.create(),typeName:v.ZodObject,...S(e)});N.lazycreate=(t,e)=>new N({shape:t,unknownKeys:"strip",catchall:ae.create(),typeName:v.ZodObject,...S(e)});class st extends x{_parse(e){const{ctx:n}=this._processInputParams(e),r=this._def.options;function s(i){for(const a of i)if(a.result.status==="valid")return a.result;for(const a of i)if(a.result.status==="dirty")return n.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(a=>new B(a.ctx.common.issues));return m(n,{code:h.invalid_union,unionErrors:o}),b}if(n.common.async)return Promise.all(r.map(async i=>{const o={...n,common:{...n.common,issues:[]},parent:null};return{result:await i._parseAsync({data:n.data,path:n.path,parent:o}),ctx:o}})).then(s);{let i;const o=[];for(const c of r){const u={...n,common:{...n.common,issues:[]},parent:null},d=c._parseSync({data:n.data,path:n.path,parent:u});if(d.status==="valid")return d;d.status==="dirty"&&!i&&(i={result:d,ctx:u}),u.common.issues.length&&o.push(u.common.issues)}if(i)return n.common.issues.push(...i.ctx.common.issues),i.result;const a=o.map(c=>new B(c));return m(n,{code:h.invalid_union,unionErrors:a}),b}}get options(){return this._def.options}}st.create=(t,e)=>new st({options:t,typeName:v.ZodUnion,...S(e)});const ne=t=>t instanceof at?ne(t.schema):t instanceof J?ne(t.innerType()):t instanceof ct?[t.value]:t instanceof ge?t.options:t instanceof ut?T.objectValues(t.enum):t instanceof dt?ne(t._def.innerType):t instanceof nt?[void 0]:t instanceof rt?[null]:t instanceof X?[void 0,...ne(t.unwrap())]:t instanceof ye?[null,...ne(t.unwrap())]:t instanceof Jn||t instanceof ft?ne(t.unwrap()):t instanceof lt?ne(t._def.innerType):[];class Xt extends x{_parse(e){const{ctx:n}=this._processInputParams(e);if(n.parsedType!==g.object)return m(n,{code:h.invalid_type,expected:g.object,received:n.parsedType}),b;const r=this.discriminator,s=n.data[r],i=this.optionsMap.get(s);return i?n.common.async?i._parseAsync({data:n.data,path:n.path,parent:n}):i._parseSync({data:n.data,path:n.path,parent:n}):(m(n,{code:h.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),b)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,n,r){const s=new Map;for(const i of n){const o=ne(i.shape[e]);if(!o.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const a of o){if(s.has(a))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);s.set(a,i)}}return new Xt({typeName:v.ZodDiscriminatedUnion,discriminator:e,options:n,optionsMap:s,...S(r)})}}function Nn(t,e){const n=le(t),r=le(e);if(t===e)return{valid:!0,data:t};if(n===g.object&&r===g.object){const s=T.objectKeys(e),i=T.objectKeys(t).filter(a=>s.indexOf(a)!==-1),o={...t,...e};for(const a of i){const c=Nn(t[a],e[a]);if(!c.valid)return{valid:!1};o[a]=c.data}return{valid:!0,data:o}}else if(n===g.array&&r===g.array){if(t.length!==e.length)return{valid:!1};const s=[];for(let i=0;i<t.length;i++){const o=t[i],a=e[i],c=Nn(o,a);if(!c.valid)return{valid:!1};s.push(c.data)}return{valid:!0,data:s}}else return n===g.date&&r===g.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class it extends x{_parse(e){const{status:n,ctx:r}=this._processInputParams(e),s=(i,o)=>{if(Rn(i)||Rn(o))return b;const a=Nn(i.value,o.value);return a.valid?((On(i)||On(o))&&n.dirty(),{status:n.value,value:a.data}):(m(r,{code:h.invalid_intersection_types}),b)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([i,o])=>s(i,o)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}it.create=(t,e,n)=>new it({left:t,right:e,typeName:v.ZodIntersection,...S(n)});class ee extends x{_parse(e){const{status:n,ctx:r}=this._processInputParams(e);if(r.parsedType!==g.array)return m(r,{code:h.invalid_type,expected:g.array,received:r.parsedType}),b;if(r.data.length<this._def.items.length)return m(r,{code:h.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),b;!this._def.rest&&r.data.length>this._def.items.length&&(m(r,{code:h.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const i=[...r.data].map((o,a)=>{const c=this._def.items[a]||this._def.rest;return c?c._parse(new Q(r,o,r.path,a)):null}).filter(o=>!!o);return r.common.async?Promise.all(i).then(o=>$.mergeArray(n,o)):$.mergeArray(n,i)}get items(){return this._def.items}rest(e){return new ee({...this._def,rest:e})}}ee.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ee({items:t,typeName:v.ZodTuple,rest:null,...S(e)})};class ot extends x{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:n,ctx:r}=this._processInputParams(e);if(r.parsedType!==g.object)return m(r,{code:h.invalid_type,expected:g.object,received:r.parsedType}),b;const s=[],i=this._def.keyType,o=this._def.valueType;for(const a in r.data)s.push({key:i._parse(new Q(r,a,r.path,a)),value:o._parse(new Q(r,r.data[a],r.path,a)),alwaysSet:a in r.data});return r.common.async?$.mergeObjectAsync(n,s):$.mergeObjectSync(n,s)}get element(){return this._def.valueType}static create(e,n,r){return n instanceof x?new ot({keyType:e,valueType:n,typeName:v.ZodRecord,...S(r)}):new ot({keyType:G.create(),valueType:e,typeName:v.ZodRecord,...S(n)})}}class Bt extends x{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:n,ctx:r}=this._processInputParams(e);if(r.parsedType!==g.map)return m(r,{code:h.invalid_type,expected:g.map,received:r.parsedType}),b;const s=this._def.keyType,i=this._def.valueType,o=[...r.data.entries()].map(([a,c],u)=>({key:s._parse(new Q(r,a,r.path,[u,"key"])),value:i._parse(new Q(r,c,r.path,[u,"value"]))}));if(r.common.async){const a=new Map;return Promise.resolve().then(async()=>{for(const c of o){const u=await c.key,d=await c.value;if(u.status==="aborted"||d.status==="aborted")return b;(u.status==="dirty"||d.status==="dirty")&&n.dirty(),a.set(u.value,d.value)}return{status:n.value,value:a}})}else{const a=new Map;for(const c of o){const u=c.key,d=c.value;if(u.status==="aborted"||d.status==="aborted")return b;(u.status==="dirty"||d.status==="dirty")&&n.dirty(),a.set(u.value,d.value)}return{status:n.value,value:a}}}}Bt.create=(t,e,n)=>new Bt({valueType:e,keyType:t,typeName:v.ZodMap,...S(n)});class Re extends x{_parse(e){const{status:n,ctx:r}=this._processInputParams(e);if(r.parsedType!==g.set)return m(r,{code:h.invalid_type,expected:g.set,received:r.parsedType}),b;const s=this._def;s.minSize!==null&&r.data.size<s.minSize.value&&(m(r,{code:h.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&r.data.size>s.maxSize.value&&(m(r,{code:h.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const i=this._def.valueType;function o(c){const u=new Set;for(const d of c){if(d.status==="aborted")return b;d.status==="dirty"&&n.dirty(),u.add(d.value)}return{status:n.value,value:u}}const a=[...r.data.values()].map((c,u)=>i._parse(new Q(r,c,r.path,u)));return r.common.async?Promise.all(a).then(c=>o(c)):o(a)}min(e,n){return new Re({...this._def,minSize:{value:e,message:_.toString(n)}})}max(e,n){return new Re({...this._def,maxSize:{value:e,message:_.toString(n)}})}size(e,n){return this.min(e,n).max(e,n)}nonempty(e){return this.min(1,e)}}Re.create=(t,e)=>new Re({valueType:t,minSize:null,maxSize:null,typeName:v.ZodSet,...S(e)});class Le extends x{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:n}=this._processInputParams(e);if(n.parsedType!==g.function)return m(n,{code:h.invalid_type,expected:g.function,received:n.parsedType}),b;function r(a,c){return Lt({data:a,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,jt(),qe].filter(u=>!!u),issueData:{code:h.invalid_arguments,argumentsError:c}})}function s(a,c){return Lt({data:a,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,jt(),qe].filter(u=>!!u),issueData:{code:h.invalid_return_type,returnTypeError:c}})}const i={errorMap:n.common.contextualErrorMap},o=n.data;if(this._def.returns instanceof We){const a=this;return L(async function(...c){const u=new B([]),d=await a._def.args.parseAsync(c,i).catch(p=>{throw u.addIssue(r(c,p)),u}),l=await Reflect.apply(o,this,d);return await a._def.returns._def.type.parseAsync(l,i).catch(p=>{throw u.addIssue(s(l,p)),u})})}else{const a=this;return L(function(...c){const u=a._def.args.safeParse(c,i);if(!u.success)throw new B([r(c,u.error)]);const d=Reflect.apply(o,this,u.data),l=a._def.returns.safeParse(d,i);if(!l.success)throw new B([s(d,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Le({...this._def,args:ee.create(e).rest(Ee.create())})}returns(e){return new Le({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,n,r){return new Le({args:e||ee.create([]).rest(Ee.create()),returns:n||Ee.create(),typeName:v.ZodFunction,...S(r)})}}class at extends x{get schema(){return this._def.getter()}_parse(e){const{ctx:n}=this._processInputParams(e);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}at.create=(t,e)=>new at({getter:t,typeName:v.ZodLazy,...S(e)});class ct extends x{_parse(e){if(e.data!==this._def.value){const n=this._getOrReturnCtx(e);return m(n,{received:n.data,code:h.invalid_literal,expected:this._def.value}),b}return{status:"valid",value:e.data}}get value(){return this._def.value}}ct.create=(t,e)=>new ct({value:t,typeName:v.ZodLiteral,...S(e)});function $s(t,e){return new ge({values:t,typeName:v.ZodEnum,...S(e)})}class ge extends x{constructor(){super(...arguments),Je.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const n=this._getOrReturnCtx(e),r=this._def.values;return m(n,{expected:T.joinValues(r),received:n.parsedType,code:h.invalid_type}),b}if(Ft(this,Je)||As(this,Je,new Set(this._def.values)),!Ft(this,Je).has(e.data)){const n=this._getOrReturnCtx(e),r=this._def.values;return m(n,{received:n.data,code:h.invalid_enum_value,options:r}),b}return L(e.data)}get options(){return this._def.values}get enum(){const e={};for(const n of this._def.values)e[n]=n;return e}get Values(){const e={};for(const n of this._def.values)e[n]=n;return e}get Enum(){const e={};for(const n of this._def.values)e[n]=n;return e}extract(e,n=this._def){return ge.create(e,{...this._def,...n})}exclude(e,n=this._def){return ge.create(this.options.filter(r=>!e.includes(r)),{...this._def,...n})}}Je=new WeakMap;ge.create=$s;class ut extends x{constructor(){super(...arguments),Ke.set(this,void 0)}_parse(e){const n=T.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==g.string&&r.parsedType!==g.number){const s=T.objectValues(n);return m(r,{expected:T.joinValues(s),received:r.parsedType,code:h.invalid_type}),b}if(Ft(this,Ke)||As(this,Ke,new Set(T.getValidEnumValues(this._def.values))),!Ft(this,Ke).has(e.data)){const s=T.objectValues(n);return m(r,{received:r.data,code:h.invalid_enum_value,options:s}),b}return L(e.data)}get enum(){return this._def.values}}Ke=new WeakMap;ut.create=(t,e)=>new ut({values:t,typeName:v.ZodNativeEnum,...S(e)});class We extends x{unwrap(){return this._def.type}_parse(e){const{ctx:n}=this._processInputParams(e);if(n.parsedType!==g.promise&&n.common.async===!1)return m(n,{code:h.invalid_type,expected:g.promise,received:n.parsedType}),b;const r=n.parsedType===g.promise?n.data:Promise.resolve(n.data);return L(r.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}We.create=(t,e)=>new We({type:t,typeName:v.ZodPromise,...S(e)});class J extends x{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===v.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:n,ctx:r}=this._processInputParams(e),s=this._def.effect||null,i={addIssue:o=>{m(r,o),o.fatal?n.abort():n.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),s.type==="preprocess"){const o=s.transform(r.data,i);if(r.common.async)return Promise.resolve(o).then(async a=>{if(n.value==="aborted")return b;const c=await this._def.schema._parseAsync({data:a,path:r.path,parent:r});return c.status==="aborted"?b:c.status==="dirty"||n.value==="dirty"?$e(c.value):c});{if(n.value==="aborted")return b;const a=this._def.schema._parseSync({data:o,path:r.path,parent:r});return a.status==="aborted"?b:a.status==="dirty"||n.value==="dirty"?$e(a.value):a}}if(s.type==="refinement"){const o=a=>{const c=s.refinement(a,i);if(r.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(r.common.async===!1){const a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?b:(a.status==="dirty"&&n.dirty(),o(a.value),{status:n.value,value:a.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(a=>a.status==="aborted"?b:(a.status==="dirty"&&n.dirty(),o(a.value).then(()=>({status:n.value,value:a.value}))))}if(s.type==="transform")if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!Qe(o))return o;const a=s.transform(o.value,i);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:a}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>Qe(o)?Promise.resolve(s.transform(o.value,i)).then(a=>({status:n.value,value:a})):o);T.assertNever(s)}}J.create=(t,e,n)=>new J({schema:t,typeName:v.ZodEffects,effect:e,...S(n)});J.createWithPreprocess=(t,e,n)=>new J({schema:e,effect:{type:"preprocess",transform:t},typeName:v.ZodEffects,...S(n)});class X extends x{_parse(e){return this._getType(e)===g.undefined?L(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}X.create=(t,e)=>new X({innerType:t,typeName:v.ZodOptional,...S(e)});class ye extends x{_parse(e){return this._getType(e)===g.null?L(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ye.create=(t,e)=>new ye({innerType:t,typeName:v.ZodNullable,...S(e)});class dt extends x{_parse(e){const{ctx:n}=this._processInputParams(e);let r=n.data;return n.parsedType===g.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}dt.create=(t,e)=>new dt({innerType:t,typeName:v.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...S(e)});class lt extends x{_parse(e){const{ctx:n}=this._processInputParams(e),r={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return et(s)?s.then(i=>({status:"valid",value:i.status==="valid"?i.value:this._def.catchValue({get error(){return new B(r.common.issues)},input:r.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new B(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}lt.create=(t,e)=>new lt({innerType:t,typeName:v.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...S(e)});class Ht extends x{_parse(e){if(this._getType(e)!==g.nan){const r=this._getOrReturnCtx(e);return m(r,{code:h.invalid_type,expected:g.nan,received:r.parsedType}),b}return{status:"valid",value:e.data}}}Ht.create=t=>new Ht({typeName:v.ZodNaN,...S(t)});const nd=Symbol("zod_brand");class Jn extends x{_parse(e){const{ctx:n}=this._processInputParams(e),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class vt extends x{_parse(e){const{status:n,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{const i=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return i.status==="aborted"?b:i.status==="dirty"?(n.dirty(),$e(i.value)):this._def.out._parseAsync({data:i.value,path:r.path,parent:r})})();{const s=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?b:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:r.path,parent:r})}}static create(e,n){return new vt({in:e,out:n,typeName:v.ZodPipeline})}}class ft extends x{_parse(e){const n=this._def.innerType._parse(e),r=s=>(Qe(s)&&(s.value=Object.freeze(s.value)),s);return et(n)?n.then(s=>r(s)):r(n)}unwrap(){return this._def.innerType}}ft.create=(t,e)=>new ft({innerType:t,typeName:v.ZodReadonly,...S(e)});function js(t,e={},n){return t?Ve.create().superRefine((r,s)=>{var i,o;if(!t(r)){const a=typeof e=="function"?e(r):typeof e=="string"?{message:e}:e,c=(o=(i=a.fatal)!==null&&i!==void 0?i:n)!==null&&o!==void 0?o:!0,u=typeof a=="string"?{message:a}:a;s.addIssue({code:"custom",...u,fatal:c})}}):Ve.create()}const rd={object:N.lazycreate};var v;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(v||(v={}));const sd=(t,e={message:`Input not instance of ${t.name}`})=>js(n=>n instanceof t,e),Ls=G.create,Fs=he.create,id=Ht.create,od=me.create,Us=tt.create,ad=Ce.create,cd=Ut.create,ud=nt.create,dd=rt.create,ld=Ve.create,fd=Ee.create,pd=ae.create,hd=Zt.create,md=Y.create,gd=N.create,yd=N.strictCreate,_d=st.create,vd=Xt.create,bd=it.create,Ed=ee.create,Sd=ot.create,wd=Bt.create,xd=Re.create,kd=Le.create,Td=at.create,Id=ct.create,Cd=ge.create,Rd=ut.create,Od=We.create,Br=J.create,Nd=X.create,Ad=ye.create,Md=J.createWithPreprocess,Pd=vt.create,Dd=()=>Ls().optional(),$d=()=>Fs().optional(),jd=()=>Us().optional(),Ld={string:t=>G.create({...t,coerce:!0}),number:t=>he.create({...t,coerce:!0}),boolean:t=>tt.create({...t,coerce:!0}),bigint:t=>me.create({...t,coerce:!0}),date:t=>Ce.create({...t,coerce:!0})},Fd=b;var V=Object.freeze({__proto__:null,defaultErrorMap:qe,setErrorMap:Fu,getErrorMap:jt,makeIssue:Lt,EMPTY_PATH:Uu,addIssueToContext:m,ParseStatus:$,INVALID:b,DIRTY:$e,OK:L,isAborted:Rn,isDirty:On,isValid:Qe,isAsync:et,get util(){return T},get objectUtil(){return Cn},ZodParsedType:g,getParsedType:le,ZodType:x,datetimeRegex:Ds,ZodString:G,ZodNumber:he,ZodBigInt:me,ZodBoolean:tt,ZodDate:Ce,ZodSymbol:Ut,ZodUndefined:nt,ZodNull:rt,ZodAny:Ve,ZodUnknown:Ee,ZodNever:ae,ZodVoid:Zt,ZodArray:Y,ZodObject:N,ZodUnion:st,ZodDiscriminatedUnion:Xt,ZodIntersection:it,ZodTuple:ee,ZodRecord:ot,ZodMap:Bt,ZodSet:Re,ZodFunction:Le,ZodLazy:at,ZodLiteral:ct,ZodEnum:ge,ZodNativeEnum:ut,ZodPromise:We,ZodEffects:J,ZodTransformer:J,ZodOptional:X,ZodNullable:ye,ZodDefault:dt,ZodCatch:lt,ZodNaN:Ht,BRAND:nd,ZodBranded:Jn,ZodPipeline:vt,ZodReadonly:ft,custom:js,Schema:x,ZodSchema:x,late:rd,get ZodFirstPartyTypeKind(){return v},coerce:Ld,any:ld,array:md,bigint:od,boolean:Us,date:ad,discriminatedUnion:vd,effect:Br,enum:Cd,function:kd,instanceof:sd,intersection:bd,lazy:Td,literal:Id,map:wd,nan:id,nativeEnum:Rd,never:pd,null:dd,nullable:Ad,number:Fs,object:gd,oboolean:jd,onumber:$d,optional:Nd,ostring:Dd,pipeline:Pd,preprocess:Md,promise:Od,record:Sd,set:xd,strictObject:yd,string:Ls,symbol:cd,transformer:Br,tuple:Ed,undefined:ud,union:_d,unknown:fd,void:hd,NEVER:Fd,ZodIssueCode:h,quotelessJson:Lu,ZodError:B});const Ud=V.enum(["initializing","running","failed"]),Zs=V.object({command:V.string(),args:V.array(V.string()).optional(),env:V.record(V.string()).optional()}),Zd=Zs.extend({status:Ud,error:V.string().optional()});V.record(V.string(),Zs);const Bd=V.record(V.string(),Zd),Hr=t=>{const{success:e,data:n}=Bd.safeParse(t);if(!e){console.error(`failed to parse mcpConfig: ${t}`);return}return n},Hd={setGlobalShortcut:t=>k.ipcRenderer.invoke(A.RequestSetGlobalShortcut,t),getGlobalShortcut:t=>{k.ipcRenderer.on($t.GetGlobalShortcut,(e,n)=>{t(n)})},setMcpConfigs:t=>k.ipcRenderer.invoke(A.RequestSetMcpConfig,t),getMcpConfigs:async()=>{const t=await k.ipcRenderer.invoke(A.GetMcpConfig);return Hr(t)},onMcpConfigChange:t=>{k.ipcRenderer.on(A.McpConfigChanged,(e,n)=>{const r=Hr(n);r!==void 0&&t(r)})},removeMcpConfigChangeListeners:()=>{k.ipcRenderer.removeAllListeners(A.McpConfigChanged)},onMcpStatusChange:t=>{k.ipcRenderer.on(A.McpStatusChanged,(e,n,r,s)=>{t(n,r,s)})},removeMcpStatusChangeListeners:()=>{k.ipcRenderer.removeAllListeners(A.McpStatusChanged)},onRevealMcpServerSettingsRequested(t){k.ipcRenderer.on(A.RevealMcpServerSettings,(e,n)=>{t(n)})},removeRevealMcpServerSettingsRequestedListeners(){k.ipcRenderer.removeAllListeners(A.RevealMcpServerSettings)},openMcpInstaller:()=>k.ipcRenderer.invoke(A.RequestOpenMcpInstaller),openWebSettings:()=>k.ipcRenderer.invoke(A.RequestOpenWebSettings),openFeedback:()=>k.ipcRenderer.invoke(A.RequestOpenFeedback),revealMcpConfig:()=>k.ipcRenderer.invoke(A.RevealMcpConfig),revealMcpLogs:()=>k.ipcRenderer.invoke(A.RevealMcpLogs)},qd={onUpdateTitleBar:t=>{k.ipcRenderer.on($t.UpdateTitleBar,(e,n)=>{t(n)})},onUpdateDarkMode:t=>{k.ipcRenderer.on($t.UpdateDarkMode,(e,n)=>t(n))},requestMainMenuPopup:()=>{k.ipcRenderer.send(A.RequestMainMenuPopup)},onShowLoadError:t=>{k.ipcRenderer.on(A.ShowLoadErrorState,(e,n)=>{t(n)})},onHideLoadError:t=>{k.ipcRenderer.on(A.HideLoadErrorState,()=>{t()})},requestReloadWebview:()=>{k.ipcRenderer.send(A.RequestReloadWebview)},titleBarReady:()=>{k.ipcRenderer.send(A.TitleBarReady)}};var An=function(t,e){return An=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(n[s]=r[s])},An(t,e)};function Kn(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");An(t,e);function n(){this.constructor=t}t.prototype=e===null?Object.create(e):(n.prototype=e.prototype,new n)}function Vd(t,e,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(l){o(l)}}function c(d){try{u(r.throw(d))}catch(l){o(l)}}function u(d){d.done?i(d.value):s(d.value).then(a,c)}u((r=r.apply(t,e||[])).next())})}function Bs(t,e){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,s,i,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(u){return function(d){return c([u,d])}}function c(u){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,u[0]&&(n=0)),n;)try{if(r=1,s&&(i=u[0]&2?s.return:u[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,u[1])).done)return i;switch(s=0,i&&(u=[u[0]&2,i.value]),u[0]){case 0:case 1:i=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,s=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!i||u[1]>i[0]&&u[1]<i[3])){n.label=u[1];break}if(u[0]===6&&n.label<i[1]){n.label=i[1],i=u;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(u);break}i[2]&&n.ops.pop(),n.trys.pop();continue}u=e.call(t,n)}catch(d){u=[6,d],s=0}finally{r=i=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function pt(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Mn(t,e){var n=typeof Symbol=="function"&&t[Symbol.iterator];if(!n)return t;var r=n.call(t),s,i=[],o;try{for(;(e===void 0||e-- >0)&&!(s=r.next()).done;)i.push(s.value)}catch(a){o={error:a}}finally{try{s&&!s.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return i}function Pn(t,e,n){if(n||arguments.length===2)for(var r=0,s=e.length,i;r<s;r++)(i||!(r in e))&&(i||(i=Array.prototype.slice.call(e,0,r)),i[r]=e[r]);return t.concat(i||Array.prototype.slice.call(e))}function Fe(t){return this instanceof Fe?(this.v=t,this):new Fe(t)}function Wd(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(t,e||[]),s,i=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",o),s[Symbol.asyncIterator]=function(){return this},s;function o(p){return function(y){return Promise.resolve(y).then(p,l)}}function a(p,y){r[p]&&(s[p]=function(w){return new Promise(function(I,P){i.push([p,w,I,P])>1||c(p,w)})},y&&(s[p]=y(s[p])))}function c(p,y){try{u(r[p](y))}catch(w){f(i[0][3],w)}}function u(p){p.value instanceof Fe?Promise.resolve(p.value.v).then(d,l):f(i[0][2],p)}function d(p){c("next",p)}function l(p){c("throw",p)}function f(p,y){p(y),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Gd(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof pt=="function"?pt(t):t[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=t[i]&&function(o){return new Promise(function(a,c){o=t[i](o),s(a,c,o.done,o.value)})}}function s(i,o,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},o)}}function M(t){return typeof t=="function"}function zd(t){var e=function(r){Error.call(r),r.stack=new Error().stack},n=t(e);return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var on=zd(function(t){return function(n){t(this),this.message=n?n.length+` errors occurred during unsubscription:
`+n.map(function(r,s){return s+1+") "+r.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=n}});function qr(t,e){if(t){var n=t.indexOf(e);0<=n&&t.splice(n,1)}}var Xn=function(){function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var e,n,r,s,i;if(!this.closed){this.closed=!0;var o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var a=pt(o),c=a.next();!c.done;c=a.next()){var u=c.value;u.remove(this)}}catch(w){e={error:w}}finally{try{c&&!c.done&&(n=a.return)&&n.call(a)}finally{if(e)throw e.error}}else o.remove(this);var d=this.initialTeardown;if(M(d))try{d()}catch(w){i=w instanceof on?w.errors:[w]}var l=this._finalizers;if(l){this._finalizers=null;try{for(var f=pt(l),p=f.next();!p.done;p=f.next()){var y=p.value;try{Vr(y)}catch(w){i=i??[],w instanceof on?i=Pn(Pn([],Mn(i)),Mn(w.errors)):i.push(w)}}}catch(w){r={error:w}}finally{try{p&&!p.done&&(s=f.return)&&s.call(f)}finally{if(r)throw r.error}}}if(i)throw new on(i)}},t.prototype.add=function(e){var n;if(e&&e!==this)if(this.closed)Vr(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(e)}},t.prototype._hasParent=function(e){var n=this._parentage;return n===e||Array.isArray(n)&&n.includes(e)},t.prototype._addParent=function(e){var n=this._parentage;this._parentage=Array.isArray(n)?(n.push(e),n):n?[n,e]:e},t.prototype._removeParent=function(e){var n=this._parentage;n===e?this._parentage=null:Array.isArray(n)&&qr(n,e)},t.prototype.remove=function(e){var n=this._finalizers;n&&qr(n,e),e instanceof t&&e._removeParent(this)},t.EMPTY=function(){var e=new t;return e.closed=!0,e}(),t}();Xn.EMPTY;function Hs(t){return t instanceof Xn||t&&"closed"in t&&M(t.remove)&&M(t.add)&&M(t.unsubscribe)}function Vr(t){M(t)?t():t.unsubscribe()}var qs={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},Vs={setTimeout:function(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,Pn([t,e],Mn(n)))},clearTimeout:function(t){var e=Vs.delegate;return((e==null?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function Ws(t){Vs.setTimeout(function(){throw t})}function qt(){}function Yd(t){t()}var Qn=function(t){Kn(e,t);function e(n){var r=t.call(this)||this;return r.isStopped=!1,n?(r.destination=n,Hs(n)&&n.add(r)):r.destination=Qd,r}return e.create=function(n,r,s){return new Dn(n,r,s)},e.prototype.next=function(n){this.isStopped||this._next(n)},e.prototype.error=function(n){this.isStopped||(this.isStopped=!0,this._error(n))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(n){this.destination.next(n)},e.prototype._error=function(n){try{this.destination.error(n)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(Xn),Jd=Function.prototype.bind;function an(t,e){return Jd.call(t,e)}var Kd=function(){function t(e){this.partialObserver=e}return t.prototype.next=function(e){var n=this.partialObserver;if(n.next)try{n.next(e)}catch(r){Rt(r)}},t.prototype.error=function(e){var n=this.partialObserver;if(n.error)try{n.error(e)}catch(r){Rt(r)}else Rt(e)},t.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(n){Rt(n)}},t}(),Dn=function(t){Kn(e,t);function e(n,r,s){var i=t.call(this)||this,o;if(M(n)||!n)o={next:n??void 0,error:r??void 0,complete:s??void 0};else{var a;i&&qs.useDeprecatedNextContext?(a=Object.create(n),a.unsubscribe=function(){return i.unsubscribe()},o={next:n.next&&an(n.next,a),error:n.error&&an(n.error,a),complete:n.complete&&an(n.complete,a)}):o=n}return i.destination=new Kd(o),i}return e}(Qn);function Rt(t){Ws(t)}function Xd(t){throw t}var Qd={closed:!0,next:qt,error:Xd,complete:qt},er=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function el(t){return t}function tl(t){return t.length===0?el:t.length===1?t[0]:function(n){return t.reduce(function(r,s){return s(r)},n)}}var W=function(){function t(e){e&&(this._subscribe=e)}return t.prototype.lift=function(e){var n=new t;return n.source=this,n.operator=e,n},t.prototype.subscribe=function(e,n,r){var s=this,i=rl(e)?e:new Dn(e,n,r);return Yd(function(){var o=s,a=o.operator,c=o.source;i.add(a?a.call(i,c):c?s._subscribe(i):s._trySubscribe(i))}),i},t.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(n){e.error(n)}},t.prototype.forEach=function(e,n){var r=this;return n=Wr(n),new n(function(s,i){var o=new Dn({next:function(a){try{e(a)}catch(c){i(c),o.unsubscribe()}},error:i,complete:s});r.subscribe(o)})},t.prototype._subscribe=function(e){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(e)},t.prototype[er]=function(){return this},t.prototype.pipe=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return tl(e)(this)},t.prototype.toPromise=function(e){var n=this;return e=Wr(e),new e(function(r,s){var i;n.subscribe(function(o){return i=o},function(o){return s(o)},function(){return r(i)})})},t.create=function(e){return new t(e)},t}();function Wr(t){var e;return(e=t??qs.Promise)!==null&&e!==void 0?e:Promise}function nl(t){return t&&M(t.next)&&M(t.error)&&M(t.complete)}function rl(t){return t&&t instanceof Qn||nl(t)&&Hs(t)}function sl(t){return M(t==null?void 0:t.lift)}function ce(t){return function(e){if(sl(e))return e.lift(function(n){try{return t(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function _e(t,e,n,r,s){return new il(t,e,n,r,s)}var il=function(t){Kn(e,t);function e(n,r,s,i,o,a){var c=t.call(this,n)||this;return c.onFinalize=o,c.shouldUnsubscribe=a,c._next=r?function(u){try{r(u)}catch(d){n.error(d)}}:t.prototype._next,c._error=i?function(u){try{i(u)}catch(d){n.error(d)}finally{this.unsubscribe()}}:t.prototype._error,c._complete=s?function(){try{s()}catch(u){n.error(u)}finally{this.unsubscribe()}}:t.prototype._complete,c}return e.prototype.unsubscribe=function(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;t.prototype.unsubscribe.call(this),!r&&((n=this.onFinalize)===null||n===void 0||n.call(this))}},e}(Qn);function ol(t){return t&&M(t.schedule)}function al(t){return t[t.length-1]}function cl(t){return ol(al(t))?t.pop():void 0}var Gs=function(t){return t&&typeof t.length=="number"&&typeof t!="function"};function zs(t){return M(t==null?void 0:t.then)}function Ys(t){return M(t[er])}function Js(t){return Symbol.asyncIterator&&M(t==null?void 0:t[Symbol.asyncIterator])}function Ks(t){return new TypeError("You provided "+(t!==null&&typeof t=="object"?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function ul(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Xs=ul();function Qs(t){return M(t==null?void 0:t[Xs])}function ei(t){return Wd(this,arguments,function(){var n,r,s,i;return Bs(this,function(o){switch(o.label){case 0:n=t.getReader(),o.label=1;case 1:o.trys.push([1,,9,10]),o.label=2;case 2:return[4,Fe(n.read())];case 3:return r=o.sent(),s=r.value,i=r.done,i?[4,Fe(void 0)]:[3,5];case 4:return[2,o.sent()];case 5:return[4,Fe(s)];case 6:return[4,o.sent()];case 7:return o.sent(),[3,2];case 8:return[3,10];case 9:return n.releaseLock(),[7];case 10:return[2]}})})}function ti(t){return M(t==null?void 0:t.getReader)}function Me(t){if(t instanceof W)return t;if(t!=null){if(Ys(t))return dl(t);if(Gs(t))return ll(t);if(zs(t))return fl(t);if(Js(t))return ni(t);if(Qs(t))return pl(t);if(ti(t))return hl(t)}throw Ks(t)}function dl(t){return new W(function(e){var n=t[er]();if(M(n.subscribe))return n.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function ll(t){return new W(function(e){for(var n=0;n<t.length&&!e.closed;n++)e.next(t[n]);e.complete()})}function fl(t){return new W(function(e){t.then(function(n){e.closed||(e.next(n),e.complete())},function(n){return e.error(n)}).then(null,Ws)})}function pl(t){return new W(function(e){var n,r;try{for(var s=pt(t),i=s.next();!i.done;i=s.next()){var o=i.value;if(e.next(o),e.closed)return}}catch(a){n={error:a}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}e.complete()})}function ni(t){return new W(function(e){ml(t,e).catch(function(n){return e.error(n)})})}function hl(t){return ni(ei(t))}function ml(t,e){var n,r,s,i;return Vd(this,void 0,void 0,function(){var o,a;return Bs(this,function(c){switch(c.label){case 0:c.trys.push([0,5,6,11]),n=Gd(t),c.label=1;case 1:return[4,n.next()];case 2:if(r=c.sent(),!!r.done)return[3,4];if(o=r.value,e.next(o),e.closed)return[2];c.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return a=c.sent(),s={error:a},[3,11];case 6:return c.trys.push([6,,9,10]),r&&!r.done&&(i=n.return)?[4,i.call(n)]:[3,8];case 7:c.sent(),c.label=8;case 8:return[3,10];case 9:if(s)throw s.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})})}function Se(t,e,n,r,s){r===void 0&&(r=0),s===void 0&&(s=!1);var i=e.schedule(function(){n(),s?t.add(this.schedule(null,r)):this.unsubscribe()},r);if(t.add(i),!s)return i}function ri(t,e){return e===void 0&&(e=0),ce(function(n,r){n.subscribe(_e(r,function(s){return Se(r,t,function(){return r.next(s)},e)},function(){return Se(r,t,function(){return r.complete()},e)},function(s){return Se(r,t,function(){return r.error(s)},e)}))})}function si(t,e){return e===void 0&&(e=0),ce(function(n,r){r.add(t.schedule(function(){return n.subscribe(r)},e))})}function gl(t,e){return Me(t).pipe(si(e),ri(e))}function yl(t,e){return Me(t).pipe(si(e),ri(e))}function _l(t,e){return new W(function(n){var r=0;return e.schedule(function(){r===t.length?n.complete():(n.next(t[r++]),n.closed||this.schedule())})})}function vl(t,e){return new W(function(n){var r;return Se(n,e,function(){r=t[Xs](),Se(n,e,function(){var s,i,o;try{s=r.next(),i=s.value,o=s.done}catch(a){n.error(a);return}o?n.complete():n.next(i)},0,!0)}),function(){return M(r==null?void 0:r.return)&&r.return()}})}function ii(t,e){if(!t)throw new Error("Iterable cannot be null");return new W(function(n){Se(n,e,function(){var r=t[Symbol.asyncIterator]();Se(n,e,function(){r.next().then(function(s){s.done?n.complete():n.next(s.value)})},0,!0)})})}function bl(t,e){return ii(ei(t),e)}function El(t,e){if(t!=null){if(Ys(t))return gl(t,e);if(Gs(t))return _l(t,e);if(zs(t))return yl(t,e);if(Js(t))return ii(t,e);if(Qs(t))return vl(t,e);if(ti(t))return bl(t,e)}throw Ks(t)}function oi(t,e){return e?El(t,e):Me(t)}function cn(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=cl(t);return oi(t,n)}function ai(t,e){return ce(function(n,r){var s=0;n.subscribe(_e(r,function(i){r.next(t.call(e,i,s++))}))})}function Sl(t,e,n,r,s,i,o,a){var c=[],u=0,d=0,l=!1,f=function(){l&&!c.length&&!u&&e.complete()},p=function(w){return u<r?y(w):c.push(w)},y=function(w){u++;var I=!1;Me(n(w,d++)).subscribe(_e(e,function(P){e.next(P)},function(){I=!0},void 0,function(){if(I)try{u--;for(var P=function(){var H=c.shift();o||y(H)};c.length&&u<r;)P();f()}catch(H){e.error(H)}}))};return t.subscribe(_e(e,p,function(){l=!0,f()})),function(){}}function ci(t,e,n){return n===void 0&&(n=1/0),M(e)?ci(function(r,s){return ai(function(i,o){return e(r,i,s,o)})(Me(t(r,s)))},n):(typeof e=="number"&&(n=e),ce(function(r,s){return Sl(r,s,t,n)}))}var wl=new W(qt);function Gr(t,e){return ce(function(n,r){var s=0;n.subscribe(_e(r,function(i){return t.call(e,i,s++)&&r.next(i)}))})}function ui(t){return ce(function(e,n){var r=null,s=!1,i;r=e.subscribe(_e(n,void 0,void 0,function(o){i=Me(t(o,ui(t)(e))),r?(r.unsubscribe(),r=null,i.subscribe(n)):s=!0})),s&&(r.unsubscribe(),r=null,i.subscribe(n))})}function xl(t,e,n,r,s){return function(i,o){var a=n,c=e,u=0;i.subscribe(_e(o,function(d){var l=u++;c=a?t(c,d,l):(a=!0,d)},function(){a&&o.next(c),o.complete()}))}}function kl(t,e){return ce(xl(t,e,arguments.length>=2,!1,!0))}var Tl=function(t,e){return t.push(e),t};function Il(){return ce(function(t,e){kl(Tl,[])(t).subscribe(e)})}function di(t){return ce(function(e,n){Me(t).subscribe(_e(n,function(){return n.complete()},qt)),!n.closed&&e.subscribe(n)})}function Cl(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var $n={exports:{}},un,zr;function Rl(){if(zr)return un;zr=1;var t=1e3,e=t*60,n=e*60,r=n*24,s=r*7,i=r*365.25;un=function(d,l){l=l||{};var f=typeof d;if(f==="string"&&d.length>0)return o(d);if(f==="number"&&isFinite(d))return l.long?c(d):a(d);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(d))};function o(d){if(d=String(d),!(d.length>100)){var l=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(d);if(l){var f=parseFloat(l[1]),p=(l[2]||"ms").toLowerCase();switch(p){case"years":case"year":case"yrs":case"yr":case"y":return f*i;case"weeks":case"week":case"w":return f*s;case"days":case"day":case"d":return f*r;case"hours":case"hour":case"hrs":case"hr":case"h":return f*n;case"minutes":case"minute":case"mins":case"min":case"m":return f*e;case"seconds":case"second":case"secs":case"sec":case"s":return f*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return f;default:return}}}}function a(d){var l=Math.abs(d);return l>=r?Math.round(d/r)+"d":l>=n?Math.round(d/n)+"h":l>=e?Math.round(d/e)+"m":l>=t?Math.round(d/t)+"s":d+"ms"}function c(d){var l=Math.abs(d);return l>=r?u(d,l,r,"day"):l>=n?u(d,l,n,"hour"):l>=e?u(d,l,e,"minute"):l>=t?u(d,l,t,"second"):d+" ms"}function u(d,l,f,p){var y=l>=f*1.5;return Math.round(d/f)+" "+p+(y?"s":"")}return un}function Ol(t){n.debug=n,n.default=n,n.coerce=c,n.disable=o,n.enable=s,n.enabled=a,n.humanize=Rl(),n.destroy=u,Object.keys(t).forEach(d=>{n[d]=t[d]}),n.names=[],n.skips=[],n.formatters={};function e(d){let l=0;for(let f=0;f<d.length;f++)l=(l<<5)-l+d.charCodeAt(f),l|=0;return n.colors[Math.abs(l)%n.colors.length]}n.selectColor=e;function n(d){let l,f=null,p,y;function w(...I){if(!w.enabled)return;const P=w,H=Number(new Date),bt=H-(l||H);P.diff=bt,P.prev=l,P.curr=H,l=H,I[0]=n.coerce(I[0]),typeof I[0]!="string"&&I.unshift("%O");let Et=0;I[0]=I[0].replace(/%([a-zA-Z%])/g,(en,pi)=>{if(en==="%%")return"%";Et++;const tr=n.formatters[pi];if(typeof tr=="function"){const hi=I[Et];en=tr.call(P,hi),I.splice(Et,1),Et--}return en}),n.formatArgs.call(P,I),(P.log||n.log).apply(P,I)}return w.namespace=d,w.useColors=n.useColors(),w.color=n.selectColor(d),w.extend=r,w.destroy=n.destroy,Object.defineProperty(w,"enabled",{enumerable:!0,configurable:!1,get:()=>f!==null?f:(p!==n.namespaces&&(p=n.namespaces,y=n.enabled(d)),y),set:I=>{f=I}}),typeof n.init=="function"&&n.init(w),w}function r(d,l){const f=n(this.namespace+(typeof l>"u"?":":l)+d);return f.log=this.log,f}function s(d){n.save(d),n.namespaces=d,n.names=[],n.skips=[];const l=(typeof d=="string"?d:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const f of l)f[0]==="-"?n.skips.push(f.slice(1)):n.names.push(f)}function i(d,l){let f=0,p=0,y=-1,w=0;for(;f<d.length;)if(p<l.length&&(l[p]===d[f]||l[p]==="*"))l[p]==="*"?(y=p,w=f,p++):(f++,p++);else if(y!==-1)p=y+1,w++,f=w;else return!1;for(;p<l.length&&l[p]==="*";)p++;return p===l.length}function o(){const d=[...n.names,...n.skips.map(l=>"-"+l)].join(",");return n.enable(""),d}function a(d){for(const l of n.skips)if(i(d,l))return!1;for(const l of n.names)if(i(d,l))return!0;return!1}function c(d){return d instanceof Error?d.stack||d.message:d}function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}var Nl=Ol;(function(t,e){var n={};e.formatArgs=s,e.save=i,e.load=o,e.useColors=r,e.storage=a(),e.destroy=(()=>{let u=!1;return()=>{u||(u=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function r(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let u;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(u=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(u[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function s(u){if(u[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+u[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const d="color: "+this.color;u.splice(1,0,d,"color: inherit");let l=0,f=0;u[0].replace(/%[a-zA-Z%]/g,p=>{p!=="%%"&&(l++,p==="%c"&&(f=l))}),u.splice(f,0,d)}e.log=console.debug||console.log||(()=>{});function i(u){try{u?e.storage.setItem("debug",u):e.storage.removeItem("debug")}catch{}}function o(){let u;try{u=e.storage.getItem("debug")||e.storage.getItem("DEBUG")}catch{}return!u&&typeof process<"u"&&"env"in process&&(u=n.DEBUG),u}function a(){try{return localStorage}catch{}}t.exports=Nl(e);const{formatters:c}=t.exports;c.j=function(u){try{return JSON.stringify(u)}catch(d){return"[UnexpectedJSONParseError]: "+d.message}}})($n,$n.exports);var Al=$n.exports;const li=Cl(Al);process.platform;process.platform;process.platform;process.type;process.type;const Yr=li("claude:ipc");var ve=(t=>(t.MainToRenderer="rpc-async-api",t.RendererToMain="reverse-rpc-async-api",t.MetadataService="metadata-service",t))(ve||{});class Ml{constructor(e,n,r){this.outgoing=e,this.incoming=n,this.onDestroy=r}next(e){this.outgoing.next(e)}error(e){this.outgoing.error(e)}complete(){this.outgoing.complete()}subscribe(e){return this.incoming.pipe(di(this.onDestroy)).subscribe(e)}}function Pl(t){return new W(e=>t.subscribe(e))}function Dl(t){return t==null?!1:typeof t=="object"&&"then"in t}function $l(t){return t!==null&&(typeof t=="object"||typeof t=="function")&&typeof t.subscribe=="function"}let dn={};function jl(t,e,n,r){var o,a;const s=Ll(t,e,n,r),i=fi(e);return(a=(o=dn[t])==null?void 0:o.subscription)==null||a.unsubscribe(),dn[t]={subscription:s,metadata:i},s.add(()=>delete dn[t]),s}function Ll(t,e,n,r){return Pl(n).pipe(Gr(s=>s.methodChain.split(".")[0]===t),Gr(s=>r(s)?!0:(console.error(`Invalid message received: ${JSON.stringify(s)}`),!1)),ci(s=>{const i=s.methodChain.split(".").splice(1),o=i.pop(),a=i.reduce((l,f)=>l[f],e),c=s.customSendMethod??n.next.bind(n);let u;try{const l=a[o];Yr('Calling method "%s" with args %o',o,s.argList),u=l.call(a,...s.argList)}catch(l){return Yr(`Error in API call for message: %o
%o`,s,l),cn({sendMethod:c,result:{error:l,callId:s.callId}})}let d=cn(u);return Dl(u)?d=oi(u):$l(u)&&(d=u.pipe(Il())),d.pipe(ai(l=>({sendMethod:c,result:{result:l,callId:s.callId}})),ui(l=>cn({sendMethod:c,result:{result:null,callId:s.callId,error:l}})))}),di(n.onDestroy)).subscribe({next:s=>s.sendMethod(s.result),error:s=>{console.error(`Error in API Handler - this should not happen! ${s}
${s.stack}`)}})}function fi(t){return Fl(t).reduce((e,n)=>(typeof t[n]=="function"&&(e[n]=!0),typeof t[n]=="object"&&t[n]!==null&&(e[n]=fi(t[n])),e),{})}function Fl(t){const e=Object.keys(t),n=Object.getOwnPropertyNames(t).filter(o=>!e.includes(o)),r=[];let s=Object.getPrototypeOf(t);for(;s&&s!==Object.prototype;)Object.getOwnPropertyNames(s).filter(o=>!["constructor"].includes(o)).forEach(o=>{!r.includes(o)&&!e.includes(o)&&!n.includes(o)&&r.push(o)}),s=Object.getPrototypeOf(s);const i=Object.getOwnPropertySymbols(t).map(o=>o.toString());return[...e,...n,...r,...i]}li("claude:ipc");const Vt={rpcAsyncRecv:t=>{const e=(n,...r)=>t(r);return k.ipcRenderer.on(ve.MainToRenderer,e),()=>k.ipcRenderer.off(ve.MainToRenderer,e)},rpcAsyncSend:(...t)=>k.ipcRenderer.send(ve.MainToRenderer,t),reverseRpcAsyncRecv:t=>{const e=(n,...r)=>t(r);return k.ipcRenderer.on(ve.RendererToMain,e),()=>k.ipcRenderer.off(ve.RendererToMain,e)},reverseRpcAsyncSend:(...t)=>k.ipcRenderer.send(ve.RendererToMain,t)};function Ul(){window.rpcInternal=Vt,k.contextBridge.exposeInMainWorld("rpcInternal",Vt),k.contextBridge.exposeInMainWorld("registerDesktopApi",(t,e)=>{const n=jl(t,e,Zl(),()=>!0);return()=>n.unsubscribe()})}function Zl(){return new Ml({next:t=>Vt.reverseRpcAsyncSend(t),error:t=>{throw new Error(t)},complete:()=>{}},new W(t=>Vt.reverseRpcAsyncRecv(n=>{Array.isArray(n)&&t.next(n[0])})),wl)}const Bl="0.10.14",Hl=Object.fromEntries(["arch","platform","type"].map(t=>[t,!0])),Qt=Object.fromEntries(Object.entries(process).filter(([t])=>Hl[t])),ql=process.type==="browser"?/dist.electron/i.test(process.execPath):!process.argv.includes("--claude-dev");Qt.isAppPackaged=ql?"true":"false";Qt.version=Bl;const Vl=process.type==="browser"?/internal/i.test(process.execPath):process.argv.includes("--claude-internal");Qt.isInternalBuild=Vl.toString();Du();const Wl={titleBarApi:qd,settingsWindowApi:Hd,debugApi:ju};Ul();const{messages:Gl,locale:zl}=k.ipcRenderer.sendSync(Os.IntlSync);k.contextBridge.exposeInMainWorld("mainProcess",Wl);k.contextBridge.exposeInMainWorld("process",Qt);k.contextBridge.exposeInMainWorld("initialMessages",Gl);k.contextBridge.exposeInMainWorld("initialLocale",zl);
//# sourceMappingURL=mainWindow.js.map
