"use strict";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="a9f24dc8-3212-4ef6-a3f5-f61825775e81",e._sentryDebugIdIdentifier="sentry-dbid-a9f24dc8-3212-4ef6-a3f5-f61825775e81")}catch{}})();const m=require("electron");var Ue=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};Ue.SENTRY_RELEASE={id:"27cc6f763724a1af75b35c386a6b8d014eedc334"};var de=(e=>(e.cmdK="cmdK",e.googleAuthCode="googleAuthCode",e))(de||{});const ie=e=>{if(!Object.values(de).includes(e))throw new Error(`unsupported Claude App binding: "${e}"`)},$e=process.platform==="darwin";process.platform;process.platform;process.type;process.type;var E=(e=>(e.RequestSetGlobalShortcut="request-set-global-shortcut",e.RequestSetMcpConfig="request-set-mcp-config",e.GetMcpConfig="get-mcp-config",e.McpConfigChanged="mcp-config-changed",e.McpStatusChanged="mcp-status-changed",e.ListMcpServers="list-mcp-servers",e.ConnectToMcpServer="connect-to-mcp-server",e.RequestMainMenuPopup="request-main-menu",e.McpServerConnected="mcp-server-connected",e.RequestOpenMcpSettings="request-open-mcp-settings",e.RequestOpenMcpInstaller="request-open-mcp-installer",e.RequestOpenWebSettings="request-open-web-settings",e.RequestOpenFeedback="request-open-feedback",e.RevealMcpServerSettings="reveal-mcp-server-settings",e.RevealMcpConfig="reveal-mcp-config",e.RevealMcpLogs="reveal-mcp-logs",e.TitleBarReady="title-bar-ready",e.ShowLoadErrorState="show-load-error-state",e.HideLoadErrorState="hide-load-error-state",e.RequestReloadWebview="request-reload-webview",e.GetBuildProps="get-build-props",e))(E||{}),B=(e=>(e.QuickWindow="QuickWindow",e.Find="Find",e.StartupSettings="StartupSettings",e.Filesystem="Filesystem",e.Intl="Intl",e.IntlSync="IntlSync",e.AboutWindow="AboutWindow",e.WindowControl="WindowControl",e))(B||{}),J=function(e,t){return J=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])},J(e,t)};function M(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");J(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}function Ve(e,t,r,n){function o(i){return i instanceof r?i:new r(function(c){c(i)})}return new(r||(r=Promise))(function(i,c){function l(s){try{u(n.next(s))}catch(a){c(a)}}function f(s){try{u(n.throw(s))}catch(a){c(a)}}function u(s){s.done?i(s.value):o(s.value).then(l,f)}u((n=n.apply(e,t||[])).next())})}function pe(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,c=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return c.next=l(0),c.throw=l(1),c.return=l(2),typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function l(u){return function(s){return f([u,s])}}function f(u){if(n)throw new TypeError("Generator is already executing.");for(;c&&(c=0,u[0]&&(r=0)),r;)try{if(n=1,o&&(i=u[0]&2?o.return:u[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,u[1])).done)return i;switch(o=0,i&&(u=[u[0]&2,i.value]),u[0]){case 0:case 1:i=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,o=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!i||u[1]>i[0]&&u[1]<i[3])){r.label=u[1];break}if(u[0]===6&&r.label<i[1]){r.label=i[1],i=u;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(u);break}i[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(s){u=[6,s],o=0}finally{n=i=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function P(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function j(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],c;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(l){c={error:l}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(c)throw c.error}}return i}function q(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,i;n<o;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}function O(e){return this instanceof O?(this.v=e,this):new O(e)}function Ge(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),l("next"),l("throw"),l("return",c),o[Symbol.asyncIterator]=function(){return this},o;function c(p){return function(y){return Promise.resolve(y).then(p,a)}}function l(p,y){n[p]&&(o[p]=function(h){return new Promise(function(v,g){i.push([p,h,v,g])>1||f(p,h)})},y&&(o[p]=y(o[p])))}function f(p,y){try{u(n[p](y))}catch(h){d(i[0][3],h)}}function u(p){p.value instanceof O?Promise.resolve(p.value.v).then(s,a):d(i[0][2],p)}function s(p){f("next",p)}function a(p){f("throw",p)}function d(p,y){p(y),i.shift(),i.length&&f(i[0][0],i[0][1])}}function He(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof P=="function"?P(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(i){r[i]=e[i]&&function(c){return new Promise(function(l,f){c=e[i](c),o(l,f,c.done,c.value)})}}function o(i,c,l,f){Promise.resolve(f).then(function(u){i({value:u,done:l})},c)}}function b(e){return typeof e=="function"}function K(e){var t=function(n){Error.call(n),n.stack=new Error().stack},r=e(t);return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var G=K(function(e){return function(r){e(this),this.message=r?r.length+` errors occurred during unsubscription:
`+r.map(function(n,o){return o+1+") "+n.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=r}});function Q(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var W=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,r,n,o,i;if(!this.closed){this.closed=!0;var c=this._parentage;if(c)if(this._parentage=null,Array.isArray(c))try{for(var l=P(c),f=l.next();!f.done;f=l.next()){var u=f.value;u.remove(this)}}catch(h){t={error:h}}finally{try{f&&!f.done&&(r=l.return)&&r.call(l)}finally{if(t)throw t.error}}else c.remove(this);var s=this.initialTeardown;if(b(s))try{s()}catch(h){i=h instanceof G?h.errors:[h]}var a=this._finalizers;if(a){this._finalizers=null;try{for(var d=P(a),p=d.next();!p.done;p=d.next()){var y=p.value;try{ce(y)}catch(h){i=i??[],h instanceof G?i=q(q([],j(i)),j(h.errors)):i.push(h)}}}catch(h){n={error:h}}finally{try{p&&!p.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}}if(i)throw new G(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)ce(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(t)}},e.prototype._hasParent=function(t){var r=this._parentage;return r===t||Array.isArray(r)&&r.includes(t)},e.prototype._addParent=function(t){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(t),r):r?[r,t]:t},e.prototype._removeParent=function(t){var r=this._parentage;r===t?this._parentage=null:Array.isArray(r)&&Q(r,t)},e.prototype.remove=function(t){var r=this._finalizers;r&&Q(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}();W.EMPTY;function he(e){return e instanceof W||e&&"closed"in e&&b(e.remove)&&b(e.add)&&b(e.unsubscribe)}function ce(e){b(e)?e():e.unsubscribe()}var ye={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},ve={setTimeout:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setTimeout.apply(void 0,q([e,t],j(r)))},clearTimeout:function(e){var t=ve.delegate;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function be(e){ve.setTimeout(function(){throw e})}function L(){}function Ye(e){e()}var N=function(e){M(t,e);function t(r){var n=e.call(this)||this;return n.isStopped=!1,r?(n.destination=r,he(r)&&r.add(n)):n.destination=Ze,n}return t.create=function(r,n,o){return new D(r,n,o)},t.prototype.next=function(r){this.isStopped||this._next(r)},t.prototype.error=function(r){this.isStopped||(this.isStopped=!0,this._error(r))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(r){this.destination.next(r)},t.prototype._error=function(r){try{this.destination.error(r)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(W),ze=Function.prototype.bind;function H(e,t){return ze.call(e,t)}var Je=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var r=this.partialObserver;if(r.next)try{r.next(t)}catch(n){_(n)}},e.prototype.error=function(t){var r=this.partialObserver;if(r.error)try{r.error(t)}catch(n){_(n)}else _(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(r){_(r)}},e}(),D=function(e){M(t,e);function t(r,n,o){var i=e.call(this)||this,c;if(b(r)||!r)c={next:r??void 0,error:n??void 0,complete:o??void 0};else{var l;i&&ye.useDeprecatedNextContext?(l=Object.create(r),l.unsubscribe=function(){return i.unsubscribe()},c={next:r.next&&H(r.next,l),error:r.error&&H(r.error,l),complete:r.complete&&H(r.complete,l)}):c=r}return i.destination=new Je(c),i}return t}(N);function _(e){be(e)}function Qe(e){throw e}var Ze={closed:!0,next:L,error:Qe,complete:L},ee=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function Xe(e){return e}function Ke(e){return e.length===0?Xe:e.length===1?e[0]:function(r){return e.reduce(function(n,o){return o(n)},r)}}var w=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(t,r,n){var o=this,i=er(t)?t:new D(t,r,n);return Ye(function(){var c=o,l=c.operator,f=c.source;i.add(l?l.call(i,f):f?o._subscribe(i):o._trySubscribe(i))}),i},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(r){t.error(r)}},e.prototype.forEach=function(t,r){var n=this;return r=se(r),new r(function(o,i){var c=new D({next:function(l){try{t(l)}catch(f){i(f),c.unsubscribe()}},error:i,complete:o});n.subscribe(c)})},e.prototype._subscribe=function(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)},e.prototype[ee]=function(){return this},e.prototype.pipe=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Ke(t)(this)},e.prototype.toPromise=function(t){var r=this;return t=se(t),new t(function(n,o){var i;r.subscribe(function(c){return i=c},function(c){return o(c)},function(){return n(i)})})},e.create=function(t){return new e(t)},e}();function se(e){var t;return(t=e??ye.Promise)!==null&&t!==void 0?t:Promise}function Ne(e){return e&&b(e.next)&&b(e.error)&&b(e.complete)}function er(e){return e&&e instanceof N||Ne(e)&&he(e)}function rr(e){return b(e==null?void 0:e.lift)}function S(e){return function(t){if(rr(t))return t.lift(function(r){try{return e(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function I(e,t,r,n,o){return new tr(e,t,r,n,o)}var tr=function(e){M(t,e);function t(r,n,o,i,c,l){var f=e.call(this,r)||this;return f.onFinalize=c,f.shouldUnsubscribe=l,f._next=n?function(u){try{n(u)}catch(s){r.error(s)}}:e.prototype._next,f._error=i?function(u){try{i(u)}catch(s){r.error(s)}finally{this.unsubscribe()}}:e.prototype._error,f._complete=o?function(){try{o()}catch(u){r.error(u)}finally{this.unsubscribe()}}:e.prototype._complete,f}return t.prototype.unsubscribe=function(){var r;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;e.prototype.unsubscribe.call(this),!n&&((r=this.onFinalize)===null||r===void 0||r.call(this))}},t}(N),nr={now:function(){return Date.now()},delegate:void 0},or=function(e){M(t,e);function t(r,n){return e.call(this)||this}return t.prototype.schedule=function(r,n){return this},t}(W),ue={setInterval:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setInterval.apply(void 0,q([e,t],j(r)))},clearInterval:function(e){return clearInterval(e)},delegate:void 0},ir=function(e){M(t,e);function t(r,n){var o=e.call(this,r,n)||this;return o.scheduler=r,o.work=n,o.pending=!1,o}return t.prototype.schedule=function(r,n){var o;if(n===void 0&&(n=0),this.closed)return this;this.state=r;var i=this.id,c=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(c,i,n)),this.pending=!0,this.delay=n,this.id=(o=this.id)!==null&&o!==void 0?o:this.requestAsyncId(c,this.id,n),this},t.prototype.requestAsyncId=function(r,n,o){return o===void 0&&(o=0),ue.setInterval(r.flush.bind(r,this),o)},t.prototype.recycleAsyncId=function(r,n,o){if(o===void 0&&(o=0),o!=null&&this.delay===o&&this.pending===!1)return n;n!=null&&ue.clearInterval(n)},t.prototype.execute=function(r,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var o=this._execute(r,n);if(o)return o;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(r,n){var o=!1,i;try{this.work(r)}catch(c){o=!0,i=c||new Error("Scheduled action threw falsy error")}if(o)return this.unsubscribe(),i},t.prototype.unsubscribe=function(){if(!this.closed){var r=this,n=r.id,o=r.scheduler,i=o.actions;this.work=this.state=this.scheduler=null,this.pending=!1,Q(i,this),n!=null&&(this.id=this.recycleAsyncId(o,n,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(or),ae=function(){function e(t,r){r===void 0&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(t,r,n){return r===void 0&&(r=0),new this.schedulerActionCtor(this,t).schedule(n,r)},e.now=nr.now,e}(),cr=function(e){M(t,e);function t(r,n){n===void 0&&(n=ae.now);var o=e.call(this,r,n)||this;return o.actions=[],o._active=!1,o}return t.prototype.flush=function(r){var n=this.actions;if(this._active){n.push(r);return}var o;this._active=!0;do if(o=r.execute(r.state,r.delay))break;while(r=n.shift());if(this._active=!1,o){for(;r=n.shift();)r.unsubscribe();throw o}},t}(ae),sr=new cr(ir);function ur(e){return e&&b(e.schedule)}function ar(e){return e[e.length-1]}function lr(e){return ur(ar(e))?e.pop():void 0}var me=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function ge(e){return b(e==null?void 0:e.then)}function we(e){return b(e[ee])}function Ce(e){return Symbol.asyncIterator&&b(e==null?void 0:e[Symbol.asyncIterator])}function Se(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function fr(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Fe=fr();function Ie(e){return b(e==null?void 0:e[Fe])}function xe(e){return Ge(this,arguments,function(){var r,n,o,i;return pe(this,function(c){switch(c.label){case 0:r=e.getReader(),c.label=1;case 1:c.trys.push([1,,9,10]),c.label=2;case 2:return[4,O(r.read())];case 3:return n=c.sent(),o=n.value,i=n.done,i?[4,O(void 0)]:[3,5];case 4:return[2,c.sent()];case 5:return[4,O(o)];case 6:return[4,c.sent()];case 7:return c.sent(),[3,2];case 8:return[3,10];case 9:return r.releaseLock(),[7];case 10:return[2]}})})}function Ae(e){return b(e==null?void 0:e.getReader)}function A(e){if(e instanceof w)return e;if(e!=null){if(we(e))return dr(e);if(me(e))return pr(e);if(ge(e))return hr(e);if(Ce(e))return Ee(e);if(Ie(e))return yr(e);if(Ae(e))return vr(e)}throw Se(e)}function dr(e){return new w(function(t){var r=e[ee]();if(b(r.subscribe))return r.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function pr(e){return new w(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function hr(e){return new w(function(t){e.then(function(r){t.closed||(t.next(r),t.complete())},function(r){return t.error(r)}).then(null,be)})}function yr(e){return new w(function(t){var r,n;try{for(var o=P(e),i=o.next();!i.done;i=o.next()){var c=i.value;if(t.next(c),t.closed)return}}catch(l){r={error:l}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}t.complete()})}function Ee(e){return new w(function(t){br(e,t).catch(function(r){return t.error(r)})})}function vr(e){return Ee(xe(e))}function br(e,t){var r,n,o,i;return Ve(this,void 0,void 0,function(){var c,l;return pe(this,function(f){switch(f.label){case 0:f.trys.push([0,5,6,11]),r=He(e),f.label=1;case 1:return[4,r.next()];case 2:if(n=f.sent(),!!n.done)return[3,4];if(c=n.value,t.next(c),t.closed)return[2];f.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return l=f.sent(),o={error:l},[3,11];case 6:return f.trys.push([6,,9,10]),n&&!n.done&&(i=r.return)?[4,i.call(r)]:[3,8];case 7:f.sent(),f.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function x(e,t,r,n,o){n===void 0&&(n=0),o===void 0&&(o=!1);var i=t.schedule(function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(i),!o)return i}function Oe(e,t){return t===void 0&&(t=0),S(function(r,n){r.subscribe(I(n,function(o){return x(n,e,function(){return n.next(o)},t)},function(){return x(n,e,function(){return n.complete()},t)},function(o){return x(n,e,function(){return n.error(o)},t)}))})}function Re(e,t){return t===void 0&&(t=0),S(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}function mr(e,t){return A(e).pipe(Re(t),Oe(t))}function gr(e,t){return A(e).pipe(Re(t),Oe(t))}function wr(e,t){return new w(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})})}function Cr(e,t){return new w(function(r){var n;return x(r,t,function(){n=e[Fe](),x(r,t,function(){var o,i,c;try{o=n.next(),i=o.value,c=o.done}catch(l){r.error(l);return}c?r.complete():r.next(i)},0,!0)}),function(){return b(n==null?void 0:n.return)&&n.return()}})}function Me(e,t){if(!e)throw new Error("Iterable cannot be null");return new w(function(r){x(r,t,function(){var n=e[Symbol.asyncIterator]();x(r,t,function(){n.next().then(function(o){o.done?r.complete():r.next(o.value)})},0,!0)})})}function Sr(e,t){return Me(xe(e),t)}function Fr(e,t){if(e!=null){if(we(e))return mr(e,t);if(me(e))return wr(e,t);if(ge(e))return gr(e,t);if(Ce(e))return Me(e,t);if(Ie(e))return Cr(e,t);if(Ae(e))return Sr(e,t)}throw Se(e)}function Pe(e,t){return t?Fr(e,t):A(e)}function k(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=lr(e);return Pe(e,r)}function Ir(e,t){var r=b(e)?e:function(){return e},n=function(o){return o.error(r())};return new w(n)}var xr=K(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function Ar(e,t){return new Promise(function(r,n){var o=new D({next:function(i){r(i),o.unsubscribe()},error:n,complete:function(){n(new xr)}});e.subscribe(o)})}function Er(e){return e instanceof Date&&!isNaN(e)}var Or=K(function(e){return function(r){r===void 0&&(r=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=r}});function Rr(e,t){var r=Er(e)?{first:e}:typeof e=="number"?{each:e}:e,n=r.first,o=r.each,i=r.with,c=i===void 0?Mr:i,l=r.scheduler,f=l===void 0?sr:l,u=r.meta,s=u===void 0?null:u;if(n==null&&o==null)throw new TypeError("No timeout provided.");return S(function(a,d){var p,y,h=null,v=0,g=function(C){y=x(d,f,function(){try{p.unsubscribe(),A(c({meta:s,lastValue:h,seen:v})).subscribe(d)}catch($){d.error($)}},C)};p=a.subscribe(I(d,function(C){y==null||y.unsubscribe(),v++,d.next(h=C),o>0&&g(o)},void 0,void 0,function(){y!=null&&y.closed||y==null||y.unsubscribe(),h=null})),!v&&g(n!=null?typeof n=="number"?n:+n-f.now():o)})}function Mr(e){throw new Or(e)}function Te(e,t){return S(function(r,n){var o=0;r.subscribe(I(n,function(i){n.next(e.call(t,i,o++))}))})}function Pr(e,t,r,n,o,i,c,l){var f=[],u=0,s=0,a=!1,d=function(){a&&!f.length&&!u&&t.complete()},p=function(h){return u<n?y(h):f.push(h)},y=function(h){u++;var v=!1;A(r(h,s++)).subscribe(I(t,function(g){t.next(g)},function(){v=!0},void 0,function(){if(v)try{u--;for(var g=function(){var C=f.shift();c||y(C)};f.length&&u<n;)g();d()}catch(C){t.error(C)}}))};return e.subscribe(I(t,p,function(){a=!0,d()})),function(){}}function re(e,t,r){return r===void 0&&(r=1/0),b(t)?re(function(n,o){return Te(function(i,c){return t(n,i,o,c)})(A(e(n,o)))},r):(typeof t=="number"&&(r=t),S(function(n,o){return Pr(n,o,e,r)}))}var _e=new w(L);function Z(e,t){return S(function(r,n){var o=0;r.subscribe(I(n,function(i){return e.call(t,i,o++)&&n.next(i)}))})}function ke(e){return S(function(t,r){var n=null,o=!1,i;n=t.subscribe(I(r,void 0,void 0,function(c){i=A(e(c,ke(e)(t))),n?(n.unsubscribe(),n=null,i.subscribe(r)):o=!0})),o&&(n.unsubscribe(),n=null,i.subscribe(r))})}function Tr(e,t,r,n,o){return function(i,c){var l=r,f=t,u=0;i.subscribe(I(c,function(s){var a=u++;f=l?e(f,s,a):(l=!0,s)},function(){l&&c.next(f),c.complete()}))}}function _r(e,t){return S(Tr(e,t,arguments.length>=2,!1,!0))}var kr=function(e,t){return e.push(t),e};function jr(){return S(function(e,t){_r(kr,[])(e).subscribe(t)})}function te(e){return S(function(t,r){A(e).subscribe(I(r,function(){return r.complete()},L)),!r.closed&&t.subscribe(r)})}function qr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var X={exports:{}},Y,le;function Lr(){if(le)return Y;le=1;var e=1e3,t=e*60,r=t*60,n=r*24,o=n*7,i=n*365.25;Y=function(s,a){a=a||{};var d=typeof s;if(d==="string"&&s.length>0)return c(s);if(d==="number"&&isFinite(s))return a.long?f(s):l(s);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(s))};function c(s){if(s=String(s),!(s.length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(s);if(a){var d=parseFloat(a[1]),p=(a[2]||"ms").toLowerCase();switch(p){case"years":case"year":case"yrs":case"yr":case"y":return d*i;case"weeks":case"week":case"w":return d*o;case"days":case"day":case"d":return d*n;case"hours":case"hour":case"hrs":case"hr":case"h":return d*r;case"minutes":case"minute":case"mins":case"min":case"m":return d*t;case"seconds":case"second":case"secs":case"sec":case"s":return d*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return d;default:return}}}}function l(s){var a=Math.abs(s);return a>=n?Math.round(s/n)+"d":a>=r?Math.round(s/r)+"h":a>=t?Math.round(s/t)+"m":a>=e?Math.round(s/e)+"s":s+"ms"}function f(s){var a=Math.abs(s);return a>=n?u(s,a,n,"day"):a>=r?u(s,a,r,"hour"):a>=t?u(s,a,t,"minute"):a>=e?u(s,a,e,"second"):s+" ms"}function u(s,a,d,p){var y=a>=d*1.5;return Math.round(s/d)+" "+p+(y?"s":"")}return Y}function Dr(e){r.debug=r,r.default=r,r.coerce=f,r.disable=c,r.enable=o,r.enabled=l,r.humanize=Lr(),r.destroy=u,Object.keys(e).forEach(s=>{r[s]=e[s]}),r.names=[],r.skips=[],r.formatters={};function t(s){let a=0;for(let d=0;d<s.length;d++)a=(a<<5)-a+s.charCodeAt(d),a|=0;return r.colors[Math.abs(a)%r.colors.length]}r.selectColor=t;function r(s){let a,d=null,p,y;function h(...v){if(!h.enabled)return;const g=h,C=Number(new Date),$=C-(a||C);g.diff=$,g.prev=a,g.curr=C,a=C,v[0]=r.coerce(v[0]),typeof v[0]!="string"&&v.unshift("%O");let T=0;v[0]=v[0].replace(/%([a-zA-Z%])/g,(V,Be)=>{if(V==="%%")return"%";T++;const oe=r.formatters[Be];if(typeof oe=="function"){const We=v[T];V=oe.call(g,We),v.splice(T,1),T--}return V}),r.formatArgs.call(g,v),(g.log||r.log).apply(g,v)}return h.namespace=s,h.useColors=r.useColors(),h.color=r.selectColor(s),h.extend=n,h.destroy=r.destroy,Object.defineProperty(h,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(p!==r.namespaces&&(p=r.namespaces,y=r.enabled(s)),y),set:v=>{d=v}}),typeof r.init=="function"&&r.init(h),h}function n(s,a){const d=r(this.namespace+(typeof a>"u"?":":a)+s);return d.log=this.log,d}function o(s){r.save(s),r.namespaces=s,r.names=[],r.skips=[];const a=(typeof s=="string"?s:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const d of a)d[0]==="-"?r.skips.push(d.slice(1)):r.names.push(d)}function i(s,a){let d=0,p=0,y=-1,h=0;for(;d<s.length;)if(p<a.length&&(a[p]===s[d]||a[p]==="*"))a[p]==="*"?(y=p,h=d,p++):(d++,p++);else if(y!==-1)p=y+1,h++,d=h;else return!1;for(;p<a.length&&a[p]==="*";)p++;return p===a.length}function c(){const s=[...r.names,...r.skips.map(a=>"-"+a)].join(",");return r.enable(""),s}function l(s){for(const a of r.skips)if(i(s,a))return!1;for(const a of r.names)if(i(s,a))return!0;return!1}function f(s){return s instanceof Error?s.stack||s.message:s}function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}var Br=Dr;(function(e,t){var r={};t.formatArgs=o,t.save=i,t.load=c,t.useColors=n,t.storage=l(),t.destroy=(()=>{let u=!1;return()=>{u||(u=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function n(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let u;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(u=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(u[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function o(u){if(u[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+u[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const s="color: "+this.color;u.splice(1,0,s,"color: inherit");let a=0,d=0;u[0].replace(/%[a-zA-Z%]/g,p=>{p!=="%%"&&(a++,p==="%c"&&(d=a))}),u.splice(d,0,s)}t.log=console.debug||console.log||(()=>{});function i(u){try{u?t.storage.setItem("debug",u):t.storage.removeItem("debug")}catch{}}function c(){let u;try{u=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch{}return!u&&typeof process<"u"&&"env"in process&&(u=r.DEBUG),u}function l(){try{return localStorage}catch{}}e.exports=Br(t);const{formatters:f}=e.exports;f.j=function(u){try{return JSON.stringify(u)}catch(s){return"[UnexpectedJSONParseError]: "+s.message}}})(X,X.exports);var Wr=X.exports;const je=qr(Wr),fe=je("claude:ipc");var F=(e=>(e.MainToRenderer="rpc-async-api",e.RendererToMain="reverse-rpc-async-api",e.MetadataService="metadata-service",e))(F||{});class Ur{constructor(t,r,n){this.outgoing=t,this.incoming=r,this.onDestroy=n}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(te(this.onDestroy)).subscribe(t)}}function $r(e){return new w(t=>e.subscribe(t))}function Vr(e){return e==null?!1:typeof e=="object"&&"then"in e}function Gr(e){return e!==null&&(typeof e=="object"||typeof e=="function")&&typeof e.subscribe=="function"}let z={};function Hr(e,t,r,n){var c,l;const o=Yr(e,t,r,n),i=qe(t);return(l=(c=z[e])==null?void 0:c.subscription)==null||l.unsubscribe(),z[e]={subscription:o,metadata:i},o.add(()=>delete z[e]),o}function Yr(e,t,r,n){return $r(r).pipe(Z(o=>o.methodChain.split(".")[0]===e),Z(o=>n(o)?!0:(console.error(`Invalid message received: ${JSON.stringify(o)}`),!1)),re(o=>{const i=o.methodChain.split(".").splice(1),c=i.pop(),l=i.reduce((a,d)=>a[d],t),f=o.customSendMethod??r.next.bind(r);let u;try{const a=l[c];fe('Calling method "%s" with args %o',c,o.argList),u=a.call(l,...o.argList)}catch(a){return fe(`Error in API call for message: %o
%o`,o,a),k({sendMethod:f,result:{error:a,callId:o.callId}})}let s=k(u);return Vr(u)?s=Pe(u):Gr(u)&&(s=u.pipe(jr())),s.pipe(Te(a=>({sendMethod:f,result:{result:a,callId:o.callId}})),ke(a=>k({sendMethod:f,result:{result:null,callId:o.callId,error:a}})))}),te(r.onDestroy)).subscribe({next:o=>o.sendMethod(o.result),error:o=>{console.error(`Error in API Handler - this should not happen! ${o}
${o.stack}`)}})}function qe(e){return zr(e).reduce((t,r)=>(typeof e[r]=="function"&&(t[r]=!0),typeof e[r]=="object"&&e[r]!==null&&(t[r]=qe(e[r])),t),{})}function zr(e){const t=Object.keys(e),r=Object.getOwnPropertyNames(e).filter(c=>!t.includes(c)),n=[];let o=Object.getPrototypeOf(e);for(;o&&o!==Object.prototype;)Object.getOwnPropertyNames(o).filter(c=>!["constructor"].includes(c)).forEach(c=>{!n.includes(c)&&!t.includes(c)&&!r.includes(c)&&n.push(c)}),o=Object.getPrototypeOf(o);const i=Object.getOwnPropertySymbols(e).map(c=>c.toString());return[...t,...r,...n,...i]}function Jr(){return""}function Qr(e){return new w(t=>e.subscribe(t))}const Le=5e3,Zr=je("claude:ipc");let Xr=0;function Kr(e,t,r,n=Le){const o=++Xr,i={sender:{},callId:`${o}`,methodChain:t,argList:r};Zr("sending message %o, stack: %s",i,Jr());let c=Ar(Qr(e).pipe(Z(l=>l.callId===`${o}`),re(l=>l.error?Ir(()=>l.error):k(l.result)),Rr(n)));return e.next(i),c}function Nr(e,t,r,n=Le){return De(r,t,e,n)}function De(e,t,r,n){const o={};return Object.keys(e).forEach(i=>{e[i]===!0&&(o[i]=(...c)=>Kr(t,`${r}.${i}`,c,n)),e[i]instanceof Object&&(o[i]=De(e[i],t,`${r}.${i}`,n))}),o}class et{constructor(t,r,n){this.outgoing=t,this.incoming=r,this.onDestroy=n}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(te(this.onDestroy)).subscribe(t)}}const R={rpcAsyncRecv:e=>{const t=(r,...n)=>e(n);return m.ipcRenderer.on(F.MainToRenderer,t),()=>m.ipcRenderer.off(F.MainToRenderer,t)},rpcAsyncSend:(...e)=>m.ipcRenderer.send(F.MainToRenderer,e),reverseRpcAsyncRecv:e=>{const t=(r,...n)=>e(n);return m.ipcRenderer.on(F.RendererToMain,t),()=>m.ipcRenderer.off(F.RendererToMain,t)},reverseRpcAsyncSend:(...e)=>m.ipcRenderer.send(F.RendererToMain,e)};function rt(){window.rpcInternal=R,m.contextBridge.exposeInMainWorld("rpcInternal",R),m.contextBridge.exposeInMainWorld("registerDesktopApi",(e,t)=>{const r=Hr(e,t,ot(),()=>!0);return()=>r.unsubscribe()})}function tt(e){const t={sender:{},callId:"dontcare",methodChain:`${F.MetadataService}.getMetadataForApi`,argList:[e]},r=m.ipcRenderer.sendSync(F.MainToRenderer,[t]);if(r.error)throw new Error(r.error.toString());return r.result}function nt(){return new et({next:e=>R.rpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new w(e=>R.rpcAsyncRecv(r=>{Array.isArray(r)&&e.next(r[0])})),_e)}function ot(){return new Ur({next:e=>R.reverseRpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new w(e=>R.reverseRpcAsyncRecv(r=>{Array.isArray(r)&&e.next(r[0])})),_e)}function ne(e,t,r){const n=tt(e);m.contextBridge.exposeInMainWorld(t??e,Nr(e,nt(),n,r))}const it="0.10.14",ct=Object.fromEntries(["arch","platform","type"].map(e=>[e,!0])),U=Object.fromEntries(Object.entries(process).filter(([e])=>ct[e])),st=process.type==="browser"?/dist.electron/i.test(process.execPath):!process.argv.includes("--claude-dev");U.isAppPackaged=st?"true":"false";U.version=it;const ut=process.type==="browser"?/internal/i.test(process.execPath):process.argv.includes("--claude-internal");U.isInternalBuild=ut.toString();m.contextBridge.exposeInMainWorld("claudeAppBindings",{registerBinding:(e,t)=>{ie(e),m.ipcRenderer.on(e,t)},unregisterBinding:e=>{ie(e),m.ipcRenderer.removeAllListeners(e)},listMcpServers:()=>m.ipcRenderer.invoke(E.ListMcpServers),connectToMcpServer:e=>m.ipcRenderer.invoke(E.ConnectToMcpServer,e),openMcpSettings:e=>m.ipcRenderer.invoke(E.RequestOpenMcpSettings,e)});m.ipcRenderer.on(E.McpServerConnected,(e,t)=>{window.postMessage({type:E.McpServerConnected,serverName:t},"*",e.ports)});rt();m.contextBridge.exposeInMainWorld("process",U);ne(B.Filesystem,"electronFileSystem",5*60*1e3);ne(B.Intl,"electronIntl",500);ne(B.WindowControl,"electronWindowControl",500);$e||m.webFrame.insertCSS(`
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 6px;
  border: 3px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
* {
  text-rendering: optimizeLegibility;
}
`,{cssOrigin:"author"});
//# sourceMappingURL=mainView.js.map
