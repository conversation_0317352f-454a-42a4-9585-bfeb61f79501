var Jc=e=>{throw TypeError(e)};var da=(e,t,n)=>t.has(e)||Jc("Cannot "+n);var ze=(e,t,n)=>(da(e,t,"read from private field"),n?n.call(e):t.get(e)),cn=(e,t,n)=>t.has(e)?Jc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),$r=(e,t,n,r)=>(da(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),ha=(e,t,n)=>(da(e,t,"access private method"),n);(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="da2268b2-d298-4bd1-b00b-c2e482e9a73a",e._sentryDebugIdIdentifier="sentry-dbid-da2268b2-d298-4bd1-b00b-c2e482e9a73a")}catch{}})();var $1=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};$1.SENTRY_RELEASE={id:"27cc6f763724a1af75b35c386a6b8d014eedc334"};(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();const H1=""+new URL("Copernicus-Book-Dqxs7atU.otf",import.meta.url).href,B1=""+new URL("Copernicus-BookItalic-DE-FEDdC.otf",import.meta.url).href,U1=""+new URL("Copernicus-Medium-BDFrxFZK.otf",import.meta.url).href,j1=""+new URL("Copernicus-MediumItalic-DwCSBWW0.otf",import.meta.url).href,z1=""+new URL("Copernicus-Semibold-CN_XmW6o.otf",import.meta.url).href,G1=""+new URL("StyreneBLC-Medium-Cw-IvyMy.otf",import.meta.url).href,V1=""+new URL("StyreneBLC-MediumItalic-CKHvGCIz.otf",import.meta.url).href,W1=""+new URL("StyreneBLC-Regular-DLVQLT8g.otf",import.meta.url).href,X1=""+new URL("StyreneBLC-RegularItalic-hJCoPVD5.otf",import.meta.url).href,Y1=""+new URL("TiemposText-Medium-vqMEr0TH.otf",import.meta.url).href,Q1=""+new URL("TiemposText-MediumItalic-CIeY-CUo.otf",import.meta.url).href,Z1=""+new URL("TiemposText-Regular-CoJqehkj.otf",import.meta.url).href,K1=""+new URL("TiemposText-RegularItalic-C4EVGPqi.otf",import.meta.url).href,q1=`
@font-face {
    font-family: 'Copernicus Book';
    src: url('${H1}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus Book';
    src: url('${B1}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${U1}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${j1}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${z1}') format('opentype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${G1}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${V1}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${W1}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${X1}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Y1}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Q1}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Z1}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${K1}') format('opentype');
    font-weight: normal;
    font-style: italic;
}
`,kh=document.createElement("style");kh.textContent=q1;document.head.appendChild(kh);function J1(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ph={exports:{}},Ls={},Nh={exports:{}},R={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mi=Symbol.for("react.element"),eg=Symbol.for("react.portal"),tg=Symbol.for("react.fragment"),ng=Symbol.for("react.strict_mode"),rg=Symbol.for("react.profiler"),ig=Symbol.for("react.provider"),og=Symbol.for("react.context"),sg=Symbol.for("react.forward_ref"),ag=Symbol.for("react.suspense"),lg=Symbol.for("react.memo"),ug=Symbol.for("react.lazy"),ef=Symbol.iterator;function cg(e){return e===null||typeof e!="object"?null:(e=ef&&e[ef]||e["@@iterator"],typeof e=="function"?e:null)}var Ah={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Rh=Object.assign,Oh={};function Nr(e,t,n){this.props=e,this.context=t,this.refs=Oh,this.updater=n||Ah}Nr.prototype.isReactComponent={};Nr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Nr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function bh(){}bh.prototype=Nr.prototype;function ku(e,t,n){this.props=e,this.context=t,this.refs=Oh,this.updater=n||Ah}var Pu=ku.prototype=new bh;Pu.constructor=ku;Rh(Pu,Nr.prototype);Pu.isPureReactComponent=!0;var tf=Array.isArray,Mh=Object.prototype.hasOwnProperty,Nu={current:null},Dh={key:!0,ref:!0,__self:!0,__source:!0};function Fh(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Mh.call(t,r)&&!Dh.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Mi,type:e,key:o,ref:s,props:i,_owner:Nu.current}}function fg(e,t){return{$$typeof:Mi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Au(e){return typeof e=="object"&&e!==null&&e.$$typeof===Mi}function dg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var nf=/\/+/g;function pa(e,t){return typeof e=="object"&&e!==null&&e.key!=null?dg(""+e.key):t.toString(36)}function Co(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Mi:case eg:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+pa(s,0):r,tf(i)?(n="",e!=null&&(n=e.replace(nf,"$&/")+"/"),Co(i,t,n,"",function(u){return u})):i!=null&&(Au(i)&&(i=fg(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(nf,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",tf(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+pa(o,a);s+=Co(o,t,n,l,i)}else if(l=cg(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+pa(o,a++),s+=Co(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Zi(e,t,n){if(e==null)return e;var r=[],i=0;return Co(e,r,"","",function(o){return t.call(n,o,i++)}),r}function hg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},To={transition:null},pg={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:To,ReactCurrentOwner:Nu};function $h(){throw Error("act(...) is not supported in production builds of React.")}R.Children={map:Zi,forEach:function(e,t,n){Zi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Zi(e,function(){t++}),t},toArray:function(e){return Zi(e,function(t){return t})||[]},only:function(e){if(!Au(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};R.Component=Nr;R.Fragment=tg;R.Profiler=rg;R.PureComponent=ku;R.StrictMode=ng;R.Suspense=ag;R.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=pg;R.act=$h;R.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Rh({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Nu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Mh.call(t,l)&&!Dh.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Mi,type:e.type,key:i,ref:o,props:r,_owner:s}};R.createContext=function(e){return e={$$typeof:og,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ig,_context:e},e.Consumer=e};R.createElement=Fh;R.createFactory=function(e){var t=Fh.bind(null,e);return t.type=e,t};R.createRef=function(){return{current:null}};R.forwardRef=function(e){return{$$typeof:sg,render:e}};R.isValidElement=Au;R.lazy=function(e){return{$$typeof:ug,_payload:{_status:-1,_result:e},_init:hg}};R.memo=function(e,t){return{$$typeof:lg,type:e,compare:t===void 0?null:t}};R.startTransition=function(e){var t=To.transition;To.transition={};try{e()}finally{To.transition=t}};R.unstable_act=$h;R.useCallback=function(e,t){return Te.current.useCallback(e,t)};R.useContext=function(e){return Te.current.useContext(e)};R.useDebugValue=function(){};R.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};R.useEffect=function(e,t){return Te.current.useEffect(e,t)};R.useId=function(){return Te.current.useId()};R.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};R.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};R.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};R.useMemo=function(e,t){return Te.current.useMemo(e,t)};R.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};R.useRef=function(e){return Te.current.useRef(e)};R.useState=function(e){return Te.current.useState(e)};R.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};R.useTransition=function(){return Te.current.useTransition()};R.version="18.3.1";Nh.exports=R;var xe=Nh.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mg=xe,gg=Symbol.for("react.element"),yg=Symbol.for("react.fragment"),vg=Object.prototype.hasOwnProperty,_g=mg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Eg={key:!0,ref:!0,__self:!0,__source:!0};function Hh(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)vg.call(t,r)&&!Eg.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:gg,type:e,key:o,ref:s,props:i,_owner:_g.current}}Ls.Fragment=yg;Ls.jsx=Hh;Ls.jsxs=Hh;Ph.exports=Ls;var tl=Ph.exports,Bh={exports:{}},Be={},Uh={exports:{}},jh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,N){var A=C.length;C.push(N);e:for(;0<A;){var J=A-1>>>1,ie=C[J];if(0<i(ie,N))C[J]=N,C[A]=ie,A=J;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var N=C[0],A=C.pop();if(A!==N){C[0]=A;e:for(var J=0,ie=C.length,Yi=ie>>>1;J<Yi;){var ln=2*(J+1)-1,fa=C[ln],un=ln+1,Qi=C[un];if(0>i(fa,A))un<ie&&0>i(Qi,fa)?(C[J]=Qi,C[un]=A,J=un):(C[J]=fa,C[ln]=A,J=ln);else if(un<ie&&0>i(Qi,A))C[J]=Qi,C[un]=A,J=un;else break e}}return N}function i(C,N){var A=C.sortIndex-N.sortIndex;return A!==0?A:C.id-N.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,f=null,d=3,m=!1,y=!1,v=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(C){for(var N=n(u);N!==null;){if(N.callback===null)r(u);else if(N.startTime<=C)r(u),N.sortIndex=N.expirationTime,t(l,N);else break;N=n(u)}}function _(C){if(v=!1,g(C),!y)if(n(l)!==null)y=!0,ua(w);else{var N=n(u);N!==null&&ca(_,N.startTime-C)}}function w(C,N){y=!1,v&&(v=!1,p(k),k=-1),m=!0;var A=d;try{for(g(N),f=n(l);f!==null&&(!(f.expirationTime>N)||C&&!Je());){var J=f.callback;if(typeof J=="function"){f.callback=null,d=f.priorityLevel;var ie=J(f.expirationTime<=N);N=e.unstable_now(),typeof ie=="function"?f.callback=ie:f===n(l)&&r(l),g(N)}else r(l);f=n(l)}if(f!==null)var Yi=!0;else{var ln=n(u);ln!==null&&ca(_,ln.startTime-N),Yi=!1}return Yi}finally{f=null,d=A,m=!1}}var L=!1,T=null,k=-1,Y=5,O=-1;function Je(){return!(e.unstable_now()-O<Y)}function Dr(){if(T!==null){var C=e.unstable_now();O=C;var N=!0;try{N=T(!0,C)}finally{N?Fr():(L=!1,T=null)}}else L=!1}var Fr;if(typeof h=="function")Fr=function(){h(Dr)};else if(typeof MessageChannel<"u"){var qc=new MessageChannel,F1=qc.port2;qc.port1.onmessage=Dr,Fr=function(){F1.postMessage(null)}}else Fr=function(){S(Dr,0)};function ua(C){T=C,L||(L=!0,Fr())}function ca(C,N){k=S(function(){C(e.unstable_now())},N)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,ua(w))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(C){switch(d){case 1:case 2:case 3:var N=3;break;default:N=d}var A=d;d=N;try{return C()}finally{d=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,N){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var A=d;d=C;try{return N()}finally{d=A}},e.unstable_scheduleCallback=function(C,N,A){var J=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?J+A:J):A=J,C){case 1:var ie=-1;break;case 2:ie=250;break;case 5:ie=**********;break;case 4:ie=1e4;break;default:ie=5e3}return ie=A+ie,C={id:c++,callback:N,priorityLevel:C,startTime:A,expirationTime:ie,sortIndex:-1},A>J?(C.sortIndex=A,t(u,C),n(l)===null&&C===n(u)&&(v?(p(k),k=-1):v=!0,ca(_,A-J))):(C.sortIndex=ie,t(l,C),y||m||(y=!0,ua(w))),C},e.unstable_shouldYield=Je,e.unstable_wrapCallback=function(C){var N=d;return function(){var A=d;d=N;try{return C.apply(this,arguments)}finally{d=A}}}})(jh);Uh.exports=jh;var Sg=Uh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wg=xe,He=Sg;function E(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var zh=new Set,fi={};function Mn(e,t){pr(e,t),pr(e+"Capture",t)}function pr(e,t){for(fi[e]=t,e=0;e<t.length;e++)zh.add(t[e])}var kt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),nl=Object.prototype.hasOwnProperty,Lg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,rf={},of={};function xg(e){return nl.call(of,e)?!0:nl.call(rf,e)?!1:Lg.test(e)?of[e]=!0:(rf[e]=!0,!1)}function Cg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Tg(e,t,n,r){if(t===null||typeof t>"u"||Cg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ie(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Ie(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Ie(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Ie(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Ie(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Ie(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Ie(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Ie(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Ie(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Ie(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ru=/[\-:]([a-z])/g;function Ou(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ru,Ou);fe[t]=new Ie(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ru,Ou);fe[t]=new Ie(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ru,Ou);fe[t]=new Ie(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Ie(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Ie("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Ie(e,1,!1,e.toLowerCase(),null,!0,!0)});function bu(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Tg(t,n,i,r)&&(n=null),r||i===null?xg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Rt=wg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ki=Symbol.for("react.element"),Gn=Symbol.for("react.portal"),Vn=Symbol.for("react.fragment"),Mu=Symbol.for("react.strict_mode"),rl=Symbol.for("react.profiler"),Gh=Symbol.for("react.provider"),Vh=Symbol.for("react.context"),Du=Symbol.for("react.forward_ref"),il=Symbol.for("react.suspense"),ol=Symbol.for("react.suspense_list"),Fu=Symbol.for("react.memo"),bt=Symbol.for("react.lazy"),Wh=Symbol.for("react.offscreen"),sf=Symbol.iterator;function Hr(e){return e===null||typeof e!="object"?null:(e=sf&&e[sf]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,ma;function Qr(e){if(ma===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ma=t&&t[1]||""}return`
`+ma+e}var ga=!1;function ya(e,t){if(!e||ga)return"";ga=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{ga=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Qr(e):""}function Ig(e){switch(e.tag){case 5:return Qr(e.type);case 16:return Qr("Lazy");case 13:return Qr("Suspense");case 19:return Qr("SuspenseList");case 0:case 2:case 15:return e=ya(e.type,!1),e;case 11:return e=ya(e.type.render,!1),e;case 1:return e=ya(e.type,!0),e;default:return""}}function sl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vn:return"Fragment";case Gn:return"Portal";case rl:return"Profiler";case Mu:return"StrictMode";case il:return"Suspense";case ol:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Vh:return(e.displayName||"Context")+".Consumer";case Gh:return(e._context.displayName||"Context")+".Provider";case Du:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Fu:return t=e.displayName||null,t!==null?t:sl(e.type)||"Memo";case bt:t=e._payload,e=e._init;try{return sl(e(t))}catch{}}return null}function kg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return sl(t);case 8:return t===Mu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Jt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Xh(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Pg(e){var t=Xh(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function qi(e){e._valueTracker||(e._valueTracker=Pg(e))}function Yh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Xh(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function jo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function al(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function af(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Jt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Qh(e,t){t=t.checked,t!=null&&bu(e,"checked",t,!1)}function ll(e,t){Qh(e,t);var n=Jt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ul(e,t.type,n):t.hasOwnProperty("defaultValue")&&ul(e,t.type,Jt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function lf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ul(e,t,n){(t!=="number"||jo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Zr=Array.isArray;function nr(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Jt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function cl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(E(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function uf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(E(92));if(Zr(n)){if(1<n.length)throw Error(E(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Jt(n)}}function Zh(e,t){var n=Jt(t.value),r=Jt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function cf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Kh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function fl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Kh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ji,qh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ji=Ji||document.createElement("div"),Ji.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ji.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function di(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ei={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ng=["Webkit","ms","Moz","O"];Object.keys(ei).forEach(function(e){Ng.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ei[t]=ei[e]})});function Jh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ei.hasOwnProperty(e)&&ei[e]?(""+t).trim():t+"px"}function ep(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Jh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Ag=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function dl(e,t){if(t){if(Ag[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(E(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(E(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(E(61))}if(t.style!=null&&typeof t.style!="object")throw Error(E(62))}}function hl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var pl=null;function $u(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ml=null,rr=null,ir=null;function ff(e){if(e=$i(e)){if(typeof ml!="function")throw Error(E(280));var t=e.stateNode;t&&(t=ks(t),ml(e.stateNode,e.type,t))}}function tp(e){rr?ir?ir.push(e):ir=[e]:rr=e}function np(){if(rr){var e=rr,t=ir;if(ir=rr=null,ff(e),t)for(e=0;e<t.length;e++)ff(t[e])}}function rp(e,t){return e(t)}function ip(){}var va=!1;function op(e,t,n){if(va)return e(t,n);va=!0;try{return rp(e,t,n)}finally{va=!1,(rr!==null||ir!==null)&&(ip(),np())}}function hi(e,t){var n=e.stateNode;if(n===null)return null;var r=ks(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(E(231,t,typeof n));return n}var gl=!1;if(kt)try{var Br={};Object.defineProperty(Br,"passive",{get:function(){gl=!0}}),window.addEventListener("test",Br,Br),window.removeEventListener("test",Br,Br)}catch{gl=!1}function Rg(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var ti=!1,zo=null,Go=!1,yl=null,Og={onError:function(e){ti=!0,zo=e}};function bg(e,t,n,r,i,o,s,a,l){ti=!1,zo=null,Rg.apply(Og,arguments)}function Mg(e,t,n,r,i,o,s,a,l){if(bg.apply(this,arguments),ti){if(ti){var u=zo;ti=!1,zo=null}else throw Error(E(198));Go||(Go=!0,yl=u)}}function Dn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function sp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function df(e){if(Dn(e)!==e)throw Error(E(188))}function Dg(e){var t=e.alternate;if(!t){if(t=Dn(e),t===null)throw Error(E(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return df(i),e;if(o===r)return df(i),t;o=o.sibling}throw Error(E(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?e:t}function ap(e){return e=Dg(e),e!==null?lp(e):null}function lp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=lp(e);if(t!==null)return t;e=e.sibling}return null}var up=He.unstable_scheduleCallback,hf=He.unstable_cancelCallback,Fg=He.unstable_shouldYield,$g=He.unstable_requestPaint,ee=He.unstable_now,Hg=He.unstable_getCurrentPriorityLevel,Hu=He.unstable_ImmediatePriority,cp=He.unstable_UserBlockingPriority,Vo=He.unstable_NormalPriority,Bg=He.unstable_LowPriority,fp=He.unstable_IdlePriority,xs=null,pt=null;function Ug(e){if(pt&&typeof pt.onCommitFiberRoot=="function")try{pt.onCommitFiberRoot(xs,e,void 0,(e.current.flags&128)===128)}catch{}}var it=Math.clz32?Math.clz32:Gg,jg=Math.log,zg=Math.LN2;function Gg(e){return e>>>=0,e===0?32:31-(jg(e)/zg|0)|0}var eo=64,to=4194304;function Kr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Wo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=Kr(a):(o&=s,o!==0&&(r=Kr(o)))}else s=n&~i,s!==0?r=Kr(s):o!==0&&(r=Kr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-it(t),i=1<<n,r|=e[n],t&=~i;return r}function Vg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-it(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=Vg(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function vl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function dp(){var e=eo;return eo<<=1,!(eo&4194240)&&(eo=64),e}function _a(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Di(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-it(t),e[t]=n}function Xg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-it(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Bu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var $=0;function hp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var pp,Uu,mp,gp,yp,_l=!1,no=[],zt=null,Gt=null,Vt=null,pi=new Map,mi=new Map,$t=[],Yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function pf(e,t){switch(e){case"focusin":case"focusout":zt=null;break;case"dragenter":case"dragleave":Gt=null;break;case"mouseover":case"mouseout":Vt=null;break;case"pointerover":case"pointerout":pi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":mi.delete(t.pointerId)}}function Ur(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=$i(t),t!==null&&Uu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Qg(e,t,n,r,i){switch(t){case"focusin":return zt=Ur(zt,e,t,n,r,i),!0;case"dragenter":return Gt=Ur(Gt,e,t,n,r,i),!0;case"mouseover":return Vt=Ur(Vt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return pi.set(o,Ur(pi.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,mi.set(o,Ur(mi.get(o)||null,e,t,n,r,i)),!0}return!1}function vp(e){var t=mn(e.target);if(t!==null){var n=Dn(t);if(n!==null){if(t=n.tag,t===13){if(t=sp(n),t!==null){e.blockedOn=t,yp(e.priority,function(){mp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Io(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=El(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);pl=r,n.target.dispatchEvent(r),pl=null}else return t=$i(n),t!==null&&Uu(t),e.blockedOn=n,!1;t.shift()}return!0}function mf(e,t,n){Io(e)&&n.delete(t)}function Zg(){_l=!1,zt!==null&&Io(zt)&&(zt=null),Gt!==null&&Io(Gt)&&(Gt=null),Vt!==null&&Io(Vt)&&(Vt=null),pi.forEach(mf),mi.forEach(mf)}function jr(e,t){e.blockedOn===t&&(e.blockedOn=null,_l||(_l=!0,He.unstable_scheduleCallback(He.unstable_NormalPriority,Zg)))}function gi(e){function t(i){return jr(i,e)}if(0<no.length){jr(no[0],e);for(var n=1;n<no.length;n++){var r=no[n];r.blockedOn===e&&(r.blockedOn=null)}}for(zt!==null&&jr(zt,e),Gt!==null&&jr(Gt,e),Vt!==null&&jr(Vt,e),pi.forEach(t),mi.forEach(t),n=0;n<$t.length;n++)r=$t[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<$t.length&&(n=$t[0],n.blockedOn===null);)vp(n),n.blockedOn===null&&$t.shift()}var or=Rt.ReactCurrentBatchConfig,Xo=!0;function Kg(e,t,n,r){var i=$,o=or.transition;or.transition=null;try{$=1,ju(e,t,n,r)}finally{$=i,or.transition=o}}function qg(e,t,n,r){var i=$,o=or.transition;or.transition=null;try{$=4,ju(e,t,n,r)}finally{$=i,or.transition=o}}function ju(e,t,n,r){if(Xo){var i=El(e,t,n,r);if(i===null)Pa(e,t,r,Yo,n),pf(e,r);else if(Qg(i,e,t,n,r))r.stopPropagation();else if(pf(e,r),t&4&&-1<Yg.indexOf(e)){for(;i!==null;){var o=$i(i);if(o!==null&&pp(o),o=El(e,t,n,r),o===null&&Pa(e,t,r,Yo,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Pa(e,t,r,null,n)}}var Yo=null;function El(e,t,n,r){if(Yo=null,e=$u(r),e=mn(e),e!==null)if(t=Dn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=sp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Yo=e,null}function _p(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Hg()){case Hu:return 1;case cp:return 4;case Vo:case Bg:return 16;case fp:return 536870912;default:return 16}default:return 16}}var Ut=null,zu=null,ko=null;function Ep(){if(ko)return ko;var e,t=zu,n=t.length,r,i="value"in Ut?Ut.value:Ut.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return ko=i.slice(e,1<r?1-r:void 0)}function Po(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ro(){return!0}function gf(){return!1}function Ue(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?ro:gf,this.isPropagationStopped=gf,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ro)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ro)},persist:function(){},isPersistent:ro}),t}var Ar={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Gu=Ue(Ar),Fi=K({},Ar,{view:0,detail:0}),Jg=Ue(Fi),Ea,Sa,zr,Cs=K({},Fi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Vu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==zr&&(zr&&e.type==="mousemove"?(Ea=e.screenX-zr.screenX,Sa=e.screenY-zr.screenY):Sa=Ea=0,zr=e),Ea)},movementY:function(e){return"movementY"in e?e.movementY:Sa}}),yf=Ue(Cs),ey=K({},Cs,{dataTransfer:0}),ty=Ue(ey),ny=K({},Fi,{relatedTarget:0}),wa=Ue(ny),ry=K({},Ar,{animationName:0,elapsedTime:0,pseudoElement:0}),iy=Ue(ry),oy=K({},Ar,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),sy=Ue(oy),ay=K({},Ar,{data:0}),vf=Ue(ay),ly={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},uy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},cy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=cy[e])?!!t[e]:!1}function Vu(){return fy}var dy=K({},Fi,{key:function(e){if(e.key){var t=ly[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Po(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?uy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Vu,charCode:function(e){return e.type==="keypress"?Po(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Po(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),hy=Ue(dy),py=K({},Cs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),_f=Ue(py),my=K({},Fi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Vu}),gy=Ue(my),yy=K({},Ar,{propertyName:0,elapsedTime:0,pseudoElement:0}),vy=Ue(yy),_y=K({},Cs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ey=Ue(_y),Sy=[9,13,27,32],Wu=kt&&"CompositionEvent"in window,ni=null;kt&&"documentMode"in document&&(ni=document.documentMode);var wy=kt&&"TextEvent"in window&&!ni,Sp=kt&&(!Wu||ni&&8<ni&&11>=ni),Ef=" ",Sf=!1;function wp(e,t){switch(e){case"keyup":return Sy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Lp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wn=!1;function Ly(e,t){switch(e){case"compositionend":return Lp(t);case"keypress":return t.which!==32?null:(Sf=!0,Ef);case"textInput":return e=t.data,e===Ef&&Sf?null:e;default:return null}}function xy(e,t){if(Wn)return e==="compositionend"||!Wu&&wp(e,t)?(e=Ep(),ko=zu=Ut=null,Wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Sp&&t.locale!=="ko"?null:t.data;default:return null}}var Cy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function wf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Cy[e.type]:t==="textarea"}function xp(e,t,n,r){tp(r),t=Qo(t,"onChange"),0<t.length&&(n=new Gu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ri=null,yi=null;function Ty(e){Mp(e,0)}function Ts(e){var t=Qn(e);if(Yh(t))return e}function Iy(e,t){if(e==="change")return t}var Cp=!1;if(kt){var La;if(kt){var xa="oninput"in document;if(!xa){var Lf=document.createElement("div");Lf.setAttribute("oninput","return;"),xa=typeof Lf.oninput=="function"}La=xa}else La=!1;Cp=La&&(!document.documentMode||9<document.documentMode)}function xf(){ri&&(ri.detachEvent("onpropertychange",Tp),yi=ri=null)}function Tp(e){if(e.propertyName==="value"&&Ts(yi)){var t=[];xp(t,yi,e,$u(e)),op(Ty,t)}}function ky(e,t,n){e==="focusin"?(xf(),ri=t,yi=n,ri.attachEvent("onpropertychange",Tp)):e==="focusout"&&xf()}function Py(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ts(yi)}function Ny(e,t){if(e==="click")return Ts(t)}function Ay(e,t){if(e==="input"||e==="change")return Ts(t)}function Ry(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:Ry;function vi(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!nl.call(t,i)||!at(e[i],t[i]))return!1}return!0}function Cf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Tf(e,t){var n=Cf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Cf(n)}}function Ip(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ip(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function kp(){for(var e=window,t=jo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=jo(e.document)}return t}function Xu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Oy(e){var t=kp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ip(n.ownerDocument.documentElement,n)){if(r!==null&&Xu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Tf(n,o);var s=Tf(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var by=kt&&"documentMode"in document&&11>=document.documentMode,Xn=null,Sl=null,ii=null,wl=!1;function If(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;wl||Xn==null||Xn!==jo(r)||(r=Xn,"selectionStart"in r&&Xu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ii&&vi(ii,r)||(ii=r,r=Qo(Sl,"onSelect"),0<r.length&&(t=new Gu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Xn)))}function io(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Yn={animationend:io("Animation","AnimationEnd"),animationiteration:io("Animation","AnimationIteration"),animationstart:io("Animation","AnimationStart"),transitionend:io("Transition","TransitionEnd")},Ca={},Pp={};kt&&(Pp=document.createElement("div").style,"AnimationEvent"in window||(delete Yn.animationend.animation,delete Yn.animationiteration.animation,delete Yn.animationstart.animation),"TransitionEvent"in window||delete Yn.transitionend.transition);function Is(e){if(Ca[e])return Ca[e];if(!Yn[e])return e;var t=Yn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Pp)return Ca[e]=t[n];return e}var Np=Is("animationend"),Ap=Is("animationiteration"),Rp=Is("animationstart"),Op=Is("transitionend"),bp=new Map,kf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function rn(e,t){bp.set(e,t),Mn(t,[e])}for(var Ta=0;Ta<kf.length;Ta++){var Ia=kf[Ta],My=Ia.toLowerCase(),Dy=Ia[0].toUpperCase()+Ia.slice(1);rn(My,"on"+Dy)}rn(Np,"onAnimationEnd");rn(Ap,"onAnimationIteration");rn(Rp,"onAnimationStart");rn("dblclick","onDoubleClick");rn("focusin","onFocus");rn("focusout","onBlur");rn(Op,"onTransitionEnd");pr("onMouseEnter",["mouseout","mouseover"]);pr("onMouseLeave",["mouseout","mouseover"]);pr("onPointerEnter",["pointerout","pointerover"]);pr("onPointerLeave",["pointerout","pointerover"]);Mn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Mn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Mn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Mn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Mn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Mn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var qr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fy=new Set("cancel close invalid load scroll toggle".split(" ").concat(qr));function Pf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Mg(r,t,void 0,e),e.currentTarget=null}function Mp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break e;Pf(i,a,u),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break e;Pf(i,a,u),o=l}}}if(Go)throw e=yl,Go=!1,yl=null,e}function G(e,t){var n=t[Il];n===void 0&&(n=t[Il]=new Set);var r=e+"__bubble";n.has(r)||(Dp(t,e,2,!1),n.add(r))}function ka(e,t,n){var r=0;t&&(r|=4),Dp(n,e,r,t)}var oo="_reactListening"+Math.random().toString(36).slice(2);function _i(e){if(!e[oo]){e[oo]=!0,zh.forEach(function(n){n!=="selectionchange"&&(Fy.has(n)||ka(n,!1,e),ka(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[oo]||(t[oo]=!0,ka("selectionchange",!1,t))}}function Dp(e,t,n,r){switch(_p(t)){case 1:var i=Kg;break;case 4:i=qg;break;default:i=ju}n=i.bind(null,t,n,e),i=void 0,!gl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Pa(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=mn(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue e}a=a.parentNode}}r=r.return}op(function(){var u=o,c=$u(n),f=[];e:{var d=bp.get(e);if(d!==void 0){var m=Gu,y=e;switch(e){case"keypress":if(Po(n)===0)break e;case"keydown":case"keyup":m=hy;break;case"focusin":y="focus",m=wa;break;case"focusout":y="blur",m=wa;break;case"beforeblur":case"afterblur":m=wa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=yf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=ty;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=gy;break;case Np:case Ap:case Rp:m=iy;break;case Op:m=vy;break;case"scroll":m=Jg;break;case"wheel":m=Ey;break;case"copy":case"cut":case"paste":m=sy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=_f}var v=(t&4)!==0,S=!v&&e==="scroll",p=v?d!==null?d+"Capture":null:d;v=[];for(var h=u,g;h!==null;){g=h;var _=g.stateNode;if(g.tag===5&&_!==null&&(g=_,p!==null&&(_=hi(h,p),_!=null&&v.push(Ei(h,_,g)))),S)break;h=h.return}0<v.length&&(d=new m(d,y,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",d&&n!==pl&&(y=n.relatedTarget||n.fromElement)&&(mn(y)||y[Pt]))break e;if((m||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?mn(y):null,y!==null&&(S=Dn(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=u),m!==y)){if(v=yf,_="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(v=_f,_="onPointerLeave",p="onPointerEnter",h="pointer"),S=m==null?d:Qn(m),g=y==null?d:Qn(y),d=new v(_,h+"leave",m,n,c),d.target=S,d.relatedTarget=g,_=null,mn(c)===u&&(v=new v(p,h+"enter",y,n,c),v.target=g,v.relatedTarget=S,_=v),S=_,m&&y)t:{for(v=m,p=y,h=0,g=v;g;g=Un(g))h++;for(g=0,_=p;_;_=Un(_))g++;for(;0<h-g;)v=Un(v),h--;for(;0<g-h;)p=Un(p),g--;for(;h--;){if(v===p||p!==null&&v===p.alternate)break t;v=Un(v),p=Un(p)}v=null}else v=null;m!==null&&Nf(f,d,m,v,!1),y!==null&&S!==null&&Nf(f,S,y,v,!0)}}e:{if(d=u?Qn(u):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var w=Iy;else if(wf(d))if(Cp)w=Ay;else{w=Py;var L=ky}else(m=d.nodeName)&&m.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(w=Ny);if(w&&(w=w(e,u))){xp(f,w,n,c);break e}L&&L(e,d,u),e==="focusout"&&(L=d._wrapperState)&&L.controlled&&d.type==="number"&&ul(d,"number",d.value)}switch(L=u?Qn(u):window,e){case"focusin":(wf(L)||L.contentEditable==="true")&&(Xn=L,Sl=u,ii=null);break;case"focusout":ii=Sl=Xn=null;break;case"mousedown":wl=!0;break;case"contextmenu":case"mouseup":case"dragend":wl=!1,If(f,n,c);break;case"selectionchange":if(by)break;case"keydown":case"keyup":If(f,n,c)}var T;if(Wu)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Wn?wp(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(Sp&&n.locale!=="ko"&&(Wn||k!=="onCompositionStart"?k==="onCompositionEnd"&&Wn&&(T=Ep()):(Ut=c,zu="value"in Ut?Ut.value:Ut.textContent,Wn=!0)),L=Qo(u,k),0<L.length&&(k=new vf(k,e,null,n,c),f.push({event:k,listeners:L}),T?k.data=T:(T=Lp(n),T!==null&&(k.data=T)))),(T=wy?Ly(e,n):xy(e,n))&&(u=Qo(u,"onBeforeInput"),0<u.length&&(c=new vf("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=T))}Mp(f,t)})}function Ei(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qo(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=hi(e,n),o!=null&&r.unshift(Ei(e,o,i)),o=hi(e,t),o!=null&&r.push(Ei(e,o,i))),e=e.return}return r}function Un(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Nf(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=hi(n,o),l!=null&&s.unshift(Ei(n,l,a))):i||(l=hi(n,o),l!=null&&s.push(Ei(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var $y=/\r\n?/g,Hy=/\u0000|\uFFFD/g;function Af(e){return(typeof e=="string"?e:""+e).replace($y,`
`).replace(Hy,"")}function so(e,t,n){if(t=Af(t),Af(e)!==t&&n)throw Error(E(425))}function Zo(){}var Ll=null,xl=null;function Cl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Tl=typeof setTimeout=="function"?setTimeout:void 0,By=typeof clearTimeout=="function"?clearTimeout:void 0,Rf=typeof Promise=="function"?Promise:void 0,Uy=typeof queueMicrotask=="function"?queueMicrotask:typeof Rf<"u"?function(e){return Rf.resolve(null).then(e).catch(jy)}:Tl;function jy(e){setTimeout(function(){throw e})}function Na(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),gi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);gi(t)}function Wt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Of(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Rr=Math.random().toString(36).slice(2),dt="__reactFiber$"+Rr,Si="__reactProps$"+Rr,Pt="__reactContainer$"+Rr,Il="__reactEvents$"+Rr,zy="__reactListeners$"+Rr,Gy="__reactHandles$"+Rr;function mn(e){var t=e[dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Pt]||n[dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Of(e);e!==null;){if(n=e[dt])return n;e=Of(e)}return t}e=n,n=e.parentNode}return null}function $i(e){return e=e[dt]||e[Pt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Qn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(E(33))}function ks(e){return e[Si]||null}var kl=[],Zn=-1;function on(e){return{current:e}}function W(e){0>Zn||(e.current=kl[Zn],kl[Zn]=null,Zn--)}function z(e,t){Zn++,kl[Zn]=e.current,e.current=t}var en={},ye=on(en),Ne=on(!1),Ln=en;function mr(e,t){var n=e.type.contextTypes;if(!n)return en;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ae(e){return e=e.childContextTypes,e!=null}function Ko(){W(Ne),W(ye)}function bf(e,t,n){if(ye.current!==en)throw Error(E(168));z(ye,t),z(Ne,n)}function Fp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(E(108,kg(e)||"Unknown",i));return K({},n,r)}function qo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||en,Ln=ye.current,z(ye,e),z(Ne,Ne.current),!0}function Mf(e,t,n){var r=e.stateNode;if(!r)throw Error(E(169));n?(e=Fp(e,t,Ln),r.__reactInternalMemoizedMergedChildContext=e,W(Ne),W(ye),z(ye,e)):W(Ne),z(Ne,n)}var St=null,Ps=!1,Aa=!1;function $p(e){St===null?St=[e]:St.push(e)}function Vy(e){Ps=!0,$p(e)}function sn(){if(!Aa&&St!==null){Aa=!0;var e=0,t=$;try{var n=St;for($=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}St=null,Ps=!1}catch(i){throw St!==null&&(St=St.slice(e+1)),up(Hu,sn),i}finally{$=t,Aa=!1}}return null}var Kn=[],qn=0,Jo=null,es=0,Ge=[],Ve=0,xn=null,Lt=1,xt="";function hn(e,t){Kn[qn++]=es,Kn[qn++]=Jo,Jo=e,es=t}function Hp(e,t,n){Ge[Ve++]=Lt,Ge[Ve++]=xt,Ge[Ve++]=xn,xn=e;var r=Lt;e=xt;var i=32-it(r)-1;r&=~(1<<i),n+=1;var o=32-it(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,Lt=1<<32-it(t)+i|n<<i|r,xt=o+e}else Lt=1<<o|n<<i|r,xt=e}function Yu(e){e.return!==null&&(hn(e,1),Hp(e,1,0))}function Qu(e){for(;e===Jo;)Jo=Kn[--qn],Kn[qn]=null,es=Kn[--qn],Kn[qn]=null;for(;e===xn;)xn=Ge[--Ve],Ge[Ve]=null,xt=Ge[--Ve],Ge[Ve]=null,Lt=Ge[--Ve],Ge[Ve]=null}var Fe=null,be=null,X=!1,rt=null;function Bp(e,t){var n=Xe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Df(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Fe=e,be=Wt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Fe=e,be=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xn!==null?{id:Lt,overflow:xt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Xe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Fe=e,be=null,!0):!1;default:return!1}}function Pl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Nl(e){if(X){var t=be;if(t){var n=t;if(!Df(e,t)){if(Pl(e))throw Error(E(418));t=Wt(n.nextSibling);var r=Fe;t&&Df(e,t)?Bp(r,n):(e.flags=e.flags&-4097|2,X=!1,Fe=e)}}else{if(Pl(e))throw Error(E(418));e.flags=e.flags&-4097|2,X=!1,Fe=e}}}function Ff(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Fe=e}function ao(e){if(e!==Fe)return!1;if(!X)return Ff(e),X=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Cl(e.type,e.memoizedProps)),t&&(t=be)){if(Pl(e))throw Up(),Error(E(418));for(;t;)Bp(e,t),t=Wt(t.nextSibling)}if(Ff(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(E(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){be=Wt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}be=null}}else be=Fe?Wt(e.stateNode.nextSibling):null;return!0}function Up(){for(var e=be;e;)e=Wt(e.nextSibling)}function gr(){be=Fe=null,X=!1}function Zu(e){rt===null?rt=[e]:rt.push(e)}var Wy=Rt.ReactCurrentBatchConfig;function Gr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,e))}return e}function lo(e,t){throw e=Object.prototype.toString.call(t),Error(E(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function $f(e){var t=e._init;return t(e._payload)}function jp(e){function t(p,h){if(e){var g=p.deletions;g===null?(p.deletions=[h],p.flags|=16):g.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function i(p,h){return p=Zt(p,h),p.index=0,p.sibling=null,p}function o(p,h,g){return p.index=g,e?(g=p.alternate,g!==null?(g=g.index,g<h?(p.flags|=2,h):g):(p.flags|=2,h)):(p.flags|=1048576,h)}function s(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,h,g,_){return h===null||h.tag!==6?(h=$a(g,p.mode,_),h.return=p,h):(h=i(h,g),h.return=p,h)}function l(p,h,g,_){var w=g.type;return w===Vn?c(p,h,g.props.children,_,g.key):h!==null&&(h.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===bt&&$f(w)===h.type)?(_=i(h,g.props),_.ref=Gr(p,h,g),_.return=p,_):(_=Do(g.type,g.key,g.props,null,p.mode,_),_.ref=Gr(p,h,g),_.return=p,_)}function u(p,h,g,_){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=Ha(g,p.mode,_),h.return=p,h):(h=i(h,g.children||[]),h.return=p,h)}function c(p,h,g,_,w){return h===null||h.tag!==7?(h=Sn(g,p.mode,_,w),h.return=p,h):(h=i(h,g),h.return=p,h)}function f(p,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=$a(""+h,p.mode,g),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Ki:return g=Do(h.type,h.key,h.props,null,p.mode,g),g.ref=Gr(p,null,h),g.return=p,g;case Gn:return h=Ha(h,p.mode,g),h.return=p,h;case bt:var _=h._init;return f(p,_(h._payload),g)}if(Zr(h)||Hr(h))return h=Sn(h,p.mode,g,null),h.return=p,h;lo(p,h)}return null}function d(p,h,g,_){var w=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return w!==null?null:a(p,h,""+g,_);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Ki:return g.key===w?l(p,h,g,_):null;case Gn:return g.key===w?u(p,h,g,_):null;case bt:return w=g._init,d(p,h,w(g._payload),_)}if(Zr(g)||Hr(g))return w!==null?null:c(p,h,g,_,null);lo(p,g)}return null}function m(p,h,g,_,w){if(typeof _=="string"&&_!==""||typeof _=="number")return p=p.get(g)||null,a(h,p,""+_,w);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case Ki:return p=p.get(_.key===null?g:_.key)||null,l(h,p,_,w);case Gn:return p=p.get(_.key===null?g:_.key)||null,u(h,p,_,w);case bt:var L=_._init;return m(p,h,g,L(_._payload),w)}if(Zr(_)||Hr(_))return p=p.get(g)||null,c(h,p,_,w,null);lo(h,_)}return null}function y(p,h,g,_){for(var w=null,L=null,T=h,k=h=0,Y=null;T!==null&&k<g.length;k++){T.index>k?(Y=T,T=null):Y=T.sibling;var O=d(p,T,g[k],_);if(O===null){T===null&&(T=Y);break}e&&T&&O.alternate===null&&t(p,T),h=o(O,h,k),L===null?w=O:L.sibling=O,L=O,T=Y}if(k===g.length)return n(p,T),X&&hn(p,k),w;if(T===null){for(;k<g.length;k++)T=f(p,g[k],_),T!==null&&(h=o(T,h,k),L===null?w=T:L.sibling=T,L=T);return X&&hn(p,k),w}for(T=r(p,T);k<g.length;k++)Y=m(T,p,k,g[k],_),Y!==null&&(e&&Y.alternate!==null&&T.delete(Y.key===null?k:Y.key),h=o(Y,h,k),L===null?w=Y:L.sibling=Y,L=Y);return e&&T.forEach(function(Je){return t(p,Je)}),X&&hn(p,k),w}function v(p,h,g,_){var w=Hr(g);if(typeof w!="function")throw Error(E(150));if(g=w.call(g),g==null)throw Error(E(151));for(var L=w=null,T=h,k=h=0,Y=null,O=g.next();T!==null&&!O.done;k++,O=g.next()){T.index>k?(Y=T,T=null):Y=T.sibling;var Je=d(p,T,O.value,_);if(Je===null){T===null&&(T=Y);break}e&&T&&Je.alternate===null&&t(p,T),h=o(Je,h,k),L===null?w=Je:L.sibling=Je,L=Je,T=Y}if(O.done)return n(p,T),X&&hn(p,k),w;if(T===null){for(;!O.done;k++,O=g.next())O=f(p,O.value,_),O!==null&&(h=o(O,h,k),L===null?w=O:L.sibling=O,L=O);return X&&hn(p,k),w}for(T=r(p,T);!O.done;k++,O=g.next())O=m(T,p,k,O.value,_),O!==null&&(e&&O.alternate!==null&&T.delete(O.key===null?k:O.key),h=o(O,h,k),L===null?w=O:L.sibling=O,L=O);return e&&T.forEach(function(Dr){return t(p,Dr)}),X&&hn(p,k),w}function S(p,h,g,_){if(typeof g=="object"&&g!==null&&g.type===Vn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Ki:e:{for(var w=g.key,L=h;L!==null;){if(L.key===w){if(w=g.type,w===Vn){if(L.tag===7){n(p,L.sibling),h=i(L,g.props.children),h.return=p,p=h;break e}}else if(L.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===bt&&$f(w)===L.type){n(p,L.sibling),h=i(L,g.props),h.ref=Gr(p,L,g),h.return=p,p=h;break e}n(p,L);break}else t(p,L);L=L.sibling}g.type===Vn?(h=Sn(g.props.children,p.mode,_,g.key),h.return=p,p=h):(_=Do(g.type,g.key,g.props,null,p.mode,_),_.ref=Gr(p,h,g),_.return=p,p=_)}return s(p);case Gn:e:{for(L=g.key;h!==null;){if(h.key===L)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(p,h.sibling),h=i(h,g.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=Ha(g,p.mode,_),h.return=p,p=h}return s(p);case bt:return L=g._init,S(p,h,L(g._payload),_)}if(Zr(g))return y(p,h,g,_);if(Hr(g))return v(p,h,g,_);lo(p,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(p,h.sibling),h=i(h,g),h.return=p,p=h):(n(p,h),h=$a(g,p.mode,_),h.return=p,p=h),s(p)):n(p,h)}return S}var yr=jp(!0),zp=jp(!1),ts=on(null),ns=null,Jn=null,Ku=null;function qu(){Ku=Jn=ns=null}function Ju(e){var t=ts.current;W(ts),e._currentValue=t}function Al(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function sr(e,t){ns=e,Ku=Jn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Pe=!0),e.firstContext=null)}function Qe(e){var t=e._currentValue;if(Ku!==e)if(e={context:e,memoizedValue:t,next:null},Jn===null){if(ns===null)throw Error(E(308));Jn=e,ns.dependencies={lanes:0,firstContext:e}}else Jn=Jn.next=e;return t}var gn=null;function ec(e){gn===null?gn=[e]:gn.push(e)}function Gp(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,ec(t)):(n.next=i.next,i.next=n),t.interleaved=n,Nt(e,r)}function Nt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Mt=!1;function tc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Vp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,D&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Nt(e,n)}return i=r.interleaved,i===null?(t.next=t,ec(r)):(t.next=i.next,i.next=t),r.interleaved=t,Nt(e,n)}function No(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Bu(e,n)}}function Hf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function rs(e,t,n,r){var i=e.updateQueue;Mt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?o=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(o!==null){var f=i.baseState;s=0,c=u=l=null,a=o;do{var d=a.lane,m=a.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,v=a;switch(d=t,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(m,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(m,f,d):y,d==null)break e;f=K({},f,d);break e;case 2:Mt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[a]:d.push(a))}else m={eventTime:m,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=m,l=f):c=c.next=m,s|=d;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;d=a,a=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(l=f),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Tn|=s,e.lanes=s,e.memoizedState=f}}function Bf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(E(191,i));i.call(r)}}}var Hi={},mt=on(Hi),wi=on(Hi),Li=on(Hi);function yn(e){if(e===Hi)throw Error(E(174));return e}function nc(e,t){switch(z(Li,t),z(wi,e),z(mt,Hi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:fl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=fl(t,e)}W(mt),z(mt,t)}function vr(){W(mt),W(wi),W(Li)}function Wp(e){yn(Li.current);var t=yn(mt.current),n=fl(t,e.type);t!==n&&(z(wi,e),z(mt,n))}function rc(e){wi.current===e&&(W(mt),W(wi))}var Q=on(0);function is(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ra=[];function ic(){for(var e=0;e<Ra.length;e++)Ra[e]._workInProgressVersionPrimary=null;Ra.length=0}var Ao=Rt.ReactCurrentDispatcher,Oa=Rt.ReactCurrentBatchConfig,Cn=0,Z=null,ne=null,oe=null,os=!1,oi=!1,xi=0,Xy=0;function he(){throw Error(E(321))}function oc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!at(e[n],t[n]))return!1;return!0}function sc(e,t,n,r,i,o){if(Cn=o,Z=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ao.current=e===null||e.memoizedState===null?Ky:qy,e=n(r,i),oi){o=0;do{if(oi=!1,xi=0,25<=o)throw Error(E(301));o+=1,oe=ne=null,t.updateQueue=null,Ao.current=Jy,e=n(r,i)}while(oi)}if(Ao.current=ss,t=ne!==null&&ne.next!==null,Cn=0,oe=ne=Z=null,os=!1,t)throw Error(E(300));return e}function ac(){var e=xi!==0;return xi=0,e}function ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?Z.memoizedState=oe=e:oe=oe.next=e,oe}function Ze(){if(ne===null){var e=Z.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=oe===null?Z.memoizedState:oe.next;if(t!==null)oe=t,ne=e;else{if(e===null)throw Error(E(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},oe===null?Z.memoizedState=oe=e:oe=oe.next=e}return oe}function Ci(e,t){return typeof t=="function"?t(e):t}function ba(e){var t=Ze(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=ne,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,u=o;do{var c=u.lane;if((Cn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,Z.lanes|=c,Tn|=c}u=u.next}while(u!==null&&u!==o);l===null?s=r:l.next=a,at(r,t.memoizedState)||(Pe=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Z.lanes|=o,Tn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ma(e){var t=Ze(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);at(o,t.memoizedState)||(Pe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Xp(){}function Yp(e,t){var n=Z,r=Ze(),i=t(),o=!at(r.memoizedState,i);if(o&&(r.memoizedState=i,Pe=!0),r=r.queue,lc(Kp.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,Ti(9,Zp.bind(null,n,r,i,t),void 0,null),ae===null)throw Error(E(349));Cn&30||Qp(n,t,i)}return i}function Qp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Zp(e,t,n,r){t.value=n,t.getSnapshot=r,qp(t)&&Jp(e)}function Kp(e,t,n){return n(function(){qp(t)&&Jp(e)})}function qp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!at(e,n)}catch{return!0}}function Jp(e){var t=Nt(e,1);t!==null&&ot(t,e,1,-1)}function Uf(e){var t=ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ci,lastRenderedState:e},t.queue=e,e=e.dispatch=Zy.bind(null,Z,e),[t.memoizedState,e]}function Ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function e0(){return Ze().memoizedState}function Ro(e,t,n,r){var i=ft();Z.flags|=e,i.memoizedState=Ti(1|t,n,void 0,r===void 0?null:r)}function Ns(e,t,n,r){var i=Ze();r=r===void 0?null:r;var o=void 0;if(ne!==null){var s=ne.memoizedState;if(o=s.destroy,r!==null&&oc(r,s.deps)){i.memoizedState=Ti(t,n,o,r);return}}Z.flags|=e,i.memoizedState=Ti(1|t,n,o,r)}function jf(e,t){return Ro(8390656,8,e,t)}function lc(e,t){return Ns(2048,8,e,t)}function t0(e,t){return Ns(4,2,e,t)}function n0(e,t){return Ns(4,4,e,t)}function r0(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function i0(e,t,n){return n=n!=null?n.concat([e]):null,Ns(4,4,r0.bind(null,t,e),n)}function uc(){}function o0(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&oc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function s0(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&oc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function a0(e,t,n){return Cn&21?(at(n,t)||(n=dp(),Z.lanes|=n,Tn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Pe=!0),e.memoizedState=n)}function Yy(e,t){var n=$;$=n!==0&&4>n?n:4,e(!0);var r=Oa.transition;Oa.transition={};try{e(!1),t()}finally{$=n,Oa.transition=r}}function l0(){return Ze().memoizedState}function Qy(e,t,n){var r=Qt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},u0(e))c0(t,n);else if(n=Gp(e,t,n,r),n!==null){var i=Ce();ot(n,e,r,i),f0(n,t,r)}}function Zy(e,t,n){var r=Qt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(u0(e))c0(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,at(a,s)){var l=t.interleaved;l===null?(i.next=i,ec(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Gp(e,t,i,r),n!==null&&(i=Ce(),ot(n,e,r,i),f0(n,t,r))}}function u0(e){var t=e.alternate;return e===Z||t!==null&&t===Z}function c0(e,t){oi=os=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function f0(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Bu(e,n)}}var ss={readContext:Qe,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},Ky={readContext:Qe,useCallback:function(e,t){return ft().memoizedState=[e,t===void 0?null:t],e},useContext:Qe,useEffect:jf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ro(4194308,4,r0.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ro(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ro(4,2,e,t)},useMemo:function(e,t){var n=ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qy.bind(null,Z,e),[r.memoizedState,e]},useRef:function(e){var t=ft();return e={current:e},t.memoizedState=e},useState:Uf,useDebugValue:uc,useDeferredValue:function(e){return ft().memoizedState=e},useTransition:function(){var e=Uf(!1),t=e[0];return e=Yy.bind(null,e[1]),ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Z,i=ft();if(X){if(n===void 0)throw Error(E(407));n=n()}else{if(n=t(),ae===null)throw Error(E(349));Cn&30||Qp(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,jf(Kp.bind(null,r,o,e),[e]),r.flags|=2048,Ti(9,Zp.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=ft(),t=ae.identifierPrefix;if(X){var n=xt,r=Lt;n=(r&~(1<<32-it(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=xi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Xy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},qy={readContext:Qe,useCallback:o0,useContext:Qe,useEffect:lc,useImperativeHandle:i0,useInsertionEffect:t0,useLayoutEffect:n0,useMemo:s0,useReducer:ba,useRef:e0,useState:function(){return ba(Ci)},useDebugValue:uc,useDeferredValue:function(e){var t=Ze();return a0(t,ne.memoizedState,e)},useTransition:function(){var e=ba(Ci)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Xp,useSyncExternalStore:Yp,useId:l0,unstable_isNewReconciler:!1},Jy={readContext:Qe,useCallback:o0,useContext:Qe,useEffect:lc,useImperativeHandle:i0,useInsertionEffect:t0,useLayoutEffect:n0,useMemo:s0,useReducer:Ma,useRef:e0,useState:function(){return Ma(Ci)},useDebugValue:uc,useDeferredValue:function(e){var t=Ze();return ne===null?t.memoizedState=e:a0(t,ne.memoizedState,e)},useTransition:function(){var e=Ma(Ci)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Xp,useSyncExternalStore:Yp,useId:l0,unstable_isNewReconciler:!1};function tt(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Rl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var As={isMounted:function(e){return(e=e._reactInternals)?Dn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ce(),i=Qt(e),o=Ct(r,i);o.payload=t,n!=null&&(o.callback=n),t=Xt(e,o,i),t!==null&&(ot(t,e,i,r),No(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ce(),i=Qt(e),o=Ct(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Xt(e,o,i),t!==null&&(ot(t,e,i,r),No(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ce(),r=Qt(e),i=Ct(n,r);i.tag=2,t!=null&&(i.callback=t),t=Xt(e,i,r),t!==null&&(ot(t,e,r,n),No(t,e,r))}};function zf(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!vi(n,r)||!vi(i,o):!0}function d0(e,t,n){var r=!1,i=en,o=t.contextType;return typeof o=="object"&&o!==null?o=Qe(o):(i=Ae(t)?Ln:ye.current,r=t.contextTypes,o=(r=r!=null)?mr(e,i):en),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=As,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Gf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&As.enqueueReplaceState(t,t.state,null)}function Ol(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},tc(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Qe(o):(o=Ae(t)?Ln:ye.current,i.context=mr(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Rl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&As.enqueueReplaceState(i,i.state,null),rs(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function _r(e,t){try{var n="",r=t;do n+=Ig(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Da(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function bl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ev=typeof WeakMap=="function"?WeakMap:Map;function h0(e,t,n){n=Ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ls||(ls=!0,Gl=r),bl(e,t)},n}function p0(e,t,n){n=Ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){bl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){bl(e,t),typeof r!="function"&&(Yt===null?Yt=new Set([this]):Yt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Vf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ev;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=pv.bind(null,e,t,n),t.then(e,e))}function Wf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Xf(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ct(-1,1),t.tag=2,Xt(n,t,1))),n.lanes|=1),e)}var tv=Rt.ReactCurrentOwner,Pe=!1;function Ee(e,t,n,r){t.child=e===null?zp(t,null,n,r):yr(t,e.child,n,r)}function Yf(e,t,n,r,i){n=n.render;var o=t.ref;return sr(t,i),r=sc(e,t,n,r,o,i),n=ac(),e!==null&&!Pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,At(e,t,i)):(X&&n&&Yu(t),t.flags|=1,Ee(e,t,r,i),t.child)}function Qf(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!yc(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,m0(e,t,o,r,i)):(e=Do(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:vi,n(s,r)&&e.ref===t.ref)return At(e,t,i)}return t.flags|=1,e=Zt(o,r),e.ref=t.ref,e.return=t,t.child=e}function m0(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(vi(o,r)&&e.ref===t.ref)if(Pe=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Pe=!0);else return t.lanes=e.lanes,At(e,t,i)}return Ml(e,t,n,r,i)}function g0(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},z(tr,Oe),Oe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,z(tr,Oe),Oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,z(tr,Oe),Oe|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,z(tr,Oe),Oe|=r;return Ee(e,t,i,n),t.child}function y0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ml(e,t,n,r,i){var o=Ae(n)?Ln:ye.current;return o=mr(t,o),sr(t,i),n=sc(e,t,n,r,o,i),r=ac(),e!==null&&!Pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,At(e,t,i)):(X&&r&&Yu(t),t.flags|=1,Ee(e,t,n,i),t.child)}function Zf(e,t,n,r,i){if(Ae(n)){var o=!0;qo(t)}else o=!1;if(sr(t,i),t.stateNode===null)Oo(e,t),d0(t,n,r),Ol(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Qe(u):(u=Ae(n)?Ln:ye.current,u=mr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Gf(t,s,r,u),Mt=!1;var d=t.memoizedState;s.state=d,rs(t,r,s,i),l=t.memoizedState,a!==r||d!==l||Ne.current||Mt?(typeof c=="function"&&(Rl(t,n,c,r),l=t.memoizedState),(a=Mt||zf(t,n,a,r,d,l,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Vp(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:tt(t.type,a),s.props=u,f=t.pendingProps,d=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Qe(l):(l=Ae(n)?Ln:ye.current,l=mr(t,l));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||d!==l)&&Gf(t,s,r,l),Mt=!1,d=t.memoizedState,s.state=d,rs(t,r,s,i);var y=t.memoizedState;a!==f||d!==y||Ne.current||Mt?(typeof m=="function"&&(Rl(t,n,m,r),y=t.memoizedState),(u=Mt||zf(t,n,u,r,d,y,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Dl(e,t,n,r,o,i)}function Dl(e,t,n,r,i,o){y0(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Mf(t,n,!1),At(e,t,o);r=t.stateNode,tv.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=yr(t,e.child,null,o),t.child=yr(t,null,a,o)):Ee(e,t,a,o),t.memoizedState=r.state,i&&Mf(t,n,!0),t.child}function v0(e){var t=e.stateNode;t.pendingContext?bf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&bf(e,t.context,!1),nc(e,t.containerInfo)}function Kf(e,t,n,r,i){return gr(),Zu(i),t.flags|=256,Ee(e,t,n,r),t.child}var Fl={dehydrated:null,treeContext:null,retryLane:0};function $l(e){return{baseLanes:e,cachePool:null,transitions:null}}function _0(e,t,n){var r=t.pendingProps,i=Q.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),z(Q,i&1),e===null)return Nl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=bs(s,r,0,null),e=Sn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=$l(n),t.memoizedState=Fl,e):cc(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return nv(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Zt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=Zt(a,o):(o=Sn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?$l(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Fl,r}return o=e.child,e=o.sibling,r=Zt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function cc(e,t){return t=bs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function uo(e,t,n,r){return r!==null&&Zu(r),yr(t,e.child,null,n),e=cc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function nv(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Da(Error(E(422))),uo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=bs({mode:"visible",children:r.children},i,0,null),o=Sn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&yr(t,e.child,null,s),t.child.memoizedState=$l(s),t.memoizedState=Fl,o);if(!(t.mode&1))return uo(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(E(419)),r=Da(o,r,void 0),uo(e,t,s,r)}if(a=(s&e.childLanes)!==0,Pe||a){if(r=ae,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,Nt(e,i),ot(r,e,i,-1))}return gc(),r=Da(Error(E(421))),uo(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=mv.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,be=Wt(i.nextSibling),Fe=t,X=!0,rt=null,e!==null&&(Ge[Ve++]=Lt,Ge[Ve++]=xt,Ge[Ve++]=xn,Lt=e.id,xt=e.overflow,xn=t),t=cc(t,r.children),t.flags|=4096,t)}function qf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Al(e.return,t,n)}function Fa(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function E0(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ee(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&qf(e,n,t);else if(e.tag===19)qf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(z(Q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&is(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Fa(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&is(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Fa(t,!0,n,null,o);break;case"together":Fa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Oo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function At(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(E(153));if(t.child!==null){for(e=t.child,n=Zt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Zt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function rv(e,t,n){switch(t.tag){case 3:v0(t),gr();break;case 5:Wp(t);break;case 1:Ae(t.type)&&qo(t);break;case 4:nc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;z(ts,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(z(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?_0(e,t,n):(z(Q,Q.current&1),e=At(e,t,n),e!==null?e.sibling:null);z(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return E0(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),z(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,g0(e,t,n)}return At(e,t,n)}var S0,Hl,w0,L0;S0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Hl=function(){};w0=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,yn(mt.current);var o=null;switch(n){case"input":i=al(e,i),r=al(e,r),o=[];break;case"select":i=K({},i,{value:void 0}),r=K({},r,{value:void 0}),o=[];break;case"textarea":i=cl(e,i),r=cl(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Zo)}dl(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(fi.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(fi.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&G("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};L0=function(e,t,n,r){n!==r&&(t.flags|=4)};function Vr(e,t){if(!X)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function iv(e,t,n){var r=t.pendingProps;switch(Qu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Ae(t.type)&&Ko(),pe(t),null;case 3:return r=t.stateNode,vr(),W(Ne),W(ye),ic(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ao(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,rt!==null&&(Xl(rt),rt=null))),Hl(e,t),pe(t),null;case 5:rc(t);var i=yn(Li.current);if(n=t.type,e!==null&&t.stateNode!=null)w0(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(E(166));return pe(t),null}if(e=yn(mt.current),ao(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[dt]=t,r[Si]=o,e=(t.mode&1)!==0,n){case"dialog":G("cancel",r),G("close",r);break;case"iframe":case"object":case"embed":G("load",r);break;case"video":case"audio":for(i=0;i<qr.length;i++)G(qr[i],r);break;case"source":G("error",r);break;case"img":case"image":case"link":G("error",r),G("load",r);break;case"details":G("toggle",r);break;case"input":af(r,o),G("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},G("invalid",r);break;case"textarea":uf(r,o),G("invalid",r)}dl(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&so(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&so(r.textContent,a,e),i=["children",""+a]):fi.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&G("scroll",r)}switch(n){case"input":qi(r),lf(r,o,!0);break;case"textarea":qi(r),cf(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Zo)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Kh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[dt]=t,e[Si]=r,S0(e,t,!1,!1),t.stateNode=e;e:{switch(s=hl(n,r),n){case"dialog":G("cancel",e),G("close",e),i=r;break;case"iframe":case"object":case"embed":G("load",e),i=r;break;case"video":case"audio":for(i=0;i<qr.length;i++)G(qr[i],e);i=r;break;case"source":G("error",e),i=r;break;case"img":case"image":case"link":G("error",e),G("load",e),i=r;break;case"details":G("toggle",e),i=r;break;case"input":af(e,r),i=al(e,r),G("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=K({},r,{value:void 0}),G("invalid",e);break;case"textarea":uf(e,r),i=cl(e,r),G("invalid",e);break;default:i=r}dl(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?ep(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&qh(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&di(e,l):typeof l=="number"&&di(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(fi.hasOwnProperty(o)?l!=null&&o==="onScroll"&&G("scroll",e):l!=null&&bu(e,o,l,s))}switch(n){case"input":qi(e),lf(e,r,!1);break;case"textarea":qi(e),cf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Jt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?nr(e,!!r.multiple,o,!1):r.defaultValue!=null&&nr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Zo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)L0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(E(166));if(n=yn(Li.current),yn(mt.current),ao(t)){if(r=t.stateNode,n=t.memoizedProps,r[dt]=t,(o=r.nodeValue!==n)&&(e=Fe,e!==null))switch(e.tag){case 3:so(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&so(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[dt]=t,t.stateNode=r}return pe(t),null;case 13:if(W(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(X&&be!==null&&t.mode&1&&!(t.flags&128))Up(),gr(),t.flags|=98560,o=!1;else if(o=ao(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(E(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(E(317));o[dt]=t}else gr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),o=!1}else rt!==null&&(Xl(rt),rt=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?re===0&&(re=3):gc())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return vr(),Hl(e,t),e===null&&_i(t.stateNode.containerInfo),pe(t),null;case 10:return Ju(t.type._context),pe(t),null;case 17:return Ae(t.type)&&Ko(),pe(t),null;case 19:if(W(Q),o=t.memoizedState,o===null)return pe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)Vr(o,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=is(e),s!==null){for(t.flags|=128,Vr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return z(Q,Q.current&1|2),t.child}e=e.sibling}o.tail!==null&&ee()>Er&&(t.flags|=128,r=!0,Vr(o,!1),t.lanes=4194304)}else{if(!r)if(e=is(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Vr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!X)return pe(t),null}else 2*ee()-o.renderingStartTime>Er&&n!==1073741824&&(t.flags|=128,r=!0,Vr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ee(),t.sibling=null,n=Q.current,z(Q,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return mc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Oe&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(E(156,t.tag))}function ov(e,t){switch(Qu(t),t.tag){case 1:return Ae(t.type)&&Ko(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return vr(),W(Ne),W(ye),ic(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return rc(t),null;case 13:if(W(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(E(340));gr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(Q),null;case 4:return vr(),null;case 10:return Ju(t.type._context),null;case 22:case 23:return mc(),null;case 24:return null;default:return null}}var co=!1,me=!1,sv=typeof WeakSet=="function"?WeakSet:Set,x=null;function er(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function Bl(e,t,n){try{n()}catch(r){q(e,t,r)}}var Jf=!1;function av(e,t){if(Ll=Xo,e=kp(),Xu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var m;f!==n||i!==0&&f.nodeType!==3||(a=s+i),f!==o||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===e)break t;if(d===n&&++u===i&&(a=s),d===o&&++c===r&&(l=s),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(xl={focusedElem:e,selectionRange:n},Xo=!1,x=t;x!==null;)if(t=x,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,x=e;else for(;x!==null;){t=x;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?v:tt(t.type,v),S);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(_){q(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,x=e;break}x=t.return}return y=Jf,Jf=!1,y}function si(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Bl(t,n,o)}i=i.next}while(i!==r)}}function Rs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ul(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function x0(e){var t=e.alternate;t!==null&&(e.alternate=null,x0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[dt],delete t[Si],delete t[Il],delete t[zy],delete t[Gy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function C0(e){return e.tag===5||e.tag===3||e.tag===4}function ed(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||C0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function jl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Zo));else if(r!==4&&(e=e.child,e!==null))for(jl(e,t,n),e=e.sibling;e!==null;)jl(e,t,n),e=e.sibling}function zl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(zl(e,t,n),e=e.sibling;e!==null;)zl(e,t,n),e=e.sibling}var ue=null,nt=!1;function Ot(e,t,n){for(n=n.child;n!==null;)T0(e,t,n),n=n.sibling}function T0(e,t,n){if(pt&&typeof pt.onCommitFiberUnmount=="function")try{pt.onCommitFiberUnmount(xs,n)}catch{}switch(n.tag){case 5:me||er(n,t);case 6:var r=ue,i=nt;ue=null,Ot(e,t,n),ue=r,nt=i,ue!==null&&(nt?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(nt?(e=ue,n=n.stateNode,e.nodeType===8?Na(e.parentNode,n):e.nodeType===1&&Na(e,n),gi(e)):Na(ue,n.stateNode));break;case 4:r=ue,i=nt,ue=n.stateNode.containerInfo,nt=!0,Ot(e,t,n),ue=r,nt=i;break;case 0:case 11:case 14:case 15:if(!me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Bl(n,t,s),i=i.next}while(i!==r)}Ot(e,t,n);break;case 1:if(!me&&(er(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){q(n,t,a)}Ot(e,t,n);break;case 21:Ot(e,t,n);break;case 22:n.mode&1?(me=(r=me)||n.memoizedState!==null,Ot(e,t,n),me=r):Ot(e,t,n);break;default:Ot(e,t,n)}}function td(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new sv),t.forEach(function(r){var i=gv.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function et(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:ue=a.stateNode,nt=!1;break e;case 3:ue=a.stateNode.containerInfo,nt=!0;break e;case 4:ue=a.stateNode.containerInfo,nt=!0;break e}a=a.return}if(ue===null)throw Error(E(160));T0(o,s,i),ue=null,nt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){q(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)I0(t,e),t=t.sibling}function I0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(et(t,e),ct(e),r&4){try{si(3,e,e.return),Rs(3,e)}catch(v){q(e,e.return,v)}try{si(5,e,e.return)}catch(v){q(e,e.return,v)}}break;case 1:et(t,e),ct(e),r&512&&n!==null&&er(n,n.return);break;case 5:if(et(t,e),ct(e),r&512&&n!==null&&er(n,n.return),e.flags&32){var i=e.stateNode;try{di(i,"")}catch(v){q(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&Qh(i,o),hl(a,s);var u=hl(a,o);for(s=0;s<l.length;s+=2){var c=l[s],f=l[s+1];c==="style"?ep(i,f):c==="dangerouslySetInnerHTML"?qh(i,f):c==="children"?di(i,f):bu(i,c,f,u)}switch(a){case"input":ll(i,o);break;case"textarea":Zh(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var m=o.value;m!=null?nr(i,!!o.multiple,m,!1):d!==!!o.multiple&&(o.defaultValue!=null?nr(i,!!o.multiple,o.defaultValue,!0):nr(i,!!o.multiple,o.multiple?[]:"",!1))}i[Si]=o}catch(v){q(e,e.return,v)}}break;case 6:if(et(t,e),ct(e),r&4){if(e.stateNode===null)throw Error(E(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){q(e,e.return,v)}}break;case 3:if(et(t,e),ct(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{gi(t.containerInfo)}catch(v){q(e,e.return,v)}break;case 4:et(t,e),ct(e);break;case 13:et(t,e),ct(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(hc=ee())),r&4&&td(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(me=(u=me)||c,et(t,e),me=u):et(t,e),ct(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(x=e,c=e.child;c!==null;){for(f=x=c;x!==null;){switch(d=x,m=d.child,d.tag){case 0:case 11:case 14:case 15:si(4,d,d.return);break;case 1:er(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){q(r,n,v)}}break;case 5:er(d,d.return);break;case 22:if(d.memoizedState!==null){rd(f);continue}}m!==null?(m.return=d,x=m):rd(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Jh("display",s))}catch(v){q(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){q(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:et(t,e),ct(e),r&4&&td(e);break;case 21:break;default:et(t,e),ct(e)}}function ct(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(C0(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(di(i,""),r.flags&=-33);var o=ed(e);zl(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=ed(e);jl(e,a,s);break;default:throw Error(E(161))}}catch(l){q(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function lv(e,t,n){x=e,k0(e)}function k0(e,t,n){for(var r=(e.mode&1)!==0;x!==null;){var i=x,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||co;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||me;a=co;var u=me;if(co=s,(me=l)&&!u)for(x=i;x!==null;)s=x,l=s.child,s.tag===22&&s.memoizedState!==null?id(i):l!==null?(l.return=s,x=l):id(i);for(;o!==null;)x=o,k0(o),o=o.sibling;x=i,co=a,me=u}nd(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,x=o):nd(e)}}function nd(e){for(;x!==null;){var t=x;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:me||Rs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!me)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:tt(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Bf(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Bf(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&gi(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}me||t.flags&512&&Ul(t)}catch(d){q(t,t.return,d)}}if(t===e){x=null;break}if(n=t.sibling,n!==null){n.return=t.return,x=n;break}x=t.return}}function rd(e){for(;x!==null;){var t=x;if(t===e){x=null;break}var n=t.sibling;if(n!==null){n.return=t.return,x=n;break}x=t.return}}function id(e){for(;x!==null;){var t=x;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Rs(4,t)}catch(l){q(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){q(t,i,l)}}var o=t.return;try{Ul(t)}catch(l){q(t,o,l)}break;case 5:var s=t.return;try{Ul(t)}catch(l){q(t,s,l)}}}catch(l){q(t,t.return,l)}if(t===e){x=null;break}var a=t.sibling;if(a!==null){a.return=t.return,x=a;break}x=t.return}}var uv=Math.ceil,as=Rt.ReactCurrentDispatcher,fc=Rt.ReactCurrentOwner,Ye=Rt.ReactCurrentBatchConfig,D=0,ae=null,te=null,ce=0,Oe=0,tr=on(0),re=0,Ii=null,Tn=0,Os=0,dc=0,ai=null,ke=null,hc=0,Er=1/0,_t=null,ls=!1,Gl=null,Yt=null,fo=!1,jt=null,us=0,li=0,Vl=null,bo=-1,Mo=0;function Ce(){return D&6?ee():bo!==-1?bo:bo=ee()}function Qt(e){return e.mode&1?D&2&&ce!==0?ce&-ce:Wy.transition!==null?(Mo===0&&(Mo=dp()),Mo):(e=$,e!==0||(e=window.event,e=e===void 0?16:_p(e.type)),e):1}function ot(e,t,n,r){if(50<li)throw li=0,Vl=null,Error(E(185));Di(e,n,r),(!(D&2)||e!==ae)&&(e===ae&&(!(D&2)&&(Os|=n),re===4&&Ht(e,ce)),Re(e,r),n===1&&D===0&&!(t.mode&1)&&(Er=ee()+500,Ps&&sn()))}function Re(e,t){var n=e.callbackNode;Wg(e,t);var r=Wo(e,e===ae?ce:0);if(r===0)n!==null&&hf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&hf(n),t===1)e.tag===0?Vy(od.bind(null,e)):$p(od.bind(null,e)),Uy(function(){!(D&6)&&sn()}),n=null;else{switch(hp(r)){case 1:n=Hu;break;case 4:n=cp;break;case 16:n=Vo;break;case 536870912:n=fp;break;default:n=Vo}n=D0(n,P0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function P0(e,t){if(bo=-1,Mo=0,D&6)throw Error(E(327));var n=e.callbackNode;if(ar()&&e.callbackNode!==n)return null;var r=Wo(e,e===ae?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=cs(e,r);else{t=r;var i=D;D|=2;var o=A0();(ae!==e||ce!==t)&&(_t=null,Er=ee()+500,En(e,t));do try{dv();break}catch(a){N0(e,a)}while(!0);qu(),as.current=o,D=i,te!==null?t=0:(ae=null,ce=0,t=re)}if(t!==0){if(t===2&&(i=vl(e),i!==0&&(r=i,t=Wl(e,i))),t===1)throw n=Ii,En(e,0),Ht(e,r),Re(e,ee()),n;if(t===6)Ht(e,r);else{if(i=e.current.alternate,!(r&30)&&!cv(i)&&(t=cs(e,r),t===2&&(o=vl(e),o!==0&&(r=o,t=Wl(e,o))),t===1))throw n=Ii,En(e,0),Ht(e,r),Re(e,ee()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(E(345));case 2:pn(e,ke,_t);break;case 3:if(Ht(e,r),(r&130023424)===r&&(t=hc+500-ee(),10<t)){if(Wo(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ce(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Tl(pn.bind(null,e,ke,_t),t);break}pn(e,ke,_t);break;case 4:if(Ht(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-it(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*uv(r/1960))-r,10<r){e.timeoutHandle=Tl(pn.bind(null,e,ke,_t),r);break}pn(e,ke,_t);break;case 5:pn(e,ke,_t);break;default:throw Error(E(329))}}}return Re(e,ee()),e.callbackNode===n?P0.bind(null,e):null}function Wl(e,t){var n=ai;return e.current.memoizedState.isDehydrated&&(En(e,t).flags|=256),e=cs(e,t),e!==2&&(t=ke,ke=n,t!==null&&Xl(t)),e}function Xl(e){ke===null?ke=e:ke.push.apply(ke,e)}function cv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!at(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ht(e,t){for(t&=~dc,t&=~Os,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function od(e){if(D&6)throw Error(E(327));ar();var t=Wo(e,0);if(!(t&1))return Re(e,ee()),null;var n=cs(e,t);if(e.tag!==0&&n===2){var r=vl(e);r!==0&&(t=r,n=Wl(e,r))}if(n===1)throw n=Ii,En(e,0),Ht(e,t),Re(e,ee()),n;if(n===6)throw Error(E(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,pn(e,ke,_t),Re(e,ee()),null}function pc(e,t){var n=D;D|=1;try{return e(t)}finally{D=n,D===0&&(Er=ee()+500,Ps&&sn())}}function In(e){jt!==null&&jt.tag===0&&!(D&6)&&ar();var t=D;D|=1;var n=Ye.transition,r=$;try{if(Ye.transition=null,$=1,e)return e()}finally{$=r,Ye.transition=n,D=t,!(D&6)&&sn()}}function mc(){Oe=tr.current,W(tr)}function En(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,By(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(Qu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ko();break;case 3:vr(),W(Ne),W(ye),ic();break;case 5:rc(r);break;case 4:vr();break;case 13:W(Q);break;case 19:W(Q);break;case 10:Ju(r.type._context);break;case 22:case 23:mc()}n=n.return}if(ae=e,te=e=Zt(e.current,null),ce=Oe=t,re=0,Ii=null,dc=Os=Tn=0,ke=ai=null,gn!==null){for(t=0;t<gn.length;t++)if(n=gn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}gn=null}return e}function N0(e,t){do{var n=te;try{if(qu(),Ao.current=ss,os){for(var r=Z.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}os=!1}if(Cn=0,oe=ne=Z=null,oi=!1,xi=0,fc.current=null,n===null||n.return===null){re=1,Ii=t,te=null;break}e:{var o=e,s=n.return,a=n,l=t;if(t=ce,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=Wf(s);if(m!==null){m.flags&=-257,Xf(m,s,a,o,t),m.mode&1&&Vf(o,u,t),t=m,l=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(l),t.updateQueue=v}else y.add(l);break e}else{if(!(t&1)){Vf(o,u,t),gc();break e}l=Error(E(426))}}else if(X&&a.mode&1){var S=Wf(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Xf(S,s,a,o,t),Zu(_r(l,a));break e}}o=l=_r(l,a),re!==4&&(re=2),ai===null?ai=[o]:ai.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=h0(o,l,t);Hf(o,p);break e;case 1:a=l;var h=o.type,g=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Yt===null||!Yt.has(g)))){o.flags|=65536,t&=-t,o.lanes|=t;var _=p0(o,a,t);Hf(o,_);break e}}o=o.return}while(o!==null)}O0(n)}catch(w){t=w,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function A0(){var e=as.current;return as.current=ss,e===null?ss:e}function gc(){(re===0||re===3||re===2)&&(re=4),ae===null||!(Tn&268435455)&&!(Os&268435455)||Ht(ae,ce)}function cs(e,t){var n=D;D|=2;var r=A0();(ae!==e||ce!==t)&&(_t=null,En(e,t));do try{fv();break}catch(i){N0(e,i)}while(!0);if(qu(),D=n,as.current=r,te!==null)throw Error(E(261));return ae=null,ce=0,re}function fv(){for(;te!==null;)R0(te)}function dv(){for(;te!==null&&!Fg();)R0(te)}function R0(e){var t=M0(e.alternate,e,Oe);e.memoizedProps=e.pendingProps,t===null?O0(e):te=t,fc.current=null}function O0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ov(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,te=null;return}}else if(n=iv(n,t,Oe),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);re===0&&(re=5)}function pn(e,t,n){var r=$,i=Ye.transition;try{Ye.transition=null,$=1,hv(e,t,n,r)}finally{Ye.transition=i,$=r}return null}function hv(e,t,n,r){do ar();while(jt!==null);if(D&6)throw Error(E(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(E(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Xg(e,o),e===ae&&(te=ae=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||fo||(fo=!0,D0(Vo,function(){return ar(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ye.transition,Ye.transition=null;var s=$;$=1;var a=D;D|=4,fc.current=null,av(e,n),I0(n,e),Oy(xl),Xo=!!Ll,xl=Ll=null,e.current=n,lv(n),$g(),D=a,$=s,Ye.transition=o}else e.current=n;if(fo&&(fo=!1,jt=e,us=i),o=e.pendingLanes,o===0&&(Yt=null),Ug(n.stateNode),Re(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ls)throw ls=!1,e=Gl,Gl=null,e;return us&1&&e.tag!==0&&ar(),o=e.pendingLanes,o&1?e===Vl?li++:(li=0,Vl=e):li=0,sn(),null}function ar(){if(jt!==null){var e=hp(us),t=Ye.transition,n=$;try{if(Ye.transition=null,$=16>e?16:e,jt===null)var r=!1;else{if(e=jt,jt=null,us=0,D&6)throw Error(E(331));var i=D;for(D|=4,x=e.current;x!==null;){var o=x,s=o.child;if(x.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(x=u;x!==null;){var c=x;switch(c.tag){case 0:case 11:case 15:si(8,c,o)}var f=c.child;if(f!==null)f.return=c,x=f;else for(;x!==null;){c=x;var d=c.sibling,m=c.return;if(x0(c),c===u){x=null;break}if(d!==null){d.return=m,x=d;break}x=m}}}var y=o.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}x=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,x=s;else e:for(;x!==null;){if(o=x,o.flags&2048)switch(o.tag){case 0:case 11:case 15:si(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,x=p;break e}x=o.return}}var h=e.current;for(x=h;x!==null;){s=x;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,x=g;else e:for(s=h;x!==null;){if(a=x,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Rs(9,a)}}catch(w){q(a,a.return,w)}if(a===s){x=null;break e}var _=a.sibling;if(_!==null){_.return=a.return,x=_;break e}x=a.return}}if(D=i,sn(),pt&&typeof pt.onPostCommitFiberRoot=="function")try{pt.onPostCommitFiberRoot(xs,e)}catch{}r=!0}return r}finally{$=n,Ye.transition=t}}return!1}function sd(e,t,n){t=_r(n,t),t=h0(e,t,1),e=Xt(e,t,1),t=Ce(),e!==null&&(Di(e,1,t),Re(e,t))}function q(e,t,n){if(e.tag===3)sd(e,e,n);else for(;t!==null;){if(t.tag===3){sd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Yt===null||!Yt.has(r))){e=_r(n,e),e=p0(t,e,1),t=Xt(t,e,1),e=Ce(),t!==null&&(Di(t,1,e),Re(t,e));break}}t=t.return}}function pv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ce(),e.pingedLanes|=e.suspendedLanes&n,ae===e&&(ce&n)===n&&(re===4||re===3&&(ce&130023424)===ce&&500>ee()-hc?En(e,0):dc|=n),Re(e,t)}function b0(e,t){t===0&&(e.mode&1?(t=to,to<<=1,!(to&130023424)&&(to=4194304)):t=1);var n=Ce();e=Nt(e,t),e!==null&&(Di(e,t,n),Re(e,n))}function mv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),b0(e,n)}function gv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(t),b0(e,n)}var M0;M0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)Pe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Pe=!1,rv(e,t,n);Pe=!!(e.flags&131072)}else Pe=!1,X&&t.flags&1048576&&Hp(t,es,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Oo(e,t),e=t.pendingProps;var i=mr(t,ye.current);sr(t,n),i=sc(null,t,r,e,i,n);var o=ac();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ae(r)?(o=!0,qo(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,tc(t),i.updater=As,t.stateNode=i,i._reactInternals=t,Ol(t,r,e,n),t=Dl(null,t,r,!0,o,n)):(t.tag=0,X&&o&&Yu(t),Ee(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Oo(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=vv(r),e=tt(r,e),i){case 0:t=Ml(null,t,r,e,n);break e;case 1:t=Zf(null,t,r,e,n);break e;case 11:t=Yf(null,t,r,e,n);break e;case 14:t=Qf(null,t,r,tt(r.type,e),n);break e}throw Error(E(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:tt(r,i),Ml(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:tt(r,i),Zf(e,t,r,i,n);case 3:e:{if(v0(t),e===null)throw Error(E(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Vp(e,t),rs(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=_r(Error(E(423)),t),t=Kf(e,t,r,n,i);break e}else if(r!==i){i=_r(Error(E(424)),t),t=Kf(e,t,r,n,i);break e}else for(be=Wt(t.stateNode.containerInfo.firstChild),Fe=t,X=!0,rt=null,n=zp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(gr(),r===i){t=At(e,t,n);break e}Ee(e,t,r,n)}t=t.child}return t;case 5:return Wp(t),e===null&&Nl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Cl(r,i)?s=null:o!==null&&Cl(r,o)&&(t.flags|=32),y0(e,t),Ee(e,t,s,n),t.child;case 6:return e===null&&Nl(t),null;case 13:return _0(e,t,n);case 4:return nc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=yr(t,null,r,n):Ee(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:tt(r,i),Yf(e,t,r,i,n);case 7:return Ee(e,t,t.pendingProps,n),t.child;case 8:return Ee(e,t,t.pendingProps.children,n),t.child;case 12:return Ee(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,z(ts,r._currentValue),r._currentValue=s,o!==null)if(at(o.value,s)){if(o.children===i.children&&!Ne.current){t=At(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=Ct(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Al(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(E(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Al(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ee(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,sr(t,n),i=Qe(i),r=r(i),t.flags|=1,Ee(e,t,r,n),t.child;case 14:return r=t.type,i=tt(r,t.pendingProps),i=tt(r.type,i),Qf(e,t,r,i,n);case 15:return m0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:tt(r,i),Oo(e,t),t.tag=1,Ae(r)?(e=!0,qo(t)):e=!1,sr(t,n),d0(t,r,i),Ol(t,r,i,n),Dl(null,t,r,!0,e,n);case 19:return E0(e,t,n);case 22:return g0(e,t,n)}throw Error(E(156,t.tag))};function D0(e,t){return up(e,t)}function yv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xe(e,t,n,r){return new yv(e,t,n,r)}function yc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function vv(e){if(typeof e=="function")return yc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Du)return 11;if(e===Fu)return 14}return 2}function Zt(e,t){var n=e.alternate;return n===null?(n=Xe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Do(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")yc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Vn:return Sn(n.children,i,o,t);case Mu:s=8,i|=8;break;case rl:return e=Xe(12,n,t,i|2),e.elementType=rl,e.lanes=o,e;case il:return e=Xe(13,n,t,i),e.elementType=il,e.lanes=o,e;case ol:return e=Xe(19,n,t,i),e.elementType=ol,e.lanes=o,e;case Wh:return bs(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Gh:s=10;break e;case Vh:s=9;break e;case Du:s=11;break e;case Fu:s=14;break e;case bt:s=16,r=null;break e}throw Error(E(130,e==null?e:typeof e,""))}return t=Xe(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Sn(e,t,n,r){return e=Xe(7,e,r,t),e.lanes=n,e}function bs(e,t,n,r){return e=Xe(22,e,r,t),e.elementType=Wh,e.lanes=n,e.stateNode={isHidden:!1},e}function $a(e,t,n){return e=Xe(6,e,null,t),e.lanes=n,e}function Ha(e,t,n){return t=Xe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function _v(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=_a(0),this.expirationTimes=_a(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_a(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function vc(e,t,n,r,i,o,s,a,l){return e=new _v(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Xe(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},tc(o),e}function Ev(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Gn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function F0(e){if(!e)return en;e=e._reactInternals;e:{if(Dn(e)!==e||e.tag!==1)throw Error(E(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ae(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(E(171))}if(e.tag===1){var n=e.type;if(Ae(n))return Fp(e,n,t)}return t}function $0(e,t,n,r,i,o,s,a,l){return e=vc(n,r,!0,e,i,o,s,a,l),e.context=F0(null),n=e.current,r=Ce(),i=Qt(n),o=Ct(r,i),o.callback=t??null,Xt(n,o,i),e.current.lanes=i,Di(e,i,r),Re(e,r),e}function Ms(e,t,n,r){var i=t.current,o=Ce(),s=Qt(i);return n=F0(n),t.context===null?t.context=n:t.pendingContext=n,t=Ct(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Xt(i,t,s),e!==null&&(ot(e,i,s,o),No(e,i,s)),s}function fs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ad(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function _c(e,t){ad(e,t),(e=e.alternate)&&ad(e,t)}function Sv(){return null}var H0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ec(e){this._internalRoot=e}Ds.prototype.render=Ec.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(E(409));Ms(e,t,null,null)};Ds.prototype.unmount=Ec.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;In(function(){Ms(null,e,null,null)}),t[Pt]=null}};function Ds(e){this._internalRoot=e}Ds.prototype.unstable_scheduleHydration=function(e){if(e){var t=gp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$t.length&&t!==0&&t<$t[n].priority;n++);$t.splice(n,0,e),n===0&&vp(e)}};function Sc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Fs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ld(){}function wv(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=fs(s);o.call(u)}}var s=$0(t,r,e,0,null,!1,!1,"",ld);return e._reactRootContainer=s,e[Pt]=s.current,_i(e.nodeType===8?e.parentNode:e),In(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=fs(l);a.call(u)}}var l=vc(e,0,!1,null,null,!1,!1,"",ld);return e._reactRootContainer=l,e[Pt]=l.current,_i(e.nodeType===8?e.parentNode:e),In(function(){Ms(t,l,n,r)}),l}function $s(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=fs(s);a.call(l)}}Ms(t,s,e,i)}else s=wv(n,t,e,i,r);return fs(s)}pp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Kr(t.pendingLanes);n!==0&&(Bu(t,n|1),Re(t,ee()),!(D&6)&&(Er=ee()+500,sn()))}break;case 13:In(function(){var r=Nt(e,1);if(r!==null){var i=Ce();ot(r,e,1,i)}}),_c(e,1)}};Uu=function(e){if(e.tag===13){var t=Nt(e,134217728);if(t!==null){var n=Ce();ot(t,e,134217728,n)}_c(e,134217728)}};mp=function(e){if(e.tag===13){var t=Qt(e),n=Nt(e,t);if(n!==null){var r=Ce();ot(n,e,t,r)}_c(e,t)}};gp=function(){return $};yp=function(e,t){var n=$;try{return $=e,t()}finally{$=n}};ml=function(e,t,n){switch(t){case"input":if(ll(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=ks(r);if(!i)throw Error(E(90));Yh(r),ll(r,i)}}}break;case"textarea":Zh(e,n);break;case"select":t=n.value,t!=null&&nr(e,!!n.multiple,t,!1)}};rp=pc;ip=In;var Lv={usingClientEntryPoint:!1,Events:[$i,Qn,ks,tp,np,pc]},Wr={findFiberByHostInstance:mn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},xv={bundleType:Wr.bundleType,version:Wr.version,rendererPackageName:Wr.rendererPackageName,rendererConfig:Wr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Rt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ap(e),e===null?null:e.stateNode},findFiberByHostInstance:Wr.findFiberByHostInstance||Sv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ho=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ho.isDisabled&&ho.supportsFiber)try{xs=ho.inject(xv),pt=ho}catch{}}Be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Lv;Be.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Sc(t))throw Error(E(200));return Ev(e,t,null,n)};Be.createRoot=function(e,t){if(!Sc(e))throw Error(E(299));var n=!1,r="",i=H0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=vc(e,1,!1,null,null,n,!1,r,i),e[Pt]=t.current,_i(e.nodeType===8?e.parentNode:e),new Ec(t)};Be.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(E(188)):(e=Object.keys(e).join(","),Error(E(268,e)));return e=ap(t),e=e===null?null:e.stateNode,e};Be.flushSync=function(e){return In(e)};Be.hydrate=function(e,t,n){if(!Fs(t))throw Error(E(200));return $s(null,e,t,!0,n)};Be.hydrateRoot=function(e,t,n){if(!Sc(e))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=H0;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=$0(t,null,e,1,n??null,i,!1,o,s),e[Pt]=t.current,_i(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Ds(t)};Be.render=function(e,t,n){if(!Fs(t))throw Error(E(200));return $s(null,e,t,!1,n)};Be.unmountComponentAtNode=function(e){if(!Fs(e))throw Error(E(40));return e._reactRootContainer?(In(function(){$s(null,null,e,!1,function(){e._reactRootContainer=null,e[Pt]=null})}),!0):!1};Be.unstable_batchedUpdates=pc;Be.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Fs(n))throw Error(E(200));if(e==null||e._reactInternals===void 0)throw Error(E(38));return $s(e,t,n,!1,r)};Be.version="18.3.1-next-f1338f8080-20240426";function B0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(B0)}catch(e){console.error(e)}}B0(),Bh.exports=Be;var Cv=Bh.exports,U0,ud=Cv;U0=ud.createRoot,ud.hydrateRoot;process.platform;process.platform;process.platform;process.type;process.type;var Yl=function(e,t){return Yl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])},Yl(e,t)};function Ke(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Yl(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}var I=function(){return I=Object.assign||function(t){for(var n,r=1,i=arguments.length;r<i;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},I.apply(this,arguments)};function Hs(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}function Le(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,o;r<i;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}function Se(e,t){var n=t&&t.cache?t.cache:Av,r=t&&t.serializer?t.serializer:Nv,i=t&&t.strategy?t.strategy:Iv;return i(e,{cache:n,serializer:r})}function Tv(e){return e==null||typeof e=="number"||typeof e=="boolean"}function j0(e,t,n,r){var i=Tv(r)?r:n(r),o=t.get(i);return typeof o>"u"&&(o=e.call(this,r),t.set(i,o)),o}function z0(e,t,n){var r=Array.prototype.slice.call(arguments,3),i=n(r),o=t.get(i);return typeof o>"u"&&(o=e.apply(this,r),t.set(i,o)),o}function wc(e,t,n,r,i){return n.bind(t,e,r,i)}function Iv(e,t){var n=e.length===1?j0:z0;return wc(e,this,n,t.cache.create(),t.serializer)}function kv(e,t){return wc(e,this,z0,t.cache.create(),t.serializer)}function Pv(e,t){return wc(e,this,j0,t.cache.create(),t.serializer)}var Nv=function(){return JSON.stringify(arguments)};function Lc(){this.cache=Object.create(null)}Lc.prototype.get=function(e){return this.cache[e]};Lc.prototype.set=function(e,t){this.cache[e]=t};var Av={create:function(){return new Lc}},we={variadic:kv,monadic:Pv};function G0(e,t,n){if(n===void 0&&(n=Error),!e)throw new n(t)}Se(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,Le([void 0],t,!1)))},{strategy:we.variadic});Se(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,Le([void 0],t,!1)))},{strategy:we.variadic});Se(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,Le([void 0],t,!1)))},{strategy:we.variadic});Se(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.Locale).bind.apply(e,Le([void 0],t,!1)))},{strategy:we.variadic});Se(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.ListFormat).bind.apply(e,Le([void 0],t,!1)))},{strategy:we.variadic});var b;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(b||(b={}));var V;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(V||(V={}));var Sr;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(Sr||(Sr={}));function cd(e){return e.type===V.literal}function Rv(e){return e.type===V.argument}function V0(e){return e.type===V.number}function W0(e){return e.type===V.date}function X0(e){return e.type===V.time}function Y0(e){return e.type===V.select}function Q0(e){return e.type===V.plural}function Ov(e){return e.type===V.pound}function Z0(e){return e.type===V.tag}function K0(e){return!!(e&&typeof e=="object"&&e.type===Sr.number)}function Ql(e){return!!(e&&typeof e=="object"&&e.type===Sr.dateTime)}var q0=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,bv=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function Mv(e){var t={};return e.replace(bv,function(n){var r=n.length;switch(n[0]){case"G":t.era=r===4?"long":r===5?"narrow":"short";break;case"y":t.year=r===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=r===4?"long":r===5?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var Dv=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Fv(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(Dv).filter(function(d){return d.length>0}),n=[],r=0,i=t;r<i.length;r++){var o=i[r],s=o.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var a=s[0],l=s.slice(1),u=0,c=l;u<c.length;u++){var f=c[u];if(f.length===0)throw new Error("Invalid number skeleton")}n.push({stem:a,options:l})}return n}function $v(e){return e.replace(/^(.*?)-/,"")}var fd=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,J0=/^(@+)?(\+|#+)?[rs]?$/g,Hv=/(\*)(0+)|(#+)(0+)|(0+)/g,em=/^(0+)$/;function dd(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(J0,function(n,r,i){return typeof i!="string"?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):i==="+"?t.minimumSignificantDigits=r.length:r[0]==="#"?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+(typeof i=="string"?i.length:0)),""}),t}function tm(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Bv(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var n=e.slice(0,2);if(n==="+!"?(t.signDisplay="always",e=e.slice(2)):n==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!em.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function hd(e){var t={},n=tm(e);return n||t}function Uv(e){for(var t={},n=0,r=e;n<r.length;n++){var i=r[n];switch(i.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=i.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=$v(i.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=I(I(I({},t),{notation:"scientific"}),i.options.reduce(function(l,u){return I(I({},l),hd(u))},{}));continue;case"engineering":t=I(I(I({},t),{notation:"engineering"}),i.options.reduce(function(l,u){return I(I({},l),hd(u))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(i.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(i.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");i.options[0].replace(Hv,function(l,u,c,f,d,m){if(u)t.minimumIntegerDigits=c.length;else{if(f&&d)throw new Error("We currently do not support maximum integer digits");if(m)throw new Error("We currently do not support exact integer digits")}return""});continue}if(em.test(i.stem)){t.minimumIntegerDigits=i.stem.length;continue}if(fd.test(i.stem)){if(i.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");i.stem.replace(fd,function(l,u,c,f,d,m){return c==="*"?t.minimumFractionDigits=u.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:d&&m?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+m.length):(t.minimumFractionDigits=u.length,t.maximumFractionDigits=u.length),""});var o=i.options[0];o==="w"?t=I(I({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=I(I({},t),dd(o)));continue}if(J0.test(i.stem)){t=I(I({},t),dd(i.stem));continue}var s=tm(i.stem);s&&(t=I(I({},t),s));var a=Bv(i.stem);a&&(t=I(I({},t),a))}return t}var po={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function jv(e,t){for(var n="",r=0;r<e.length;r++){var i=e.charAt(r);if(i==="j"){for(var o=0;r+1<e.length&&e.charAt(r+1)===i;)o++,r++;var s=1+(o&1),a=o<2?1:3+(o>>1),l="a",u=zv(t);for((u=="H"||u=="k")&&(a=0);a-- >0;)n+=l;for(;s-- >0;)n=u+n}else i==="J"?n+="H":n+=i}return n}function zv(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n=e.language,r;n!=="root"&&(r=e.maximize().region);var i=po[r||""]||po[n||""]||po["".concat(n,"-001")]||po["001"];return i[0]}var Ba,Gv=new RegExp("^".concat(q0.source,"*")),Vv=new RegExp("".concat(q0.source,"*$"));function M(e,t){return{start:e,end:t}}var Wv=!!String.prototype.startsWith&&"_a".startsWith("a",1),Xv=!!String.fromCodePoint,Yv=!!Object.fromEntries,Qv=!!String.prototype.codePointAt,Zv=!!String.prototype.trimStart,Kv=!!String.prototype.trimEnd,qv=!!Number.isSafeInteger,Jv=qv?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},Zl=!0;try{var e2=rm("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Zl=((Ba=e2.exec("a"))===null||Ba===void 0?void 0:Ba[0])==="a"}catch{Zl=!1}var pd=Wv?function(t,n,r){return t.startsWith(n,r)}:function(t,n,r){return t.slice(r,r+n.length)===n},Kl=Xv?String.fromCodePoint:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var r="",i=t.length,o=0,s;i>o;){if(s=t[o++],s>1114111)throw RangeError(s+" is not a valid code point");r+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return r},md=Yv?Object.fromEntries:function(t){for(var n={},r=0,i=t;r<i.length;r++){var o=i[r],s=o[0],a=o[1];n[s]=a}return n},nm=Qv?function(t,n){return t.codePointAt(n)}:function(t,n){var r=t.length;if(!(n<0||n>=r)){var i=t.charCodeAt(n),o;return i<55296||i>56319||n+1===r||(o=t.charCodeAt(n+1))<56320||o>57343?i:(i-55296<<10)+(o-56320)+65536}},t2=Zv?function(t){return t.trimStart()}:function(t){return t.replace(Gv,"")},n2=Kv?function(t){return t.trimEnd()}:function(t){return t.replace(Vv,"")};function rm(e,t){return new RegExp(e,t)}var ql;if(Zl){var gd=rm("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");ql=function(t,n){var r;gd.lastIndex=n;var i=gd.exec(t);return(r=i[1])!==null&&r!==void 0?r:""}}else ql=function(t,n){for(var r=[];;){var i=nm(t,n);if(i===void 0||im(i)||s2(i))break;r.push(i),n+=i>=65536?2:1}return Kl.apply(void 0,r)};var r2=function(){function e(t,n){n===void 0&&(n={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!n.ignoreTag,this.locale=n.locale,this.requiresOtherClause=!!n.requiresOtherClause,this.shouldParseSkeletons=!!n.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,n,r){for(var i=[];!this.isEOF();){var o=this.char();if(o===123){var s=this.parseArgument(t,r);if(s.err)return s;i.push(s.val)}else{if(o===125&&t>0)break;if(o===35&&(n==="plural"||n==="selectordinal")){var a=this.clonePosition();this.bump(),i.push({type:V.pound,location:M(a,this.clonePosition())})}else if(o===60&&!this.ignoreTag&&this.peek()===47){if(r)break;return this.error(b.UNMATCHED_CLOSING_TAG,M(this.clonePosition(),this.clonePosition()))}else if(o===60&&!this.ignoreTag&&Jl(this.peek()||0)){var s=this.parseTag(t,n);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(t,n);if(s.err)return s;i.push(s.val)}}}return{val:i,err:null}},e.prototype.parseTag=function(t,n){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:V.literal,value:"<".concat(i,"/>"),location:M(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var o=this.parseMessage(t+1,n,!0);if(o.err)return o;var s=o.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!Jl(this.char()))return this.error(b.INVALID_TAG,M(a,this.clonePosition()));var l=this.clonePosition(),u=this.parseTagName();return i!==u?this.error(b.UNMATCHED_CLOSING_TAG,M(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:V.tag,value:i,children:s,location:M(r,this.clonePosition())},err:null}:this.error(b.INVALID_TAG,M(a,this.clonePosition())))}else return this.error(b.UNCLOSED_TAG,M(r,this.clonePosition()))}else return this.error(b.INVALID_TAG,M(r,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&o2(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,n){for(var r=this.clonePosition(),i="";;){var o=this.tryParseQuote(n);if(o){i+=o;continue}var s=this.tryParseUnquoted(t,n);if(s){i+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){i+=a;continue}break}var l=M(r,this.clonePosition());return{val:{type:V.literal,value:i,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!i2(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var n=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(r===39)if(this.peek()===39)n.push(39),this.bump();else{this.bump();break}else n.push(r);this.bump()}return Kl.apply(void 0,n)},e.prototype.tryParseUnquoted=function(t,n){if(this.isEOF())return null;var r=this.char();return r===60||r===123||r===35&&(n==="plural"||n==="selectordinal")||r===125&&t>0?null:(this.bump(),Kl(r))},e.prototype.parseArgument=function(t,n){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,M(r,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(b.EMPTY_ARGUMENT,M(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(b.MALFORMED_ARGUMENT,M(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,M(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:V.argument,value:i,location:M(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,M(r,this.clonePosition())):this.parseArgumentOptions(t,n,i,r);default:return this.error(b.MALFORMED_ARGUMENT,M(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),n=this.offset(),r=ql(this.message,n),i=n+r.length;this.bumpTo(i);var o=this.clonePosition(),s=M(t,o);return{value:r,location:s}},e.prototype.parseArgumentOptions=function(t,n,r,i){var o,s=this.clonePosition(),a=this.parseIdentifierIfPossible().value,l=this.clonePosition();switch(a){case"":return this.error(b.EXPECT_ARGUMENT_TYPE,M(s,l));case"number":case"date":case"time":{this.bumpSpace();var u=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var d=n2(f.val);if(d.length===0)return this.error(b.EXPECT_ARGUMENT_STYLE,M(this.clonePosition(),this.clonePosition()));var m=M(c,this.clonePosition());u={style:d,styleLocation:m}}var y=this.tryParseArgumentClose(i);if(y.err)return y;var v=M(i,this.clonePosition());if(u&&pd(u==null?void 0:u.style,"::",0)){var S=t2(u.style.slice(2));if(a==="number"){var f=this.parseNumberSkeletonFromString(S,u.styleLocation);return f.err?f:{val:{type:V.number,value:r,location:v,style:f.val},err:null}}else{if(S.length===0)return this.error(b.EXPECT_DATE_TIME_SKELETON,v);var p=S;this.locale&&(p=jv(S,this.locale));var d={type:Sr.dateTime,pattern:p,location:u.styleLocation,parsedOptions:this.shouldParseSkeletons?Mv(p):{}},h=a==="date"?V.date:V.time;return{val:{type:h,value:r,location:v,style:d},err:null}}}return{val:{type:a==="number"?V.number:a==="date"?V.date:V.time,value:r,location:v,style:(o=u==null?void 0:u.style)!==null&&o!==void 0?o:null},err:null}}case"plural":case"selectordinal":case"select":{var g=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(b.EXPECT_SELECT_ARGUMENT_OPTIONS,M(g,I({},g)));this.bumpSpace();var _=this.parseIdentifierIfPossible(),w=0;if(a!=="select"&&_.value==="offset"){if(!this.bumpIf(":"))return this.error(b.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,M(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(b.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,b.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),_=this.parseIdentifierIfPossible(),w=f.val}var L=this.tryParsePluralOrSelectOptions(t,a,n,_);if(L.err)return L;var y=this.tryParseArgumentClose(i);if(y.err)return y;var T=M(i,this.clonePosition());return a==="select"?{val:{type:V.select,value:r,options:md(L.val),location:T},err:null}:{val:{type:V.plural,value:r,options:md(L.val),offset:w,pluralType:a==="plural"?"cardinal":"ordinal",location:T},err:null}}default:return this.error(b.INVALID_ARGUMENT_TYPE,M(s,l))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,M(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,n=this.clonePosition();!this.isEOF();){var r=this.char();switch(r){case 39:{this.bump();var i=this.clonePosition();if(!this.bumpUntil("'"))return this.error(b.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,M(i,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(n.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(n.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,n){var r=[];try{r=Fv(t)}catch{return this.error(b.INVALID_NUMBER_SKELETON,n)}return{val:{type:Sr.number,tokens:r,location:n,parsedOptions:this.shouldParseSkeletons?Uv(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,n,r,i){for(var o,s=!1,a=[],l=new Set,u=i.value,c=i.location;;){if(u.length===0){var f=this.clonePosition();if(n!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(b.EXPECT_PLURAL_ARGUMENT_SELECTOR,b.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=M(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}else break}if(l.has(u))return this.error(n==="select"?b.DUPLICATE_SELECT_ARGUMENT_SELECTOR:b.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);u==="other"&&(s=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error(n==="select"?b.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:b.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,M(this.clonePosition(),this.clonePosition()));var y=this.parseMessage(t+1,n,r);if(y.err)return y;var v=this.tryParseArgumentClose(m);if(v.err)return v;a.push([u,{value:y.val,location:M(m,this.clonePosition())}]),l.add(u),this.bumpSpace(),o=this.parseIdentifierIfPossible(),u=o.value,c=o.location}return a.length===0?this.error(n==="select"?b.EXPECT_SELECT_ARGUMENT_SELECTOR:b.EXPECT_PLURAL_ARGUMENT_SELECTOR,M(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(b.MISSING_OTHER_CLAUSE,M(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(t,n){var r=1,i=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,s=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,s=s*10+(a-48),this.bump();else break}var l=M(i,this.clonePosition());return o?(s*=r,Jv(s)?{val:s,err:null}:this.error(n,l)):this.error(t,l)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var n=nm(this.message,t);if(n===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return n},e.prototype.error=function(t,n){return{val:null,err:{kind:t,message:this.message,location:n}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(pd(this.message,t,this.offset())){for(var n=0;n<t.length;n++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var n=this.offset(),r=this.message.indexOf(t,n);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var n=this.offset();if(n===t)break;if(n>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&im(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),n=this.offset(),r=this.message.charCodeAt(n+(t>=65536?2:1));return r??null},e}();function Jl(e){return e>=97&&e<=122||e>=65&&e<=90}function i2(e){return Jl(e)||e===47}function o2(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function im(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function s2(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function eu(e){e.forEach(function(t){if(delete t.location,Y0(t)||Q0(t))for(var n in t.options)delete t.options[n].location,eu(t.options[n].value);else V0(t)&&K0(t.style)||(W0(t)||X0(t))&&Ql(t.style)?delete t.style.location:Z0(t)&&eu(t.children)})}function a2(e,t){t===void 0&&(t={}),t=I({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var n=new r2(e,t).parse();if(n.err){var r=SyntaxError(b[n.err.kind]);throw r.location=n.err.location,r.originalMessage=n.err.message,r}return t!=null&&t.captureLocation||eu(n.val),n.val}var gt;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(gt||(gt={}));var an=function(e){Ke(t,e);function t(n,r,i){var o=e.call(this,n)||this;return o.code=r,o.originalMessage=i,o}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),yd=function(e){Ke(t,e);function t(n,r,i,o){return e.call(this,'Invalid values for "'.concat(n,'": "').concat(r,'". Options are "').concat(Object.keys(i).join('", "'),'"'),gt.INVALID_VALUE,o)||this}return t}(an),l2=function(e){Ke(t,e);function t(n,r,i){return e.call(this,'Value for "'.concat(n,'" must be of type ').concat(r),gt.INVALID_VALUE,i)||this}return t}(an),u2=function(e){Ke(t,e);function t(n,r){return e.call(this,'The intl string context variable "'.concat(n,'" was not provided to the string "').concat(r,'"'),gt.MISSING_VALUE,r)||this}return t}(an),_e;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(_e||(_e={}));function c2(e){return e.length<2?e:e.reduce(function(t,n){var r=t[t.length-1];return!r||r.type!==_e.literal||n.type!==_e.literal?t.push(n):r.value+=n.value,t},[])}function om(e){return typeof e=="function"}function Fo(e,t,n,r,i,o,s){if(e.length===1&&cd(e[0]))return[{type:_e.literal,value:e[0].value}];for(var a=[],l=0,u=e;l<u.length;l++){var c=u[l];if(cd(c)){a.push({type:_e.literal,value:c.value});continue}if(Ov(c)){typeof o=="number"&&a.push({type:_e.literal,value:n.getNumberFormat(t).format(o)});continue}var f=c.value;if(!(i&&f in i))throw new u2(f,s);var d=i[f];if(Rv(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),a.push({type:typeof d=="string"?_e.literal:_e.object,value:d});continue}if(W0(c)){var m=typeof c.style=="string"?r.date[c.style]:Ql(c.style)?c.style.parsedOptions:void 0;a.push({type:_e.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(X0(c)){var m=typeof c.style=="string"?r.time[c.style]:Ql(c.style)?c.style.parsedOptions:r.time.medium;a.push({type:_e.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(V0(c)){var m=typeof c.style=="string"?r.number[c.style]:K0(c.style)?c.style.parsedOptions:void 0;m&&m.scale&&(d=d*(m.scale||1)),a.push({type:_e.literal,value:n.getNumberFormat(t,m).format(d)});continue}if(Z0(c)){var y=c.children,v=c.value,S=i[v];if(!om(S))throw new l2(v,"function",s);var p=Fo(y,t,n,r,i,o),h=S(p.map(function(w){return w.value}));Array.isArray(h)||(h=[h]),a.push.apply(a,h.map(function(w){return{type:typeof w=="string"?_e.literal:_e.object,value:w}}))}if(Y0(c)){var g=c.options[d]||c.options.other;if(!g)throw new yd(c.value,d,Object.keys(c.options),s);a.push.apply(a,Fo(g.value,t,n,r,i));continue}if(Q0(c)){var g=c.options["=".concat(d)];if(!g){if(!Intl.PluralRules)throw new an(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,gt.MISSING_INTL_API,s);var _=n.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));g=c.options[_]||c.options.other}if(!g)throw new yd(c.value,d,Object.keys(c.options),s);a.push.apply(a,Fo(g.value,t,n,r,i,d-(c.offset||0)));continue}}return c2(a)}function f2(e,t){return t?I(I(I({},e||{}),t||{}),Object.keys(e).reduce(function(n,r){return n[r]=I(I({},e[r]),t[r]||{}),n},{})):e}function d2(e,t){return t?Object.keys(e).reduce(function(n,r){return n[r]=f2(e[r],t[r]),n},I({},e)):e}function Ua(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function h2(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:Se(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.NumberFormat).bind.apply(t,Le([void 0],n,!1)))},{cache:Ua(e.number),strategy:we.variadic}),getDateTimeFormat:Se(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.DateTimeFormat).bind.apply(t,Le([void 0],n,!1)))},{cache:Ua(e.dateTime),strategy:we.variadic}),getPluralRules:Se(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.PluralRules).bind.apply(t,Le([void 0],n,!1)))},{cache:Ua(e.pluralRules),strategy:we.variadic})}}var sm=function(){function e(t,n,r,i){var o=this;if(n===void 0&&(n=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(l){var u=o.formatToParts(l);if(u.length===1)return u[0].value;var c=u.reduce(function(f,d){return!f.length||d.type!==_e.literal||typeof f[f.length-1]!="string"?f.push(d.value):f[f.length-1]+=d.value,f},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(l){return Fo(o.ast,o.locales,o.formatters,o.formats,l,void 0,o.message)},this.resolvedOptions=function(){var l;return{locale:((l=o.resolvedLocale)===null||l===void 0?void 0:l.toString())||Intl.NumberFormat.supportedLocalesOf(o.locales)[0]}},this.getAst=function(){return o.ast},this.locales=n,this.resolvedLocale=e.resolveLocale(n),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=i||{};s.formatters;var a=Hs(s,["formatters"]);this.ast=e.__parse(t,I(I({},a),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=d2(e.formats,r),this.formatters=i&&i.formatters||h2(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var n=Intl.NumberFormat.supportedLocalesOf(t);return n.length>0?new Intl.Locale(n[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=a2,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),kn;(function(e){e.FORMAT_ERROR="FORMAT_ERROR",e.UNSUPPORTED_FORMATTER="UNSUPPORTED_FORMATTER",e.INVALID_CONFIG="INVALID_CONFIG",e.MISSING_DATA="MISSING_DATA",e.MISSING_TRANSLATION="MISSING_TRANSLATION"})(kn||(kn={}));var Bi=function(e){Ke(t,e);function t(n,r,i){var o=this,s=i?i instanceof Error?i:new Error(String(i)):void 0;return o=e.call(this,"[@formatjs/intl Error ".concat(n,"] ").concat(r,`
`).concat(s?`
`.concat(s.message,`
`).concat(s.stack):""))||this,o.code=n,typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(o,t),o}return t}(Error),p2=function(e){Ke(t,e);function t(n,r){return e.call(this,kn.UNSUPPORTED_FORMATTER,n,r)||this}return t}(Bi),m2=function(e){Ke(t,e);function t(n,r){return e.call(this,kn.INVALID_CONFIG,n,r)||this}return t}(Bi),vd=function(e){Ke(t,e);function t(n,r){return e.call(this,kn.MISSING_DATA,n,r)||this}return t}(Bi),qe=function(e){Ke(t,e);function t(n,r,i){var o=e.call(this,kn.FORMAT_ERROR,"".concat(n,`
Locale: `).concat(r,`
`),i)||this;return o.locale=r,o}return t}(Bi),ja=function(e){Ke(t,e);function t(n,r,i,o){var s=e.call(this,"".concat(n,`
MessageID: `).concat(i==null?void 0:i.id,`
Default Message: `).concat(i==null?void 0:i.defaultMessage,`
Description: `).concat(i==null?void 0:i.description,`
`),r,o)||this;return s.descriptor=i,s.locale=r,s}return t}(qe),g2=function(e){Ke(t,e);function t(n,r){var i=e.call(this,kn.MISSING_TRANSLATION,'Missing message: "'.concat(n.id,'" for locale "').concat(r,'", using ').concat(n.defaultMessage?"default message (".concat(typeof n.defaultMessage=="string"?n.defaultMessage:n.defaultMessage.map(function(o){var s;return(s=o.value)!==null&&s!==void 0?s:JSON.stringify(o)}).join(),")"):"id"," as fallback."))||this;return i.descriptor=n,i}return t}(Bi);function Fn(e,t,n){return n===void 0&&(n={}),t.reduce(function(r,i){return i in e?r[i]=e[i]:i in n&&(r[i]=n[i]),r},{})}var y2=function(e){},v2=function(e){},am={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:y2,onWarn:v2};function xc(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function fn(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function _2(e){e===void 0&&(e=xc());var t=Intl.RelativeTimeFormat,n=Intl.ListFormat,r=Intl.DisplayNames,i=Se(function(){for(var a,l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return new((a=Intl.DateTimeFormat).bind.apply(a,Le([void 0],l,!1)))},{cache:fn(e.dateTime),strategy:we.variadic}),o=Se(function(){for(var a,l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return new((a=Intl.NumberFormat).bind.apply(a,Le([void 0],l,!1)))},{cache:fn(e.number),strategy:we.variadic}),s=Se(function(){for(var a,l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return new((a=Intl.PluralRules).bind.apply(a,Le([void 0],l,!1)))},{cache:fn(e.pluralRules),strategy:we.variadic});return{getDateTimeFormat:i,getNumberFormat:o,getMessageFormat:Se(function(a,l,u,c){return new sm(a,l,u,I({formatters:{getNumberFormat:o,getDateTimeFormat:i,getPluralRules:s}},c||{}))},{cache:fn(e.message),strategy:we.variadic}),getRelativeTimeFormat:Se(function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new(t.bind.apply(t,Le([void 0],a,!1)))},{cache:fn(e.relativeTime),strategy:we.variadic}),getPluralRules:s,getListFormat:Se(function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new(n.bind.apply(n,Le([void 0],a,!1)))},{cache:fn(e.list),strategy:we.variadic}),getDisplayNames:Se(function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new(r.bind.apply(r,Le([void 0],a,!1)))},{cache:fn(e.displayNames),strategy:we.variadic})}}function Cc(e,t,n,r){var i=e&&e[t],o;if(i&&(o=i[n]),o)return o;r(new p2("No ".concat(t," format named: ").concat(n)))}function mo(e,t){return Object.keys(e).reduce(function(n,r){return n[r]=I({timeZone:t},e[r]),n},{})}function _d(e,t){var n=Object.keys(I(I({},e),t));return n.reduce(function(r,i){return r[i]=I(I({},e[i]||{}),t[i]||{}),r},{})}function Ed(e,t){if(!t)return e;var n=sm.formats;return I(I(I({},n),e),{date:_d(mo(n.date,t),mo(e.date||{},t)),time:_d(mo(n.time,t),mo(e.time||{},t))})}var tu=function(e,t,n,r,i){var o=e.locale,s=e.formats,a=e.messages,l=e.defaultLocale,u=e.defaultFormats,c=e.fallbackOnEmptyString,f=e.onError,d=e.timeZone,m=e.defaultRichTextElements;n===void 0&&(n={id:""});var y=n.id,v=n.defaultMessage;G0(!!y,"[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");var S=String(y),p=a&&Object.prototype.hasOwnProperty.call(a,S)&&a[S];if(Array.isArray(p)&&p.length===1&&p[0].type===V.literal)return p[0].value;if(!r&&p&&typeof p=="string"&&!m)return p.replace(/'\{(.*?)\}'/gi,"{$1}");if(r=I(I({},m),r||{}),s=Ed(s,d),u=Ed(u,d),!p){if(c===!1&&p==="")return p;if((!v||o&&o.toLowerCase()!==l.toLowerCase())&&f(new g2(n,o)),v)try{var h=t.getMessageFormat(v,l,u,i);return h.format(r)}catch(g){return f(new ja('Error formatting default message for: "'.concat(S,'", rendering default message verbatim'),o,n,g)),typeof v=="string"?v:S}return S}try{var h=t.getMessageFormat(p,o,s,I({formatters:t},i||{}));return h.format(r)}catch(g){f(new ja('Error formatting message: "'.concat(S,'", using ').concat(v?"default message":"id"," as fallback."),o,n,g))}if(v)try{var h=t.getMessageFormat(v,l,u,i);return h.format(r)}catch(g){f(new ja('Error formatting the default message for: "'.concat(S,'", rendering message verbatim'),o,n,g))}return typeof p=="string"?p:typeof v=="string"?v:S},lm=["formatMatcher","timeZone","hour12","weekday","era","year","month","day","hour","minute","second","timeZoneName","hourCycle","dateStyle","timeStyle","calendar","numberingSystem","fractionalSecondDigits"];function Bs(e,t,n,r){var i=e.locale,o=e.formats,s=e.onError,a=e.timeZone;r===void 0&&(r={});var l=r.format,u=I(I({},a&&{timeZone:a}),l&&Cc(o,t,l,s)),c=Fn(r,lm,u);return t==="time"&&!c.hour&&!c.minute&&!c.second&&!c.timeStyle&&!c.dateStyle&&(c=I(I({},c),{hour:"numeric",minute:"numeric"})),n(i,c)}function E2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=o===void 0?{}:o,a=typeof i=="string"?new Date(i||0):i;try{return Bs(e,"date",t,s).format(a)}catch(l){e.onError(new qe("Error formatting date.",e.locale,l))}return String(a)}function S2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=o===void 0?{}:o,a=typeof i=="string"?new Date(i||0):i;try{return Bs(e,"time",t,s).format(a)}catch(l){e.onError(new qe("Error formatting time.",e.locale,l))}return String(a)}function w2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=n[2],a=s===void 0?{}:s,l=e.timeZone,u=e.locale,c=e.onError,f=Fn(a,lm,l?{timeZone:l}:{});try{return t(u,f).formatRange(i,o)}catch(d){c(new qe("Error formatting date time range.",e.locale,d))}return String(i)}function L2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=o===void 0?{}:o,a=typeof i=="string"?new Date(i||0):i;try{return Bs(e,"date",t,s).formatToParts(a)}catch(l){e.onError(new qe("Error formatting date.",e.locale,l))}return[]}function x2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=o===void 0?{}:o,a=typeof i=="string"?new Date(i||0):i;try{return Bs(e,"time",t,s).formatToParts(a)}catch(l){e.onError(new qe("Error formatting time.",e.locale,l))}return[]}var C2=["style","type","fallback","languageDisplay"];function T2(e,t,n,r){var i=e.locale,o=e.onError,s=Intl.DisplayNames;s||o(new an(`Intl.DisplayNames is not available in this environment.
Try polyfilling it using "@formatjs/intl-displaynames"
`,gt.MISSING_INTL_API));var a=Fn(r,C2);try{return t(i,a).of(n)}catch(l){o(new qe("Error formatting display name.",i,l))}}var I2=["type","style"],Sd=Date.now();function k2(e){return"".concat(Sd,"_").concat(e,"_").concat(Sd)}function P2(e,t,n,r){r===void 0&&(r={});var i=um(e,t,n,r).reduce(function(o,s){var a=s.value;return typeof a!="string"?o.push(a):typeof o[o.length-1]=="string"?o[o.length-1]+=a:o.push(a),o},[]);return i.length===1?i[0]:i.length===0?"":i}function um(e,t,n,r){var i=e.locale,o=e.onError;r===void 0&&(r={});var s=Intl.ListFormat;s||o(new an(`Intl.ListFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-listformat"
`,gt.MISSING_INTL_API));var a=Fn(r,I2);try{var l={},u=n.map(function(c,f){if(typeof c=="object"){var d=k2(f);return l[d]=c,d}return String(c)});return t(i,a).formatToParts(u).map(function(c){return c.type==="literal"?c:I(I({},c),{value:l[c.value]||c.value})})}catch(c){o(new qe("Error formatting list.",i,c))}return n}var N2=["type"];function A2(e,t,n,r){var i=e.locale,o=e.onError;r===void 0&&(r={}),Intl.PluralRules||o(new an(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,gt.MISSING_INTL_API));var s=Fn(r,N2);try{return t(i,s).select(n)}catch(a){o(new qe("Error formatting plural.",i,a))}return"other"}var R2=["numeric","style"];function O2(e,t,n){var r=e.locale,i=e.formats,o=e.onError;n===void 0&&(n={});var s=n.format,a=!!s&&Cc(i,"relative",s,o)||{},l=Fn(n,R2,a);return t(r,l)}function b2(e,t,n,r,i){i===void 0&&(i={}),r||(r="second");var o=Intl.RelativeTimeFormat;o||e.onError(new an(`Intl.RelativeTimeFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-relativetimeformat"
`,gt.MISSING_INTL_API));try{return O2(e,t,i).format(n,r)}catch(s){e.onError(new qe("Error formatting relative time.",e.locale,s))}return String(n)}var M2=["style","currency","unit","unitDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","currencyDisplay","currencySign","notation","signDisplay","unit","unitDisplay","numberingSystem","trailingZeroDisplay","roundingPriority","roundingIncrement","roundingMode"];function cm(e,t,n){var r=e.locale,i=e.formats,o=e.onError;n===void 0&&(n={});var s=n.format,a=s&&Cc(i,"number",s,o)||{},l=Fn(n,M2,a);return t(r,l)}function D2(e,t,n,r){r===void 0&&(r={});try{return cm(e,t,r).format(n)}catch(i){e.onError(new qe("Error formatting number.",e.locale,i))}return String(n)}function F2(e,t,n,r){r===void 0&&(r={});try{return cm(e,t,r).formatToParts(n)}catch(i){e.onError(new qe("Error formatting number.",e.locale,i))}return[]}function $2(e){var t=e?e[Object.keys(e)[0]]:void 0;return typeof t=="string"}function H2(e){e.onWarn&&e.defaultRichTextElements&&$2(e.messages||{})&&e.onWarn(`[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. 
Please consider using "@formatjs/cli" to pre-compile your messages for performance.
For more details see https://formatjs.io/docs/getting-started/message-distribution`)}function Tc(e,t){var n=_2(t),r=I(I({},am),e),i=r.locale,o=r.defaultLocale,s=r.onError;return i?!Intl.NumberFormat.supportedLocalesOf(i).length&&s?s(new vd('Missing locale data for locale: "'.concat(i,'" in Intl.NumberFormat. Using default locale: "').concat(o,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):!Intl.DateTimeFormat.supportedLocalesOf(i).length&&s&&s(new vd('Missing locale data for locale: "'.concat(i,'" in Intl.DateTimeFormat. Using default locale: "').concat(o,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):(s&&s(new m2('"locale" was not configured, using "'.concat(o,'" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details'))),r.locale=r.defaultLocale||"en"),H2(r),I(I({},r),{formatters:n,formatNumber:D2.bind(null,r,n.getNumberFormat),formatNumberToParts:F2.bind(null,r,n.getNumberFormat),formatRelativeTime:b2.bind(null,r,n.getRelativeTimeFormat),formatDate:E2.bind(null,r,n.getDateTimeFormat),formatDateToParts:L2.bind(null,r,n.getDateTimeFormat),formatTime:S2.bind(null,r,n.getDateTimeFormat),formatDateTimeRange:w2.bind(null,r,n.getDateTimeFormat),formatTimeToParts:x2.bind(null,r,n.getDateTimeFormat),formatPlural:A2.bind(null,r,n.getPluralRules),formatMessage:tu.bind(null,r,n),$t:tu.bind(null,r,n),formatList:P2.bind(null,r,n.getListFormat),formatListToParts:um.bind(null,r,n.getListFormat),formatDisplayName:T2.bind(null,r,n.getDisplayNames)})}function fm(e){G0(e,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}var dm=I(I({},am),{textComponent:xe.Fragment});function B2(e){return function(t){return e(xe.Children.toArray(t))}}function U2(e,t){if(e===t)return!0;if(!e||!t)return!1;var n=Object.keys(e),r=Object.keys(t),i=n.length;if(r.length!==i)return!1;for(var o=0;o<i;o++){var s=n[o];if(e[s]!==t[s]||!Object.prototype.hasOwnProperty.call(t,s))return!1}return!0}var hm={exports:{}},H={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var le=typeof Symbol=="function"&&Symbol.for,Ic=le?Symbol.for("react.element"):60103,kc=le?Symbol.for("react.portal"):60106,Us=le?Symbol.for("react.fragment"):60107,js=le?Symbol.for("react.strict_mode"):60108,zs=le?Symbol.for("react.profiler"):60114,Gs=le?Symbol.for("react.provider"):60109,Vs=le?Symbol.for("react.context"):60110,Pc=le?Symbol.for("react.async_mode"):60111,Ws=le?Symbol.for("react.concurrent_mode"):60111,Xs=le?Symbol.for("react.forward_ref"):60112,Ys=le?Symbol.for("react.suspense"):60113,j2=le?Symbol.for("react.suspense_list"):60120,Qs=le?Symbol.for("react.memo"):60115,Zs=le?Symbol.for("react.lazy"):60116,z2=le?Symbol.for("react.block"):60121,G2=le?Symbol.for("react.fundamental"):60117,V2=le?Symbol.for("react.responder"):60118,W2=le?Symbol.for("react.scope"):60119;function je(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Ic:switch(e=e.type,e){case Pc:case Ws:case Us:case zs:case js:case Ys:return e;default:switch(e=e&&e.$$typeof,e){case Vs:case Xs:case Zs:case Qs:case Gs:return e;default:return t}}case kc:return t}}}function pm(e){return je(e)===Ws}H.AsyncMode=Pc;H.ConcurrentMode=Ws;H.ContextConsumer=Vs;H.ContextProvider=Gs;H.Element=Ic;H.ForwardRef=Xs;H.Fragment=Us;H.Lazy=Zs;H.Memo=Qs;H.Portal=kc;H.Profiler=zs;H.StrictMode=js;H.Suspense=Ys;H.isAsyncMode=function(e){return pm(e)||je(e)===Pc};H.isConcurrentMode=pm;H.isContextConsumer=function(e){return je(e)===Vs};H.isContextProvider=function(e){return je(e)===Gs};H.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ic};H.isForwardRef=function(e){return je(e)===Xs};H.isFragment=function(e){return je(e)===Us};H.isLazy=function(e){return je(e)===Zs};H.isMemo=function(e){return je(e)===Qs};H.isPortal=function(e){return je(e)===kc};H.isProfiler=function(e){return je(e)===zs};H.isStrictMode=function(e){return je(e)===js};H.isSuspense=function(e){return je(e)===Ys};H.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Us||e===Ws||e===zs||e===js||e===Ys||e===j2||typeof e=="object"&&e!==null&&(e.$$typeof===Zs||e.$$typeof===Qs||e.$$typeof===Gs||e.$$typeof===Vs||e.$$typeof===Xs||e.$$typeof===G2||e.$$typeof===V2||e.$$typeof===W2||e.$$typeof===z2)};H.typeOf=je;hm.exports=H;var X2=hm.exports,mm=X2,Y2={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Q2={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},gm={};gm[mm.ForwardRef]=Y2;gm[mm.Memo]=Q2;var Nc=typeof window<"u"&&!window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=xe.createContext(null)):xe.createContext(null);Nc.Consumer;var Z2=Nc.Provider,K2=Z2,q2=Nc;function ym(){var e=xe.useContext(q2);return fm(e),e}var nu;(function(e){e.formatDate="FormattedDate",e.formatTime="FormattedTime",e.formatNumber="FormattedNumber",e.formatList="FormattedList",e.formatDisplayName="FormattedDisplayName"})(nu||(nu={}));var ru;(function(e){e.formatDate="FormattedDateParts",e.formatTime="FormattedTimeParts",e.formatNumber="FormattedNumberParts",e.formatList="FormattedListParts"})(ru||(ru={}));function vm(e){var t=function(n){var r=ym(),i=n.value,o=n.children,s=Hs(n,["value","children"]),a=typeof i=="string"?new Date(i||0):i,l=e==="formatDate"?r.formatDateToParts(a,s):r.formatTimeToParts(a,s);return o(l)};return t.displayName=ru[e],t}function Ui(e){var t=function(n){var r=ym(),i=n.value,o=n.children,s=Hs(n,["value","children"]),a=r[e](i,s);if(typeof o=="function")return o(a);var l=r.textComponent||xe.Fragment;return xe.createElement(l,null,a)};return t.displayName=nu[e],t}function _m(e){return e&&Object.keys(e).reduce(function(t,n){var r=e[n];return t[n]=om(r)?B2(r):r,t},{})}var wd=function(e,t,n,r){for(var i=[],o=4;o<arguments.length;o++)i[o-4]=arguments[o];var s=_m(r),a=tu.apply(void 0,Le([e,t,n,s],i,!1));return Array.isArray(a)?xe.Children.toArray(a):a},Ld=function(e,t){var n=e.defaultRichTextElements,r=Hs(e,["defaultRichTextElements"]),i=_m(n),o=Tc(I(I(I({},dm),r),{defaultRichTextElements:i}),t),s={locale:o.locale,timeZone:o.timeZone,fallbackOnEmptyString:o.fallbackOnEmptyString,formats:o.formats,defaultLocale:o.defaultLocale,defaultFormats:o.defaultFormats,messages:o.messages,onError:o.onError,defaultRichTextElements:i};return I(I({},o),{formatMessage:wd.bind(null,s,o.formatters),$t:wd.bind(null,s,o.formatters)})};function za(e){return{locale:e.locale,timeZone:e.timeZone,fallbackOnEmptyString:e.fallbackOnEmptyString,formats:e.formats,textComponent:e.textComponent,messages:e.messages,defaultLocale:e.defaultLocale,defaultFormats:e.defaultFormats,onError:e.onError,onWarn:e.onWarn,wrapRichTextChunksInFragment:e.wrapRichTextChunksInFragment,defaultRichTextElements:e.defaultRichTextElements}}var J2=function(e){Ke(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.cache=xc(),n.state={cache:n.cache,intl:Ld(za(n.props),n.cache),prevConfig:za(n.props)},n}return t.getDerivedStateFromProps=function(n,r){var i=r.prevConfig,o=r.cache,s=za(n);return U2(i,s)?null:{intl:Ld(s,o),prevConfig:s}},t.prototype.render=function(){return fm(this.state.intl),xe.createElement(K2,{value:this.state.intl},this.props.children)},t.displayName="IntlProvider",t.defaultProps=dm,t}(xe.PureComponent);Ui("formatDate");Ui("formatTime");Ui("formatNumber");Ui("formatList");Ui("formatDisplayName");vm("formatDate");vm("formatTime");var iu=function(e,t){return iu=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])},iu(e,t)};function Or(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");iu(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}function e3(e,t,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function a(c){try{u(r.next(c))}catch(f){s(f)}}function l(c){try{u(r.throw(c))}catch(f){s(f)}}function u(c){c.done?o(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})}function Em(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,s=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(u){return function(c){return l([u,c])}}function l(u){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,u[0]&&(n=0)),n;)try{if(r=1,i&&(o=u[0]&2?i.return:u[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,u[1])).done)return o;switch(i=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,i=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){n.label=u[1];break}if(u[0]===6&&n.label<o[1]){n.label=o[1],o=u;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(u);break}o[2]&&n.ops.pop(),n.trys.pop();continue}u=t.call(e,n)}catch(c){u=[6,c],i=0}finally{r=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function ki(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function wr(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,o=[],s;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)o.push(i.value)}catch(a){s={error:a}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(s)throw s.error}}return o}function Pi(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,o;r<i;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}function lr(e){return this instanceof lr?(this.v=e,this):new lr(e)}function t3(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),i,o=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(m){return function(y){return Promise.resolve(y).then(m,f)}}function a(m,y){r[m]&&(i[m]=function(v){return new Promise(function(S,p){o.push([m,v,S,p])>1||l(m,v)})},y&&(i[m]=y(i[m])))}function l(m,y){try{u(r[m](y))}catch(v){d(o[0][3],v)}}function u(m){m.value instanceof lr?Promise.resolve(m.value.v).then(c,f):d(o[0][2],m)}function c(m){l("next",m)}function f(m){l("throw",m)}function d(m,y){m(y),o.shift(),o.length&&l(o[0][0],o[0][1])}}function n3(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof ki=="function"?ki(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(o){n[o]=e[o]&&function(s){return new Promise(function(a,l){s=e[o](s),i(a,l,s.done,s.value)})}}function i(o,s,a,l){Promise.resolve(l).then(function(u){o({value:u,done:a})},s)}}function j(e){return typeof e=="function"}function Ac(e){var t=function(r){Error.call(r),r.stack=new Error().stack},n=e(t);return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ga=Ac(function(e){return function(n){e(this),this.message=n?n.length+` errors occurred during unsubscription:
`+n.map(function(r,i){return i+1+") "+r.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=n}});function ou(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Ks=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,n,r,i,o;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var a=ki(s),l=a.next();!l.done;l=a.next()){var u=l.value;u.remove(this)}}catch(v){t={error:v}}finally{try{l&&!l.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}else s.remove(this);var c=this.initialTeardown;if(j(c))try{c()}catch(v){o=v instanceof Ga?v.errors:[v]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var d=ki(f),m=d.next();!m.done;m=d.next()){var y=m.value;try{xd(y)}catch(v){o=o??[],v instanceof Ga?o=Pi(Pi([],wr(o)),wr(v.errors)):o.push(v)}}}catch(v){r={error:v}}finally{try{m&&!m.done&&(i=d.return)&&i.call(d)}finally{if(r)throw r.error}}}if(o)throw new Ga(o)}},e.prototype.add=function(t){var n;if(t&&t!==this)if(this.closed)xd(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}},e.prototype._hasParent=function(t){var n=this._parentage;return n===t||Array.isArray(n)&&n.includes(t)},e.prototype._addParent=function(t){var n=this._parentage;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t},e.prototype._removeParent=function(t){var n=this._parentage;n===t?this._parentage=null:Array.isArray(n)&&ou(n,t)},e.prototype.remove=function(t){var n=this._finalizers;n&&ou(n,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}();Ks.EMPTY;function Sm(e){return e instanceof Ks||e&&"closed"in e&&j(e.remove)&&j(e.add)&&j(e.unsubscribe)}function xd(e){j(e)?e():e.unsubscribe()}var wm={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},Lm={setTimeout:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,Pi([e,t],wr(n)))},clearTimeout:function(e){var t=Lm.delegate;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function xm(e){Lm.setTimeout(function(){throw e})}function ds(){}function r3(e){e()}var Rc=function(e){Or(t,e);function t(n){var r=e.call(this)||this;return r.isStopped=!1,n?(r.destination=n,Sm(n)&&n.add(r)):r.destination=a3,r}return t.create=function(n,r,i){return new hs(n,r,i)},t.prototype.next=function(n){this.isStopped||this._next(n)},t.prototype.error=function(n){this.isStopped||(this.isStopped=!0,this._error(n))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(n){this.destination.next(n)},t.prototype._error=function(n){try{this.destination.error(n)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(Ks),i3=Function.prototype.bind;function Va(e,t){return i3.call(e,t)}var o3=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var n=this.partialObserver;if(n.next)try{n.next(t)}catch(r){go(r)}},e.prototype.error=function(t){var n=this.partialObserver;if(n.error)try{n.error(t)}catch(r){go(r)}else go(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(n){go(n)}},e}(),hs=function(e){Or(t,e);function t(n,r,i){var o=e.call(this)||this,s;if(j(n)||!n)s={next:n??void 0,error:r??void 0,complete:i??void 0};else{var a;o&&wm.useDeprecatedNextContext?(a=Object.create(n),a.unsubscribe=function(){return o.unsubscribe()},s={next:n.next&&Va(n.next,a),error:n.error&&Va(n.error,a),complete:n.complete&&Va(n.complete,a)}):s=n}return o.destination=new o3(s),o}return t}(Rc);function go(e){xm(e)}function s3(e){throw e}var a3={closed:!0,next:ds,error:s3,complete:ds},Oc=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function l3(e){return e}function u3(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Cm(e)}function Cm(e){return e.length===0?l3:e.length===1?e[0]:function(n){return e.reduce(function(r,i){return i(r)},n)}}var de=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(t,n,r){var i=this,o=f3(t)?t:new hs(t,n,r);return r3(function(){var s=i,a=s.operator,l=s.source;o.add(a?a.call(o,l):l?i._subscribe(o):i._trySubscribe(o))}),o},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(n){t.error(n)}},e.prototype.forEach=function(t,n){var r=this;return n=Cd(n),new n(function(i,o){var s=new hs({next:function(a){try{t(a)}catch(l){o(l),s.unsubscribe()}},error:o,complete:i});r.subscribe(s)})},e.prototype._subscribe=function(t){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(t)},e.prototype[Oc]=function(){return this},e.prototype.pipe=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Cm(t)(this)},e.prototype.toPromise=function(t){var n=this;return t=Cd(t),new t(function(r,i){var o;n.subscribe(function(s){return o=s},function(s){return i(s)},function(){return r(o)})})},e.create=function(t){return new e(t)},e}();function Cd(e){var t;return(t=e??wm.Promise)!==null&&t!==void 0?t:Promise}function c3(e){return e&&j(e.next)&&j(e.error)&&j(e.complete)}function f3(e){return e&&e instanceof Rc||c3(e)&&Sm(e)}function d3(e){return j(e==null?void 0:e.lift)}function ut(e){return function(t){if(d3(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function lt(e,t,n,r,i){return new h3(e,t,n,r,i)}var h3=function(e){Or(t,e);function t(n,r,i,o,s,a){var l=e.call(this,n)||this;return l.onFinalize=s,l.shouldUnsubscribe=a,l._next=r?function(u){try{r(u)}catch(c){n.error(c)}}:e.prototype._next,l._error=o?function(u){try{o(u)}catch(c){n.error(c)}finally{this.unsubscribe()}}:e.prototype._error,l._complete=i?function(){try{i()}catch(u){n.error(u)}finally{this.unsubscribe()}}:e.prototype._complete,l}return t.prototype.unsubscribe=function(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&((n=this.onFinalize)===null||n===void 0||n.call(this))}},t}(Rc),p3={now:function(){return Date.now()},delegate:void 0},m3=function(e){Or(t,e);function t(n,r){return e.call(this)||this}return t.prototype.schedule=function(n,r){return this},t}(Ks),Td={setInterval:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setInterval.apply(void 0,Pi([e,t],wr(n)))},clearInterval:function(e){return clearInterval(e)},delegate:void 0},g3=function(e){Or(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.scheduler=n,i.work=r,i.pending=!1,i}return t.prototype.schedule=function(n,r){var i;if(r===void 0&&(r=0),this.closed)return this;this.state=n;var o=this.id,s=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(s,o,r)),this.pending=!0,this.delay=r,this.id=(i=this.id)!==null&&i!==void 0?i:this.requestAsyncId(s,this.id,r),this},t.prototype.requestAsyncId=function(n,r,i){return i===void 0&&(i=0),Td.setInterval(n.flush.bind(n,this),i)},t.prototype.recycleAsyncId=function(n,r,i){if(i===void 0&&(i=0),i!=null&&this.delay===i&&this.pending===!1)return r;r!=null&&Td.clearInterval(r)},t.prototype.execute=function(n,r){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var i=this._execute(n,r);if(i)return i;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(n,r){var i=!1,o;try{this.work(n)}catch(s){i=!0,o=s||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),o},t.prototype.unsubscribe=function(){if(!this.closed){var n=this,r=n.id,i=n.scheduler,o=i.actions;this.work=this.state=this.scheduler=null,this.pending=!1,ou(o,this),r!=null&&(this.id=this.recycleAsyncId(i,r,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(m3),Id=function(){function e(t,n){n===void 0&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(t,n,r){return n===void 0&&(n=0),new this.schedulerActionCtor(this,t).schedule(r,n)},e.now=p3.now,e}(),y3=function(e){Or(t,e);function t(n,r){r===void 0&&(r=Id.now);var i=e.call(this,n,r)||this;return i.actions=[],i._active=!1,i}return t.prototype.flush=function(n){var r=this.actions;if(this._active){r.push(n);return}var i;this._active=!0;do if(i=n.execute(n.state,n.delay))break;while(n=r.shift());if(this._active=!1,i){for(;n=r.shift();)n.unsubscribe();throw i}},t}(Id),Tm=new y3(g3),v3=Tm;function _3(e){return e&&j(e.schedule)}function E3(e){return e[e.length-1]}function S3(e){return _3(E3(e))?e.pop():void 0}var bc=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function Im(e){return j(e==null?void 0:e.then)}function km(e){return j(e[Oc])}function Pm(e){return Symbol.asyncIterator&&j(e==null?void 0:e[Symbol.asyncIterator])}function Nm(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function w3(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Am=w3();function Rm(e){return j(e==null?void 0:e[Am])}function Om(e){return t3(this,arguments,function(){var n,r,i,o;return Em(this,function(s){switch(s.label){case 0:n=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,lr(n.read())];case 3:return r=s.sent(),i=r.value,o=r.done,o?[4,lr(void 0)]:[3,5];case 4:return[2,s.sent()];case 5:return[4,lr(i)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return n.releaseLock(),[7];case 10:return[2]}})})}function bm(e){return j(e==null?void 0:e.getReader)}function yt(e){if(e instanceof de)return e;if(e!=null){if(km(e))return L3(e);if(bc(e))return x3(e);if(Im(e))return C3(e);if(Pm(e))return Mm(e);if(Rm(e))return T3(e);if(bm(e))return I3(e)}throw Nm(e)}function L3(e){return new de(function(t){var n=e[Oc]();if(j(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function x3(e){return new de(function(t){for(var n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function C3(e){return new de(function(t){e.then(function(n){t.closed||(t.next(n),t.complete())},function(n){return t.error(n)}).then(null,xm)})}function T3(e){return new de(function(t){var n,r;try{for(var i=ki(e),o=i.next();!o.done;o=i.next()){var s=o.value;if(t.next(s),t.closed)return}}catch(a){n={error:a}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}t.complete()})}function Mm(e){return new de(function(t){k3(e,t).catch(function(n){return t.error(n)})})}function I3(e){return Mm(Om(e))}function k3(e,t){var n,r,i,o;return e3(this,void 0,void 0,function(){var s,a;return Em(this,function(l){switch(l.label){case 0:l.trys.push([0,5,6,11]),n=n3(e),l.label=1;case 1:return[4,n.next()];case 2:if(r=l.sent(),!!r.done)return[3,4];if(s=r.value,t.next(s),t.closed)return[2];l.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return a=l.sent(),i={error:a},[3,11];case 6:return l.trys.push([6,,9,10]),r&&!r.done&&(o=n.return)?[4,o.call(n)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function Kt(e,t,n,r,i){r===void 0&&(r=0),i===void 0&&(i=!1);var o=t.schedule(function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function Dm(e,t){return t===void 0&&(t=0),ut(function(n,r){n.subscribe(lt(r,function(i){return Kt(r,e,function(){return r.next(i)},t)},function(){return Kt(r,e,function(){return r.complete()},t)},function(i){return Kt(r,e,function(){return r.error(i)},t)}))})}function Fm(e,t){return t===void 0&&(t=0),ut(function(n,r){r.add(e.schedule(function(){return n.subscribe(r)},t))})}function P3(e,t){return yt(e).pipe(Fm(t),Dm(t))}function N3(e,t){return yt(e).pipe(Fm(t),Dm(t))}function A3(e,t){return new de(function(n){var r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function R3(e,t){return new de(function(n){var r;return Kt(n,t,function(){r=e[Am](),Kt(n,t,function(){var i,o,s;try{i=r.next(),o=i.value,s=i.done}catch(a){n.error(a);return}s?n.complete():n.next(o)},0,!0)}),function(){return j(r==null?void 0:r.return)&&r.return()}})}function $m(e,t){if(!e)throw new Error("Iterable cannot be null");return new de(function(n){Kt(n,t,function(){var r=e[Symbol.asyncIterator]();Kt(n,t,function(){r.next().then(function(i){i.done?n.complete():n.next(i.value)})},0,!0)})})}function O3(e,t){return $m(Om(e),t)}function b3(e,t){if(e!=null){if(km(e))return P3(e,t);if(bc(e))return A3(e,t);if(Im(e))return N3(e,t);if(Pm(e))return $m(e,t);if(Rm(e))return R3(e,t);if(bm(e))return O3(e,t)}throw Nm(e)}function Hm(e,t){return t?b3(e,t):yt(e)}function $o(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=S3(e);return Hm(e,n)}function M3(e,t){var n=j(e)?e:function(){return e},r=function(i){return i.error(n())};return new de(r)}var D3=Ac(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function F3(e,t){return new Promise(function(n,r){var i=new hs({next:function(o){n(o),i.unsubscribe()},error:r,complete:function(){r(new D3)}});e.subscribe(i)})}function Bm(e){return e instanceof Date&&!isNaN(e)}var $3=Ac(function(e){return function(n){n===void 0&&(n=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n}});function H3(e,t){var n=Bm(e)?{first:e}:typeof e=="number"?{each:e}:e,r=n.first,i=n.each,o=n.with,s=o===void 0?B3:o,a=n.scheduler,l=a===void 0?Tm:a,u=n.meta,c=u===void 0?null:u;if(r==null&&i==null)throw new TypeError("No timeout provided.");return ut(function(f,d){var m,y,v=null,S=0,p=function(h){y=Kt(d,l,function(){try{m.unsubscribe(),yt(s({meta:c,lastValue:v,seen:S})).subscribe(d)}catch(g){d.error(g)}},h)};m=f.subscribe(lt(d,function(h){y==null||y.unsubscribe(),S++,d.next(v=h),i>0&&p(i)},void 0,void 0,function(){y!=null&&y.closed||y==null||y.unsubscribe(),v=null})),!S&&p(r!=null?typeof r=="number"?r:+r-l.now():i)})}function B3(e){throw new $3(e)}function qs(e,t){return ut(function(n,r){var i=0;n.subscribe(lt(r,function(o){r.next(e.call(t,o,i++))}))})}var U3=Array.isArray;function j3(e,t){return U3(t)?e.apply(void 0,Pi([],wr(t))):e(t)}function z3(e){return qs(function(t){return j3(e,t)})}function G3(e,t,n,r,i,o,s,a){var l=[],u=0,c=0,f=!1,d=function(){f&&!l.length&&!u&&t.complete()},m=function(v){return u<r?y(v):l.push(v)},y=function(v){u++;var S=!1;yt(n(v,c++)).subscribe(lt(t,function(p){t.next(p)},function(){S=!0},void 0,function(){if(S)try{u--;for(var p=function(){var h=l.shift();s||y(h)};l.length&&u<r;)p();d()}catch(h){t.error(h)}}))};return e.subscribe(lt(t,m,function(){f=!0,d()})),function(){}}function Js(e,t,n){return n===void 0&&(n=1/0),j(t)?Js(function(r,i){return qs(function(o,s){return t(r,o,i,s)})(yt(e(r,i)))},n):(typeof t=="number"&&(n=t),ut(function(r,i){return G3(r,i,e,n)}))}var V3=["addListener","removeListener"],W3=["addEventListener","removeEventListener"],X3=["on","off"];function su(e,t,n,r){if(j(n)&&(r=n,n=void 0),r)return su(e,t,n).pipe(z3(r));var i=wr(Z3(e)?W3.map(function(a){return function(l){return e[a](t,l,n)}}):Y3(e)?V3.map(kd(e,t)):Q3(e)?X3.map(kd(e,t)):[],2),o=i[0],s=i[1];if(!o&&bc(e))return Js(function(a){return su(a,t,n)})(yt(e));if(!o)throw new TypeError("Invalid event target");return new de(function(a){var l=function(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];return a.next(1<u.length?u:u[0])};return o(l),function(){return s(l)}})}function kd(e,t){return function(n){return function(r){return e[n](t,r)}}}function Y3(e){return j(e.addListener)&&j(e.removeListener)}function Q3(e){return j(e.on)&&j(e.off)}function Z3(e){return j(e.addEventListener)&&j(e.removeEventListener)}function K3(e,t,n){return n===void 0&&(n=v3),new de(function(r){var i=Bm(e)?+e-n.now():e;i<0&&(i=0);var o=0;return n.schedule(function(){r.closed||(r.next(o++),r.complete())},i)})}var Um=new de(ds);function au(e,t){return ut(function(n,r){var i=0;n.subscribe(lt(r,function(o){return e.call(t,o,i++)&&r.next(o)}))})}function jm(e){return ut(function(t,n){var r=null,i=!1,o;r=t.subscribe(lt(n,void 0,void 0,function(s){o=yt(e(s,jm(e)(t))),r?(r.unsubscribe(),r=null,o.subscribe(n)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(n))})}function q3(e,t,n,r,i){return function(o,s){var a=n,l=t,u=0;o.subscribe(lt(s,function(c){var f=u++;l=a?e(l,c,f):(a=!0,c)},function(){a&&s.next(l),s.complete()}))}}function J3(e,t){return ut(q3(e,t,arguments.length>=2,!1,!0))}var e_=function(e,t){return e.push(t),e};function t_(){return ut(function(e,t){J3(e_,[])(e).subscribe(t)})}function n_(e,t){return ut(function(n,r){var i=null,o=0,s=!1,a=function(){return s&&!i&&r.complete()};n.subscribe(lt(r,function(l){i==null||i.unsubscribe();var u=0,c=o++;yt(e(l,c)).subscribe(i=lt(r,function(f){return r.next(t?t(l,f,c,u++):f)},function(){i=null,a()}))},function(){s=!0,a()}))})}function Mc(e){return ut(function(t,n){yt(e).subscribe(lt(n,function(){return n.complete()},ds)),!n.closed&&t.subscribe(n)})}var lu={exports:{}},Wa,Pd;function r_(){if(Pd)return Wa;Pd=1;var e=1e3,t=e*60,n=t*60,r=n*24,i=r*7,o=r*365.25;Wa=function(c,f){f=f||{};var d=typeof c;if(d==="string"&&c.length>0)return s(c);if(d==="number"&&isFinite(c))return f.long?l(c):a(c);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(c))};function s(c){if(c=String(c),!(c.length>100)){var f=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(f){var d=parseFloat(f[1]),m=(f[2]||"ms").toLowerCase();switch(m){case"years":case"year":case"yrs":case"yr":case"y":return d*o;case"weeks":case"week":case"w":return d*i;case"days":case"day":case"d":return d*r;case"hours":case"hour":case"hrs":case"hr":case"h":return d*n;case"minutes":case"minute":case"mins":case"min":case"m":return d*t;case"seconds":case"second":case"secs":case"sec":case"s":return d*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return d;default:return}}}}function a(c){var f=Math.abs(c);return f>=r?Math.round(c/r)+"d":f>=n?Math.round(c/n)+"h":f>=t?Math.round(c/t)+"m":f>=e?Math.round(c/e)+"s":c+"ms"}function l(c){var f=Math.abs(c);return f>=r?u(c,f,r,"day"):f>=n?u(c,f,n,"hour"):f>=t?u(c,f,t,"minute"):f>=e?u(c,f,e,"second"):c+" ms"}function u(c,f,d,m){var y=f>=d*1.5;return Math.round(c/d)+" "+m+(y?"s":"")}return Wa}function i_(e){n.debug=n,n.default=n,n.coerce=l,n.disable=s,n.enable=i,n.enabled=a,n.humanize=r_(),n.destroy=u,Object.keys(e).forEach(c=>{n[c]=e[c]}),n.names=[],n.skips=[],n.formatters={};function t(c){let f=0;for(let d=0;d<c.length;d++)f=(f<<5)-f+c.charCodeAt(d),f|=0;return n.colors[Math.abs(f)%n.colors.length]}n.selectColor=t;function n(c){let f,d=null,m,y;function v(...S){if(!v.enabled)return;const p=v,h=Number(new Date),g=h-(f||h);p.diff=g,p.prev=f,p.curr=h,f=h,S[0]=n.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let _=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(L,T)=>{if(L==="%%")return"%";_++;const k=n.formatters[T];if(typeof k=="function"){const Y=S[_];L=k.call(p,Y),S.splice(_,1),_--}return L}),n.formatArgs.call(p,S),(p.log||n.log).apply(p,S)}return v.namespace=c,v.useColors=n.useColors(),v.color=n.selectColor(c),v.extend=r,v.destroy=n.destroy,Object.defineProperty(v,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==n.namespaces&&(m=n.namespaces,y=n.enabled(c)),y),set:S=>{d=S}}),typeof n.init=="function"&&n.init(v),v}function r(c,f){const d=n(this.namespace+(typeof f>"u"?":":f)+c);return d.log=this.log,d}function i(c){n.save(c),n.namespaces=c,n.names=[],n.skips=[];const f=(typeof c=="string"?c:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const d of f)d[0]==="-"?n.skips.push(d.slice(1)):n.names.push(d)}function o(c,f){let d=0,m=0,y=-1,v=0;for(;d<c.length;)if(m<f.length&&(f[m]===c[d]||f[m]==="*"))f[m]==="*"?(y=m,v=d,m++):(d++,m++);else if(y!==-1)m=y+1,v++,d=v;else return!1;for(;m<f.length&&f[m]==="*";)m++;return m===f.length}function s(){const c=[...n.names,...n.skips.map(f=>"-"+f)].join(",");return n.enable(""),c}function a(c){for(const f of n.skips)if(o(c,f))return!1;for(const f of n.names)if(o(c,f))return!0;return!1}function l(c){return c instanceof Error?c.stack||c.message:c}function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}var o_=i_;(function(e,t){var n={};t.formatArgs=i,t.save=o,t.load=s,t.useColors=r,t.storage=a(),t.destroy=(()=>{let u=!1;return()=>{u||(u=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function r(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let u;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(u=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(u[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function i(u){if(u[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+u[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const c="color: "+this.color;u.splice(1,0,c,"color: inherit");let f=0,d=0;u[0].replace(/%[a-zA-Z%]/g,m=>{m!=="%%"&&(f++,m==="%c"&&(d=f))}),u.splice(d,0,c)}t.log=console.debug||console.log||(()=>{});function o(u){try{u?t.storage.setItem("debug",u):t.storage.removeItem("debug")}catch{}}function s(){let u;try{u=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch{}return!u&&typeof process<"u"&&"env"in process&&(u=n.DEBUG),u}function a(){try{return localStorage}catch{}}e.exports=o_(t);const{formatters:l}=e.exports;l.j=function(u){try{return JSON.stringify(u)}catch(c){return"[UnexpectedJSONParseError]: "+c.message}}})(lu,lu.exports);var s_=lu.exports;const zm=J1(s_),Nd=zm("claude:ipc");class a_{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(Mc(this.onDestroy)).subscribe(t)}}function l_(e){return new de(t=>e.subscribe(t))}function u_(e){return e==null?!1:typeof e=="object"&&"then"in e}function c_(e){return e!==null&&(typeof e=="object"||typeof e=="function")&&typeof e.subscribe=="function"}let Xa={};function f_(e,t,n,r){var s,a;const i=d_(e,t,n,r),o=Gm(t);return(a=(s=Xa[e])==null?void 0:s.subscription)==null||a.unsubscribe(),Xa[e]={subscription:i,metadata:o},i.add(()=>delete Xa[e]),i}function d_(e,t,n,r){return l_(n).pipe(au(i=>i.methodChain.split(".")[0]===e),au(i=>r(i)?!0:(console.error(`Invalid message received: ${JSON.stringify(i)}`),!1)),Js(i=>{const o=i.methodChain.split(".").splice(1),s=o.pop(),a=o.reduce((f,d)=>f[d],t),l=i.customSendMethod??n.next.bind(n);let u;try{const f=a[s];Nd('Calling method "%s" with args %o',s,i.argList),u=f.call(a,...i.argList)}catch(f){return Nd(`Error in API call for message: %o
%o`,i,f),$o({sendMethod:l,result:{error:f,callId:i.callId}})}let c=$o(u);return u_(u)?c=Hm(u):c_(u)&&(c=u.pipe(t_())),c.pipe(qs(f=>({sendMethod:l,result:{result:f,callId:i.callId}})),jm(f=>$o({sendMethod:l,result:{result:null,callId:i.callId,error:f}})))}),Mc(n.onDestroy)).subscribe({next:i=>i.sendMethod(i.result),error:i=>{console.error(`Error in API Handler - this should not happen! ${i}
${i.stack}`)}})}function Gm(e){return h_(e).reduce((t,n)=>(typeof e[n]=="function"&&(t[n]=!0),typeof e[n]=="object"&&e[n]!==null&&(t[n]=Gm(e[n])),t),{})}function h_(e){const t=Object.keys(e),n=Object.getOwnPropertyNames(e).filter(s=>!t.includes(s)),r=[];let i=Object.getPrototypeOf(e);for(;i&&i!==Object.prototype;)Object.getOwnPropertyNames(i).filter(s=>!["constructor"].includes(s)).forEach(s=>{!r.includes(s)&&!t.includes(s)&&!n.includes(s)&&r.push(s)}),i=Object.getPrototypeOf(i);const o=Object.getOwnPropertySymbols(e).map(s=>s.toString());return[...t,...n,...r,...o]}function p_(){return""}function m_(e,t){return u3(n_(n=>K3(e).pipe(qs(()=>n))))}function g_(e){return new de(t=>e.subscribe(t))}const Vm=5e3,y_=zm("claude:ipc");function v_(e,t,n=Vm){return uu.create(e,(r,i)=>E_(t,r.join("."),i,n))}let __=0;function E_(e,t,n,r=Vm){const i=++__,o={sender:{},callId:`${i}`,methodChain:t,argList:n};y_("sending message %o, stack: %s",o,p_());let s=F3(g_(e).pipe(au(a=>a.callId===`${i}`),Js(a=>a.error?M3(()=>a.error):$o(a.result)),H3(r)));return e.next(o),s}var fr,dr,hr,_n,bi,Pr,Wm,Xm;const ws=class ws{constructor(t,n,r=null,i=null){cn(this,Pr);cn(this,fr);cn(this,dr);cn(this,hr);cn(this,_n);cn(this,bi,{});$r(this,fr,t),$r(this,dr,n),$r(this,hr,r),$r(this,_n,i)}static create(t,n,r=null){return new Proxy(()=>{},new ws(t,n,null,r))}get(t,n){return ze(this,_n)&&n in ze(this,_n)?ze(this,_n)[n]:new Proxy(()=>{},ha(this,Pr,Wm).call(this,n))}apply(t,n,r){const i=[ha(this,Pr,Xm).call(this,ze(this,fr))];let o=ze(this,hr);for(;o;)i.unshift(ze(o,fr)),o=ze(o,hr);return ze(this,dr).call(this,i,r)}};fr=new WeakMap,dr=new WeakMap,hr=new WeakMap,_n=new WeakMap,bi=new WeakMap,Pr=new WeakSet,Wm=function(t){let n=ze(this,bi)[t];return n||(n=new ws(t.toString(),ze(this,dr),this),ze(this,bi)[t]=n,n)},Xm=function(t){return t.replace(/_get$/,"")};let uu=ws;class S_{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(Mc(this.onDestroy)).subscribe(t)}}function w_(){return new S_({next:e=>window.rpcInternal.rpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new de(e=>window.rpcInternal.rpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),Um)}function L_(){return new a_({next:e=>window.rpcInternal.reverseRpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new de(e=>window.rpcInternal.reverseRpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),Um)}function Ym(e,t){return f_(e,t,window.replyPort,()=>!0)}window.sendPort=w_();window.replyPort=L_();var ea=(e=>(e.QuickWindow="QuickWindow",e.Find="Find",e.StartupSettings="StartupSettings",e.Filesystem="Filesystem",e.Intl="Intl",e.IntlSync="IntlSync",e.AboutWindow="AboutWindow",e.WindowControl="WindowControl",e))(ea||{});function x_(e){const[t,n]=xe.useState(window.initialLocale),[r,i]=xe.useState(window.initialMessages);return xe.useEffect(()=>{const o=Ym(ea.Intl,{localeChanged:(s,a)=>{n(s),i(a)}});return()=>o.unsubscribe()},[n,i]),tl.jsx(J2,{locale:t,messages:r,...e})}async function C_(e,t,n){const r=await t,i="default"in r?r.default:r,o=U0(e),s=n??{};return o.render(tl.jsx(x_,{children:tl.jsx(i,{...s})})),()=>{o.unmount()}}window.attachReactToElement=C_;const Qm=Object.prototype.toString;function Dc(e){switch(Qm.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Pn(e,Error)}}function br(e,t){return Qm.call(e)===`[object ${t}]`}function Zm(e){return br(e,"ErrorEvent")}function Ad(e){return br(e,"DOMError")}function T_(e){return br(e,"DOMException")}function Tt(e){return br(e,"String")}function Fc(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function $c(e){return e===null||Fc(e)||typeof e!="object"&&typeof e!="function"}function Lr(e){return br(e,"Object")}function ta(e){return typeof Event<"u"&&Pn(e,Event)}function I_(e){return typeof Element<"u"&&Pn(e,Element)}function k_(e){return br(e,"RegExp")}function na(e){return!!(e&&e.then&&typeof e.then=="function")}function P_(e){return Lr(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function Pn(e,t){try{return e instanceof t}catch{return!1}}function Km(e){return!!(typeof e=="object"&&e!==null&&(e.__isVue||e._isVue))}function ur(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function Rd(e,t){if(!Array.isArray(e))return"";const n=[];for(let r=0;r<e.length;r++){const i=e[r];try{Km(i)?n.push("[VueViewModel]"):n.push(String(i))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function N_(e,t,n=!1){return Tt(e)?k_(t)?t.test(e):Tt(t)?n?e===t:e.includes(t):!1:!1}function ra(e,t=[],n=!1){return t.some(r=>N_(e,r,n))}function A_(e,t,n=250,r,i,o,s){if(!o.exception||!o.exception.values||!s||!Pn(s.originalException,Error))return;const a=o.exception.values.length>0?o.exception.values[o.exception.values.length-1]:void 0;a&&(o.exception.values=R_(cu(e,t,i,s.originalException,r,o.exception.values,a,0),n))}function cu(e,t,n,r,i,o,s,a){if(o.length>=n+1)return o;let l=[...o];if(Pn(r[i],Error)){Od(s,a);const u=e(t,r[i]),c=l.length;bd(u,i,c,a),l=cu(e,t,n,r[i],i,[u,...l],u,c)}return Array.isArray(r.errors)&&r.errors.forEach((u,c)=>{if(Pn(u,Error)){Od(s,a);const f=e(t,u),d=l.length;bd(f,`errors[${c}]`,d,a),l=cu(e,t,n,u,i,[f,...l],f,d)}}),l}function Od(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function bd(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function R_(e,t){return e.map(n=>(n.value&&(n.value=ur(n.value,t)),n))}function qm(e){if(e!==void 0)return e>=400&&e<500?"warning":e>=500?"error":void 0}const wn="8.33.1",F=globalThis;function ia(e,t,n){const r=n||F,i=r.__SENTRY__=r.__SENTRY__||{},o=i[wn]=i[wn]||{};return o[e]||(o[e]=t())}const Hc=F,O_=80;function Jm(e,t={}){if(!e)return"<unknown>";try{let n=e;const r=5,i=[];let o=0,s=0;const a=" > ",l=a.length;let u;const c=Array.isArray(t)?t:t.keyAttrs,f=!Array.isArray(t)&&t.maxStringLength||O_;for(;n&&o++<r&&(u=b_(n,c),!(u==="html"||o>1&&s+i.length*l+u.length>=f));)i.push(u),s+=u.length,n=n.parentNode;return i.reverse().join(a)}catch{return"<unknown>"}}function b_(e,t){const n=e,r=[];if(!n||!n.tagName)return"";if(Hc.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const i=t&&t.length?t.filter(s=>n.getAttribute(s)).map(s=>[s,n.getAttribute(s)]):null;if(i&&i.length)i.forEach(s=>{r.push(`[${s[0]}="${s[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const s=n.className;if(s&&Tt(s)){const a=s.split(/\s+/);for(const l of a)r.push(`.${l}`)}}const o=["aria-label","type","name","title","alt"];for(const s of o){const a=n.getAttribute(s);a&&r.push(`[${s}="${a}"]`)}return r.join("")}function M_(){try{return Hc.document.location.href}catch{return""}}function D_(e){if(!Hc.HTMLElement)return null;let t=e;const n=5;for(let r=0;r<n;r++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}const ji=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,F_="Sentry Logger ",fu=["debug","info","warn","error","log","assert","trace"],ps={};function zi(e){if(!("console"in F))return e();const t=F.console,n={},r=Object.keys(ps);r.forEach(i=>{const o=ps[i];n[i]=t[i],t[i]=o});try{return e()}finally{r.forEach(i=>{t[i]=n[i]})}}function $_(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return ji?fu.forEach(n=>{t[n]=(...r)=>{e&&zi(()=>{F.console[n](`${F_}[${n}]:`,...r)})}}):fu.forEach(n=>{t[n]=()=>{}}),t}const P=ia("logger",$_),H_=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function B_(e){return e==="http"||e==="https"}function oa(e,t=!1){const{host:n,path:r,pass:i,port:o,projectId:s,protocol:a,publicKey:l}=e;return`${a}://${l}${t&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${s}`}function U_(e){const t=H_.exec(e);if(!t){zi(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});return}const[n,r,i="",o="",s="",a=""]=t.slice(1);let l="",u=a;const c=u.split("/");if(c.length>1&&(l=c.slice(0,-1).join("/"),u=c.pop()),u){const f=u.match(/^\d+/);f&&(u=f[0])}return e1({host:o,pass:i,path:l,projectId:u,port:s,protocol:n,publicKey:r})}function e1(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function j_(e){if(!ji)return!0;const{port:t,projectId:n,protocol:r}=e;return["protocol","publicKey","host","projectId"].find(s=>e[s]?!1:(P.error(`Invalid Sentry Dsn: ${s} missing`),!0))?!1:n.match(/^\d+$/)?B_(r)?t&&isNaN(parseInt(t,10))?(P.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):!0:(P.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(P.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function z_(e){const t=typeof e=="string"?U_(e):e1(e);if(!(!t||!j_(t)))return t}class ht extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}function Me(e,t,n){if(!(t in e))return;const r=e[t],i=n(r);typeof i=="function"&&t1(i,r),e[t]=i}function Nn(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{ji&&P.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function t1(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,Nn(e,"__sentry_original__",t)}catch{}}function Bc(e){return e.__sentry_original__}function G_(e){return Object.keys(e).map(t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`).join("&")}function n1(e){if(Dc(e))return{message:e.message,name:e.name,stack:e.stack,...Dd(e)};if(ta(e)){const t={type:e.type,target:Md(e.target),currentTarget:Md(e.currentTarget),...Dd(e)};return typeof CustomEvent<"u"&&Pn(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function Md(e){try{return I_(e)?Jm(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function Dd(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}else return{}}function V_(e,t=40){const n=Object.keys(n1(e));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return ur(r,t);for(let i=n.length;i>0;i--){const o=n.slice(0,i).join(", ");if(!(o.length>t))return i===n.length?o:ur(o,t)}return""}function De(e){return du(e,new Map)}function du(e,t){if(W_(e)){const n=t.get(e);if(n!==void 0)return n;const r={};t.set(e,r);for(const i of Object.getOwnPropertyNames(e))typeof e[i]<"u"&&(r[i]=du(e[i],t));return r}if(Array.isArray(e)){const n=t.get(e);if(n!==void 0)return n;const r=[];return t.set(e,r),e.forEach(i=>{r.push(du(i,t))}),r}return e}function W_(e){if(!Lr(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||t==="Object"}catch{return!0}}const r1=50,tn="?",Fd=/\(error: (.*)\)/,$d=/captureMessage|captureException/;function i1(...e){const t=e.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,i=0)=>{const o=[],s=n.split(`
`);for(let a=r;a<s.length;a++){const l=s[a];if(l.length>1024)continue;const u=Fd.test(l)?l.replace(Fd,"$1"):l;if(!u.match(/\S*Error: /)){for(const c of t){const f=c(u);if(f){o.push(f);break}}if(o.length>=r1+i)break}}return o1(o.slice(i))}}function X_(e){return Array.isArray(e)?i1(...e):e}function o1(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test(yo(t).function||"")&&t.pop(),t.reverse(),$d.test(yo(t).function||"")&&(t.pop(),$d.test(yo(t).function||"")&&t.pop()),t.slice(0,r1).map(n=>({...n,filename:n.filename||yo(t).filename,function:n.function||tn}))}function yo(e){return e[e.length-1]||{}}const Ya="<anonymous>";function nn(e){try{return!e||typeof e!="function"?Ya:e.name||Ya}catch{return Ya}}function Hd(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Ho={},Bd={};function $n(e,t){Ho[e]=Ho[e]||[],Ho[e].push(t)}function Hn(e,t){Bd[e]||(t(),Bd[e]=!0)}function st(e,t){const n=e&&Ho[e];if(n)for(const r of n)try{r(t)}catch(i){ji&&P.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${nn(r)}
Error:`,i)}}function Y_(e){const t="console";$n(t,e),Hn(t,Q_)}function Q_(){"console"in F&&fu.forEach(function(e){e in F.console&&Me(F.console,e,function(t){return ps[e]=t,function(...n){st("console",{args:n,level:e});const i=ps[e];i&&i.apply(F.console,n)}})})}const hu=F;function s1(){if(!("fetch"in hu))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function pu(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function Z_(){if(typeof EdgeRuntime=="string")return!0;if(!s1())return!1;if(pu(hu.fetch))return!0;let e=!1;const t=hu.document;if(t&&typeof t.createElement=="function")try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=pu(n.contentWindow.fetch)),t.head.removeChild(n)}catch(n){ji&&P.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return e}const a1=1e3;function Gi(){return Date.now()/a1}function K_(){const{performance:e}=F;if(!e||!e.now)return Gi;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/a1}const It=K_();(()=>{const{performance:e}=F;if(!e||!e.now)return;const t=3600*1e3,n=e.now(),r=Date.now(),i=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,o=i<t,s=e.timing&&e.timing.navigationStart,l=typeof s=="number"?Math.abs(s+n-r):t,u=l<t;return o||u?i<=l?e.timeOrigin:s:r})();function q_(e,t){const n="fetch";$n(n,e),Hn(n,()=>J_(void 0,t))}function J_(e,t=!1){t&&!Z_()||Me(F,"fetch",function(n){return function(...r){const{method:i,url:o}=eE(r),s={args:r,fetchData:{method:i,url:o},startTimestamp:It()*1e3};st("fetch",{...s});const a=new Error().stack;return n.apply(F,r).then(async l=>(st("fetch",{...s,endTimestamp:It()*1e3,response:l}),l),l=>{throw st("fetch",{...s,endTimestamp:It()*1e3,error:l}),Dc(l)&&l.stack===void 0&&(l.stack=a,Nn(l,"framesToPop",1)),l})}})}function mu(e,t){return!!e&&typeof e=="object"&&!!e[t]}function Ud(e){return typeof e=="string"?e:e?mu(e,"url")?e.url:e.toString?e.toString():"":""}function eE(e){if(e.length===0)return{method:"GET",url:""};if(e.length===2){const[n,r]=e;return{url:Ud(n),method:mu(r,"method")?String(r.method).toUpperCase():"GET"}}const t=e[0];return{url:Ud(t),method:mu(t,"method")?String(t.method).toUpperCase():"GET"}}let vo=null;function tE(e){const t="error";$n(t,e),Hn(t,nE)}function nE(){vo=F.onerror,F.onerror=function(e,t,n,r,i){return st("error",{column:r,error:i,line:n,msg:e,url:t}),vo&&!vo.__SENTRY_LOADER__?vo.apply(this,arguments):!1},F.onerror.__SENTRY_INSTRUMENTED__=!0}let _o=null;function rE(e){const t="unhandledrejection";$n(t,e),Hn(t,iE)}function iE(){_o=F.onunhandledrejection,F.onunhandledrejection=function(e){return st("unhandledrejection",e),_o&&!_o.__SENTRY_LOADER__?_o.apply(this,arguments):!0},F.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function oE(){return"npm"}function sE(){const e=typeof WeakSet=="function",t=e?new WeakSet:[];function n(i){if(e)return t.has(i)?!0:(t.add(i),!1);for(let o=0;o<t.length;o++)if(t[o]===i)return!0;return t.push(i),!1}function r(i){if(e)t.delete(i);else for(let o=0;o<t.length;o++)if(t[o]===i){t.splice(o,1);break}}return[n,r]}function $e(){const e=F,t=e.crypto||e.msCrypto;let n=()=>Math.random()*16;try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(n=()=>{const r=new Uint8Array(1);return t.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function l1(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function Dt(e){const{message:t,event_id:n}=e;if(t)return t;const r=l1(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function gu(e,t,n){const r=e.exception=e.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{};o.value||(o.value=t||""),o.type||(o.type="Error")}function Ni(e,t){const n=l1(e);if(!n)return;const r={type:"generic",handled:!0},i=n.mechanism;if(n.mechanism={...r,...i,...t},t&&"data"in t){const o={...i&&i.data,...t.data};n.mechanism.data=o}}function jd(e){if(e&&e.__sentry_captured__)return!0;try{Nn(e,"__sentry_captured__",!0)}catch{}return!1}function u1(e){return Array.isArray(e)?e:[e]}function wt(e,t=100,n=1/0){try{return yu("",e,t,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function c1(e,t=3,n=100*1024){const r=wt(e,t);return cE(r)>n?c1(e,t-1,n):r}function yu(e,t,n=1/0,r=1/0,i=sE()){const[o,s]=i;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const a=aE(e,t);if(!a.startsWith("[object "))return a;if(t.__sentry_skip_normalization__)return t;const l=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(l===0)return a.replace("object ","");if(o(t))return"[Circular ~]";const u=t;if(u&&typeof u.toJSON=="function")try{const m=u.toJSON();return yu("",m,l-1,r,i)}catch{}const c=Array.isArray(t)?[]:{};let f=0;const d=n1(t);for(const m in d){if(!Object.prototype.hasOwnProperty.call(d,m))continue;if(f>=r){c[m]="[MaxProperties ~]";break}const y=d[m];c[m]=yu(m,y,l-1,r,i),f++}return s(t),c}function aE(e,t){try{if(e==="domain"&&t&&typeof t=="object"&&t._events)return"[Domain]";if(e==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&t===global)return"[Global]";if(typeof window<"u"&&t===window)return"[Window]";if(typeof document<"u"&&t===document)return"[Document]";if(Km(t))return"[VueViewModel]";if(P_(t))return"[SyntheticEvent]";if(typeof t=="number"&&!Number.isFinite(t))return`[${t}]`;if(typeof t=="function")return`[Function: ${nn(t)}]`;if(typeof t=="symbol")return`[${String(t)}]`;if(typeof t=="bigint")return`[BigInt: ${String(t)}]`;const n=lE(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function lE(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}function uE(e){return~-encodeURI(e).split(/%..|./).length}function cE(e){return uE(JSON.stringify(e))}var Et;(function(e){e[e.PENDING=0]="PENDING";const n=1;e[e.RESOLVED=n]="RESOLVED";const r=2;e[e.REJECTED=r]="REJECTED"})(Et||(Et={}));function An(e){return new We(t=>{t(e)})}function ms(e){return new We((t,n)=>{n(e)})}class We{constructor(t){We.prototype.__init.call(this),We.prototype.__init2.call(this),We.prototype.__init3.call(this),We.prototype.__init4.call(this),this._state=Et.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(n){this._reject(n)}}then(t,n){return new We((r,i)=>{this._handlers.push([!1,o=>{if(!t)r(o);else try{r(t(o))}catch(s){i(s)}},o=>{if(!n)i(o);else try{r(n(o))}catch(s){i(s)}}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new We((n,r)=>{let i,o;return this.then(s=>{o=!1,i=s,t&&t()},s=>{o=!0,i=s,t&&t()}).then(()=>{if(o){r(i);return}n(i)})})}__init(){this._resolve=t=>{this._setResult(Et.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(Et.REJECTED,t)}}__init3(){this._setResult=(t,n)=>{if(this._state===Et.PENDING){if(na(n)){n.then(this._resolve,this._reject);return}this._state=t,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===Et.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===Et.RESOLVED&&n[1](this._value),this._state===Et.REJECTED&&n[2](this._value),n[0]=!0)})}}}function fE(e){const t=[];function n(){return e===void 0||t.length<e}function r(s){return t.splice(t.indexOf(s),1)[0]||Promise.resolve(void 0)}function i(s){if(!n())return ms(new ht("Not adding Promise because buffer limit was reached."));const a=s();return t.indexOf(a)===-1&&t.push(a),a.then(()=>r(a)).then(null,()=>r(a).then(null,()=>{})),a}function o(s){return new We((a,l)=>{let u=t.length;if(!u)return a(!0);const c=setTimeout(()=>{s&&s>0&&a(!1)},s);t.forEach(f=>{An(f).then(()=>{--u||(clearTimeout(c),a(!0))},l)})})}return{$:t,add:i,drain:o}}function Qa(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}const dE=["fatal","error","warning","log","info","debug"];function hE(e){return e==="warn"?"warning":dE.includes(e)?e:"log"}function pE(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&e!==void 0&&!e.includes("node_modules/")}function mE(e){const t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{const i=r.match(n);if(i){let o,s,a,l,u;if(i[1]){a=i[1];let d=a.lastIndexOf(".");if(a[d-1]==="."&&d--,d>0){o=a.slice(0,d),s=a.slice(d+1);const m=o.indexOf(".Module");m>0&&(a=a.slice(m+1),o=o.slice(0,m))}l=void 0}s&&(l=o,u=s),s==="<anonymous>"&&(u=void 0,a=void 0),a===void 0&&(u=u||tn,a=l?`${l}.${u}`:u);let c=i[2]&&i[2].startsWith("file://")?i[2].slice(7):i[2];const f=i[5]==="native";return c&&c.match(/\/[A-Z]:/)&&(c=c.slice(1)),!c&&i[5]&&!f&&(c=i[5]),{filename:c,module:void 0,function:a,lineno:zd(i[3]),colno:zd(i[4]),in_app:pE(c||"",f)}}if(r.match(t))return{filename:r}}}function gE(e){return[90,mE()]}function zd(e){return parseInt(e||"",10)||void 0}const yE="sentry-",vE=/^sentry-/;function _E(e){const t=EE(e);if(!t)return;const n=Object.entries(t).reduce((r,[i,o])=>{if(i.match(vE)){const s=i.slice(yE.length);r[s]=o}return r},{});if(Object.keys(n).length>0)return n}function EE(e){if(!(!e||!Tt(e)&&!Array.isArray(e)))return Array.isArray(e)?e.reduce((t,n)=>{const r=Gd(n);return Object.entries(r).forEach(([i,o])=>{t[i]=o}),t},{}):Gd(e)}function Gd(e){return e.split(",").map(t=>t.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((t,[n,r])=>(n&&r&&(t[n]=r),t),{})}function Vi(e,t=[]){return[e,t]}function SE(e,t){const[n,r]=e;return[n,[...r,t]]}function Vd(e,t){const n=e[1];for(const r of n){const i=r[0].type;if(t(r,i))return!0}return!1}function vu(e){return F.__SENTRY__&&F.__SENTRY__.encodePolyfill?F.__SENTRY__.encodePolyfill(e):new TextEncoder().encode(e)}function wE(e){const[t,n]=e;let r=JSON.stringify(t);function i(o){typeof r=="string"?r=typeof o=="string"?r+o:[vu(r),o]:r.push(typeof o=="string"?vu(o):o)}for(const o of n){const[s,a]=o;if(i(`
${JSON.stringify(s)}
`),typeof a=="string"||a instanceof Uint8Array)i(a);else{let l;try{l=JSON.stringify(a)}catch{l=JSON.stringify(wt(a))}i(l)}}return typeof r=="string"?r:LE(r)}function LE(e){const t=e.reduce((i,o)=>i+o.length,0),n=new Uint8Array(t);let r=0;for(const i of e)n.set(i,r),r+=i.length;return n}function xE(e){const t=typeof e.data=="string"?vu(e.data):e.data;return[De({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const CE={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function Wd(e){return CE[e]}function f1(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function TE(e,t,n,r){const i=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:oa(r)},...i&&{trace:De({...i})}}}function IE(e,t,n){const r=[{type:"client_report"},{timestamp:Gi(),discarded_events:e}];return Vi(t?{dsn:t}:{},[r])}const kE=60*1e3;function PE(e,t=Date.now()){const n=parseInt(`${e}`,10);if(!isNaN(n))return n*1e3;const r=Date.parse(`${e}`);return isNaN(r)?kE:r-t}function NE(e,t){return e[t]||e.all||0}function AE(e,t,n=Date.now()){return NE(e,t)>n}function RE(e,{statusCode:t,headers:n},r=Date.now()){const i={...e},o=n&&n["x-sentry-rate-limits"],s=n&&n["retry-after"];if(o)for(const a of o.trim().split(",")){const[l,u,,,c]=a.split(":",5),f=parseInt(l,10),d=(isNaN(f)?60:f)*1e3;if(!u)i.all=r+d;else for(const m of u.split(";"))m==="metric_bucket"?(!c||c.split(";").includes("custom"))&&(i[m]=r+d):i[m]=r+d}else s?i.all=r+PE(s,r):t===429&&(i.all=r+60*1e3);return i}function Xd(){return{traceId:$e(),spanId:$e().substring(16)}}const Eo=F;function OE(){const e=Eo.chrome,t=e&&e.app&&e.app.runtime,n="history"in Eo&&!!Eo.history.pushState&&!!Eo.history.replaceState;return!t&&n}const B=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function sa(){return Uc(F),F}function Uc(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||wn,t[wn]=t[wn]||{}}function bE(e){const t=It(),n={sid:$e(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>DE(n)};return e&&xr(n,e),n}function xr(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||It(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:$e()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function ME(e,t){let n={};e.status==="ok"&&(n={status:"exited"}),xr(e,n)}function DE(e){return De({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}const _u="_sentrySpan";function Yd(e,t){t?Nn(e,_u,t):delete e[_u]}function Qd(e){return e[_u]}const FE=100;class jc{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=Xd()}clone(){const t=new jc;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,Yd(t,Qd(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&xr(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,[r,i]=n instanceof Rn?[n.getScopeData(),n.getRequestSession()]:Lr(n)?[t,t.requestSession]:[],{tags:o,extra:s,user:a,contexts:l,level:u,fingerprint:c=[],propagationContext:f}=r||{};return this._tags={...this._tags,...o},this._extra={...this._extra,...s},this._contexts={...this._contexts,...l},a&&Object.keys(a).length&&(this._user=a),u&&(this._level=u),c.length&&(this._fingerprint=c),f&&(this._propagationContext=f),i&&(this._requestSession=i),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,Yd(this,void 0),this._attachments=[],this._propagationContext=Xd(),this._notifyScopeListeners(),this}addBreadcrumb(t,n){const r=typeof n=="number"?n:FE;if(r<=0)return this;const i={timestamp:Gi(),...t},o=this._breadcrumbs;return o.push(i),this._breadcrumbs=o.length>r?o.slice(-r):o,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Qd(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,n){const r=n&&n.event_id?n.event_id:$e();if(!this._client)return P.warn("No client configured on scope - will not capture exception!"),r;const i=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:i,...n,event_id:r},this),r}captureMessage(t,n,r){const i=r&&r.event_id?r.event_id:$e();if(!this._client)return P.warn("No client configured on scope - will not capture message!"),i;const o=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:o,...r,event_id:i},this),i}captureEvent(t,n){const r=n&&n.event_id?n.event_id:$e();return this._client?(this._client.captureEvent(t,{...n,event_id:r},this),r):(P.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}const Rn=jc;function $E(){return ia("defaultCurrentScope",()=>new Rn)}function HE(){return ia("defaultIsolationScope",()=>new Rn)}class BE{constructor(t,n){let r;t?r=t:r=new Rn;let i;n?i=n:i=new Rn,this._stack=[{scope:r}],this._isolationScope=i}withScope(t){const n=this._pushScope();let r;try{r=t(n)}catch(i){throw this._popScope(),i}return na(r)?r.then(i=>(this._popScope(),i),i=>{throw this._popScope(),i}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function Cr(){const e=sa(),t=Uc(e);return t.stack=t.stack||new BE($E(),HE())}function UE(e){return Cr().withScope(e)}function jE(e,t){const n=Cr();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function Zd(e){return Cr().withScope(()=>e(Cr().getIsolationScope()))}function zE(){return{withIsolationScope:Zd,withScope:UE,withSetScope:jE,withSetIsolationScope:(e,t)=>Zd(t),getCurrentScope:()=>Cr().getScope(),getIsolationScope:()=>Cr().getIsolationScope()}}function zc(e){const t=Uc(e);return t.acs?t.acs:zE()}function vt(){const e=sa();return zc(e).getCurrentScope()}function Bn(){const e=sa();return zc(e).getIsolationScope()}function GE(){return ia("globalScope",()=>new Rn)}function VE(...e){const t=sa(),n=zc(t);if(e.length===2){const[r,i]=e;return r?n.withSetScope(r,i):n.withScope(i)}return n.withScope(e[0])}function ve(){return vt().getClient()}const WE="_sentryMetrics";function XE(e){const t=e[WE];if(!t)return;const n={};for(const[,[r,i]]of t)(n[r]||(n[r]=[])).push(De(i));return n}const YE="sentry.source",QE="sentry.sample_rate",ZE="sentry.op",KE="sentry.origin",qE=0,JE=1,e4=1;function t4(e){const{spanId:t,traceId:n}=e.spanContext(),{parent_span_id:r}=gs(e);return De({parent_span_id:r,span_id:t,trace_id:n})}function Kd(e){return typeof e=="number"?qd(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?qd(e.getTime()):It()}function qd(e){return e>9999999999?e/1e3:e}function gs(e){if(r4(e))return e.getSpanJSON();try{const{spanId:t,traceId:n}=e.spanContext();if(n4(e)){const{attributes:r,startTime:i,name:o,endTime:s,parentSpanId:a,status:l}=e;return De({span_id:t,trace_id:n,data:r,description:o,parent_span_id:a,start_timestamp:Kd(i),timestamp:Kd(s)||void 0,status:o4(l),op:r[ZE],origin:r[KE],_metrics_summary:XE(e)})}return{span_id:t,trace_id:n}}catch{return{}}}function n4(e){const t=e;return!!t.attributes&&!!t.startTime&&!!t.name&&!!t.endTime&&!!t.status}function r4(e){return typeof e.getSpanJSON=="function"}function i4(e){const{traceFlags:t}=e.spanContext();return t===e4}function o4(e){if(!(!e||e.code===qE))return e.code===JE?"ok":e.message||"unknown_error"}const s4="_sentryRootSpan";function d1(e){return e[s4]||e}function a4(e){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=ve(),n=t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const Gc="production",l4="_frozenDsc";function h1(e,t){const n=t.getOptions(),{publicKey:r}=t.getDsn()||{},i=De({environment:n.environment||Gc,release:n.release,public_key:r,trace_id:e});return t.emit("createDsc",i),i}function u4(e){const t=ve();if(!t)return{};const n=h1(gs(e).trace_id||"",t),r=d1(e),i=r[l4];if(i)return i;const o=r.spanContext().traceState,s=o&&o.get("sentry.dsc"),a=s&&_E(s);if(a)return a;const l=gs(r),u=l.data||{},c=u[QE];c!=null&&(n.sample_rate=`${c}`);const f=u[YE],d=l.description;return f!=="url"&&d&&(n.transaction=d),a4()&&(n.sampled=String(i4(r))),t.emit("createDsc",n,r),n}function c4(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;if(typeof t!="number"||isNaN(t)||t<0||t>1){B&&P.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`);return}return t}function f4(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function d4(e,t,n,r){const i=f1(n),o={sent_at:new Date().toISOString(),...i&&{sdk:i},...!!r&&t&&{dsn:oa(t)}},s="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return Vi(o,[s])}function h4(e,t,n,r){const i=f1(n),o=e.type&&e.type!=="replay_event"?e.type:"event";f4(e,n&&n.sdk);const s=TE(e,i,r,t);return delete e.sdkProcessingMetadata,Vi(s,[[{type:o},e]])}function Eu(e,t,n,r=0){return new We((i,o)=>{const s=e[r];if(t===null||typeof s!="function")i(t);else{const a=s({...t},n);B&&s.id&&a===null&&P.log(`Event processor "${s.id}" dropped event`),na(a)?a.then(l=>Eu(e,l,n,r+1).then(i)).then(null,o):Eu(e,a,n,r+1).then(i).then(null,o)}})}function p4(e,t){const{fingerprint:n,span:r,breadcrumbs:i,sdkProcessingMetadata:o}=t;m4(e,t),r&&v4(e,r),_4(e,n),g4(e,i),y4(e,o)}function Su(e,t){const{extra:n,tags:r,user:i,contexts:o,level:s,sdkProcessingMetadata:a,breadcrumbs:l,fingerprint:u,eventProcessors:c,attachments:f,propagationContext:d,transactionName:m,span:y}=t;Xr(e,"extra",n),Xr(e,"tags",r),Xr(e,"user",i),Xr(e,"contexts",o),Xr(e,"sdkProcessingMetadata",a),s&&(e.level=s),m&&(e.transactionName=m),y&&(e.span=y),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),f.length&&(e.attachments=[...e.attachments,...f]),e.propagationContext={...e.propagationContext,...d}}function Xr(e,t,n){if(n&&Object.keys(n).length){e[t]={...e[t]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[t][r]=n[r])}}function m4(e,t){const{extra:n,tags:r,user:i,contexts:o,level:s,transactionName:a}=t,l=De(n);l&&Object.keys(l).length&&(e.extra={...l,...e.extra});const u=De(r);u&&Object.keys(u).length&&(e.tags={...u,...e.tags});const c=De(i);c&&Object.keys(c).length&&(e.user={...c,...e.user});const f=De(o);f&&Object.keys(f).length&&(e.contexts={...f,...e.contexts}),s&&(e.level=s),a&&e.type!=="transaction"&&(e.transaction=a)}function g4(e,t){const n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}function y4(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}function v4(e,t){e.contexts={trace:t4(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:u4(t),...e.sdkProcessingMetadata};const n=d1(t),r=gs(n).description;r&&!e.transaction&&e.type==="transaction"&&(e.transaction=r)}function _4(e,t){e.fingerprint=e.fingerprint?u1(e.fingerprint):[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}function E4(e,t,n,r,i,o){const{normalizeDepth:s=3,normalizeMaxBreadth:a=1e3}=e,l={...t,event_id:t.event_id||n.event_id||$e(),timestamp:t.timestamp||Gi()},u=n.integrations||e.integrations.map(S=>S.name);S4(l,e),x4(l,u),i&&i.emit("applyFrameMetadata",t),t.type===void 0&&w4(l,e.stackParser);const c=T4(r,n.captureContext);n.mechanism&&Ni(l,n.mechanism);const f=i?i.getEventProcessors():[],d=GE().getScopeData();if(o){const S=o.getScopeData();Su(d,S)}if(c){const S=c.getScopeData();Su(d,S)}const m=[...n.attachments||[],...d.attachments];m.length&&(n.attachments=m),p4(l,d);const y=[...f,...d.eventProcessors];return Eu(y,l,n).then(S=>(S&&L4(S),typeof s=="number"&&s>0?C4(S,s,a):S))}function S4(e,t){const{environment:n,release:r,dist:i,maxValueLength:o=250}=t;"environment"in e||(e.environment="environment"in t?n:Gc),e.release===void 0&&r!==void 0&&(e.release=r),e.dist===void 0&&i!==void 0&&(e.dist=i),e.message&&(e.message=ur(e.message,o));const s=e.exception&&e.exception.values&&e.exception.values[0];s&&s.value&&(s.value=ur(s.value,o));const a=e.request;a&&a.url&&(a.url=ur(a.url,o))}const Jd=new WeakMap;function w4(e,t){const n=F._sentryDebugIds;if(!n)return;let r;const i=Jd.get(t);i?r=i:(r=new Map,Jd.set(t,r));const o=Object.entries(n).reduce((s,[a,l])=>{let u;const c=r.get(a);c?u=c:(u=t(a),r.set(a,u));for(let f=u.length-1;f>=0;f--){const d=u[f];if(d.filename){s[d.filename]=l;break}}return s},{});try{e.exception.values.forEach(s=>{s.stacktrace.frames.forEach(a=>{a.filename&&(a.debug_id=o[a.filename])})})}catch{}}function L4(e){const t={};try{e.exception.values.forEach(r=>{r.stacktrace.frames.forEach(i=>{i.debug_id&&(i.abs_path?t[i.abs_path]=i.debug_id:i.filename&&(t[i.filename]=i.debug_id),delete i.debug_id)})})}catch{}if(Object.keys(t).length===0)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.entries(t).forEach(([r,i])=>{n.push({type:"sourcemap",code_file:r,debug_id:i})})}function x4(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}function C4(e,t,n){if(!e)return null;const r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(i=>({...i,...i.data&&{data:wt(i.data,t,n)}}))},...e.user&&{user:wt(e.user,t,n)},...e.contexts&&{contexts:wt(e.contexts,t,n)},...e.extra&&{extra:wt(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=wt(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(i=>({...i,...i.data&&{data:wt(i.data,t,n)}}))),r}function T4(e,t){if(!t)return e;const n=e?e.clone():new Rn;return n.update(t),n}function I4(e,t){return vt().captureException(e,void 0)}function p1(e,t){return vt().captureEvent(e,t)}function eh(e){const t=ve(),n=Bn(),r=vt(),{release:i,environment:o=Gc}=t&&t.getOptions()||{},{userAgent:s}=F.navigator||{},a=bE({release:i,environment:o,user:r.getUser()||n.getUser(),...s&&{userAgent:s},...e}),l=n.getSession();return l&&l.status==="ok"&&xr(l,{status:"exited"}),m1(),n.setSession(a),r.setSession(a),a}function m1(){const e=Bn(),t=vt(),n=t.getSession()||e.getSession();n&&ME(n),g1(),e.setSession(),t.setSession()}function g1(){const e=Bn(),t=vt(),n=ve(),r=t.getSession()||e.getSession();r&&n&&n.captureSession(r)}function th(e=!1){if(e){m1();return}g1()}const k4="7";function P4(e){const t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function N4(e){return`${P4(e)}${e.projectId}/envelope/`}function A4(e,t){return G_({sentry_key:e.publicKey,sentry_version:k4,...t&&{sentry_client:`${t.name}/${t.version}`}})}function R4(e,t,n){return t||`${N4(e)}?${A4(e,n)}`}const nh=[];function O4(e){const t={};return e.forEach(n=>{const{name:r}=n,i=t[r];i&&!i.isDefaultInstance&&n.isDefaultInstance||(t[r]=n)}),Object.values(t)}function b4(e){const t=e.defaultIntegrations||[],n=e.integrations;t.forEach(s=>{s.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...t,...n]:typeof n=="function"?r=u1(n(t)):r=t;const i=O4(r),o=i.findIndex(s=>s.name==="Debug");if(o>-1){const[s]=i.splice(o,1);i.push(s)}return i}function M4(e,t){const n={};return t.forEach(r=>{r&&y1(e,r,n)}),n}function rh(e,t){for(const n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function y1(e,t,n){if(n[t.name]){B&&P.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,nh.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),nh.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(i,o)=>r(i,o,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),i=Object.assign((o,s)=>r(o,s,e),{id:t.name});e.addEventProcessor(i)}B&&P.log(`Integration installed: ${t.name}`)}const ih="Not capturing exception because it's already been captured.";class D4{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=z_(t.dsn):B&&P.warn("No DSN provided, client will not send events."),this._dsn){const n=R4(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const i=$e();if(jd(t))return B&&P.log(ih),i;const o={event_id:i,...n};return this._process(this.eventFromException(t,o).then(s=>this._captureEvent(s,o,r))),o.event_id}captureMessage(t,n,r,i){const o={event_id:$e(),...r},s=Fc(t)?t:String(t),a=$c(t)?this.eventFromMessage(s,n,o):this.eventFromException(t,o);return this._process(a.then(l=>this._captureEvent(l,o,i))),o.event_id}captureEvent(t,n,r){const i=$e();if(n&&n.originalException&&jd(n.originalException))return B&&P.log(ih),i;const o={event_id:i,...n},a=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,o,a||r)),o.event_id}captureSession(t){typeof t.release!="string"?B&&P.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),xr(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(i=>r&&i))):An(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];y1(this,t,this._integrations),n||rh(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=h4(t,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=SE(r,xE(o));const i=this.sendEnvelope(r);i&&i.then(o=>this.emit("afterSendEvent",t,o),null)}sendSession(t){const n=d4(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(t,n,r){if(this._options.sendClientReports){const i=typeof r=="number"?r:1,o=`${t}:${n}`;B&&P.log(`Recording outcome: "${o}"${i>1?` (${i} times)`:""}`),this._outcomes[o]=(this._outcomes[o]||0)+i}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const i=r.indexOf(n);i>-1&&r.splice(i,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(i=>i(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(B&&P.error("Error while sending event:",n),n)):(B&&P.error("Transport disabled"),An({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=M4(this,t),rh(this,t)}_updateSessionFromEvent(t,n){let r=!1,i=!1;const o=n.exception&&n.exception.values;if(o){i=!0;for(const l of o){const u=l.mechanism;if(u&&u.handled===!1){r=!0;break}}}const s=t.status==="ok";(s&&t.errors===0||s&&r)&&(xr(t,{...r&&{status:"crashed"},errors:t.errors||Number(i||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new We(n=>{let r=0;const i=1,o=setInterval(()=>{this._numProcessing==0?(clearInterval(o),n(!0)):(r+=i,t&&r>=t&&(clearInterval(o),n(!1)))},i)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,i=Bn()){const o=this.getOptions(),s=Object.keys(this._integrations);return!n.integrations&&s.length>0&&(n.integrations=s),this.emit("preprocessEvent",t,n),t.type||i.setLastEventId(t.event_id||n.event_id),E4(o,t,n,r,this,i).then(a=>{if(a===null)return a;const l={...i.getPropagationContext(),...r?r.getPropagationContext():void 0};if(!(a.contexts&&a.contexts.trace)&&l){const{traceId:c,spanId:f,parentSpanId:d,dsc:m}=l;a.contexts={trace:De({trace_id:c,span_id:f,parent_span_id:d}),...a.contexts};const y=m||h1(c,this);a.sdkProcessingMetadata={dynamicSamplingContext:y,...a.sdkProcessingMetadata}}return a})}_captureEvent(t,n={},r){return this._processEvent(t,n,r).then(i=>i.event_id,i=>{if(B){const o=i;o.logLevel==="log"?P.log(o.message):P.warn(o)}})}_processEvent(t,n,r){const i=this.getOptions(),{sampleRate:o}=i,s=_1(t),a=v1(t),l=t.type||"error",u=`before send for type \`${l}\``,c=typeof o>"u"?void 0:c4(o);if(a&&typeof c=="number"&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",t),ms(new ht(`Discarding event because it's not included in the random sample (sampling rate = ${o})`,"log"));const f=l==="replay_event"?"replay":l,m=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,n,r,m).then(y=>{if(y===null)throw this.recordDroppedEvent("event_processor",f,t),new ht("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return y;const S=$4(this,i,y,n);return F4(S,u)}).then(y=>{if(y===null){if(this.recordDroppedEvent("before_send",f,t),s){const h=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",h)}throw new ht(`${u} returned \`null\`, will not send event.`,"log")}const v=r&&r.getSession();if(!s&&v&&this._updateSessionFromEvent(v,y),s){const p=y.sdkProcessingMetadata&&y.sdkProcessingMetadata.spanCountBeforeProcessing||0,h=y.spans?y.spans.length:0,g=p-h;g>0&&this.recordDroppedEvent("before_send","span",g)}const S=y.transaction_info;if(s&&S&&y.transaction!==t.transaction){const p="custom";y.transaction_info={...S,source:p}}return this.sendEvent(y,n),y}).then(null,y=>{throw y instanceof ht?y:(this.captureException(y,{data:{__sentry__:!0},originalException:y}),new ht(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${y}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[i,o]=n.split(":");return{reason:i,category:o,quantity:r}})}_flushOutcomes(){B&&P.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0){B&&P.log("No outcomes to send");return}if(!this._dsn){B&&P.log("No dsn provided, will not send outcomes");return}B&&P.log("Sending outcomes:",t);const n=IE(t,this._options.tunnel&&oa(this._dsn));this.sendEnvelope(n)}}function F4(e,t){const n=`${t} must return \`null\` or a valid event.`;if(na(e))return e.then(r=>{if(!Lr(r)&&r!==null)throw new ht(n);return r},r=>{throw new ht(`${t} rejected with ${r}`)});if(!Lr(e)&&e!==null)throw new ht(n);return e}function $4(e,t,n,r){const{beforeSend:i,beforeSendTransaction:o,beforeSendSpan:s}=t;if(v1(n)&&i)return i(n,r);if(_1(n)){if(n.spans&&s){const a=[];for(const l of n.spans){const u=s(l);u?a.push(u):e.recordDroppedEvent("before_send","span")}n.spans=a}if(o){if(n.spans){const a=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:a}}return o(n,r)}}return n}function v1(e){return e.type===void 0}function _1(e){return e.type==="transaction"}function H4(e,t){t.debug===!0&&(B?P.enable():zi(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),vt().update(t.initialScope);const r=new e(t);return B4(r),r.init(),r}function B4(e){vt().setClient(e)}const U4=64;function E1(e,t,n=fE(e.bufferSize||U4)){let r={};const i=s=>n.drain(s);function o(s){const a=[];if(Vd(s,(f,d)=>{const m=Wd(d);if(AE(r,m)){const y=oh(f,d);e.recordDroppedEvent("ratelimit_backoff",m,y)}else a.push(f)}),a.length===0)return An({});const l=Vi(s[0],a),u=f=>{Vd(l,(d,m)=>{const y=oh(d,m);e.recordDroppedEvent(f,Wd(m),y)})},c=()=>t({body:wE(l)}).then(f=>(f.statusCode!==void 0&&(f.statusCode<200||f.statusCode>=300)&&B&&P.warn(`Sentry responded with status code ${f.statusCode} to sent event.`),r=RE(r,f),f),f=>{throw u("network_error"),f});return n.add(c).then(f=>f,f=>{if(f instanceof ht)return B&&P.error("Skipped sending event because buffer is full."),u("queue_overflow"),An({});throw f})}return{send:o,flush:i}}function oh(e,t){if(!(t!=="event"&&t!=="transaction"))return Array.isArray(e)?e[1]:void 0}function j4(e,t,n=[t],r="npm"){const i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:wn})),version:wn}),e._metadata=i}const z4=100;function On(e,t){const n=ve(),r=Bn();if(!n)return;const{beforeBreadcrumb:i=null,maxBreadcrumbs:o=z4}=n.getOptions();if(o<=0)return;const a={timestamp:Gi(),...e},l=i?zi(()=>i(a,t)):a;l!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",l,t),r.addBreadcrumb(l,o))}let sh;const G4="FunctionToString",ah=new WeakMap,V4=()=>({name:G4,setupOnce(){sh=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=Bc(this),n=ah.has(ve())&&t!==void 0?t:this;return sh.apply(n,e)}}catch{}},setup(e){ah.set(e,!0)}}),W4=V4,X4=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"],Y4="InboundFilters",Q4=(e={})=>({name:Y4,processEvent(t,n,r){const i=r.getOptions(),o=K4(e,i);return q4(t,o)?null:t}}),Z4=Q4;function K4(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:X4],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function q4(e,t){return t.ignoreInternal&&iS(e)?(B&&P.warn(`Event dropped due to being internal Sentry Error.
Event: ${Dt(e)}`),!0):J4(e,t.ignoreErrors)?(B&&P.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Dt(e)}`),!0):sS(e)?(B&&P.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${Dt(e)}`),!0):eS(e,t.ignoreTransactions)?(B&&P.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Dt(e)}`),!0):tS(e,t.denyUrls)?(B&&P.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Dt(e)}.
Url: ${ys(e)}`),!0):nS(e,t.allowUrls)?!1:(B&&P.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Dt(e)}.
Url: ${ys(e)}`),!0)}function J4(e,t){return e.type||!t||!t.length?!1:rS(e).some(n=>ra(n,t))}function eS(e,t){if(e.type!=="transaction"||!t||!t.length)return!1;const n=e.transaction;return n?ra(n,t):!1}function tS(e,t){if(!t||!t.length)return!1;const n=ys(e);return n?ra(n,t):!1}function nS(e,t){if(!t||!t.length)return!0;const n=ys(e);return n?ra(n,t):!0}function rS(e){const t=[];e.message&&t.push(e.message);let n;try{n=e.exception.values[e.exception.values.length-1]}catch{}return n&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`)),t}function iS(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function oS(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function ys(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?oS(t):null}catch{return B&&P.error(`Cannot extract url for event ${Dt(e)}`),null}}function sS(e){return e.type||!e.exception||!e.exception.values||e.exception.values.length===0?!1:!e.message&&!e.exception.values.some(t=>t.stacktrace||t.type&&t.type!=="Error"||t.value)}const aS="Dedupe",lS=()=>{let e;return{name:aS,processEvent(t){if(t.type)return t;try{if(cS(t,e))return B&&P.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}},uS=lS;function cS(e,t){return t?!!(fS(e,t)||dS(e,t)):!1}function fS(e,t){const n=e.message,r=t.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!w1(e,t)||!S1(e,t))}function dS(e,t){const n=lh(t),r=lh(e);return!(!n||!r||n.type!==r.type||n.value!==r.value||!w1(e,t)||!S1(e,t))}function S1(e,t){let n=Hd(e),r=Hd(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let i=0;i<r.length;i++){const o=r[i],s=n[i];if(o.filename!==s.filename||o.lineno!==s.lineno||o.colno!==s.colno||o.function!==s.function)return!1}return!0}function w1(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function lh(e){return e.exception&&e.exception.values&&e.exception.values[0]}const U=F;let wu=0;function L1(){return wu>0}function hS(){wu++,setTimeout(()=>{wu--})}function Tr(e,t={},n){if(typeof e!="function")return e;try{const i=e.__sentry_wrapped__;if(i)return typeof i=="function"?i:e;if(Bc(e))return e}catch{return e}const r=function(){const i=Array.prototype.slice.call(arguments);try{const o=i.map(s=>Tr(s,t));return e.apply(this,o)}catch(o){throw hS(),VE(s=>{s.addEventProcessor(a=>(t.mechanism&&(gu(a,void 0),Ni(a,t.mechanism)),a.extra={...a.extra,arguments:i},a)),I4(o)}),o}};try{for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])}catch{}t1(r,e),Nn(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return e.name}})}catch{}return r}const Wi=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Vc(e,t){const n=Wc(e,t),r={type:t&&t.name,value:vS(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function pS(e,t,n,r){const i=ve(),o=i&&i.getOptions().normalizeDepth,s=LS(t),a={__serialized__:c1(t,o)};if(s)return{exception:{values:[Vc(e,s)]},extra:a};const l={exception:{values:[{type:ta(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:SS(t,{isUnhandledRejection:r})}]},extra:a};if(n){const u=Wc(e,n);u.length&&(l.exception.values[0].stacktrace={frames:u})}return l}function Za(e,t){return{exception:{values:[Vc(e,t)]}}}function Wc(e,t){const n=t.stacktrace||t.stack||"",r=gS(t),i=yS(t);try{return e(n,r,i)}catch{}return[]}const mS=/Minified React error #\d+;/i;function gS(e){return e&&mS.test(e.message)?1:0}function yS(e){return typeof e.framesToPop=="number"?e.framesToPop:0}function vS(e){const t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function _S(e,t,n,r){const i=n&&n.syntheticException||void 0,o=Xc(e,t,i,r);return Ni(o),o.level="error",n&&n.event_id&&(o.event_id=n.event_id),An(o)}function ES(e,t,n="info",r,i){const o=r&&r.syntheticException||void 0,s=Lu(e,t,o,i);return s.level=n,r&&r.event_id&&(s.event_id=r.event_id),An(s)}function Xc(e,t,n,r,i){let o;if(Zm(t)&&t.error)return Za(e,t.error);if(Ad(t)||T_(t)){const s=t;if("stack"in t)o=Za(e,t);else{const a=s.name||(Ad(s)?"DOMError":"DOMException"),l=s.message?`${a}: ${s.message}`:a;o=Lu(e,l,n,r),gu(o,l)}return"code"in s&&(o.tags={...o.tags,"DOMException.code":`${s.code}`}),o}return Dc(t)?Za(e,t):Lr(t)||ta(t)?(o=pS(e,t,n,i),Ni(o,{synthetic:!0}),o):(o=Lu(e,t,n,r),gu(o,`${t}`),Ni(o,{synthetic:!0}),o)}function Lu(e,t,n,r){const i={};if(r&&n){const o=Wc(e,n);o.length&&(i.exception={values:[{value:t,stacktrace:{frames:o}}]})}if(Fc(t)){const{__sentry_template_string__:o,__sentry_template_values__:s}=t;return i.logentry={message:o,params:s},i}return i.message=t,i}function SS(e,{isUnhandledRejection:t}){const n=V_(e),r=t?"promise rejection":"exception";return Zm(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:ta(e)?`Event \`${wS(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function wS(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch{}}function LS(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const n=e[t];if(n instanceof Error)return n}}function xS(e,{metadata:t,tunnel:n,dsn:r}){const i={event_id:e.event_id,sent_at:new Date().toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!r&&{dsn:oa(r)}},o=CS(e);return Vi(i,[o])}function CS(e){return[{type:"user_report"},e]}class TS extends D4{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t},r=U.SENTRY_SDK_SOURCE||oE();j4(n,"browser",["browser"],r),super(n),n.sendClientReports&&U.document&&U.document.addEventListener("visibilitychange",()=>{U.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,n){return _S(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return ES(this._options.stackParser,t,n,r,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled()){Wi&&P.warn("SDK not enabled, will not capture user feedback.");return}const n=xS(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}_prepareEvent(t,n,r){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r)}}const IS=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,ge=F,kS=1e3;let uh,xu,Cu;function PS(e){const t="dom";$n(t,e),Hn(t,NS)}function NS(){if(!ge.document)return;const e=st.bind(null,"dom"),t=ch(e,!0);ge.document.addEventListener("click",t,!1),ge.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{const r=ge[n]&&ge[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(Me(r,"addEventListener",function(i){return function(o,s,a){if(o==="click"||o=="keypress")try{const l=this,u=l.__sentry_instrumentation_handlers__=l.__sentry_instrumentation_handlers__||{},c=u[o]=u[o]||{refCount:0};if(!c.handler){const f=ch(e);c.handler=f,i.call(this,o,f,a)}c.refCount++}catch{}return i.call(this,o,s,a)}}),Me(r,"removeEventListener",function(i){return function(o,s,a){if(o==="click"||o=="keypress")try{const l=this,u=l.__sentry_instrumentation_handlers__||{},c=u[o];c&&(c.refCount--,c.refCount<=0&&(i.call(this,o,c.handler,a),c.handler=void 0,delete u[o]),Object.keys(u).length===0&&delete l.__sentry_instrumentation_handlers__)}catch{}return i.call(this,o,s,a)}}))})}function AS(e){if(e.type!==xu)return!1;try{if(!e.target||e.target._sentryId!==Cu)return!1}catch{}return!0}function RS(e,t){return e!=="keypress"?!1:!t||!t.tagName?!0:!(t.tagName==="INPUT"||t.tagName==="TEXTAREA"||t.isContentEditable)}function ch(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=OS(n);if(RS(n.type,r))return;Nn(n,"_sentryCaptured",!0),r&&!r._sentryId&&Nn(r,"_sentryId",$e());const i=n.type==="keypress"?"input":n.type;AS(n)||(e({event:n,name:i,global:t}),xu=n.type,Cu=r?r._sentryId:void 0),clearTimeout(uh),uh=ge.setTimeout(()=>{Cu=void 0,xu=void 0},kS)}}function OS(e){try{return e.target}catch{return null}}let So;function x1(e){const t="history";$n(t,e),Hn(t,bS)}function bS(){if(!OE())return;const e=ge.onpopstate;ge.onpopstate=function(...n){const r=ge.location.href,i=So;if(So=r,st("history",{from:i,to:r}),e)try{return e.apply(this,n)}catch{}};function t(n){return function(...r){const i=r.length>2?r[2]:void 0;if(i){const o=So,s=String(i);So=s,st("history",{from:o,to:s})}return n.apply(this,r)}}Me(ge.history,"pushState",t),Me(ge.history,"replaceState",t)}const Bo={};function MS(e){const t=Bo[e];if(t)return t;let n=ge[e];if(pu(n))return Bo[e]=n.bind(ge);const r=ge.document;if(r&&typeof r.createElement=="function")try{const i=r.createElement("iframe");i.hidden=!0,r.head.appendChild(i);const o=i.contentWindow;o&&o[e]&&(n=o[e]),r.head.removeChild(i)}catch(i){IS&&P.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,i)}return n&&(Bo[e]=n.bind(ge))}function fh(e){Bo[e]=void 0}const Jr="__sentry_xhr_v3__";function DS(e){const t="xhr";$n(t,e),Hn(t,FS)}function FS(){if(!ge.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const i=It()*1e3,o=Tt(r[0])?r[0].toUpperCase():void 0,s=$S(r[1]);if(!o||!s)return t.apply(n,r);n[Jr]={method:o,url:s,request_headers:{}},o==="POST"&&s.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const a=()=>{const l=n[Jr];if(l&&n.readyState===4){try{l.status_code=n.status}catch{}const u={endTimestamp:It()*1e3,startTimestamp:i,xhr:n};st("xhr",u)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(l,u,c){return a(),l.apply(u,c)}}):n.addEventListener("readystatechange",a),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(l,u,c){const[f,d]=c,m=u[Jr];return m&&Tt(f)&&Tt(d)&&(m.request_headers[f.toLowerCase()]=d),l.apply(u,c)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const i=n[Jr];if(!i)return t.apply(n,r);r[0]!==void 0&&(i.body=r[0]);const o={startTimestamp:It()*1e3,xhr:n};return st("xhr",o),t.apply(n,r)}})}function $S(e){if(Tt(e))return e;try{return e.toString()}catch{}}function HS(e,t=MS("fetch")){let n=0,r=0;function i(o){const s=o.body.length;n+=s,r++;const a={body:o.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return fh("fetch"),ms("No fetch implementation available");try{return t(e.url,a).then(l=>(n-=s,r--,{statusCode:l.status,headers:{"x-sentry-rate-limits":l.headers.get("X-Sentry-Rate-Limits"),"retry-after":l.headers.get("Retry-After")}}))}catch(l){return fh("fetch"),n-=s,r--,ms(l)}}return E1(e,i)}const BS=30,US=50;function Tu(e,t,n,r){const i={filename:e,function:t==="<anonymous>"?tn:t,in_app:!0};return n!==void 0&&(i.lineno=n),r!==void 0&&(i.colno=r),i}const jS=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,zS=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,GS=/\((\S*)(?::(\d+))(?::(\d+))\)/,VS=e=>{const t=jS.exec(e);if(t){const[,r,i,o]=t;return Tu(r,tn,+i,+o)}const n=zS.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const s=GS.exec(n[2]);s&&(n[2]=s[1],n[3]=s[2],n[4]=s[3])}const[i,o]=T1(n[1]||tn,n[2]);return Tu(o,i,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},C1=[BS,VS],WS=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,XS=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,YS=e=>{const t=WS.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const o=XS.exec(t[3]);o&&(t[1]=t[1]||"eval",t[3]=o[1],t[4]=o[2],t[5]="")}let r=t[3],i=t[1]||tn;return[i,r]=T1(i,r),Tu(r,i,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},QS=[US,YS],ZS=[C1,QS],KS=i1(...ZS),T1=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:tn,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},wo=1024,qS="Breadcrumbs",JS=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:qS,setup(n){t.console&&Y_(r5(n)),t.dom&&PS(n5(n,t.dom)),t.xhr&&DS(i5(n)),t.fetch&&q_(o5(n)),t.history&&x1(s5(n)),t.sentry&&n.on("beforeSendEvent",t5(n))}}},e5=JS;function t5(e){return function(n){ve()===e&&On({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:Dt(n)},{event:n})}}function n5(e,t){return function(r){if(ve()!==e)return;let i,o,s=typeof t=="object"?t.serializeAttribute:void 0,a=typeof t=="object"&&typeof t.maxStringLength=="number"?t.maxStringLength:void 0;a&&a>wo&&(Wi&&P.warn(`\`dom.maxStringLength\` cannot exceed ${wo}, but a value of ${a} was configured. Sentry will use ${wo} instead.`),a=wo),typeof s=="string"&&(s=[s]);try{const u=r.event,c=a5(u)?u.target:u;i=Jm(c,{keyAttrs:s,maxStringLength:a}),o=D_(c)}catch{i="<unknown>"}if(i.length===0)return;const l={category:`ui.${r.name}`,message:i};o&&(l.data={"ui.component_name":o}),On(l,{event:r.event,name:r.name,global:r.global})}}function r5(e){return function(n){if(ve()!==e)return;const r={category:"console",data:{arguments:n.args,logger:"console"},level:hE(n.level),message:Rd(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)r.message=`Assertion failed: ${Rd(n.args.slice(1)," ")||"console.assert"}`,r.data.arguments=n.args.slice(1);else return;On(r,{input:n.args,level:n.level})}}function i5(e){return function(n){if(ve()!==e)return;const{startTimestamp:r,endTimestamp:i}=n,o=n.xhr[Jr];if(!r||!i||!o)return;const{method:s,url:a,status_code:l,body:u}=o,c={method:s,url:a,status_code:l},f={xhr:n.xhr,input:u,startTimestamp:r,endTimestamp:i},d=qm(l);On({category:"xhr",data:c,type:"http",level:d},f)}}function o5(e){return function(n){if(ve()!==e)return;const{startTimestamp:r,endTimestamp:i}=n;if(i&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.error){const o=n.fetchData,s={data:n.error,input:n.args,startTimestamp:r,endTimestamp:i};On({category:"fetch",data:o,level:"error",type:"http"},s)}else{const o=n.response,s={...n.fetchData,status_code:o&&o.status},a={input:n.args,response:o,startTimestamp:r,endTimestamp:i},l=qm(s.status_code);On({category:"fetch",data:s,type:"http",level:l},a)}}}function s5(e){return function(n){if(ve()!==e)return;let r=n.from,i=n.to;const o=Qa(U.location.href);let s=r?Qa(r):void 0;const a=Qa(i);(!s||!s.path)&&(s=o),o.protocol===a.protocol&&o.host===a.host&&(i=a.relative),o.protocol===s.protocol&&o.host===s.host&&(r=s.relative),On({category:"navigation",data:{from:r,to:i}})}}function a5(e){return!!e&&!!e.target}const l5=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],u5="BrowserApiErrors",c5=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:u5,setupOnce(){t.setTimeout&&Me(U,"setTimeout",dh),t.setInterval&&Me(U,"setInterval",dh),t.requestAnimationFrame&&Me(U,"requestAnimationFrame",d5),t.XMLHttpRequest&&"XMLHttpRequest"in U&&Me(XMLHttpRequest.prototype,"send",h5);const n=t.eventTarget;n&&(Array.isArray(n)?n:l5).forEach(p5)}}},f5=c5;function dh(e){return function(...t){const n=t[0];return t[0]=Tr(n,{mechanism:{data:{function:nn(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function d5(e){return function(t){return e.apply(this,[Tr(t,{mechanism:{data:{function:"requestAnimationFrame",handler:nn(e)},handled:!1,type:"instrument"}})])}}function h5(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(i=>{i in n&&typeof n[i]=="function"&&Me(n,i,function(o){const s={mechanism:{data:{function:i,handler:nn(o)},handled:!1,type:"instrument"}},a=Bc(o);return a&&(s.mechanism.data.handler=nn(a)),Tr(o,s)})}),e.apply(this,t)}}function p5(e){const t=U,n=t[e]&&t[e].prototype;!n||!n.hasOwnProperty||!n.hasOwnProperty("addEventListener")||(Me(n,"addEventListener",function(r){return function(i,o,s){try{typeof o.handleEvent=="function"&&(o.handleEvent=Tr(o.handleEvent,{mechanism:{data:{function:"handleEvent",handler:nn(o),target:e},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[i,Tr(o,{mechanism:{data:{function:"addEventListener",handler:nn(o),target:e},handled:!1,type:"instrument"}}),s])}}),Me(n,"removeEventListener",function(r){return function(i,o,s){const a=o;try{const l=a&&a.__sentry_wrapped__;l&&r.call(this,i,l,s)}catch{}return r.call(this,i,a,s)}}))}const m5="GlobalHandlers",g5=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:m5,setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(v5(n),hh("onerror")),t.onunhandledrejection&&(_5(n),hh("onunhandledrejection"))}}},y5=g5;function v5(e){tE(t=>{const{stackParser:n,attachStacktrace:r}=I1();if(ve()!==e||L1())return;const{msg:i,url:o,line:s,column:a,error:l}=t,u=w5(Xc(n,l||i,void 0,r,!1),o,s,a);u.level="error",p1(u,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})}function _5(e){rE(t=>{const{stackParser:n,attachStacktrace:r}=I1();if(ve()!==e||L1())return;const i=E5(t),o=$c(i)?S5(i):Xc(n,i,void 0,r,!0);o.level="error",p1(o,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function E5(e){if($c(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}function S5(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function w5(e,t,n,r){const i=e.exception=e.exception||{},o=i.values=i.values||[],s=o[0]=o[0]||{},a=s.stacktrace=s.stacktrace||{},l=a.frames=a.frames||[],u=isNaN(parseInt(r,10))?void 0:r,c=isNaN(parseInt(n,10))?void 0:n,f=Tt(t)&&t.length>0?t:M_();return l.length===0&&l.push({colno:u,filename:f,function:tn,in_app:!0,lineno:c}),e}function hh(e){Wi&&P.log(`Global Handler attached: ${e}`)}function I1(){const e=ve();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const L5=()=>({name:"HttpContext",preprocessEvent(e){if(!U.navigator&&!U.location&&!U.document)return;const t=e.request&&e.request.url||U.location&&U.location.href,{referrer:n}=U.document||{},{userAgent:r}=U.navigator||{},i={...e.request&&e.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},o={...e.request,...t&&{url:t},headers:i};e.request=o}}),x5="cause",C5=5,T5="LinkedErrors",I5=(e={})=>{const t=e.limit||C5,n=e.key||x5;return{name:T5,preprocessEvent(r,i,o){const s=o.getOptions();A_(Vc,s.stackParser,s.maxValueLength,n,t,r,i)}}},k5=I5;function k1(e){return[Z4(),W4(),f5(),e5(),y5(),k5(),uS(),L5()]}function P5(e={}){const t={defaultIntegrations:k1(),release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:U.SENTRY_RELEASE&&U.SENTRY_RELEASE.id?U.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return e.defaultIntegrations==null&&delete e.defaultIntegrations,{...t,...e}}function N5(){const e=typeof U.window<"u"&&U;if(!e)return!1;const t=e.chrome?"chrome":"browser",n=e[t],r=n&&n.runtime&&n.runtime.id,i=U.location&&U.location.href||"",o=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],s=!!r&&U===U.top&&o.some(l=>i.startsWith(`${l}//`)),a=typeof e.nw<"u";return!!r&&!s&&!a}function A5(e={}){const t=P5(e);if(N5()){zi(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});return}Wi&&(s1()||P.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...t,stackParser:X_(t.stackParser||KS),integrations:b4(t),transport:t.transport||HS},r=H4(TS,n);return t.autoSessionTracking&&R5(),r}function R5(){if(typeof U.document>"u"){Wi&&P.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}eh({ignoreDuration:!0}),th(),x1(({from:e,to:t})=>{e!==void 0&&e!==t&&(eh({ignoreDuration:!0}),th())})}function ph(){const e=Bn().getScopeData();return Su(e,vt().getScopeData()),e.eventProcessors=[],e}function O5(e){Bn().addScopeListener(t=>{const n=ph();e(n,t)}),vt().addScopeListener(t=>{const n=ph();e(n,t)})}var mh;(function(e){e[e.Classic=1]="Classic",e[e.Protocol=2]="Protocol",e[e.Both=3]="Both"})(mh||(mh={}));const b5="sentry-ipc";var Ft;(function(e){e.RENDERER_START="sentry-electron.renderer-start",e.EVENT="sentry-electron.event",e.SCOPE="sentry-electron.scope",e.ENVELOPE="sentry-electron.envelope",e.STATUS="sentry-electron.status",e.ADD_METRIC="sentry-electron.add-metric"})(Ft||(Ft={}));const M5="sentry-electron-renderer-id";function jn(e){return`${b5}://${e}/sentry_key`}function D5(){if(window.__SENTRY_IPC__)return window.__SENTRY_IPC__;{P.log("IPC was not configured in preload script, falling back to custom protocol and fetch");const e=window.__SENTRY_RENDERER_ID__=$e(),t={[M5]:e};return{sendRendererStart:()=>{fetch(jn(Ft.RENDERER_START),{method:"POST",body:"",headers:t}).catch(()=>{console.error(`Sentry SDK failed to establish connection with the Electron main process.
  - Ensure you have initialized the SDK in the main process
  - If your renderers use custom sessions, be sure to set 'getSessions' in the main process options
  - If you are bundling your main process code and using Electron < v5, you'll need to manually configure a preload script`)})},sendScope:n=>{fetch(jn(Ft.SCOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEvent:n=>{fetch(jn(Ft.EVENT),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEnvelope:n=>{fetch(jn(Ft.ENVELOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendStatus:n=>{fetch(jn(Ft.STATUS),{method:"POST",body:JSON.stringify({status:n}),headers:t}).catch(()=>{})},sendAddMetric:n=>{fetch(jn(Ft.ADD_METRIC),{method:"POST",body:JSON.stringify(n),headers:t}).catch(()=>{})}}}}let Lo;function Yc(){return Lo||(Lo=D5(),Lo.sendRendererStart()),Lo}const F5=()=>({name:"ScopeToMain",setup(){const e=Yc();O5((t,n)=>{e.sendScope(JSON.stringify(wt(t,20,2e3))),n.clearBreadcrumbs(),n.clearAttachments()})}});function $5(e){const t=Yc();return E1(e,async n=>(t.sendEnvelope(n.body),{statusCode:200}))}function H5(e){const t={pollInterval:1e3,anrThreshold:5e3,captureStackTrace:!1,...e},n=Yc();document.addEventListener("visibilitychange",()=>{n.sendStatus({status:document.visibilityState,config:t})}),n.sendStatus({status:document.visibilityState,config:t}),setInterval(()=>{n.sendStatus({status:"alive",config:t})},t.pollInterval)}const B5=50,[,U5]=C1,[,j5]=gE(),z5=(e,t=0)=>{const n=[];for(const r of e.split(`
`).slice(t)){const i=U5(r),o=j5(r);if(i&&(o==null?void 0:o.in_app)!==!1?n.push(i):o&&n.push(De(o)),n.length>=B5)break}return o1(n)};function G5(e){return[...k1(),F5()]}function V5(e={},t=A5){if(window!=null&&window.__SENTRY__RENDERER_INIT__){P.warn(`The browser SDK has already been initialized.
If init has been called in the preload and contextIsolation is disabled, is not required to call init in the renderer`);return}window.__SENTRY__RENDERER_INIT__=!0,e.autoSessionTracking===void 0&&(e.autoSessionTracking=!1),e.sendClientReports=!1,e.defaultIntegrations===void 0&&(e.defaultIntegrations=G5()),e.stackParser===void 0&&(e.stackParser=z5),e.dsn===void 0&&(e.dsn="https://<EMAIL>/12345"),e.transport===void 0&&(e.transport=$5),e.anrDetection&&H5(e.anrDetection===!0?{}:e.anrDetection),delete e.initialScope,t(e)}V5();window.mainProcess.titleBarApi.onUpdateDarkMode(e=>{document.body.className=e=="dark"?"darkTheme":""});/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Uo=globalThis,Qc=Uo.ShadowRoot&&(Uo.ShadyCSS===void 0||Uo.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,P1=Symbol(),gh=new WeakMap;let W5=class{constructor(t,n,r){if(this._$cssResult$=!0,r!==P1)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=n}get styleSheet(){let t=this.o;const n=this.t;if(Qc&&t===void 0){const r=n!==void 0&&n.length===1;r&&(t=gh.get(n)),t===void 0&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),r&&gh.set(n,t))}return t}toString(){return this.cssText}};const X5=e=>new W5(typeof e=="string"?e:e+"",void 0,P1),Y5=(e,t)=>{if(Qc)e.adoptedStyleSheets=t.map(n=>n instanceof CSSStyleSheet?n:n.styleSheet);else for(const n of t){const r=document.createElement("style"),i=Uo.litNonce;i!==void 0&&r.setAttribute("nonce",i),r.textContent=n.cssText,e.appendChild(r)}},yh=Qc?e=>e:e=>e instanceof CSSStyleSheet?(t=>{let n="";for(const r of t.cssRules)n+=r.cssText;return X5(n)})(e):e;/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const{is:Q5,defineProperty:Z5,getOwnPropertyDescriptor:K5,getOwnPropertyNames:q5,getOwnPropertySymbols:J5,getPrototypeOf:e6}=Object,qt=globalThis,vh=qt.trustedTypes,t6=vh?vh.emptyScript:"",Ka=qt.reactiveElementPolyfillSupport,ui=(e,t)=>e,vs={toAttribute(e,t){switch(t){case Boolean:e=e?t6:null;break;case Object:case Array:e=e==null?e:JSON.stringify(e)}return e},fromAttribute(e,t){let n=e;switch(t){case Boolean:n=e!==null;break;case Number:n=e===null?null:Number(e);break;case Object:case Array:try{n=JSON.parse(e)}catch{n=null}}return n}},Zc=(e,t)=>!Q5(e,t),_h={attribute:!0,type:String,converter:vs,reflect:!1,hasChanged:Zc};Symbol.metadata??(Symbol.metadata=Symbol("metadata")),qt.litPropertyMetadata??(qt.litPropertyMetadata=new WeakMap);class zn extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??(this.l=[])).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,n=_h){if(n.state&&(n.attribute=!1),this._$Ei(),this.elementProperties.set(t,n),!n.noAccessor){const r=Symbol(),i=this.getPropertyDescriptor(t,r,n);i!==void 0&&Z5(this.prototype,t,i)}}static getPropertyDescriptor(t,n,r){const{get:i,set:o}=K5(this.prototype,t)??{get(){return this[n]},set(s){this[n]=s}};return{get(){return i==null?void 0:i.call(this)},set(s){const a=i==null?void 0:i.call(this);o.call(this,s),this.requestUpdate(t,a,r)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??_h}static _$Ei(){if(this.hasOwnProperty(ui("elementProperties")))return;const t=e6(this);t.finalize(),t.l!==void 0&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(ui("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(ui("properties"))){const n=this.properties,r=[...q5(n),...J5(n)];for(const i of r)this.createProperty(i,n[i])}const t=this[Symbol.metadata];if(t!==null){const n=litPropertyMetadata.get(t);if(n!==void 0)for(const[r,i]of n)this.elementProperties.set(r,i)}this._$Eh=new Map;for(const[n,r]of this.elementProperties){const i=this._$Eu(n,r);i!==void 0&&this._$Eh.set(i,n)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(t){const n=[];if(Array.isArray(t)){const r=new Set(t.flat(1/0).reverse());for(const i of r)n.unshift(yh(i))}else t!==void 0&&n.push(yh(t));return n}static _$Eu(t,n){const r=n.attribute;return r===!1?void 0:typeof r=="string"?r:typeof t=="string"?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){var t;this._$ES=new Promise(n=>this.enableUpdating=n),this._$AL=new Map,this._$E_(),this.requestUpdate(),(t=this.constructor.l)==null||t.forEach(n=>n(this))}addController(t){var n;(this._$EO??(this._$EO=new Set)).add(t),this.renderRoot!==void 0&&this.isConnected&&((n=t.hostConnected)==null||n.call(t))}removeController(t){var n;(n=this._$EO)==null||n.delete(t)}_$E_(){const t=new Map,n=this.constructor.elementProperties;for(const r of n.keys())this.hasOwnProperty(r)&&(t.set(r,this[r]),delete this[r]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return Y5(t,this.constructor.elementStyles),t}connectedCallback(){var t;this.renderRoot??(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),(t=this._$EO)==null||t.forEach(n=>{var r;return(r=n.hostConnected)==null?void 0:r.call(n)})}enableUpdating(t){}disconnectedCallback(){var t;(t=this._$EO)==null||t.forEach(n=>{var r;return(r=n.hostDisconnected)==null?void 0:r.call(n)})}attributeChangedCallback(t,n,r){this._$AK(t,r)}_$EC(t,n){var o;const r=this.constructor.elementProperties.get(t),i=this.constructor._$Eu(t,r);if(i!==void 0&&r.reflect===!0){const s=(((o=r.converter)==null?void 0:o.toAttribute)!==void 0?r.converter:vs).toAttribute(n,r.type);this._$Em=t,s==null?this.removeAttribute(i):this.setAttribute(i,s),this._$Em=null}}_$AK(t,n){var o;const r=this.constructor,i=r._$Eh.get(t);if(i!==void 0&&this._$Em!==i){const s=r.getPropertyOptions(i),a=typeof s.converter=="function"?{fromAttribute:s.converter}:((o=s.converter)==null?void 0:o.fromAttribute)!==void 0?s.converter:vs;this._$Em=i,this[i]=a.fromAttribute(n,s.type),this._$Em=null}}requestUpdate(t,n,r){if(t!==void 0){if(r??(r=this.constructor.getPropertyOptions(t)),!(r.hasChanged??Zc)(this[t],n))return;this.P(t,n,r)}this.isUpdatePending===!1&&(this._$ES=this._$ET())}P(t,n,r){this._$AL.has(t)||this._$AL.set(t,n),r.reflect===!0&&this._$Em!==t&&(this._$Ej??(this._$Ej=new Set)).add(t)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(n){Promise.reject(n)}const t=this.scheduleUpdate();return t!=null&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var r;if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??(this.renderRoot=this.createRenderRoot()),this._$Ep){for(const[o,s]of this._$Ep)this[o]=s;this._$Ep=void 0}const i=this.constructor.elementProperties;if(i.size>0)for(const[o,s]of i)s.wrapped!==!0||this._$AL.has(o)||this[o]===void 0||this.P(o,this[o],s)}let t=!1;const n=this._$AL;try{t=this.shouldUpdate(n),t?(this.willUpdate(n),(r=this._$EO)==null||r.forEach(i=>{var o;return(o=i.hostUpdate)==null?void 0:o.call(i)}),this.update(n)):this._$EU()}catch(i){throw t=!1,this._$EU(),i}t&&this._$AE(n)}willUpdate(t){}_$AE(t){var n;(n=this._$EO)==null||n.forEach(r=>{var i;return(i=r.hostUpdated)==null?void 0:i.call(r)}),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Ej&&(this._$Ej=this._$Ej.forEach(n=>this._$EC(n,this[n]))),this._$EU()}updated(t){}firstUpdated(t){}}zn.elementStyles=[],zn.shadowRootOptions={mode:"open"},zn[ui("elementProperties")]=new Map,zn[ui("finalized")]=new Map,Ka==null||Ka({ReactiveElement:zn}),(qt.reactiveElementVersions??(qt.reactiveElementVersions=[])).push("2.0.4");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const ci=globalThis,_s=ci.trustedTypes,Eh=_s?_s.createPolicy("lit-html",{createHTML:e=>e}):void 0,N1="$lit$",Bt=`lit$${Math.random().toFixed(9).slice(2)}$`,A1="?"+Bt,n6=`<${A1}>`,bn=document,Ai=()=>bn.createComment(""),Ri=e=>e===null||typeof e!="object"&&typeof e!="function",Kc=Array.isArray,r6=e=>Kc(e)||typeof(e==null?void 0:e[Symbol.iterator])=="function",qa=`[ 	
\f\r]`,Yr=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,Sh=/-->/g,wh=/>/g,dn=RegExp(`>|${qa}(?:([^\\s"'>=/]+)(${qa}*=${qa}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`,"g"),Lh=/'/g,xh=/"/g,R1=/^(?:script|style|textarea|title)$/i,i6=e=>(t,...n)=>({_$litType$:e,strings:t,values:n}),Iu=i6(1),Ir=Symbol.for("lit-noChange"),se=Symbol.for("lit-nothing"),Ch=new WeakMap,vn=bn.createTreeWalker(bn,129);function O1(e,t){if(!Kc(e)||!e.hasOwnProperty("raw"))throw Error("invalid template strings array");return Eh!==void 0?Eh.createHTML(t):t}const o6=(e,t)=>{const n=e.length-1,r=[];let i,o=t===2?"<svg>":t===3?"<math>":"",s=Yr;for(let a=0;a<n;a++){const l=e[a];let u,c,f=-1,d=0;for(;d<l.length&&(s.lastIndex=d,c=s.exec(l),c!==null);)d=s.lastIndex,s===Yr?c[1]==="!--"?s=Sh:c[1]!==void 0?s=wh:c[2]!==void 0?(R1.test(c[2])&&(i=RegExp("</"+c[2],"g")),s=dn):c[3]!==void 0&&(s=dn):s===dn?c[0]===">"?(s=i??Yr,f=-1):c[1]===void 0?f=-2:(f=s.lastIndex-c[2].length,u=c[1],s=c[3]===void 0?dn:c[3]==='"'?xh:Lh):s===xh||s===Lh?s=dn:s===Sh||s===wh?s=Yr:(s=dn,i=void 0);const m=s===dn&&e[a+1].startsWith("/>")?" ":"";o+=s===Yr?l+n6:f>=0?(r.push(u),l.slice(0,f)+N1+l.slice(f)+Bt+m):l+Bt+(f===-2?a:m)}return[O1(e,o+(e[n]||"<?>")+(t===2?"</svg>":t===3?"</math>":"")),r]};class Oi{constructor({strings:t,_$litType$:n},r){let i;this.parts=[];let o=0,s=0;const a=t.length-1,l=this.parts,[u,c]=o6(t,n);if(this.el=Oi.createElement(u,r),vn.currentNode=this.el.content,n===2||n===3){const f=this.el.content.firstChild;f.replaceWith(...f.childNodes)}for(;(i=vn.nextNode())!==null&&l.length<a;){if(i.nodeType===1){if(i.hasAttributes())for(const f of i.getAttributeNames())if(f.endsWith(N1)){const d=c[s++],m=i.getAttribute(f).split(Bt),y=/([.?@])?(.*)/.exec(d);l.push({type:1,index:o,name:y[2],strings:m,ctor:y[1]==="."?a6:y[1]==="?"?l6:y[1]==="@"?u6:aa}),i.removeAttribute(f)}else f.startsWith(Bt)&&(l.push({type:6,index:o}),i.removeAttribute(f));if(R1.test(i.tagName)){const f=i.textContent.split(Bt),d=f.length-1;if(d>0){i.textContent=_s?_s.emptyScript:"";for(let m=0;m<d;m++)i.append(f[m],Ai()),vn.nextNode(),l.push({type:2,index:++o});i.append(f[d],Ai())}}}else if(i.nodeType===8)if(i.data===A1)l.push({type:2,index:o});else{let f=-1;for(;(f=i.data.indexOf(Bt,f+1))!==-1;)l.push({type:7,index:o}),f+=Bt.length-1}o++}}static createElement(t,n){const r=bn.createElement("template");return r.innerHTML=t,r}}function kr(e,t,n=e,r){var s,a;if(t===Ir)return t;let i=r!==void 0?(s=n._$Co)==null?void 0:s[r]:n._$Cl;const o=Ri(t)?void 0:t._$litDirective$;return(i==null?void 0:i.constructor)!==o&&((a=i==null?void 0:i._$AO)==null||a.call(i,!1),o===void 0?i=void 0:(i=new o(e),i._$AT(e,n,r)),r!==void 0?(n._$Co??(n._$Co=[]))[r]=i:n._$Cl=i),i!==void 0&&(t=kr(e,i._$AS(e,t.values),i,r)),t}class s6{constructor(t,n){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=n}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:n},parts:r}=this._$AD,i=((t==null?void 0:t.creationScope)??bn).importNode(n,!0);vn.currentNode=i;let o=vn.nextNode(),s=0,a=0,l=r[0];for(;l!==void 0;){if(s===l.index){let u;l.type===2?u=new Xi(o,o.nextSibling,this,t):l.type===1?u=new l.ctor(o,l.name,l.strings,this,t):l.type===6&&(u=new c6(o,this,t)),this._$AV.push(u),l=r[++a]}s!==(l==null?void 0:l.index)&&(o=vn.nextNode(),s++)}return vn.currentNode=bn,i}p(t){let n=0;for(const r of this._$AV)r!==void 0&&(r.strings!==void 0?(r._$AI(t,r,n),n+=r.strings.length-2):r._$AI(t[n])),n++}}class Xi{get _$AU(){var t;return((t=this._$AM)==null?void 0:t._$AU)??this._$Cv}constructor(t,n,r,i){this.type=2,this._$AH=se,this._$AN=void 0,this._$AA=t,this._$AB=n,this._$AM=r,this.options=i,this._$Cv=(i==null?void 0:i.isConnected)??!0}get parentNode(){let t=this._$AA.parentNode;const n=this._$AM;return n!==void 0&&(t==null?void 0:t.nodeType)===11&&(t=n.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,n=this){t=kr(this,t,n),Ri(t)?t===se||t==null||t===""?(this._$AH!==se&&this._$AR(),this._$AH=se):t!==this._$AH&&t!==Ir&&this._(t):t._$litType$!==void 0?this.$(t):t.nodeType!==void 0?this.T(t):r6(t)?this.k(t):this._(t)}O(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}_(t){this._$AH!==se&&Ri(this._$AH)?this._$AA.nextSibling.data=t:this.T(bn.createTextNode(t)),this._$AH=t}$(t){var o;const{values:n,_$litType$:r}=t,i=typeof r=="number"?this._$AC(t):(r.el===void 0&&(r.el=Oi.createElement(O1(r.h,r.h[0]),this.options)),r);if(((o=this._$AH)==null?void 0:o._$AD)===i)this._$AH.p(n);else{const s=new s6(i,this),a=s.u(this.options);s.p(n),this.T(a),this._$AH=s}}_$AC(t){let n=Ch.get(t.strings);return n===void 0&&Ch.set(t.strings,n=new Oi(t)),n}k(t){Kc(this._$AH)||(this._$AH=[],this._$AR());const n=this._$AH;let r,i=0;for(const o of t)i===n.length?n.push(r=new Xi(this.O(Ai()),this.O(Ai()),this,this.options)):r=n[i],r._$AI(o),i++;i<n.length&&(this._$AR(r&&r._$AB.nextSibling,i),n.length=i)}_$AR(t=this._$AA.nextSibling,n){var r;for((r=this._$AP)==null?void 0:r.call(this,!1,!0,n);t&&t!==this._$AB;){const i=t.nextSibling;t.remove(),t=i}}setConnected(t){var n;this._$AM===void 0&&(this._$Cv=t,(n=this._$AP)==null||n.call(this,t))}}class aa{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,n,r,i,o){this.type=1,this._$AH=se,this._$AN=void 0,this.element=t,this.name=n,this._$AM=i,this.options=o,r.length>2||r[0]!==""||r[1]!==""?(this._$AH=Array(r.length-1).fill(new String),this.strings=r):this._$AH=se}_$AI(t,n=this,r,i){const o=this.strings;let s=!1;if(o===void 0)t=kr(this,t,n,0),s=!Ri(t)||t!==this._$AH&&t!==Ir,s&&(this._$AH=t);else{const a=t;let l,u;for(t=o[0],l=0;l<o.length-1;l++)u=kr(this,a[r+l],n,l),u===Ir&&(u=this._$AH[l]),s||(s=!Ri(u)||u!==this._$AH[l]),u===se?t=se:t!==se&&(t+=(u??"")+o[l+1]),this._$AH[l]=u}s&&!i&&this.j(t)}j(t){t===se?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??"")}}class a6 extends aa{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===se?void 0:t}}class l6 extends aa{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==se)}}class u6 extends aa{constructor(t,n,r,i,o){super(t,n,r,i,o),this.type=5}_$AI(t,n=this){if((t=kr(this,t,n,0)??se)===Ir)return;const r=this._$AH,i=t===se&&r!==se||t.capture!==r.capture||t.once!==r.once||t.passive!==r.passive,o=t!==se&&(r===se||i);i&&this.element.removeEventListener(this.name,this,r),o&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){var n;typeof this._$AH=="function"?this._$AH.call(((n=this.options)==null?void 0:n.host)??this.element,t):this._$AH.handleEvent(t)}}class c6{constructor(t,n,r){this.element=t,this.type=6,this._$AN=void 0,this._$AM=n,this.options=r}get _$AU(){return this._$AM._$AU}_$AI(t){kr(this,t)}}const Ja=ci.litHtmlPolyfillSupport;Ja==null||Ja(Oi,Xi),(ci.litHtmlVersions??(ci.litHtmlVersions=[])).push("3.2.1");const f6=(e,t,n)=>{const r=(n==null?void 0:n.renderBefore)??t;let i=r._$litPart$;if(i===void 0){const o=(n==null?void 0:n.renderBefore)??null;r._$litPart$=i=new Xi(t.insertBefore(Ai(),o),o,void 0,n??{})}return i._$AI(e),i};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */let cr=class extends zn{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var n;const t=super.createRenderRoot();return(n=this.renderOptions).renderBefore??(n.renderBefore=t.firstChild),t}update(t){const n=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=f6(n,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),(t=this._$Do)==null||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),(t=this._$Do)==null||t.setConnected(!1)}render(){return Ir}};var Ih;cr._$litElement$=!0,cr.finalized=!0,(Ih=globalThis.litElementHydrateSupport)==null||Ih.call(globalThis,{LitElement:cr});const el=globalThis.litElementPolyfillSupport;el==null||el({LitElement:cr});(globalThis.litElementVersions??(globalThis.litElementVersions=[])).push("4.1.1");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const b1=e=>(t,n)=>{n!==void 0?n.addInitializer(()=>{customElements.define(e,t)}):customElements.define(e,t)};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const d6={attribute:!0,type:String,converter:vs,reflect:!1,hasChanged:Zc},h6=(e=d6,t,n)=>{const{kind:r,metadata:i}=n;let o=globalThis.litPropertyMetadata.get(i);if(o===void 0&&globalThis.litPropertyMetadata.set(i,o=new Map),o.set(n.name,e),r==="accessor"){const{name:s}=n;return{set(a){const l=t.get.call(this);t.set.call(this,a),this.requestUpdate(s,l,e)},init(a){return a!==void 0&&this.P(s,void 0,e),a}}}if(r==="setter"){const{name:s}=n;return function(a){const l=this[s];t.call(this,a),this.requestUpdate(s,l,e)}}throw Error("Unsupported decorator location: "+r)};function la(e){return(t,n)=>typeof n=="object"?h6(e,t,n):((r,i,o)=>{const s=i.hasOwnProperty(o);return i.constructor.createProperty(o,s?{...r,wrapped:!0}:r),s?Object.getOwnPropertyDescriptor(i,o):void 0})(e,t,n)}var p6=Object.defineProperty,m6=Object.getOwnPropertyDescriptor,Mr=(e,t,n,r)=>{for(var i=r>1?void 0:r?m6(t,n):t,o=e.length-1,s;o>=0;o--)(s=e[o])&&(i=(r?s(t,n,i):s(i))||i);return r&&i&&p6(t,n,i),i};let Es=class extends cr{constructor(){super(...arguments),this.width=16,this.height=16}render(){return Iu`<svg
      width="${this.width}px"
      height="${this.height}px"
      style="${this.style}"
      viewBox="0 0 248 248"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M52.4285 162.873L98.7844 136.879L99.5485 134.602L98.7844 133.334H96.4921L88.7237 132.862L62.2346 132.153L39.3113 131.207L17.0249 130.026L11.4214 128.844L6.2 121.873L6.7094 118.447L11.4214 115.257L18.171 115.847L33.0711 116.911L55.485 118.447L71.6586 119.392L95.728 121.873H99.5485L100.058 120.337L98.7844 119.392L97.7656 118.447L74.5877 102.732L49.4995 86.1905L36.3823 76.62L29.3779 71.7757L25.8121 67.2858L24.2839 57.3608L30.6515 50.2716L39.3113 50.8623L41.4763 51.4531L50.2636 58.1879L68.9842 72.7209L93.4357 90.6804L97.0015 93.6343L98.4374 92.6652L98.6571 91.9801L97.0015 89.2625L83.757 65.2772L69.621 40.8192L63.2534 30.6579L61.5978 24.632C60.9565 22.1032 60.579 20.0111 60.579 17.4246L67.8381 7.49965L71.9133 6.19995L81.7193 7.49965L85.7946 11.0443L91.9074 24.9865L101.714 46.8451L116.996 76.62L121.453 85.4816L123.873 93.6343L124.764 96.1155H126.292V94.6976L127.566 77.9197L129.858 57.3608L132.15 30.8942L132.915 23.4505L136.608 14.4708L143.994 9.62643L149.725 12.344L154.437 19.0788L153.8 23.4505L150.998 41.6463L145.522 70.1215L141.957 89.2625H143.994L146.414 86.7813L156.093 74.0206L172.266 53.698L179.398 45.6635L187.803 36.802L193.152 32.5484H203.34L210.726 43.6549L207.415 55.1159L196.972 68.3492L188.312 79.5739L175.896 96.2095L168.191 109.585L168.882 110.689L170.738 110.53L198.755 104.504L213.91 101.787L231.994 98.7149L240.144 102.496L241.036 106.395L237.852 114.311L218.495 119.037L195.826 123.645L162.07 131.592L161.696 131.893L162.137 132.547L177.36 133.925L183.855 134.279H199.774L229.447 136.524L237.215 141.605L241.8 147.867L241.036 152.711L229.065 158.737L213.019 154.956L175.45 145.977L162.587 142.787H160.805V143.85L171.502 154.366L191.242 172.089L215.82 195.011L217.094 200.682L213.91 205.172L210.599 204.699L188.949 188.394L180.544 181.069L161.696 165.118H160.422V166.772L164.752 173.152L187.803 207.771L188.949 218.405L187.294 221.832L181.308 223.959L174.813 222.777L161.187 203.754L147.305 182.486L136.098 163.345L134.745 164.2L128.075 235.42L125.019 239.082L117.887 241.8L111.902 237.31L108.718 229.984L111.902 215.452L115.722 196.547L118.779 181.541L121.58 162.873L123.291 156.636L123.14 156.219L121.773 156.449L107.699 175.752L86.304 204.699L69.3663 222.777L65.291 224.431L58.2867 220.768L58.9235 214.27L62.8713 208.48L86.304 178.705L100.44 160.155L109.551 149.507L109.462 147.967L108.959 147.924L46.6977 188.512L35.6182 189.93L30.7788 185.44L31.4156 178.115L33.7079 175.752L52.4285 162.873Z"
        fill="#D97757"
      />
    </svg>`}};Mr([la()],Es.prototype,"width",2);Mr([la()],Es.prototype,"height",2);Es=Mr([b1("app-logo")],Es);let Ss=class extends cr{constructor(){super(...arguments),this.height="16px",this.dark="false"}render(){const e=()=>Iu` <svg
        height="94"
        viewBox="0 0 434 94"
        fill="none"
        style="height: ${this.height}; ${this.style}"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.3673 62.2485L36.7852 51.9207L37.0888 51.0161L36.7852 50.5124H35.8744L32.7879 50.3246L22.2634 50.0429L13.1556 49.6674L4.30088 49.1979L2.07454 48.7285L0 45.9587L0.202395 44.5974L2.07454 43.3299L4.75627 43.5646L10.6763 43.9871L19.5817 44.5974L26.0077 44.9729L35.5708 45.9587H37.0888L37.2912 45.3485L36.7852 44.9729L36.3804 44.5974L27.1715 38.3537L17.2035 31.7815L11.9919 27.979L9.20895 26.0542L7.79219 24.2703L7.18501 20.327L9.71494 17.5103L13.1556 17.7451L14.0158 17.9798L17.5071 20.6556L24.9451 26.4298L34.6601 33.5654L36.0768 34.739L36.6473 34.354L36.7346 34.0818L36.0768 33.002L30.8146 23.4723L25.1981 13.7548L22.6682 9.71753L22.0104 7.32336C21.7556 6.31862 21.6056 5.48739 21.6056 4.45974L24.4897 0.51639L26.1089 0L30.005 0.51639L31.6242 1.92473L34.0529 7.46419L37.949 16.1489L44.0208 27.979L45.7918 31.4998L46.7531 34.739L47.1073 35.7248H47.7145V35.1615L48.2205 28.4954L49.1313 20.327L50.0421 9.81142L50.3457 6.85391L51.813 3.28612L54.7477 1.36139L57.0247 2.44112L58.8968 5.11696L58.6438 6.85391L57.5307 14.0834L55.3549 25.397L53.9382 33.002H54.7477L55.7091 32.0162L59.5546 26.9462L65.9806 18.8717L68.8142 15.6795L72.1537 12.1586L74.2788 10.4686H78.3267L81.2614 14.8814L79.9459 19.4351L75.7968 24.6928L72.3561 29.1526L67.4227 35.7622L64.3615 41.0765L64.636 41.5149L65.3735 41.4521L76.5052 39.0579L82.5264 37.9782L89.7114 36.7576L92.9497 38.2598L93.3039 39.809L92.0389 42.9543L84.3479 44.8321L75.3414 46.6629L61.9295 49.8202L61.7809 49.9396L61.9563 50.1996L68.0046 50.7471L70.5851 50.8879H76.9099L88.6994 51.7799L91.7859 53.7985L93.6075 56.2866L93.3039 58.2113L88.5476 60.6055L82.1722 59.1032L67.2456 55.5354L62.1351 54.2679H61.4268V54.6904L65.677 58.8685L73.5198 65.9102L83.2854 75.0174L83.7914 77.2708L82.5264 79.0547L81.2108 78.8669L72.6091 72.3885L69.2695 69.478L61.7809 63.1405H61.275V63.7977L62.9953 66.3327L72.1537 80.0875L72.6091 84.3125L71.9513 85.6739L69.5731 86.5189L66.9926 86.0494L61.5786 78.4913L56.0633 70.0413L51.6106 62.4363L51.0728 62.7758L48.4229 91.0725L47.2085 92.5278L44.375 93.6075L41.9969 91.8236L40.7319 88.913L41.9969 83.1389L43.5148 75.6277L44.7292 69.6658L45.8424 62.2485L46.5222 59.7707L46.4622 59.6049L45.919 59.6962L40.3271 67.3655L31.8265 78.8669L25.0969 86.0494L23.4778 86.7066L20.6948 85.2514L20.9478 82.6694L22.5164 80.3691L31.8265 68.5391L37.443 61.1688L41.0628 56.9381L41.0276 56.3261L40.8277 56.3091L16.0904 72.4355L11.6883 72.9988L9.76554 71.2149L10.0185 68.3044L10.9293 67.3655L18.3673 62.2485Z"
          fill="#D97757"
        />
        <path
          d="M201.394 16.4584V74.8077C201.394 78.6477 203.361 79.4906 208.512 80.1462V84.2672H183.037V80.1462C188.188 79.4906 190.155 78.6477 190.155 74.8077V18.4253L183.505 15.3345V12.8994L198.491 8.40381H201.862L201.394 16.4584Z"
          fill="#FAFAF8"
        />
        <path
          d="M150.069 79.0223C154.471 79.0223 158.124 78.3667 161.027 77.0555C166.928 74.4331 170.487 69.4692 173.484 61.2272H178.448L176.387 79.3033C168.426 83.0497 159.435 85.2038 148.102 85.2038C140.141 85.2038 133.211 83.6116 127.404 80.3336C115.79 73.8711 109.609 62.2575 109.609 49.1453C109.609 41.3716 111.388 34.5345 114.854 28.6341C121.784 16.8331 134.241 10.277 148.852 10.277C159.248 10.277 167.771 12.5248 174.608 16.9267L175.263 33.317H170.393C166.366 21.4223 159.341 16.4584 147.915 16.4584C130.963 16.4584 123.283 28.3531 123.283 45.4926C123.283 51.5804 124.219 57.1062 126.186 62.1638C130.026 72.3726 138.175 79.0223 150.069 79.0223Z"
          fill="#FAFAF8"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M259.369 77.8048C256.653 77.8048 255.341 75.6506 255.341 71.436V51.8614C255.341 38.4682 248.223 33.317 235.392 33.317C224.059 33.317 215.818 37.9999 215.818 45.7736C215.818 48.115 216.66 49.8945 218.346 51.1121L226.963 49.9882C226.588 47.3658 226.401 45.7736 226.401 45.118C226.401 40.716 228.742 38.4682 233.519 38.4682C240.543 38.4682 244.102 43.4321 244.102 51.3931V54.0155L226.307 59.3541C220.407 60.9462 217.035 62.3511 214.787 65.6292C213.663 67.315 213.101 69.5628 213.101 72.2789C213.101 79.8653 218.346 85.2038 227.244 85.2038C233.706 85.2038 239.419 82.3004 244.383 76.7745C246.163 82.3004 248.879 85.2038 253.749 85.2038C257.683 85.2038 261.242 83.6116 264.426 80.5209L263.49 77.2428C262.085 77.6175 260.774 77.8048 259.369 77.8048ZM244.102 72.6536C239.513 76.1189 236.797 77.6175 232.582 77.6175C227.712 77.6175 224.715 74.8077 224.715 69.8438C224.715 66.4721 226.307 64.5053 229.679 63.3814L244.102 58.7921V72.6536Z"
          fill="#FAFAF8"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M412.688 75.3697C419.899 75.3697 426.737 71.2487 430.202 65.0672L433.386 65.9102C431.981 76.9619 421.96 85.2038 409.691 85.2038C395.267 85.2038 385.339 74.6204 385.339 59.3541C385.339 44.0877 396.11 33.317 410.534 33.317C421.304 33.317 428.891 39.7794 431.326 51.0184L395.972 61.8641C398.133 70.2559 404.131 75.3697 412.688 75.3697ZM419.057 49.6136C417.745 43.3384 413.905 39.7794 408.567 39.7794C400.606 39.7794 395.08 45.7736 395.08 54.3902C395.08 55.2968 395.117 56.1772 395.185 57.0388L419.057 49.6136Z"
          fill="#FAFAF8"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M375.131 73.0282V16.4584L375.599 8.40381H372.227L357.242 12.8994V15.3345L363.892 18.4253V36.9697C360.614 34.5345 356.305 33.317 351.154 33.317C336.262 33.317 324.649 44.6497 324.649 61.6019C324.649 75.557 332.984 85.2038 346.752 85.2038C353.87 85.2038 360.052 81.7384 363.892 76.3999L363.423 85.2038H365.39L382.623 81.9258V77.7111L380.188 77.5238C376.161 77.1492 375.131 76.3062 375.131 73.0282ZM363.892 71.998C360.895 75.0887 356.493 76.9619 351.529 76.9619C341.32 76.9619 336.169 68.9072 336.169 58.1365C336.169 46.0545 342.069 38.9365 351.435 38.9365C358.553 38.9365 363.892 43.0575 363.892 50.6438V71.998Z"
          fill="#FAFAF8"
        />
        <path
          d="M314.44 42.9638V73.0282C314.44 76.3062 315.47 77.1492 319.498 77.5238L321.933 77.7111V81.9258L304.699 85.2038H302.733L303.201 76.0253C298.799 81.3638 292.899 85.2038 285.687 85.2038C276.227 85.2038 270.327 80.2399 270.327 69.7502V45.0243C270.327 41.9336 269.016 40.716 264.614 40.0604L262.928 39.7794V35.6584L279.224 33.317H282.034L281.566 42.9638V68.158C281.566 74.4331 284.844 77.1492 290.182 77.1492C294.959 77.1492 298.986 75.0887 303.201 71.8106V45.0243C303.201 41.9336 301.89 40.716 297.488 40.0604L295.896 39.7794V35.6584L312.192 33.317H314.908L314.44 42.9638Z"
          fill="#FAFAF8"
        />
      </svg>`,t=()=>Iu`
      <svg
        height="94"
        viewBox="0 0 434 94"
        fill="none"
        style="height: ${this.height}; ${this.style}"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.3673 62.2485L36.7852 51.9207L37.0888 51.0161L36.7852 50.5124H35.8744L32.7879 50.3246L22.2634 50.0429L13.1556 49.6674L4.30088 49.1979L2.07454 48.7285L0 45.9587L0.202395 44.5974L2.07454 43.3299L4.75627 43.5646L10.6763 43.9871L19.5817 44.5974L26.0077 44.9729L35.5708 45.9587H37.0888L37.2912 45.3485L36.7852 44.9729L36.3804 44.5974L27.1715 38.3537L17.2035 31.7815L11.9919 27.979L9.20895 26.0542L7.79219 24.2703L7.18501 20.327L9.71494 17.5103L13.1556 17.7451L14.0158 17.9798L17.5071 20.6556L24.9451 26.4298L34.6601 33.5654L36.0768 34.739L36.6473 34.354L36.7346 34.0818L36.0768 33.002L30.8146 23.4723L25.1981 13.7548L22.6682 9.71753L22.0104 7.32336C21.7556 6.31862 21.6056 5.48739 21.6056 4.45974L24.4897 0.51639L26.1089 0L30.005 0.51639L31.6242 1.92473L34.0529 7.46419L37.949 16.1489L44.0208 27.979L45.7918 31.4998L46.7531 34.739L47.1073 35.7248H47.7145V35.1615L48.2205 28.4954L49.1313 20.327L50.0421 9.81142L50.3457 6.85391L51.813 3.28612L54.7477 1.36139L57.0247 2.44112L58.8968 5.11696L58.6438 6.85391L57.5307 14.0834L55.3549 25.397L53.9382 33.002H54.7477L55.7091 32.0162L59.5546 26.9462L65.9806 18.8717L68.8142 15.6795L72.1537 12.1586L74.2788 10.4686H78.3267L81.2614 14.8814L79.9459 19.4351L75.7968 24.6928L72.3561 29.1526L67.4227 35.7622L64.3615 41.0765L64.636 41.5149L65.3735 41.4521L76.5052 39.0579L82.5264 37.9782L89.7114 36.7576L92.9497 38.2598L93.3039 39.809L92.0389 42.9543L84.3479 44.8321L75.3414 46.6629L61.9295 49.8202L61.7809 49.9396L61.9563 50.1996L68.0046 50.7471L70.5851 50.8879H76.9099L88.6994 51.7799L91.7859 53.7985L93.6075 56.2866L93.3039 58.2113L88.5476 60.6055L82.1722 59.1032L67.2456 55.5354L62.1351 54.2679H61.4268V54.6904L65.677 58.8685L73.5198 65.9102L83.2854 75.0174L83.7914 77.2708L82.5264 79.0547L81.2108 78.8669L72.6091 72.3885L69.2695 69.478L61.7809 63.1405H61.275V63.7977L62.9953 66.3327L72.1537 80.0875L72.6091 84.3125L71.9513 85.6739L69.5731 86.5189L66.9926 86.0494L61.5786 78.4913L56.0633 70.0413L51.6106 62.4363L51.0728 62.7758L48.4229 91.0725L47.2085 92.5278L44.375 93.6075L41.9969 91.8236L40.7319 88.913L41.9969 83.1389L43.5148 75.6277L44.7292 69.6658L45.8424 62.2485L46.5222 59.7707L46.4622 59.6049L45.919 59.6962L40.3271 67.3655L31.8265 78.8669L25.0969 86.0494L23.4778 86.7066L20.6948 85.2514L20.9478 82.6694L22.5164 80.3691L31.8265 68.5391L37.443 61.1688L41.0628 56.9381L41.0276 56.3261L40.8277 56.3091L16.0904 72.4355L11.6883 72.9988L9.76554 71.2149L10.0185 68.3044L10.9293 67.3655L18.3673 62.2485Z"
          fill="#D97757"
        />
        <path
          d="M201.401 16.4559V74.8099C201.401 78.6502 203.368 79.4932 208.52 80.1488V84.2701H183.043V80.1488C188.195 79.4932 190.162 78.6502 190.162 74.8099V18.4229L183.511 15.3319V12.8966L198.498 8.40063H201.87L201.401 16.4559Z"
          fill="#141413"
        />
        <path
          d="M150.072 79.0248C154.475 79.0248 158.128 78.3692 161.031 77.0578C166.932 74.4352 170.492 69.4709 173.489 61.2283H178.453L176.393 79.3058C168.431 83.0525 159.439 85.2068 148.106 85.2068C140.144 85.2068 133.213 83.6145 127.405 80.3362C115.791 73.8732 109.609 62.2586 109.609 49.1454C109.609 41.3711 111.388 34.5335 114.854 28.6325C121.785 16.8306 134.243 10.274 148.855 10.274C159.252 10.274 167.775 12.5219 174.613 16.9242L175.269 33.3158H170.398C166.37 21.4202 159.345 16.4559 147.918 16.4559C130.965 16.4559 123.284 28.3515 123.284 45.4924C123.284 51.5807 124.221 57.107 126.188 62.1649C130.028 72.3745 138.177 79.0248 150.072 79.0248Z"
          fill="#141413"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M259.381 77.8072C256.664 77.8072 255.353 75.6529 255.353 71.4379V51.8617C255.353 38.4674 248.235 33.3158 235.402 33.3158C224.069 33.3158 215.826 37.9991 215.826 45.7734C215.826 48.115 216.669 49.8947 218.355 51.1123L226.972 49.9884C226.598 47.3657 226.41 45.7734 226.41 45.1177C226.41 40.7154 228.752 38.4674 233.529 38.4674C240.554 38.4674 244.113 43.4317 244.113 51.3933V54.016L226.317 59.355C220.416 60.9473 217.044 62.3523 214.796 65.6306C213.672 67.3166 213.11 69.5646 213.11 72.2809C213.11 79.8678 218.355 85.2068 227.253 85.2068C233.716 85.2068 239.43 82.3031 244.394 76.7768C246.174 82.3031 248.89 85.2068 253.761 85.2068C257.695 85.2068 261.254 83.6145 264.439 80.5235L263.502 77.2452C262.097 77.6198 260.786 77.8072 259.381 77.8072ZM244.113 72.6555C239.524 76.1212 236.807 77.6198 232.592 77.6198C227.722 77.6198 224.724 74.8099 224.724 69.8456C224.724 66.4736 226.317 64.5066 229.689 63.3826L244.113 58.793V72.6555Z"
          fill="#141413"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M412.712 75.3719C419.924 75.3719 426.762 71.2505 430.228 65.0686L433.412 65.9116C432.007 76.9642 421.985 85.2068 409.715 85.2068C395.29 85.2068 385.362 74.6225 385.362 59.355C385.362 44.0874 396.133 33.3158 410.558 33.3158C421.329 33.3158 428.916 39.7788 431.352 51.0187L395.995 61.8652C398.156 70.2577 404.155 75.3719 412.712 75.3719ZM419.081 49.6137C417.77 43.3381 413.93 39.7788 408.591 39.7788C400.629 39.7788 395.103 45.7734 395.103 54.3907C395.103 55.2973 395.14 56.1778 395.208 57.0395L419.081 49.6137Z"
          fill="#141413"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M375.152 73.0302V16.4559L375.62 8.40063H372.248L357.262 12.8966V15.3319L363.912 18.4229V36.9688C360.634 34.5335 356.325 33.3158 351.173 33.3158C336.281 33.3158 324.666 44.6494 324.666 61.6029C324.666 75.5592 333.002 85.2068 346.771 85.2068C353.89 85.2068 360.072 81.7411 363.912 76.4022L363.444 85.2068H365.411L382.645 81.9285V77.7135L380.21 77.5262C376.182 77.1515 375.152 76.3085 375.152 73.0302ZM363.912 71.9999C360.915 75.0909 356.512 76.9642 351.548 76.9642C341.339 76.9642 336.187 68.9089 336.187 58.1373C336.187 46.0544 342.088 38.9358 351.454 38.9358C358.573 38.9358 363.912 43.0571 363.912 50.644V71.9999Z"
          fill="#141413"
        />
        <path
          d="M314.456 42.9634V73.0302C314.456 76.3085 315.487 77.1515 319.514 77.5262L321.95 77.7135V81.9285L304.715 85.2068H302.748L303.216 76.0275C298.814 81.3665 292.913 85.2068 285.701 85.2068C276.241 85.2068 270.34 80.2425 270.34 69.7519V45.0241C270.34 41.9331 269.028 40.7154 264.626 40.0598L262.94 39.7788V35.6575L279.238 33.3158H282.048L281.58 42.9634V68.1596C281.58 74.4352 284.858 77.1515 290.197 77.1515C294.974 77.1515 299.001 75.0909 303.216 71.8125V45.0241C303.216 41.9331 301.905 40.7154 297.503 40.0598L295.911 39.7788V35.6575L312.208 33.3158H314.925L314.456 42.9634Z"
          fill="#141413"
        />
      </svg>
    `;return this.dark==="true"?e():t()}};Mr([la()],Ss.prototype,"height",2);Mr([la()],Ss.prototype,"dark",2);Ss=Mr([b1("app-word-mark")],Ss);const xo=v_(ea.QuickWindow,window.sendPort),M1=xc();let D1=Tc({locale:window.initialLocale,messages:window.initialMessages},M1);function Th(){const e=document.getElementById("prompt-input");e.placeholder=D1.formatMessage({defaultMessage:"What can I help you with today?",description:"Placeholder text for the prompt input",id:"S3MXlbjkax"})}window.addEventListener("load",()=>{const e=document.getElementById("prompt-input"),t=document.querySelector(".container");Th(),e.addEventListener("input",()=>{e.style.height="24px";const n=Math.min(e.scrollHeight,window.innerHeight-100);e.style.height=n+"px";const r=e.scrollHeight>n;e.style.overflowY=r?"auto":"hidden",e.style.paddingTop=r?"22px":"8px",e.style.paddingBottom=r?"22px":"8px"}),su(e,"input").pipe(m_(750)).subscribe(()=>{console.log("Requesting Skooch!",t.scrollHeight),xo.requestSkooch(t.scrollWidth,t.scrollHeight)}),document.body.addEventListener("click",n=>{t&&n.target instanceof Node&&t.contains(n.target)||(e.focus(),xo.requestDismiss(null))}),document.addEventListener("keydown",n=>{n.key==="Enter"&&!n.shiftKey&&(n.preventDefault(),xo.requestDismiss(e.value),e.value="",(()=>{e.style.height="24px";const r=Math.min(e.scrollHeight,window.innerHeight-100);e.style.height=r+"px";const i=e.scrollHeight>r;e.style.overflowY=i?"auto":"hidden",e.style.paddingTop=i?"22px":"8px",e.style.paddingBottom=i?"22px":"8px"})())}),document.addEventListener("keyup",n=>{n.key==="Escape"&&xo.requestDismiss(null)}),e.addEventListener("wheel",n=>{e.scrollHeight>e.clientHeight&&(n.preventDefault(),e.scrollTop+=n.deltaY)},{passive:!1}),["dragenter","dragover","dragleave","drop"].forEach(n=>{e.addEventListener(n,r=>{r.preventDefault(),r.stopPropagation(),r instanceof DragEvent&&r.dataTransfer&&(r.dataTransfer.effectAllowed="none",r.dataTransfer.dropEffect="none")},{passive:!1})}),Ym(ea.Intl,{localeChanged:(n,r)=>{D1=Tc({locale:n,messages:r},M1),Th()}}),setTimeout(()=>e.focus(),0)});
