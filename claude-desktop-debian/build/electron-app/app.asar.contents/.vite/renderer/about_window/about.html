<!doctype html>
<html>
  <head>
    <style>*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: hsl(var(--accent-secondary-100) / 1);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: hsl(var(--accent-secondary-100) / 1);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.14 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  print-color-adjust: exact;
}

[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  appearance: none;
  padding: 0;
  print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  * {
    margin: 0;
  }

  img,
  picture,
  video,
  canvas,
  svg {
    display: block;
    max-width: 100%;
  }

  input,
  button,
  textarea,
  select {
    font: inherit;
  }

  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    overflow-wrap: break-word;
  }

  * {
    --claude-accent-clay: #d97757;
  }

  /* Values taken from claude.ai on Nov. 8 2024 */
  :root {
    --text-000: 49 6.9% 5.5%;
    --text-100: 49 19.6% 13.3%;
    --text-200: 49 18.8% 20%;
    --text-300: 49 9% 30%;
    --text-400: 49 7% 37%;
    --text-500: 51 7.5% 42.1%;
    --accent-main-000: 15 52.7% 43.9%;
    --accent-main-100: 16 53.8% 47.5%;
    --accent-main-200: 15 55.6% 52.4%;
    --accent-secondary-000: 210 74.2% 42.1%;
    --accent-secondary-100: 210 74.8% 49.8%;
    --accent-secondary-200: 210 74.8% 57%;
    --accent-secondary-900: 210 68.8% 93.3%;
    --accent-pro-000: 251 34.2% 33.3%;
    --accent-pro-100: 251 40% 45.1%;
    --accent-pro-200: 251 61% 72.2%;
    --accent-pro-900: 253 33.3% 91.8%;
    --oncolor-100: 0 0% 100%;
    --bg-000: 60 6.7% 97.1%;
    --bg-100: 50 23.1% 94.9%;
    --bg-200: 49 26.8% 92%;
    --bg-300: 49 25.8% 87.8%;
    --bg-400: 46 28.3% 82%;
    --bg-500: 47 27% 71%;
    --accent-main-900: 15 48% 90.2%;
    --border-100: 48 12.5% 39.2%;
    --border-200: 48 12.5% 39.2%;
    --border-300: 48 12.5% 39.2%;
    --oncolor-200: 60 6.7% 97.1%;
    --oncolor-300: 60 6.7% 97.1%;
    --border-400: 48 12.5% 39.2%;
    --danger-000: 5 74% 28%;
    --danger-100: 5 73.9% 37.7%;
    --danger-200: 5 49.5% 58%;
    --danger-900: 0 40.3% 89%;
  }

  .darkTheme {
    --text-000: 60 6.7% 97.1%;
    --text-100: 50 23.1% 94.9%;
    --text-200: 60 5.5% 89.2%;
    --text-300: 47 8.4% 79%;
    --text-400: 48 9.6% 69.2%;
    --text-500: 45 6.3% 62.9%;
    --accent-main-000: 18 50.4% 47.5%;
    --accent-main-100: 18 56.8% 43.5%;
    --accent-main-200: 19 58.3% 40.4%;
    --accent-secondary-000: 210 74.8% 57%;
    --accent-secondary-100: 210 74.8% 49.8%;
    --accent-secondary-200: 210 74.2% 42.1%;
    --accent-secondary-900: 210 19.5% 18%;
    --accent-pro-000: 251 84.6% 74.5%;
    --accent-pro-100: 251 40.2% 54.1%;
    --accent-pro-200: 251 40% 45.1%;
    --accent-pro-900: 250 25.3% 19.4%;
    --oncolor-100: 0 0% 100%;
    --bg-000: 60 1.8% 22%;
    --bg-100: 60 3.3% 17.8%;
    --bg-200: 45 4.9% 16.1%;
    --bg-300: 48 8.2% 12%;
    --bg-400: 48 10.6% 9.2%;
    --bg-500: 60 7.1% 5.5%;
    --accent-main-900: 16 41.3% 18%;
    --border-100: 50 5.8% 40%;
    --border-200: 50 5.9% 40%;
    --border-300: 50 5.9% 40%;
    --oncolor-200: 60 6.7% 97.1%;
    --oncolor-300: 60 6.7% 97.1%;
    --border-400: 50 5.9% 40%;
    --danger-000: 5 69.4% 72.9%;
    --danger-100: 5 79.4% 70.8%;
    --danger-200: 5 53.6% 44.8%;
    --danger-900: 0 21.4% 17.6%;
  }

  :root {
    --white: 0 0% 100%;
    --black: 0 0% 0%;
    --kraft: 25 49.7% 66.5%;
    --book-cloth: 15 52.3% 58%;
    --manilla: 40 54% 82.9%;
    --clay: 15 63.1% 59.6%;
  }

  /* NB: Legacy color variables - claude-* needs to be synced with
   * updateTitleOverlayColors */
  :root {
    --claude-foreground-color: black;
    --claude-background-color: #faf9f5;
    --claude-secondary-color: #737163;
    --claude-border: #706b5740;
    --claude-border-300: #706b5740;
    --claude-border-300-more: #706b57a6;

    --claude-text-100: #29261b;
    --claude-text-200: #3d3929;
    --claude-text-400: #656358;
    --claude-description-text: #535146;
  }

  .darkTheme {
    --claude-foreground-color: white;
    --claude-background-color: #262624;
    --claude-secondary-color: #a6a39a;
    --claude-border: #eaddd81a;
    --claude-border-300: #6c6a6040;
    --claude-border-300-more: #6c6a6094;
    --claude-text-100: #f5f4ef;
    --claude-text-200: #e5e5e2;
    --claude-text-400: #b8b5a9;
    --claude-text-500: #a6a39b;
    --claude-description-text: #ceccc5;
  }

  .secondary {
    font-size: 12px;
    line-height: 1.15;
    color: var(--claude-secondary-color);
    margin: 8px;
  }

  html,
  body {
    color: var(--claude-foreground-color);
    background-color: var(--claude-background-color);
    margin: 0;
    padding: 0;
    height: 100%;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
  }

  .nc-no-drag {
    -webkit-app-region: no-drag;
  }
  .nc-drag {
    -webkit-app-region: drag;
  }
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.bottom-0 {
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-3 {
  left: 0.75rem;
}
.right-0 {
  right: 0px;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.z-50 {
  z-index: 50;
}
.col-start-1 {
  grid-column-start: 1;
}
.col-end-2 {
  grid-column-end: 2;
}
.row-start-1 {
  grid-row-start: 1;
}
.row-end-2 {
  grid-row-end: 2;
}
.m-0 {
  margin: 0px;
}
.m-2 {
  margin: 0.5rem;
}
.m-auto {
  margin: auto;
}
.\!mb-2 {
  margin-bottom: 0.5rem !important;
}
.-mr-1\.5 {
  margin-right: -0.375rem;
}
.-mr-2 {
  margin-right: -0.5rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.ms-3 {
  margin-inline-start: 0.75rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.grid {
  display: grid;
}
.contents {
  display: contents;
}
.hidden {
  display: none;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[100\%\] {
  height: 100%;
}
.h-\[30px\] {
  height: 30px;
}
.h-full {
  height: 100%;
}
.max-h-\[16rem\] {
  max-height: 16rem;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-\[0px\] {
  min-height: 0px;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-20 {
  width: 5rem;
}
.w-3 {
  width: 0.75rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[100\%\] {
  width: 100%;
}
.w-\[30px\] {
  width: 30px;
}
.w-\[36px\] {
  width: 36px;
}
.w-full {
  width: 100%;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[13rem\] {
  min-width: 13rem;
}
.min-w-\[15rem\] {
  min-width: 15rem;
}
.min-w-\[18rem\] {
  min-width: 18rem;
}
.min-w-\[4rem\] {
  min-width: 4rem;
}
.min-w-\[5rem\] {
  min-width: 5rem;
}
.min-w-\[6rem\] {
  min-width: 6rem;
}
.max-w-md {
  max-width: 28rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink {
  flex-shrink: 1;
}
.shrink-0 {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.basis-0 {
  flex-basis: 0px;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-text {
  cursor: text;
}
.select-none {
  user-select: none;
}
.select-text {
  user-select: text;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.scroll-pb-6 {
  scroll-padding-bottom: 1.5rem;
}
.appearance-none {
  appearance: none;
}
.grid-cols-\[minmax\(0\2c _1fr\)_auto\] {
  grid-template-columns: minmax(0, 1fr) auto;
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-4 {
  gap: 1rem;
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.self-center {
  align-self: center;
}
.self-stretch {
  align-self: stretch;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-visible {
  overflow-x: visible;
}
.overscroll-contain {
  overscroll-behavior: contain;
}
.scroll-smooth {
  scroll-behavior: smooth;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.text-nowrap {
  text-wrap: nowrap;
}
.break-words {
  overflow-wrap: break-word;
}
.\!rounded-full {
  border-radius: 9999px !important;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-\[0\.25rem\] {
  border-radius: 0.25rem;
}
.rounded-\[0\.6rem\] {
  border-radius: 0.6rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-ee-lg {
  border-end-end-radius: 0.5rem;
}
.rounded-ss-md {
  border-start-start-radius: 0.375rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-0\.5 {
  border-width: 0.5px;
}
.border-1\.5 {
  border-width: 1.5px;
}
.border-2 {
  border-width: 2px;
}
.border-8 {
  border-width: 8px;
}
.border-solid {
  border-style: solid;
}
.border-none {
  border-style: none;
}
.\!border-danger-200\/50 {
  border-color: hsl(var(--danger-200) / 0.5) !important;
}
.border-accent-secondary-000 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--accent-secondary-000) / var(--tw-border-opacity));
}
.border-border-100 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border-100) / var(--tw-border-opacity));
}
.border-border-200 {
  border-color: hsl(var(--border-200) / 0.5);
}
.border-border-200\/30 {
  border-color: hsl(var(--border-200) / 0.3);
}
.border-border-300 {
  border-color: hsl(var(--border-300) / 0.25);
}
.border-border-400 {
  border-color: hsl(var(--border-400) / 0.1);
}
.border-current {
  border-color: currentColor;
}
.border-text-100 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--text-100) / var(--tw-border-opacity));
}
.border-transparent {
  border-color: transparent;
}
.border-r-transparent {
  border-right-color: transparent;
}
.\!bg-bg-400 {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--bg-400) / var(--tw-bg-opacity)) !important;
}
.bg-accent-main-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-main-100) / var(--tw-bg-opacity));
}
.bg-accent-main-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-main-200) / var(--tw-bg-opacity));
}
.bg-accent-pro-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-pro-100) / var(--tw-bg-opacity));
}
.bg-accent-secondary-000 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-secondary-000) / var(--tw-bg-opacity));
}
.bg-accent-secondary-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-secondary-200) / var(--tw-bg-opacity));
}
.bg-bg-000 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-000) / var(--tw-bg-opacity));
}
.bg-bg-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-200) / var(--tw-bg-opacity));
}
.bg-bg-300 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-300) / var(--tw-bg-opacity));
}
.bg-bg-500 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-500) / var(--tw-bg-opacity));
}
.bg-bg-500\/40 {
  background-color: hsl(var(--bg-500) / 0.4);
}
.bg-danger-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--danger-100) / var(--tw-bg-opacity));
}
.bg-danger-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--danger-200) / var(--tw-bg-opacity));
}
.bg-danger-200\/10 {
  background-color: hsl(var(--danger-200) / 0.1);
}
.bg-text-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--text-100) / var(--tw-bg-opacity));
}
.bg-text-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--text-200) / var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.bg-\[radial-gradient\(ellipse\2c _var\(--tw-gradient-stops\)\)\] {
  background-image: radial-gradient(ellipse, var(--tw-gradient-stops));
}
.bg-gradient-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-accent-main-100 {
  --tw-gradient-from: hsl(var(--accent-main-100) / 1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--accent-main-100) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-accent-pro-200 {
  --tw-gradient-from: hsl(var(--accent-pro-200) / 1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--accent-pro-200) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-accent-secondary-200\/70 {
  --tw-gradient-from: hsl(var(--accent-secondary-200) / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--accent-secondary-200) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-bg-500\/10 {
  --tw-gradient-from: hsl(var(--bg-500) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--bg-500) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-bg-500\/30 {
  --tw-gradient-from: hsl(var(--bg-500) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--bg-500) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-50\% {
  --tw-gradient-from-position: 50%;
}
.via-accent-main-200\/50 {
  --tw-gradient-to: hsl(var(--accent-main-200) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--accent-main-200) / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-accent-main-200 {
  --tw-gradient-to: hsl(var(--accent-main-200) / 1) var(--tw-gradient-to-position);
}
.to-accent-pro-100 {
  --tw-gradient-to: hsl(var(--accent-pro-100) / 1) var(--tw-gradient-to-position);
}
.to-accent-secondary-100 {
  --tw-gradient-to: hsl(var(--accent-secondary-100) / 1) var(--tw-gradient-to-position);
}
.to-bg-500\/30 {
  --tw-gradient-to: hsl(var(--bg-500) / 0.3) var(--tw-gradient-to-position);
}
.to-bg-500\/70 {
  --tw-gradient-to: hsl(var(--bg-500) / 0.7) var(--tw-gradient-to-position);
}
.bg-\[length\:200\%_100\%\] {
  background-size: 200% 100%;
}
.p-0 {
  padding: 0px;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-2\.5 {
  padding-left: 0.625rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-3\.5 {
  padding-left: 0.875rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-3\.5 {
  padding-right: 0.875rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-7 {
  padding-top: 1.75rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-top {
  vertical-align: top;
}
.align-middle {
  vertical-align: middle;
}
.align-\[-0\.125em\] {
  vertical-align: -0.125em;
}
.font-copernicus {
  font-family: Copernicus, ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  font-feature-settings: "ss01" 0;
}
.font-styrene {
  font-family: Styrene B LC, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.font-styrene-display {
  font-family: Styrene B LC, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.\!text-xs {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-\[0\.625rem\] {
  font-size: 0.625rem;
}
.text-\[12px\] {
  font-size: 12px;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-\[7px\] {
  font-size: 7px;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\!font-bold {
  font-weight: 700 !important;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.italic {
  font-style: italic;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-none {
  line-height: 1;
}
.tracking-\[0\.015rem\] {
  letter-spacing: 0.015rem;
}
.\!text-text-400 {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--text-400) / var(--tw-text-opacity)) !important;
}
.text-bg-100 {
  --tw-text-opacity: 1;
  color: hsl(var(--bg-100) / var(--tw-text-opacity));
}
.text-danger-100 {
  --tw-text-opacity: 1;
  color: hsl(var(--danger-100) / var(--tw-text-opacity));
}
.text-oncolor-100 {
  --tw-text-opacity: 1;
  color: hsl(var(--oncolor-100) / var(--tw-text-opacity));
}
.text-text-000 {
  --tw-text-opacity: 1;
  color: hsl(var(--text-000) / var(--tw-text-opacity));
}
.text-text-100 {
  --tw-text-opacity: 1;
  color: hsl(var(--text-100) / var(--tw-text-opacity));
}
.text-text-100\/90 {
  color: hsl(var(--text-100) / 0.9);
}
.text-text-200 {
  --tw-text-opacity: 1;
  color: hsl(var(--text-200) / var(--tw-text-opacity));
}
.text-text-300 {
  --tw-text-opacity: 1;
  color: hsl(var(--text-300) / var(--tw-text-opacity));
}
.text-text-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--text-400) / var(--tw-text-opacity));
}
.text-text-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--text-500) / var(--tw-text-opacity));
}
.text-transparent {
  color: transparent;
}
.underline {
  text-decoration-line: underline;
}
.opacity-40 {
  opacity: 0.4;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-80 {
  opacity: 0.8;
}
.shadow-\[inset_0_0\.5px_0px_rgba\(255\2c 255\2c 0\2c 0\.15\)\] {
  --tw-shadow: inset 0 0.5px 0px rgba(255,255,0,0.15);
  --tw-shadow-colored: inset 0 0.5px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-element {
  --tw-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.1), 0 0 20px rgba(0, 0, 0, 0.05), 0 1px 5px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 0 0 0.5px var(--tw-shadow-color), 0 0 20px var(--tw-shadow-color), 0 1px 5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-accent-main-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--accent-main-100) / var(--tw-ring-opacity));
}
.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}
.ring-offset-bg-300 {
  --tw-ring-offset-color: hsl(var(--bg-300) / 1);
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-sm {
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100 {
  transition-duration: 100ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.\[-webkit-overflow-scrolling\:touch\] {
  -webkit-overflow-scrolling: touch;
}
.\[scrollbar-color\:hsl\(var\(--text-500\)\)\] {
  scrollbar-color: hsl(var(--text-500));
}
.\[text-shadow\:_0_1px_2px_rgb\(0_0_0_\/_10\%\)\] {
  text-shadow: 0 1px 2px rgb(0 0 0 / 10%);
}
.\!\[text-shadow\:_none\] {
  text-shadow: none !important;
}
.placeholder\:text-text-400::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--text-400) / var(--tw-text-opacity));
}
.placeholder\:text-text-500::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--text-500) / var(--tw-text-opacity));
}
.placeholder\:opacity-70::placeholder {
  opacity: 0.7;
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:start-\[2px\]::after {
  content: var(--tw-content);
  inset-inline-start: 2px;
}
.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}
.after\:h-4::after {
  content: var(--tw-content);
  height: 1rem;
}
.after\:w-4::after {
  content: var(--tw-content);
  width: 1rem;
}
.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}
.after\:border-bg-000::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--bg-000) / var(--tw-border-opacity));
}
.after\:border-border-300::after {
  content: var(--tw-content);
  border-color: hsl(var(--border-300) / 0.25);
}
.after\:bg-text-500::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--text-500) / var(--tw-bg-opacity));
}
.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--white) / var(--tw-bg-opacity));
}
.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}
.read-only\:opacity-50:read-only {
  opacity: 0.5;
}
.focus-within\:\!border-accent-secondary-100:focus-within {
  --tw-border-opacity: 1 !important;
  border-color: hsl(var(--accent-secondary-100) / var(--tw-border-opacity)) !important;
}
.hover\:\!border-danger-200\/90:hover {
  border-color: hsl(var(--danger-200) / 0.9) !important;
}
.hover\:border-bg-400:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--bg-400) / var(--tw-border-opacity));
}
.hover\:border-border-100:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border-100) / var(--tw-border-opacity));
}
.hover\:border-border-200:hover {
  border-color: hsl(var(--border-200) / 0.5);
}
.hover\:bg-accent-main-200:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-main-200) / var(--tw-bg-opacity));
}
.hover\:bg-bg-200:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-200) / var(--tw-bg-opacity));
}
.hover\:bg-bg-300:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-300) / var(--tw-bg-opacity));
}
.hover\:bg-bg-400:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-400) / var(--tw-bg-opacity));
}
.hover\:bg-bg-500:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-500) / var(--tw-bg-opacity));
}
.hover\:bg-bg-500\/40:hover {
  background-color: hsl(var(--bg-500) / 0.4);
}
.hover\:bg-bg-500\/60:hover {
  background-color: hsl(var(--bg-500) / 0.6);
}
.hover\:bg-danger-200:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--danger-200) / var(--tw-bg-opacity));
}
.hover\:from-accent-main-200:hover {
  --tw-gradient-from: hsl(var(--accent-main-200) / 1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--accent-main-200) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:to-accent-main-200:hover {
  --tw-gradient-to: hsl(var(--accent-main-200) / 1) var(--tw-gradient-to-position);
}
.hover\:bg-right:hover {
  background-position: right;
}
.hover\:text-text-000:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--text-000) / var(--tw-text-opacity));
}
.hover\:text-text-100:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--text-100) / var(--tw-text-opacity));
}
.hover\:text-text-300:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--text-300) / var(--tw-text-opacity));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.focus\:\!border-danger-200:focus {
  --tw-border-opacity: 1 !important;
  border-color: hsl(var(--danger-200) / var(--tw-border-opacity)) !important;
}
.focus\:border-accent-secondary-100:focus {
  --tw-border-opacity: 1;
  border-color: hsl(var(--accent-secondary-100) / var(--tw-border-opacity));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-accent-main-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--accent-main-200) / var(--tw-ring-opacity));
}
.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.active\:scale-95:active {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.active\:scale-\[0\.985\]:active {
  --tw-scale-x: 0.985;
  --tw-scale-y: 0.985;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.active\:bg-accent-main-000:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-main-000) / var(--tw-bg-opacity));
}
.active\:bg-bg-400:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-400) / var(--tw-bg-opacity));
}
.active\:bg-bg-500\/50:active {
  background-color: hsl(var(--bg-500) / 0.5);
}
.active\:shadow-\[inset_0_1px_6px_rgba\(0\2c 0\2c 0\2c 0\.2\)\]:active {
  --tw-shadow: inset 0 1px 6px rgba(0,0,0,0.2);
  --tw-shadow-colored: inset 0 1px 6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.disabled\:shadow-none:disabled {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.disabled\:drop-shadow-none:disabled {
  --tw-drop-shadow: drop-shadow(0 0 #0000);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.peer:checked ~ .peer-checked\:bg-accent-secondary-000 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-secondary-000) / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:bg-accent-secondary-900 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-secondary-900) / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:checked ~ .peer-checked\:after\:border-accent-secondary-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--accent-secondary-900) / var(--tw-border-opacity));
}
.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--white) / var(--tw-border-opacity));
}
.peer:checked ~ .peer-checked\:after\:bg-accent-secondary-100::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-secondary-100) / var(--tw-bg-opacity));
}
.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.peer:focus ~ .peer-focus\:ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.peer:focus ~ .peer-focus\:ring-accent-secondary-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--accent-secondary-100) / var(--tw-ring-opacity));
}
.peer:focus ~ .peer-focus\:ring-offset-2 {
  --tw-ring-offset-width: 2px;
}
.peer:focus ~ .peer-focus\:ring-offset-bg-300 {
  --tw-ring-offset-color: hsl(var(--bg-300) / 1);
}
.peer:focus-visible ~ .peer-focus-visible\:ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.peer:disabled ~ .peer-disabled\:opacity-50 {
  opacity: 0.5;
}
@media (prefers-reduced-motion: reduce) {

  @keyframes spin {

    to {
      transform: rotate(360deg);
    }
  }

  .motion-reduce\:animate-\[spin_1\.5s_linear_infinite\] {
    animation: spin 1.5s linear infinite;
  }
}
.dark\:text-text-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--text-100) / var(--tw-text-opacity));
}
.peer:checked ~ .rtl\:peer-checked\:after\:-translate-x-full:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\.has-bottom-scroll\]\:\[mask-image\:linear-gradient\(to_top\2c rgba\(0\2c 0\2c 0\2c 0\)_0\%\2c _rgba\(0\2c 0\2c 0\2c 1\)_3\%\)\].has-bottom-scroll {
  mask-image: linear-gradient(to top,rgba(0,0,0,0) 0%, rgba(0,0,0,1) 3%);
}
.\[\&\:\:-webkit-scrollbar-thumb\:hover\]\:bg-text-500::-webkit-scrollbar-thumb:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--text-500) / var(--tw-bg-opacity));
}
.\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-\[1em\]::-webkit-scrollbar-thumb {
  border-radius: 1em;
}
.\[\&\:\:-webkit-scrollbar-thumb\]\:border-\[0\.25rem\]::-webkit-scrollbar-thumb {
  border-width: 0.25rem;
}
.\[\&\:\:-webkit-scrollbar-thumb\]\:border-transparent::-webkit-scrollbar-thumb {
  border-color: transparent;
}
.\[\&\:\:-webkit-scrollbar-thumb\]\:bg-text-500\/80::-webkit-scrollbar-thumb {
  background-color: hsl(var(--text-500) / 0.8);
}
.\[\&\:\:-webkit-scrollbar-thumb\]\:bg-clip-padding::-webkit-scrollbar-thumb {
  background-clip: padding-box;
}
.\[\&\:\:-webkit-scrollbar-track\]\:my-1::-webkit-scrollbar-track {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.\[\&\:\:-webkit-scrollbar-track\]\:bg-transparent::-webkit-scrollbar-track {
  background-color: transparent;
}
.\[\&\:\:-webkit-scrollbar\]\:mt-4::-webkit-scrollbar {
  margin-top: 1rem;
}
.\[\&\:\:-webkit-scrollbar\]\:w-\[0\.25rem\]::-webkit-scrollbar {
  width: 0.25rem;
}
.\[\&\[data-disabled\]\]\:cursor-default[data-disabled] {
  cursor: default;
}
.\[\&\[data-disabled\]\]\:cursor-not-allowed[data-disabled] {
  cursor: not-allowed;
}
.\[\&\[data-disabled\]\]\:bg-bg-100[data-disabled] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bg-100) / var(--tw-bg-opacity));
}
.\[\&\[data-disabled\]\]\:\!text-text-500[data-disabled] {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--text-500) / var(--tw-text-opacity)) !important;
}
.\[\&\[data-disabled\]\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}
    </style>

    <script type="module" crossorigin src="./assets/main-DH6w-Grp.js"></script>
  </head>

  <body>

  </body>
</html>
