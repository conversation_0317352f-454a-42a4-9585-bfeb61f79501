@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  * {
    margin: 0;
  }

  img,
  picture,
  video,
  canvas,
  svg {
    display: block;
    max-width: 100%;
  }

  input,
  button,
  textarea,
  select {
    font: inherit;
  }

  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    overflow-wrap: break-word;
  }

  * {
    --claude-accent-clay: #d97757;
  }

  /* Values taken from claude.ai on Nov. 8 2024 */
  :root {
    --text-000: 49 6.9% 5.5%;
    --text-100: 49 19.6% 13.3%;
    --text-200: 49 18.8% 20%;
    --text-300: 49 9% 30%;
    --text-400: 49 7% 37%;
    --text-500: 51 7.5% 42.1%;
    --accent-main-000: 15 52.7% 43.9%;
    --accent-main-100: 16 53.8% 47.5%;
    --accent-main-200: 15 55.6% 52.4%;
    --accent-secondary-000: 210 74.2% 42.1%;
    --accent-secondary-100: 210 74.8% 49.8%;
    --accent-secondary-200: 210 74.8% 57%;
    --accent-secondary-900: 210 68.8% 93.3%;
    --accent-pro-000: 251 34.2% 33.3%;
    --accent-pro-100: 251 40% 45.1%;
    --accent-pro-200: 251 61% 72.2%;
    --accent-pro-900: 253 33.3% 91.8%;
    --oncolor-100: 0 0% 100%;
    --bg-000: 60 6.7% 97.1%;
    --bg-100: 50 23.1% 94.9%;
    --bg-200: 49 26.8% 92%;
    --bg-300: 49 25.8% 87.8%;
    --bg-400: 46 28.3% 82%;
    --bg-500: 47 27% 71%;
    --accent-main-900: 15 48% 90.2%;
    --border-100: 48 12.5% 39.2%;
    --border-200: 48 12.5% 39.2%;
    --border-300: 48 12.5% 39.2%;
    --oncolor-200: 60 6.7% 97.1%;
    --oncolor-300: 60 6.7% 97.1%;
    --border-400: 48 12.5% 39.2%;
    --danger-000: 5 74% 28%;
    --danger-100: 5 73.9% 37.7%;
    --danger-200: 5 49.5% 58%;
    --danger-900: 0 40.3% 89%;
  }

  .darkTheme {
    --text-000: 60 6.7% 97.1%;
    --text-100: 50 23.1% 94.9%;
    --text-200: 60 5.5% 89.2%;
    --text-300: 47 8.4% 79%;
    --text-400: 48 9.6% 69.2%;
    --text-500: 45 6.3% 62.9%;
    --accent-main-000: 18 50.4% 47.5%;
    --accent-main-100: 18 56.8% 43.5%;
    --accent-main-200: 19 58.3% 40.4%;
    --accent-secondary-000: 210 74.8% 57%;
    --accent-secondary-100: 210 74.8% 49.8%;
    --accent-secondary-200: 210 74.2% 42.1%;
    --accent-secondary-900: 210 19.5% 18%;
    --accent-pro-000: 251 84.6% 74.5%;
    --accent-pro-100: 251 40.2% 54.1%;
    --accent-pro-200: 251 40% 45.1%;
    --accent-pro-900: 250 25.3% 19.4%;
    --oncolor-100: 0 0% 100%;
    --bg-000: 60 1.8% 22%;
    --bg-100: 60 3.3% 17.8%;
    --bg-200: 45 4.9% 16.1%;
    --bg-300: 48 8.2% 12%;
    --bg-400: 48 10.6% 9.2%;
    --bg-500: 60 7.1% 5.5%;
    --accent-main-900: 16 41.3% 18%;
    --border-100: 50 5.8% 40%;
    --border-200: 50 5.9% 40%;
    --border-300: 50 5.9% 40%;
    --oncolor-200: 60 6.7% 97.1%;
    --oncolor-300: 60 6.7% 97.1%;
    --border-400: 50 5.9% 40%;
    --danger-000: 5 69.4% 72.9%;
    --danger-100: 5 79.4% 70.8%;
    --danger-200: 5 53.6% 44.8%;
    --danger-900: 0 21.4% 17.6%;
  }

  :root {
    --white: 0 0% 100%;
    --black: 0 0% 0%;
    --kraft: 25 49.7% 66.5%;
    --book-cloth: 15 52.3% 58%;
    --manilla: 40 54% 82.9%;
    --clay: 15 63.1% 59.6%;
  }

  /* NB: Legacy color variables - claude-* needs to be synced with
   * updateTitleOverlayColors */
  :root {
    --claude-foreground-color: black;
    --claude-background-color: #faf9f5;
    --claude-secondary-color: #737163;
    --claude-border: #706b5740;
    --claude-border-300: #706b5740;
    --claude-border-300-more: #706b57a6;

    --claude-text-100: #29261b;
    --claude-text-200: #3d3929;
    --claude-text-400: #656358;
    --claude-description-text: #535146;
  }

  .darkTheme {
    --claude-foreground-color: white;
    --claude-background-color: #262624;
    --claude-secondary-color: #a6a39a;
    --claude-border: #eaddd81a;
    --claude-border-300: #6c6a6040;
    --claude-border-300-more: #6c6a6094;
    --claude-text-100: #f5f4ef;
    --claude-text-200: #e5e5e2;
    --claude-text-400: #b8b5a9;
    --claude-text-500: #a6a39b;
    --claude-description-text: #ceccc5;
  }

  .secondary {
    font-size: 12px;
    line-height: 1.15;
    color: var(--claude-secondary-color);
    margin: 8px;
  }

  html,
  body {
    color: var(--claude-foreground-color);
    background-color: var(--claude-background-color);
    margin: 0;
    padding: 0;
    height: 100%;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
  }

  .nc-no-drag {
    -webkit-app-region: no-drag;
  }
  .nc-drag {
    -webkit-app-region: drag;
  }

  .system-font {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
      "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
      "Helvetica Neue", sans-serif;
  }
}
