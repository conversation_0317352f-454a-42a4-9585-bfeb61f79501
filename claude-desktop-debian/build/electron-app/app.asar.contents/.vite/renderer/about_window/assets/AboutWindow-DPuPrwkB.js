import{r as a,_ as h,s as b,u as N,j as r,c as k,K as M}from"./main-DH6w-Grp.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4ce37512-b028-4acc-b512-6dd85b3891fb",e._sentryDebugIdIdentifier="sentry-dbid-4ce37512-b028-4acc-b512-6dd85b3891fb")}catch{}})();function H(e,t){var o=e.values,n=h(e,["values"]),s=t.values,i=h(t,["values"]);return b(s,o)&&b(n,i)}function v(e){var t=N(),o=t.formatMessage,n=t.textComponent,s=n===void 0?a.Fragment:n,i=e.id,c=e.description,L=e.defaultMessage,f=e.values,p=e.children,d=e.tagName,l=d===void 0?s:d,g=e.ignoreTag,j={id:i,description:c,defaultMessage:L},u=o(j,f,{ignoreTag:g});return typeof p=="function"?p(Array.isArray(u)?u:[u]):l?a.createElement(l,null,a.Children.toArray(u)):a.createElement(a.Fragment,null,u)}v.displayName="FormattedMessage";var m=a.memo(v,H);m.displayName="MemoizedFormattedMessage";const x=({variant:e,prepend:t,children:o,style:n,className:s,...i})=>{const c=e==="secondary"?{backgroundImage:"radial-gradient(ellipse, hsl(var(--bg-500) / 0.1) 50%, hsl(var(--bg-500) / 0.3))",backgroundColor:"transparent",borderColor:"hsl(var(--border-400) / .1)",color:"hsl(var(--text-100) / .9)"}:{backgroundImage:"linear-gradient(to right, hsl(var(--accent-main-100)), hsl(var(--accent-main-200)), hsl(var(--accent-main-200)))",backgroundColor:"hsl(var(--accent-main-100))",borderColor:"hsla(var(--border-300-rgb), 0.25)",color:"hsl(var(--oncolor-100))",textShadow:"0 1px 2px rgba(0, 0, 0, 0.1)"},L=t?{paddingRight:"0.75rem"}:{};return r.jsxs("button",{className:s,...i,style:{transition:"all 0.15s cubic-bezier(0.4, 0, 0.2, 1)",filter:"drop-shadow(0 1px 1px rgba(0, 0, 0, 0.05))",boxShadow:"inset 0 0.5px 0px rgba(255, 255, 0, 0.15)",fontWeight:500,fontSize:"14px",fontFamily:'var(--font-styrene-b), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',gap:"6px",padding:"0.5rem 1rem",backgroundSize:"200% 100%",borderWidth:"0.5px",borderRadius:"6px",whiteSpace:"nowrap",justifyContent:"center",alignItems:"center",flexShrink:0,height:"32px",display:"inline-flex",position:"relative",cursor:"pointer",...c,...L,...n},children:[t,o]})};function S({width:e=16,height:t=16,style:o}){return r.jsx("svg",{width:e,height:t,style:o,viewBox:"0 0 248 248",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M52.4285 162.873L98.7844 136.879L99.5485 134.602L98.7844 133.334H96.4921L88.7237 132.862L62.2346 132.153L39.3113 131.207L17.0249 130.026L11.4214 128.844L6.2 121.873L6.7094 118.447L11.4214 115.257L18.171 115.847L33.0711 116.911L55.485 118.447L71.6586 119.392L95.728 121.873H99.5485L100.058 120.337L98.7844 119.392L97.7656 118.447L74.5877 102.732L49.4995 86.1905L36.3823 76.62L29.3779 71.7757L25.8121 67.2858L24.2839 57.3608L30.6515 50.2716L39.3113 50.8623L41.4763 51.4531L50.2636 58.1879L68.9842 72.7209L93.4357 90.6804L97.0015 93.6343L98.4374 92.6652L98.6571 91.9801L97.0015 89.2625L83.757 65.2772L69.621 40.8192L63.2534 30.6579L61.5978 24.632C60.9565 22.1032 60.579 20.0111 60.579 17.4246L67.8381 7.49965L71.9133 6.19995L81.7193 7.49965L85.7946 11.0443L91.9074 24.9865L101.714 46.8451L116.996 76.62L121.453 85.4816L123.873 93.6343L124.764 96.1155H126.292V94.6976L127.566 77.9197L129.858 57.3608L132.15 30.8942L132.915 23.4505L136.608 14.4708L143.994 9.62643L149.725 12.344L154.437 19.0788L153.8 23.4505L150.998 41.6463L145.522 70.1215L141.957 89.2625H143.994L146.414 86.7813L156.093 74.0206L172.266 53.698L179.398 45.6635L187.803 36.802L193.152 32.5484H203.34L210.726 43.6549L207.415 55.1159L196.972 68.3492L188.312 79.5739L175.896 96.2095L168.191 109.585L168.882 110.689L170.738 110.53L198.755 104.504L213.91 101.787L231.994 98.7149L240.144 102.496L241.036 106.395L237.852 114.311L218.495 119.037L195.826 123.645L162.07 131.592L161.696 131.893L162.137 132.547L177.36 133.925L183.855 134.279H199.774L229.447 136.524L237.215 141.605L241.8 147.867L241.036 152.711L229.065 158.737L213.019 154.956L175.45 145.977L162.587 142.787H160.805V143.85L171.502 154.366L191.242 172.089L215.82 195.011L217.094 200.682L213.91 205.172L210.599 204.699L188.949 188.394L180.544 181.069L161.696 165.118H160.422V166.772L164.752 173.152L187.803 207.771L188.949 218.405L187.294 221.832L181.308 223.959L174.813 222.777L161.187 203.754L147.305 182.486L136.098 163.345L134.745 164.2L128.075 235.42L125.019 239.082L117.887 241.8L111.902 237.31L108.718 229.984L111.902 215.452L115.722 196.547L118.779 181.541L121.58 162.873L123.291 156.636L123.14 156.219L121.773 156.449L107.699 175.752L86.304 204.699L69.3663 222.777L65.291 224.431L58.2867 220.768L58.9235 214.27L62.8713 208.48L86.304 178.705L100.44 160.155L109.551 149.507L109.462 147.967L108.959 147.924L46.6977 188.512L35.6182 189.93L30.7788 185.44L31.4156 178.115L33.7079 175.752L52.4285 162.873Z",fill:"#D97757"})})}const y=k(M.AboutWindow,window.sendPort),w=a.createContext(y);function E({children:e}){return r.jsx(w.Provider,{value:y,children:e})}function C(e){var t,o,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(o=C(e[t]))&&(n&&(n+=" "),n+=o)}else for(o in e)e[o]&&(n&&(n+=" "),n+=o);return n}function F(){for(var e,t,o=0,n="",s=arguments.length;o<s;o++)(e=arguments[o])&&(t=C(e))&&(n&&(n+=" "),n+=t);return n}function A(){const e=a.useContext(w);if(!e)throw new Error("useAboutClient must be used within an AboutClientProvider");return e}function I({className:e}){const t=A(),[o,n]=a.useState("");a.useEffect(()=>{(async()=>{n(await t.getAppName())})()},[t]);const s=process.platform==="darwin"?"Mac":process.platform==="win32"?"Windows":"Linux",i=o||"Claude";return r.jsxs("h2",{className:F(e,"font-copernicus font-medium text-text-100 text-2xl select-none"),style:{letterSpacing:"-1.12px"},children:[i," ",r.jsx("em",{children:"for "}),s]})}function V(){var d;const e=A(),[t,o]=a.useState(null),[n,s]=a.useState(!1),i=(d=t==null?void 0:t.commitHash)==null?void 0:d.slice(0,6),c=`${process.version}${i?` (${i})`:""}`,L=a.useCallback(()=>{e.openHelp()},[e]),f=a.useCallback(()=>{e.openFeedback()},[e]),p=a.useCallback(async()=>{try{const g=`${await e.getAppName()} ${process.version} (${t==null?void 0:t.commitHash}) ${t==null?void 0:t.commitTimestamp}`.trim();await navigator.clipboard.writeText(g),s(!0),setTimeout(()=>s(!1),2e3)}catch(l){console.error("Failed to copy version to clipboard:",l)}},[t,e]);return a.useEffect(()=>{window.mainProcess.debugApi.getBuildProps().then(l=>{o(l)}).catch(l=>{console.error("Failed to fetch build properties:",l)})},[]),r.jsxs("div",{className:"flex flex-col items-center w-full h-full pt-16 nc-drag",children:[r.jsx(S,{width:84,height:84}),r.jsx(I,{className:"mt-4"}),r.jsx("h3",{className:"text-text-400 font-styrene text-md mt-2 nc-no-drag cursor-pointer hover:text-text-300 transition-colors",onClick:p,children:n?r.jsx(m,{defaultMessage:"Copied version to clipboard",id:"mCXNyEdJat",description:"Message shown briefly after copying version to clipboard"}):r.jsx(m,{defaultMessage:"Version {version}",id:"S3k5yXss2r",description:"Version number display in the About window. {version} is the application version number",values:{version:c}})}),r.jsxs("div",{className:"w-full px-16 mt-6 flex flex-col font-styrene-display text-xl font-medium text-text-100 nc-no-drag",children:[r.jsx(x,{variant:"secondary",onClick:L,children:r.jsx(m,{id:"zAYm/Z684h",defaultMessage:"Help",description:"Label for the Help button in the About window"})}),r.jsx(x,{className:"mt-4",variant:"secondary",onClick:f,children:r.jsx(m,{id:"sys7RHphmL",defaultMessage:"Feedback",description:"Label for the Feedback button in the About window"})})]})]})}function T(){return r.jsx(E,{children:r.jsx(V,{})})}export{T as default};
