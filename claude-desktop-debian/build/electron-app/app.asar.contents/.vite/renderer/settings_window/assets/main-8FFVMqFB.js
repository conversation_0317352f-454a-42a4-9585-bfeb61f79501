var Ic=e=>{throw TypeError(e)};var Ys=(e,t,n)=>t.has(e)||Ic("Cannot "+n);var Ve=(e,t,n)=>(Ys(e,t,"read from private field"),n?n.call(e):t.get(e)),fn=(e,t,n)=>t.has(e)?Ic("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Ar=(e,t,n,r)=>(Ys(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Qs=(e,t,n)=>(Ys(e,t,"access private method"),n);(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0496efc0-2ab5-4256-9df5-780ae6fde460",e._sentryDebugIdIdentifier="sentry-dbid-0496efc0-2ab5-4256-9df5-780ae6fde460")}catch{}})();var vg=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};vg.SENTRY_RELEASE={id:"27cc6f763724a1af75b35c386a6b8d014eedc334"};(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();const Eg=""+new URL("Copernicus-Book-Dqxs7atU.otf",import.meta.url).href,Sg=""+new URL("Copernicus-BookItalic-DE-FEDdC.otf",import.meta.url).href,_g=""+new URL("Copernicus-Medium-BDFrxFZK.otf",import.meta.url).href,wg=""+new URL("Copernicus-MediumItalic-DwCSBWW0.otf",import.meta.url).href,xg=""+new URL("Copernicus-Semibold-CN_XmW6o.otf",import.meta.url).href,Cg=""+new URL("StyreneBLC-Medium-Cw-IvyMy.otf",import.meta.url).href,Tg=""+new URL("StyreneBLC-MediumItalic-CKHvGCIz.otf",import.meta.url).href,kg=""+new URL("StyreneBLC-Regular-DLVQLT8g.otf",import.meta.url).href,Ig=""+new URL("StyreneBLC-RegularItalic-hJCoPVD5.otf",import.meta.url).href,Lg=""+new URL("TiemposText-Medium-vqMEr0TH.otf",import.meta.url).href,Ng=""+new URL("TiemposText-MediumItalic-CIeY-CUo.otf",import.meta.url).href,Pg=""+new URL("TiemposText-Regular-CoJqehkj.otf",import.meta.url).href,bg=""+new URL("TiemposText-RegularItalic-C4EVGPqi.otf",import.meta.url).href,Og=`
@font-face {
    font-family: 'Copernicus Book';
    src: url('${Eg}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus Book';
    src: url('${Sg}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${_g}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${wg}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${xg}') format('opentype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${Cg}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${Tg}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${kg}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${Ig}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Lg}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Ng}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Pg}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${bg}') format('opentype');
    font-weight: normal;
    font-style: italic;
}
`,op=document.createElement("style");op.textContent=Og;document.head.appendChild(op);function ip(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var sp={exports:{}},fs={},ap={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Io=Symbol.for("react.element"),Mg=Symbol.for("react.portal"),Rg=Symbol.for("react.fragment"),Ag=Symbol.for("react.strict_mode"),Fg=Symbol.for("react.profiler"),Dg=Symbol.for("react.provider"),jg=Symbol.for("react.context"),Hg=Symbol.for("react.forward_ref"),Bg=Symbol.for("react.suspense"),Ug=Symbol.for("react.memo"),$g=Symbol.for("react.lazy"),Lc=Symbol.iterator;function zg(e){return e===null||typeof e!="object"?null:(e=Lc&&e[Lc]||e["@@iterator"],typeof e=="function"?e:null)}var lp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},up=Object.assign,cp={};function Lr(e,t,n){this.props=e,this.context=t,this.refs=cp,this.updater=n||lp}Lr.prototype.isReactComponent={};Lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fp(){}fp.prototype=Lr.prototype;function iu(e,t,n){this.props=e,this.context=t,this.refs=cp,this.updater=n||lp}var su=iu.prototype=new fp;su.constructor=iu;up(su,Lr.prototype);su.isPureReactComponent=!0;var Nc=Array.isArray,dp=Object.prototype.hasOwnProperty,au={current:null},pp={key:!0,ref:!0,__self:!0,__source:!0};function hp(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)dp.call(t,r)&&!pp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Io,type:e,key:i,ref:s,props:o,_owner:au.current}}function Gg(e,t){return{$$typeof:Io,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function lu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Io}function Vg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Pc=/\/+/g;function Ks(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Vg(""+e.key):t.toString(36)}function hi(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Io:case Mg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Ks(s,0):r,Nc(o)?(n="",e!=null&&(n=e.replace(Pc,"$&/")+"/"),hi(o,t,n,"",function(u){return u})):o!=null&&(lu(o)&&(o=Gg(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Pc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Nc(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Ks(i,a);s+=hi(i,t,n,l,o)}else if(l=zg(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Ks(i,a++),s+=hi(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Uo(e,t,n){if(e==null)return e;var r=[],o=0;return hi(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Wg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ie={current:null},mi={transition:null},Zg={ReactCurrentDispatcher:Ie,ReactCurrentBatchConfig:mi,ReactCurrentOwner:au};function mp(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:Uo,forEach:function(e,t,n){Uo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Uo(e,function(){t++}),t},toArray:function(e){return Uo(e,function(t){return t})||[]},only:function(e){if(!lu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};A.Component=Lr;A.Fragment=Rg;A.Profiler=Fg;A.PureComponent=iu;A.StrictMode=Ag;A.Suspense=Bg;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zg;A.act=mp;A.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=up({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=au.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)dp.call(t,l)&&!pp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Io,type:e.type,key:o,ref:i,props:r,_owner:s}};A.createContext=function(e){return e={$$typeof:jg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Dg,_context:e},e.Consumer=e};A.createElement=hp;A.createFactory=function(e){var t=hp.bind(null,e);return t.type=e,t};A.createRef=function(){return{current:null}};A.forwardRef=function(e){return{$$typeof:Hg,render:e}};A.isValidElement=lu;A.lazy=function(e){return{$$typeof:$g,_payload:{_status:-1,_result:e},_init:Wg}};A.memo=function(e,t){return{$$typeof:Ug,type:e,compare:t===void 0?null:t}};A.startTransition=function(e){var t=mi.transition;mi.transition={};try{e()}finally{mi.transition=t}};A.unstable_act=mp;A.useCallback=function(e,t){return Ie.current.useCallback(e,t)};A.useContext=function(e){return Ie.current.useContext(e)};A.useDebugValue=function(){};A.useDeferredValue=function(e){return Ie.current.useDeferredValue(e)};A.useEffect=function(e,t){return Ie.current.useEffect(e,t)};A.useId=function(){return Ie.current.useId()};A.useImperativeHandle=function(e,t,n){return Ie.current.useImperativeHandle(e,t,n)};A.useInsertionEffect=function(e,t){return Ie.current.useInsertionEffect(e,t)};A.useLayoutEffect=function(e,t){return Ie.current.useLayoutEffect(e,t)};A.useMemo=function(e,t){return Ie.current.useMemo(e,t)};A.useReducer=function(e,t,n){return Ie.current.useReducer(e,t,n)};A.useRef=function(e){return Ie.current.useRef(e)};A.useState=function(e){return Ie.current.useState(e)};A.useSyncExternalStore=function(e,t,n){return Ie.current.useSyncExternalStore(e,t,n)};A.useTransition=function(){return Ie.current.useTransition()};A.version="18.3.1";ap.exports=A;var b=ap.exports;const gp=ip(b);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xg=b,Yg=Symbol.for("react.element"),Qg=Symbol.for("react.fragment"),Kg=Object.prototype.hasOwnProperty,qg=Xg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Jg={key:!0,ref:!0,__self:!0,__source:!0};function yp(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Kg.call(t,r)&&!Jg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Yg,type:e,key:i,ref:s,props:o,_owner:qg.current}}fs.Fragment=Qg;fs.jsx=yp;fs.jsxs=yp;sp.exports=fs;var E=sp.exports,vp={exports:{}},$e={},Ep={exports:{}},Sp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,M){var R=I.length;I.push(M);e:for(;0<R;){var ne=R-1>>>1,le=I[ne];if(0<o(le,M))I[ne]=M,I[R]=le,R=ne;else break e}}function n(I){return I.length===0?null:I[0]}function r(I){if(I.length===0)return null;var M=I[0],R=I.pop();if(R!==M){I[0]=R;e:for(var ne=0,le=I.length,Ho=le>>>1;ne<Ho;){var un=2*(ne+1)-1,Xs=I[un],cn=un+1,Bo=I[cn];if(0>o(Xs,R))cn<le&&0>o(Bo,Xs)?(I[ne]=Bo,I[cn]=R,ne=cn):(I[ne]=Xs,I[un]=R,ne=un);else if(cn<le&&0>o(Bo,R))I[ne]=Bo,I[cn]=R,ne=cn;else break e}}return M}function o(I,M){var R=I.sortIndex-M.sortIndex;return R!==0?R:I.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,f=null,d=3,m=!1,y=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(I){for(var M=n(u);M!==null;){if(M.callback===null)r(u);else if(M.startTime<=I)r(u),M.sortIndex=M.expirationTime,t(l,M);else break;M=n(u)}}function S(I){if(v=!1,g(I),!y)if(n(l)!==null)y=!0,Ws(C);else{var M=n(u);M!==null&&Zs(S,M.startTime-I)}}function C(I,M){y=!1,v&&(v=!1,h(P),P=-1),m=!0;var R=d;try{for(g(M),f=n(l);f!==null&&(!(f.expirationTime>M)||I&&!tt());){var ne=f.callback;if(typeof ne=="function"){f.callback=null,d=f.priorityLevel;var le=ne(f.expirationTime<=M);M=e.unstable_now(),typeof le=="function"?f.callback=le:f===n(l)&&r(l),g(M)}else r(l);f=n(l)}if(f!==null)var Ho=!0;else{var un=n(u);un!==null&&Zs(S,un.startTime-M),Ho=!1}return Ho}finally{f=null,d=R,m=!1}}var T=!1,L=null,P=-1,K=5,F=-1;function tt(){return!(e.unstable_now()-F<K)}function Mr(){if(L!==null){var I=e.unstable_now();F=I;var M=!0;try{M=L(!0,I)}finally{M?Rr():(T=!1,L=null)}}else T=!1}var Rr;if(typeof p=="function")Rr=function(){p(Mr)};else if(typeof MessageChannel<"u"){var kc=new MessageChannel,yg=kc.port2;kc.port1.onmessage=Mr,Rr=function(){yg.postMessage(null)}}else Rr=function(){w(Mr,0)};function Ws(I){L=I,T||(T=!0,Rr())}function Zs(I,M){P=w(function(){I(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,Ws(C))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(I){switch(d){case 1:case 2:case 3:var M=3;break;default:M=d}var R=d;d=M;try{return I()}finally{d=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,M){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var R=d;d=I;try{return M()}finally{d=R}},e.unstable_scheduleCallback=function(I,M,R){var ne=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?ne+R:ne):R=ne,I){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=R+le,I={id:c++,callback:M,priorityLevel:I,startTime:R,expirationTime:le,sortIndex:-1},R>ne?(I.sortIndex=R,t(u,I),n(l)===null&&I===n(u)&&(v?(h(P),P=-1):v=!0,Zs(S,R-ne))):(I.sortIndex=le,t(l,I),y||m||(y=!0,Ws(C))),I},e.unstable_shouldYield=tt,e.unstable_wrapCallback=function(I){var M=d;return function(){var R=d;d=M;try{return I.apply(this,arguments)}finally{d=R}}}})(Sp);Ep.exports=Sp;var ey=Ep.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ty=b,Ue=ey;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _p=new Set,so={};function Rn(e,t){pr(e,t),pr(e+"Capture",t)}function pr(e,t){for(so[e]=t,e=0;e<t.length;e++)_p.add(t[e])}var Lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ra=Object.prototype.hasOwnProperty,ny=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,bc={},Oc={};function ry(e){return Ra.call(Oc,e)?!0:Ra.call(bc,e)?!1:ny.test(e)?Oc[e]=!0:(bc[e]=!0,!1)}function oy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function iy(e,t,n,r){if(t===null||typeof t>"u"||oy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Le(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var he={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){he[e]=new Le(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];he[t]=new Le(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){he[e]=new Le(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){he[e]=new Le(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){he[e]=new Le(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){he[e]=new Le(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){he[e]=new Le(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){he[e]=new Le(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){he[e]=new Le(e,5,!1,e.toLowerCase(),null,!1,!1)});var uu=/[\-:]([a-z])/g;function cu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(uu,cu);he[t]=new Le(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(uu,cu);he[t]=new Le(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(uu,cu);he[t]=new Le(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){he[e]=new Le(e,1,!1,e.toLowerCase(),null,!1,!1)});he.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){he[e]=new Le(e,1,!1,e.toLowerCase(),null,!0,!0)});function fu(e,t,n,r){var o=he.hasOwnProperty(t)?he[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(iy(t,n,o,r)&&(n=null),r||o===null?ry(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Mt=ty.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$o=Symbol.for("react.element"),Gn=Symbol.for("react.portal"),Vn=Symbol.for("react.fragment"),du=Symbol.for("react.strict_mode"),Aa=Symbol.for("react.profiler"),wp=Symbol.for("react.provider"),xp=Symbol.for("react.context"),pu=Symbol.for("react.forward_ref"),Fa=Symbol.for("react.suspense"),Da=Symbol.for("react.suspense_list"),hu=Symbol.for("react.memo"),Ft=Symbol.for("react.lazy"),Cp=Symbol.for("react.offscreen"),Mc=Symbol.iterator;function Fr(e){return e===null||typeof e!="object"?null:(e=Mc&&e[Mc]||e["@@iterator"],typeof e=="function"?e:null)}var ee=Object.assign,qs;function Wr(e){if(qs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);qs=t&&t[1]||""}return`
`+qs+e}var Js=!1;function ea(e,t){if(!e||Js)return"";Js=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Js=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Wr(e):""}function sy(e){switch(e.tag){case 5:return Wr(e.type);case 16:return Wr("Lazy");case 13:return Wr("Suspense");case 19:return Wr("SuspenseList");case 0:case 2:case 15:return e=ea(e.type,!1),e;case 11:return e=ea(e.type.render,!1),e;case 1:return e=ea(e.type,!0),e;default:return""}}function ja(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vn:return"Fragment";case Gn:return"Portal";case Aa:return"Profiler";case du:return"StrictMode";case Fa:return"Suspense";case Da:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case xp:return(e.displayName||"Context")+".Consumer";case wp:return(e._context.displayName||"Context")+".Provider";case pu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case hu:return t=e.displayName||null,t!==null?t:ja(e.type)||"Memo";case Ft:t=e._payload,e=e._init;try{return ja(e(t))}catch{}}return null}function ay(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ja(t);case 8:return t===du?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Jt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Tp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ly(e){var t=Tp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function zo(e){e._valueTracker||(e._valueTracker=ly(e))}function kp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Tp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function bi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ha(e,t){var n=t.checked;return ee({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Rc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Jt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ip(e,t){t=t.checked,t!=null&&fu(e,"checked",t,!1)}function Ba(e,t){Ip(e,t);var n=Jt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ua(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ua(e,t.type,Jt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ac(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ua(e,t,n){(t!=="number"||bi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Zr=Array.isArray;function nr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Jt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function $a(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return ee({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Fc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(Zr(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Jt(n)}}function Lp(e,t){var n=Jt(t.value),r=Jt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Dc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Np(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function za(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Np(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Go,Pp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Go=Go||document.createElement("div"),Go.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Go.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ao(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Kr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},uy=["Webkit","ms","Moz","O"];Object.keys(Kr).forEach(function(e){uy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Kr[t]=Kr[e]})});function bp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Kr.hasOwnProperty(e)&&Kr[e]?(""+t).trim():t+"px"}function Op(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=bp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var cy=ee({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ga(e,t){if(t){if(cy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function Va(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Wa=null;function mu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Za=null,rr=null,or=null;function jc(e){if(e=Po(e)){if(typeof Za!="function")throw Error(_(280));var t=e.stateNode;t&&(t=gs(t),Za(e.stateNode,e.type,t))}}function Mp(e){rr?or?or.push(e):or=[e]:rr=e}function Rp(){if(rr){var e=rr,t=or;if(or=rr=null,jc(e),t)for(e=0;e<t.length;e++)jc(t[e])}}function Ap(e,t){return e(t)}function Fp(){}var ta=!1;function Dp(e,t,n){if(ta)return e(t,n);ta=!0;try{return Ap(e,t,n)}finally{ta=!1,(rr!==null||or!==null)&&(Fp(),Rp())}}function lo(e,t){var n=e.stateNode;if(n===null)return null;var r=gs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var Xa=!1;if(Lt)try{var Dr={};Object.defineProperty(Dr,"passive",{get:function(){Xa=!0}}),window.addEventListener("test",Dr,Dr),window.removeEventListener("test",Dr,Dr)}catch{Xa=!1}function fy(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var qr=!1,Oi=null,Mi=!1,Ya=null,dy={onError:function(e){qr=!0,Oi=e}};function py(e,t,n,r,o,i,s,a,l){qr=!1,Oi=null,fy.apply(dy,arguments)}function hy(e,t,n,r,o,i,s,a,l){if(py.apply(this,arguments),qr){if(qr){var u=Oi;qr=!1,Oi=null}else throw Error(_(198));Mi||(Mi=!0,Ya=u)}}function An(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function jp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Hc(e){if(An(e)!==e)throw Error(_(188))}function my(e){var t=e.alternate;if(!t){if(t=An(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Hc(o),e;if(i===r)return Hc(o),t;i=i.sibling}throw Error(_(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Hp(e){return e=my(e),e!==null?Bp(e):null}function Bp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bp(e);if(t!==null)return t;e=e.sibling}return null}var Up=Ue.unstable_scheduleCallback,Bc=Ue.unstable_cancelCallback,gy=Ue.unstable_shouldYield,yy=Ue.unstable_requestPaint,re=Ue.unstable_now,vy=Ue.unstable_getCurrentPriorityLevel,gu=Ue.unstable_ImmediatePriority,$p=Ue.unstable_UserBlockingPriority,Ri=Ue.unstable_NormalPriority,Ey=Ue.unstable_LowPriority,zp=Ue.unstable_IdlePriority,ds=null,ht=null;function Sy(e){if(ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(ds,e,void 0,(e.current.flags&128)===128)}catch{}}var st=Math.clz32?Math.clz32:xy,_y=Math.log,wy=Math.LN2;function xy(e){return e>>>=0,e===0?32:31-(_y(e)/wy|0)|0}var Vo=64,Wo=4194304;function Xr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ai(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Xr(a):(i&=s,i!==0&&(r=Xr(i)))}else s=n&~o,s!==0?r=Xr(s):i!==0&&(r=Xr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-st(t),o=1<<n,r|=e[n],t&=~o;return r}function Cy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ty(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-st(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=Cy(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Qa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gp(){var e=Vo;return Vo<<=1,!(Vo&4194240)&&(Vo=64),e}function na(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Lo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-st(t),e[t]=n}function ky(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-st(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function yu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var U=0;function Vp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Wp,vu,Zp,Xp,Yp,Ka=!1,Zo=[],Gt=null,Vt=null,Wt=null,uo=new Map,co=new Map,Bt=[],Iy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Uc(e,t){switch(e){case"focusin":case"focusout":Gt=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":Wt=null;break;case"pointerover":case"pointerout":uo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":co.delete(t.pointerId)}}function jr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Po(t),t!==null&&vu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Ly(e,t,n,r,o){switch(t){case"focusin":return Gt=jr(Gt,e,t,n,r,o),!0;case"dragenter":return Vt=jr(Vt,e,t,n,r,o),!0;case"mouseover":return Wt=jr(Wt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return uo.set(i,jr(uo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,co.set(i,jr(co.get(i)||null,e,t,n,r,o)),!0}return!1}function Qp(e){var t=mn(e.target);if(t!==null){var n=An(t);if(n!==null){if(t=n.tag,t===13){if(t=jp(n),t!==null){e.blockedOn=t,Yp(e.priority,function(){Zp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function gi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=qa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Wa=r,n.target.dispatchEvent(r),Wa=null}else return t=Po(n),t!==null&&vu(t),e.blockedOn=n,!1;t.shift()}return!0}function $c(e,t,n){gi(e)&&n.delete(t)}function Ny(){Ka=!1,Gt!==null&&gi(Gt)&&(Gt=null),Vt!==null&&gi(Vt)&&(Vt=null),Wt!==null&&gi(Wt)&&(Wt=null),uo.forEach($c),co.forEach($c)}function Hr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ka||(Ka=!0,Ue.unstable_scheduleCallback(Ue.unstable_NormalPriority,Ny)))}function fo(e){function t(o){return Hr(o,e)}if(0<Zo.length){Hr(Zo[0],e);for(var n=1;n<Zo.length;n++){var r=Zo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Gt!==null&&Hr(Gt,e),Vt!==null&&Hr(Vt,e),Wt!==null&&Hr(Wt,e),uo.forEach(t),co.forEach(t),n=0;n<Bt.length;n++)r=Bt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Bt.length&&(n=Bt[0],n.blockedOn===null);)Qp(n),n.blockedOn===null&&Bt.shift()}var ir=Mt.ReactCurrentBatchConfig,Fi=!0;function Py(e,t,n,r){var o=U,i=ir.transition;ir.transition=null;try{U=1,Eu(e,t,n,r)}finally{U=o,ir.transition=i}}function by(e,t,n,r){var o=U,i=ir.transition;ir.transition=null;try{U=4,Eu(e,t,n,r)}finally{U=o,ir.transition=i}}function Eu(e,t,n,r){if(Fi){var o=qa(e,t,n,r);if(o===null)da(e,t,r,Di,n),Uc(e,r);else if(Ly(o,e,t,n,r))r.stopPropagation();else if(Uc(e,r),t&4&&-1<Iy.indexOf(e)){for(;o!==null;){var i=Po(o);if(i!==null&&Wp(i),i=qa(e,t,n,r),i===null&&da(e,t,r,Di,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else da(e,t,r,null,n)}}var Di=null;function qa(e,t,n,r){if(Di=null,e=mu(r),e=mn(e),e!==null)if(t=An(e),t===null)e=null;else if(n=t.tag,n===13){if(e=jp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Di=e,null}function Kp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(vy()){case gu:return 1;case $p:return 4;case Ri:case Ey:return 16;case zp:return 536870912;default:return 16}default:return 16}}var $t=null,Su=null,yi=null;function qp(){if(yi)return yi;var e,t=Su,n=t.length,r,o="value"in $t?$t.value:$t.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return yi=o.slice(e,1<r?1-r:void 0)}function vi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Xo(){return!0}function zc(){return!1}function ze(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Xo:zc,this.isPropagationStopped=zc,this}return ee(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Xo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Xo)},persist:function(){},isPersistent:Xo}),t}var Nr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_u=ze(Nr),No=ee({},Nr,{view:0,detail:0}),Oy=ze(No),ra,oa,Br,ps=ee({},No,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Br&&(Br&&e.type==="mousemove"?(ra=e.screenX-Br.screenX,oa=e.screenY-Br.screenY):oa=ra=0,Br=e),ra)},movementY:function(e){return"movementY"in e?e.movementY:oa}}),Gc=ze(ps),My=ee({},ps,{dataTransfer:0}),Ry=ze(My),Ay=ee({},No,{relatedTarget:0}),ia=ze(Ay),Fy=ee({},Nr,{animationName:0,elapsedTime:0,pseudoElement:0}),Dy=ze(Fy),jy=ee({},Nr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Hy=ze(jy),By=ee({},Nr,{data:0}),Vc=ze(By),Uy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$y={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},zy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Gy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=zy[e])?!!t[e]:!1}function wu(){return Gy}var Vy=ee({},No,{key:function(e){if(e.key){var t=Uy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=vi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?$y[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wu,charCode:function(e){return e.type==="keypress"?vi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?vi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Wy=ze(Vy),Zy=ee({},ps,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wc=ze(Zy),Xy=ee({},No,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wu}),Yy=ze(Xy),Qy=ee({},Nr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ky=ze(Qy),qy=ee({},ps,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Jy=ze(qy),e1=[9,13,27,32],xu=Lt&&"CompositionEvent"in window,Jr=null;Lt&&"documentMode"in document&&(Jr=document.documentMode);var t1=Lt&&"TextEvent"in window&&!Jr,Jp=Lt&&(!xu||Jr&&8<Jr&&11>=Jr),Zc=" ",Xc=!1;function eh(e,t){switch(e){case"keyup":return e1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function th(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wn=!1;function n1(e,t){switch(e){case"compositionend":return th(t);case"keypress":return t.which!==32?null:(Xc=!0,Zc);case"textInput":return e=t.data,e===Zc&&Xc?null:e;default:return null}}function r1(e,t){if(Wn)return e==="compositionend"||!xu&&eh(e,t)?(e=qp(),yi=Su=$t=null,Wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jp&&t.locale!=="ko"?null:t.data;default:return null}}var o1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Yc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!o1[e.type]:t==="textarea"}function nh(e,t,n,r){Mp(r),t=ji(t,"onChange"),0<t.length&&(n=new _u("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var eo=null,po=null;function i1(e){ph(e,0)}function hs(e){var t=Yn(e);if(kp(t))return e}function s1(e,t){if(e==="change")return t}var rh=!1;if(Lt){var sa;if(Lt){var aa="oninput"in document;if(!aa){var Qc=document.createElement("div");Qc.setAttribute("oninput","return;"),aa=typeof Qc.oninput=="function"}sa=aa}else sa=!1;rh=sa&&(!document.documentMode||9<document.documentMode)}function Kc(){eo&&(eo.detachEvent("onpropertychange",oh),po=eo=null)}function oh(e){if(e.propertyName==="value"&&hs(po)){var t=[];nh(t,po,e,mu(e)),Dp(i1,t)}}function a1(e,t,n){e==="focusin"?(Kc(),eo=t,po=n,eo.attachEvent("onpropertychange",oh)):e==="focusout"&&Kc()}function l1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hs(po)}function u1(e,t){if(e==="click")return hs(t)}function c1(e,t){if(e==="input"||e==="change")return hs(t)}function f1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ut=typeof Object.is=="function"?Object.is:f1;function ho(e,t){if(ut(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ra.call(t,o)||!ut(e[o],t[o]))return!1}return!0}function qc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jc(e,t){var n=qc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qc(n)}}function ih(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ih(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sh(){for(var e=window,t=bi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=bi(e.document)}return t}function Cu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function d1(e){var t=sh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ih(n.ownerDocument.documentElement,n)){if(r!==null&&Cu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Jc(n,i);var s=Jc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var p1=Lt&&"documentMode"in document&&11>=document.documentMode,Zn=null,Ja=null,to=null,el=!1;function ef(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;el||Zn==null||Zn!==bi(r)||(r=Zn,"selectionStart"in r&&Cu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),to&&ho(to,r)||(to=r,r=ji(Ja,"onSelect"),0<r.length&&(t=new _u("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Zn)))}function Yo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xn={animationend:Yo("Animation","AnimationEnd"),animationiteration:Yo("Animation","AnimationIteration"),animationstart:Yo("Animation","AnimationStart"),transitionend:Yo("Transition","TransitionEnd")},la={},ah={};Lt&&(ah=document.createElement("div").style,"AnimationEvent"in window||(delete Xn.animationend.animation,delete Xn.animationiteration.animation,delete Xn.animationstart.animation),"TransitionEvent"in window||delete Xn.transitionend.transition);function ms(e){if(la[e])return la[e];if(!Xn[e])return e;var t=Xn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ah)return la[e]=t[n];return e}var lh=ms("animationend"),uh=ms("animationiteration"),ch=ms("animationstart"),fh=ms("transitionend"),dh=new Map,tf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function rn(e,t){dh.set(e,t),Rn(t,[e])}for(var ua=0;ua<tf.length;ua++){var ca=tf[ua],h1=ca.toLowerCase(),m1=ca[0].toUpperCase()+ca.slice(1);rn(h1,"on"+m1)}rn(lh,"onAnimationEnd");rn(uh,"onAnimationIteration");rn(ch,"onAnimationStart");rn("dblclick","onDoubleClick");rn("focusin","onFocus");rn("focusout","onBlur");rn(fh,"onTransitionEnd");pr("onMouseEnter",["mouseout","mouseover"]);pr("onMouseLeave",["mouseout","mouseover"]);pr("onPointerEnter",["pointerout","pointerover"]);pr("onPointerLeave",["pointerout","pointerover"]);Rn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Rn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Rn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Rn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Rn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Rn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),g1=new Set("cancel close invalid load scroll toggle".split(" ").concat(Yr));function nf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,hy(r,t,void 0,e),e.currentTarget=null}function ph(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;nf(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;nf(o,a,u),i=l}}}if(Mi)throw e=Ya,Mi=!1,Ya=null,e}function Z(e,t){var n=t[il];n===void 0&&(n=t[il]=new Set);var r=e+"__bubble";n.has(r)||(hh(t,e,2,!1),n.add(r))}function fa(e,t,n){var r=0;t&&(r|=4),hh(n,e,r,t)}var Qo="_reactListening"+Math.random().toString(36).slice(2);function mo(e){if(!e[Qo]){e[Qo]=!0,_p.forEach(function(n){n!=="selectionchange"&&(g1.has(n)||fa(n,!1,e),fa(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Qo]||(t[Qo]=!0,fa("selectionchange",!1,t))}}function hh(e,t,n,r){switch(Kp(t)){case 1:var o=Py;break;case 4:o=by;break;default:o=Eu}n=o.bind(null,t,n,e),o=void 0,!Xa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function da(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=mn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Dp(function(){var u=i,c=mu(n),f=[];e:{var d=dh.get(e);if(d!==void 0){var m=_u,y=e;switch(e){case"keypress":if(vi(n)===0)break e;case"keydown":case"keyup":m=Wy;break;case"focusin":y="focus",m=ia;break;case"focusout":y="blur",m=ia;break;case"beforeblur":case"afterblur":m=ia;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Gc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Ry;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Yy;break;case lh:case uh:case ch:m=Dy;break;case fh:m=Ky;break;case"scroll":m=Oy;break;case"wheel":m=Jy;break;case"copy":case"cut":case"paste":m=Hy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Wc}var v=(t&4)!==0,w=!v&&e==="scroll",h=v?d!==null?d+"Capture":null:d;v=[];for(var p=u,g;p!==null;){g=p;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,h!==null&&(S=lo(p,h),S!=null&&v.push(go(p,S,g)))),w)break;p=p.return}0<v.length&&(d=new m(d,y,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",d&&n!==Wa&&(y=n.relatedTarget||n.fromElement)&&(mn(y)||y[Nt]))break e;if((m||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?mn(y):null,y!==null&&(w=An(y),y!==w||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=u),m!==y)){if(v=Gc,S="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=Wc,S="onPointerLeave",h="onPointerEnter",p="pointer"),w=m==null?d:Yn(m),g=y==null?d:Yn(y),d=new v(S,p+"leave",m,n,c),d.target=w,d.relatedTarget=g,S=null,mn(c)===u&&(v=new v(h,p+"enter",y,n,c),v.target=g,v.relatedTarget=w,S=v),w=S,m&&y)t:{for(v=m,h=y,p=0,g=v;g;g=Un(g))p++;for(g=0,S=h;S;S=Un(S))g++;for(;0<p-g;)v=Un(v),p--;for(;0<g-p;)h=Un(h),g--;for(;p--;){if(v===h||h!==null&&v===h.alternate)break t;v=Un(v),h=Un(h)}v=null}else v=null;m!==null&&rf(f,d,m,v,!1),y!==null&&w!==null&&rf(f,w,y,v,!0)}}e:{if(d=u?Yn(u):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var C=s1;else if(Yc(d))if(rh)C=c1;else{C=l1;var T=a1}else(m=d.nodeName)&&m.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=u1);if(C&&(C=C(e,u))){nh(f,C,n,c);break e}T&&T(e,d,u),e==="focusout"&&(T=d._wrapperState)&&T.controlled&&d.type==="number"&&Ua(d,"number",d.value)}switch(T=u?Yn(u):window,e){case"focusin":(Yc(T)||T.contentEditable==="true")&&(Zn=T,Ja=u,to=null);break;case"focusout":to=Ja=Zn=null;break;case"mousedown":el=!0;break;case"contextmenu":case"mouseup":case"dragend":el=!1,ef(f,n,c);break;case"selectionchange":if(p1)break;case"keydown":case"keyup":ef(f,n,c)}var L;if(xu)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Wn?eh(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Jp&&n.locale!=="ko"&&(Wn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Wn&&(L=qp()):($t=c,Su="value"in $t?$t.value:$t.textContent,Wn=!0)),T=ji(u,P),0<T.length&&(P=new Vc(P,e,null,n,c),f.push({event:P,listeners:T}),L?P.data=L:(L=th(n),L!==null&&(P.data=L)))),(L=t1?n1(e,n):r1(e,n))&&(u=ji(u,"onBeforeInput"),0<u.length&&(c=new Vc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=L))}ph(f,t)})}function go(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ji(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=lo(e,n),i!=null&&r.unshift(go(e,i,o)),i=lo(e,t),i!=null&&r.push(go(e,i,o))),e=e.return}return r}function Un(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function rf(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=lo(n,i),l!=null&&s.unshift(go(n,l,a))):o||(l=lo(n,i),l!=null&&s.push(go(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var y1=/\r\n?/g,v1=/\u0000|\uFFFD/g;function of(e){return(typeof e=="string"?e:""+e).replace(y1,`
`).replace(v1,"")}function Ko(e,t,n){if(t=of(t),of(e)!==t&&n)throw Error(_(425))}function Hi(){}var tl=null,nl=null;function rl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ol=typeof setTimeout=="function"?setTimeout:void 0,E1=typeof clearTimeout=="function"?clearTimeout:void 0,sf=typeof Promise=="function"?Promise:void 0,S1=typeof queueMicrotask=="function"?queueMicrotask:typeof sf<"u"?function(e){return sf.resolve(null).then(e).catch(_1)}:ol;function _1(e){setTimeout(function(){throw e})}function pa(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),fo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);fo(t)}function Zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function af(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Pr=Math.random().toString(36).slice(2),dt="__reactFiber$"+Pr,yo="__reactProps$"+Pr,Nt="__reactContainer$"+Pr,il="__reactEvents$"+Pr,w1="__reactListeners$"+Pr,x1="__reactHandles$"+Pr;function mn(e){var t=e[dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Nt]||n[dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=af(e);e!==null;){if(n=e[dt])return n;e=af(e)}return t}e=n,n=e.parentNode}return null}function Po(e){return e=e[dt]||e[Nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Yn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function gs(e){return e[yo]||null}var sl=[],Qn=-1;function on(e){return{current:e}}function Y(e){0>Qn||(e.current=sl[Qn],sl[Qn]=null,Qn--)}function W(e,t){Qn++,sl[Qn]=e.current,e.current=t}var en={},Ee=on(en),Oe=on(!1),wn=en;function hr(e,t){var n=e.type.contextTypes;if(!n)return en;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Me(e){return e=e.childContextTypes,e!=null}function Bi(){Y(Oe),Y(Ee)}function lf(e,t,n){if(Ee.current!==en)throw Error(_(168));W(Ee,t),W(Oe,n)}function mh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(_(108,ay(e)||"Unknown",o));return ee({},n,r)}function Ui(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||en,wn=Ee.current,W(Ee,e),W(Oe,Oe.current),!0}function uf(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=mh(e,t,wn),r.__reactInternalMemoizedMergedChildContext=e,Y(Oe),Y(Ee),W(Ee,e)):Y(Oe),W(Oe,n)}var _t=null,ys=!1,ha=!1;function gh(e){_t===null?_t=[e]:_t.push(e)}function C1(e){ys=!0,gh(e)}function sn(){if(!ha&&_t!==null){ha=!0;var e=0,t=U;try{var n=_t;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}_t=null,ys=!1}catch(o){throw _t!==null&&(_t=_t.slice(e+1)),Up(gu,sn),o}finally{U=t,ha=!1}}return null}var Kn=[],qn=0,$i=null,zi=0,We=[],Ze=0,xn=null,xt=1,Ct="";function pn(e,t){Kn[qn++]=zi,Kn[qn++]=$i,$i=e,zi=t}function yh(e,t,n){We[Ze++]=xt,We[Ze++]=Ct,We[Ze++]=xn,xn=e;var r=xt;e=Ct;var o=32-st(r)-1;r&=~(1<<o),n+=1;var i=32-st(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,xt=1<<32-st(t)+o|n<<o|r,Ct=i+e}else xt=1<<i|n<<o|r,Ct=e}function Tu(e){e.return!==null&&(pn(e,1),yh(e,1,0))}function ku(e){for(;e===$i;)$i=Kn[--qn],Kn[qn]=null,zi=Kn[--qn],Kn[qn]=null;for(;e===xn;)xn=We[--Ze],We[Ze]=null,Ct=We[--Ze],We[Ze]=null,xt=We[--Ze],We[Ze]=null}var He=null,Fe=null,Q=!1,it=null;function vh(e,t){var n=Ye(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function cf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,He=e,Fe=Zt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,He=e,Fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xn!==null?{id:xt,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ye(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,He=e,Fe=null,!0):!1;default:return!1}}function al(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ll(e){if(Q){var t=Fe;if(t){var n=t;if(!cf(e,t)){if(al(e))throw Error(_(418));t=Zt(n.nextSibling);var r=He;t&&cf(e,t)?vh(r,n):(e.flags=e.flags&-4097|2,Q=!1,He=e)}}else{if(al(e))throw Error(_(418));e.flags=e.flags&-4097|2,Q=!1,He=e}}}function ff(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;He=e}function qo(e){if(e!==He)return!1;if(!Q)return ff(e),Q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!rl(e.type,e.memoizedProps)),t&&(t=Fe)){if(al(e))throw Eh(),Error(_(418));for(;t;)vh(e,t),t=Zt(t.nextSibling)}if(ff(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Fe=Zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Fe=null}}else Fe=He?Zt(e.stateNode.nextSibling):null;return!0}function Eh(){for(var e=Fe;e;)e=Zt(e.nextSibling)}function mr(){Fe=He=null,Q=!1}function Iu(e){it===null?it=[e]:it.push(e)}var T1=Mt.ReactCurrentBatchConfig;function Ur(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function Jo(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function df(e){var t=e._init;return t(e._payload)}function Sh(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=Kt(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function a(h,p,g,S){return p===null||p.tag!==6?(p=_a(g,h.mode,S),p.return=h,p):(p=o(p,g),p.return=h,p)}function l(h,p,g,S){var C=g.type;return C===Vn?c(h,p,g.props.children,S,g.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Ft&&df(C)===p.type)?(S=o(p,g.props),S.ref=Ur(h,p,g),S.return=h,S):(S=Ti(g.type,g.key,g.props,null,h.mode,S),S.ref=Ur(h,p,g),S.return=h,S)}function u(h,p,g,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=wa(g,h.mode,S),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function c(h,p,g,S,C){return p===null||p.tag!==7?(p=Sn(g,h.mode,S,C),p.return=h,p):(p=o(p,g),p.return=h,p)}function f(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=_a(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case $o:return g=Ti(p.type,p.key,p.props,null,h.mode,g),g.ref=Ur(h,null,p),g.return=h,g;case Gn:return p=wa(p,h.mode,g),p.return=h,p;case Ft:var S=p._init;return f(h,S(p._payload),g)}if(Zr(p)||Fr(p))return p=Sn(p,h.mode,g,null),p.return=h,p;Jo(h,p)}return null}function d(h,p,g,S){var C=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return C!==null?null:a(h,p,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case $o:return g.key===C?l(h,p,g,S):null;case Gn:return g.key===C?u(h,p,g,S):null;case Ft:return C=g._init,d(h,p,C(g._payload),S)}if(Zr(g)||Fr(g))return C!==null?null:c(h,p,g,S,null);Jo(h,g)}return null}function m(h,p,g,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(g)||null,a(p,h,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case $o:return h=h.get(S.key===null?g:S.key)||null,l(p,h,S,C);case Gn:return h=h.get(S.key===null?g:S.key)||null,u(p,h,S,C);case Ft:var T=S._init;return m(h,p,g,T(S._payload),C)}if(Zr(S)||Fr(S))return h=h.get(g)||null,c(p,h,S,C,null);Jo(p,S)}return null}function y(h,p,g,S){for(var C=null,T=null,L=p,P=p=0,K=null;L!==null&&P<g.length;P++){L.index>P?(K=L,L=null):K=L.sibling;var F=d(h,L,g[P],S);if(F===null){L===null&&(L=K);break}e&&L&&F.alternate===null&&t(h,L),p=i(F,p,P),T===null?C=F:T.sibling=F,T=F,L=K}if(P===g.length)return n(h,L),Q&&pn(h,P),C;if(L===null){for(;P<g.length;P++)L=f(h,g[P],S),L!==null&&(p=i(L,p,P),T===null?C=L:T.sibling=L,T=L);return Q&&pn(h,P),C}for(L=r(h,L);P<g.length;P++)K=m(L,h,P,g[P],S),K!==null&&(e&&K.alternate!==null&&L.delete(K.key===null?P:K.key),p=i(K,p,P),T===null?C=K:T.sibling=K,T=K);return e&&L.forEach(function(tt){return t(h,tt)}),Q&&pn(h,P),C}function v(h,p,g,S){var C=Fr(g);if(typeof C!="function")throw Error(_(150));if(g=C.call(g),g==null)throw Error(_(151));for(var T=C=null,L=p,P=p=0,K=null,F=g.next();L!==null&&!F.done;P++,F=g.next()){L.index>P?(K=L,L=null):K=L.sibling;var tt=d(h,L,F.value,S);if(tt===null){L===null&&(L=K);break}e&&L&&tt.alternate===null&&t(h,L),p=i(tt,p,P),T===null?C=tt:T.sibling=tt,T=tt,L=K}if(F.done)return n(h,L),Q&&pn(h,P),C;if(L===null){for(;!F.done;P++,F=g.next())F=f(h,F.value,S),F!==null&&(p=i(F,p,P),T===null?C=F:T.sibling=F,T=F);return Q&&pn(h,P),C}for(L=r(h,L);!F.done;P++,F=g.next())F=m(L,h,P,F.value,S),F!==null&&(e&&F.alternate!==null&&L.delete(F.key===null?P:F.key),p=i(F,p,P),T===null?C=F:T.sibling=F,T=F);return e&&L.forEach(function(Mr){return t(h,Mr)}),Q&&pn(h,P),C}function w(h,p,g,S){if(typeof g=="object"&&g!==null&&g.type===Vn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case $o:e:{for(var C=g.key,T=p;T!==null;){if(T.key===C){if(C=g.type,C===Vn){if(T.tag===7){n(h,T.sibling),p=o(T,g.props.children),p.return=h,h=p;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Ft&&df(C)===T.type){n(h,T.sibling),p=o(T,g.props),p.ref=Ur(h,T,g),p.return=h,h=p;break e}n(h,T);break}else t(h,T);T=T.sibling}g.type===Vn?(p=Sn(g.props.children,h.mode,S,g.key),p.return=h,h=p):(S=Ti(g.type,g.key,g.props,null,h.mode,S),S.ref=Ur(h,p,g),S.return=h,h=S)}return s(h);case Gn:e:{for(T=g.key;p!==null;){if(p.key===T)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=wa(g,h.mode,S),p.return=h,h=p}return s(h);case Ft:return T=g._init,w(h,p,T(g._payload),S)}if(Zr(g))return y(h,p,g,S);if(Fr(g))return v(h,p,g,S);Jo(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=_a(g,h.mode,S),p.return=h,h=p),s(h)):n(h,p)}return w}var gr=Sh(!0),_h=Sh(!1),Gi=on(null),Vi=null,Jn=null,Lu=null;function Nu(){Lu=Jn=Vi=null}function Pu(e){var t=Gi.current;Y(Gi),e._currentValue=t}function ul(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function sr(e,t){Vi=e,Lu=Jn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(be=!0),e.firstContext=null)}function Ke(e){var t=e._currentValue;if(Lu!==e)if(e={context:e,memoizedValue:t,next:null},Jn===null){if(Vi===null)throw Error(_(308));Jn=e,Vi.dependencies={lanes:0,firstContext:e}}else Jn=Jn.next=e;return t}var gn=null;function bu(e){gn===null?gn=[e]:gn.push(e)}function wh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,bu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Pt(e,r)}function Pt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Dt=!1;function Ou(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function xh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Pt(e,n)}return o=r.interleaved,o===null?(t.next=t,bu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Pt(e,n)}function Ei(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,yu(e,n)}}function pf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wi(e,t,n,r){var o=e.updateQueue;Dt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var f=o.baseState;s=0,c=u=l=null,a=i;do{var d=a.lane,m=a.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,v=a;switch(d=t,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(m,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(m,f,d):y,d==null)break e;f=ee({},f,d);break e;case 2:Dt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else m={eventTime:m,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=m,l=f):c=c.next=m,s|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(c===null&&(l=f),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Tn|=s,e.lanes=s,e.memoizedState=f}}function hf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(_(191,o));o.call(r)}}}var bo={},mt=on(bo),vo=on(bo),Eo=on(bo);function yn(e){if(e===bo)throw Error(_(174));return e}function Mu(e,t){switch(W(Eo,t),W(vo,e),W(mt,bo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:za(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=za(t,e)}Y(mt),W(mt,t)}function yr(){Y(mt),Y(vo),Y(Eo)}function Ch(e){yn(Eo.current);var t=yn(mt.current),n=za(t,e.type);t!==n&&(W(vo,e),W(mt,n))}function Ru(e){vo.current===e&&(Y(mt),Y(vo))}var q=on(0);function Zi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ma=[];function Au(){for(var e=0;e<ma.length;e++)ma[e]._workInProgressVersionPrimary=null;ma.length=0}var Si=Mt.ReactCurrentDispatcher,ga=Mt.ReactCurrentBatchConfig,Cn=0,J=null,se=null,ue=null,Xi=!1,no=!1,So=0,k1=0;function me(){throw Error(_(321))}function Fu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ut(e[n],t[n]))return!1;return!0}function Du(e,t,n,r,o,i){if(Cn=i,J=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Si.current=e===null||e.memoizedState===null?P1:b1,e=n(r,o),no){i=0;do{if(no=!1,So=0,25<=i)throw Error(_(301));i+=1,ue=se=null,t.updateQueue=null,Si.current=O1,e=n(r,o)}while(no)}if(Si.current=Yi,t=se!==null&&se.next!==null,Cn=0,ue=se=J=null,Xi=!1,t)throw Error(_(300));return e}function ju(){var e=So!==0;return So=0,e}function ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ue===null?J.memoizedState=ue=e:ue=ue.next=e,ue}function qe(){if(se===null){var e=J.alternate;e=e!==null?e.memoizedState:null}else e=se.next;var t=ue===null?J.memoizedState:ue.next;if(t!==null)ue=t,se=e;else{if(e===null)throw Error(_(310));se=e,e={memoizedState:se.memoizedState,baseState:se.baseState,baseQueue:se.baseQueue,queue:se.queue,next:null},ue===null?J.memoizedState=ue=e:ue=ue.next=e}return ue}function _o(e,t){return typeof t=="function"?t(e):t}function ya(e){var t=qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=se,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var c=u.lane;if((Cn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,J.lanes|=c,Tn|=c}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,ut(r,t.memoizedState)||(be=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,J.lanes|=i,Tn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function va(e){var t=qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);ut(i,t.memoizedState)||(be=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Th(){}function kh(e,t){var n=J,r=qe(),o=t(),i=!ut(r.memoizedState,o);if(i&&(r.memoizedState=o,be=!0),r=r.queue,Hu(Nh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ue!==null&&ue.memoizedState.tag&1){if(n.flags|=2048,wo(9,Lh.bind(null,n,r,o,t),void 0,null),ce===null)throw Error(_(349));Cn&30||Ih(n,t,o)}return o}function Ih(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=J.updateQueue,t===null?(t={lastEffect:null,stores:null},J.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Lh(e,t,n,r){t.value=n,t.getSnapshot=r,Ph(t)&&bh(e)}function Nh(e,t,n){return n(function(){Ph(t)&&bh(e)})}function Ph(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ut(e,n)}catch{return!0}}function bh(e){var t=Pt(e,1);t!==null&&at(t,e,1,-1)}function mf(e){var t=ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:_o,lastRenderedState:e},t.queue=e,e=e.dispatch=N1.bind(null,J,e),[t.memoizedState,e]}function wo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=J.updateQueue,t===null?(t={lastEffect:null,stores:null},J.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Oh(){return qe().memoizedState}function _i(e,t,n,r){var o=ft();J.flags|=e,o.memoizedState=wo(1|t,n,void 0,r===void 0?null:r)}function vs(e,t,n,r){var o=qe();r=r===void 0?null:r;var i=void 0;if(se!==null){var s=se.memoizedState;if(i=s.destroy,r!==null&&Fu(r,s.deps)){o.memoizedState=wo(t,n,i,r);return}}J.flags|=e,o.memoizedState=wo(1|t,n,i,r)}function gf(e,t){return _i(8390656,8,e,t)}function Hu(e,t){return vs(2048,8,e,t)}function Mh(e,t){return vs(4,2,e,t)}function Rh(e,t){return vs(4,4,e,t)}function Ah(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fh(e,t,n){return n=n!=null?n.concat([e]):null,vs(4,4,Ah.bind(null,t,e),n)}function Bu(){}function Dh(e,t){var n=qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Fu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function jh(e,t){var n=qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Fu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Hh(e,t,n){return Cn&21?(ut(n,t)||(n=Gp(),J.lanes|=n,Tn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,be=!0),e.memoizedState=n)}function I1(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=ga.transition;ga.transition={};try{e(!1),t()}finally{U=n,ga.transition=r}}function Bh(){return qe().memoizedState}function L1(e,t,n){var r=Qt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Uh(e))$h(t,n);else if(n=wh(e,t,n,r),n!==null){var o=ke();at(n,e,r,o),zh(n,t,r)}}function N1(e,t,n){var r=Qt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uh(e))$h(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,ut(a,s)){var l=t.interleaved;l===null?(o.next=o,bu(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=wh(e,t,o,r),n!==null&&(o=ke(),at(n,e,r,o),zh(n,t,r))}}function Uh(e){var t=e.alternate;return e===J||t!==null&&t===J}function $h(e,t){no=Xi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function zh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,yu(e,n)}}var Yi={readContext:Ke,useCallback:me,useContext:me,useEffect:me,useImperativeHandle:me,useInsertionEffect:me,useLayoutEffect:me,useMemo:me,useReducer:me,useRef:me,useState:me,useDebugValue:me,useDeferredValue:me,useTransition:me,useMutableSource:me,useSyncExternalStore:me,useId:me,unstable_isNewReconciler:!1},P1={readContext:Ke,useCallback:function(e,t){return ft().memoizedState=[e,t===void 0?null:t],e},useContext:Ke,useEffect:gf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,_i(4194308,4,Ah.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _i(4194308,4,e,t)},useInsertionEffect:function(e,t){return _i(4,2,e,t)},useMemo:function(e,t){var n=ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=L1.bind(null,J,e),[r.memoizedState,e]},useRef:function(e){var t=ft();return e={current:e},t.memoizedState=e},useState:mf,useDebugValue:Bu,useDeferredValue:function(e){return ft().memoizedState=e},useTransition:function(){var e=mf(!1),t=e[0];return e=I1.bind(null,e[1]),ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=J,o=ft();if(Q){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),ce===null)throw Error(_(349));Cn&30||Ih(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,gf(Nh.bind(null,r,i,e),[e]),r.flags|=2048,wo(9,Lh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=ft(),t=ce.identifierPrefix;if(Q){var n=Ct,r=xt;n=(r&~(1<<32-st(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=So++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=k1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},b1={readContext:Ke,useCallback:Dh,useContext:Ke,useEffect:Hu,useImperativeHandle:Fh,useInsertionEffect:Mh,useLayoutEffect:Rh,useMemo:jh,useReducer:ya,useRef:Oh,useState:function(){return ya(_o)},useDebugValue:Bu,useDeferredValue:function(e){var t=qe();return Hh(t,se.memoizedState,e)},useTransition:function(){var e=ya(_o)[0],t=qe().memoizedState;return[e,t]},useMutableSource:Th,useSyncExternalStore:kh,useId:Bh,unstable_isNewReconciler:!1},O1={readContext:Ke,useCallback:Dh,useContext:Ke,useEffect:Hu,useImperativeHandle:Fh,useInsertionEffect:Mh,useLayoutEffect:Rh,useMemo:jh,useReducer:va,useRef:Oh,useState:function(){return va(_o)},useDebugValue:Bu,useDeferredValue:function(e){var t=qe();return se===null?t.memoizedState=e:Hh(t,se.memoizedState,e)},useTransition:function(){var e=va(_o)[0],t=qe().memoizedState;return[e,t]},useMutableSource:Th,useSyncExternalStore:kh,useId:Bh,unstable_isNewReconciler:!1};function rt(e,t){if(e&&e.defaultProps){t=ee({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function cl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ee({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Es={isMounted:function(e){return(e=e._reactInternals)?An(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ke(),o=Qt(e),i=Tt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Xt(e,i,o),t!==null&&(at(t,e,o,r),Ei(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ke(),o=Qt(e),i=Tt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Xt(e,i,o),t!==null&&(at(t,e,o,r),Ei(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ke(),r=Qt(e),o=Tt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Xt(e,o,r),t!==null&&(at(t,e,r,n),Ei(t,e,r))}};function yf(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!ho(n,r)||!ho(o,i):!0}function Gh(e,t,n){var r=!1,o=en,i=t.contextType;return typeof i=="object"&&i!==null?i=Ke(i):(o=Me(t)?wn:Ee.current,r=t.contextTypes,i=(r=r!=null)?hr(e,o):en),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Es,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function vf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Es.enqueueReplaceState(t,t.state,null)}function fl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Ou(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Ke(i):(i=Me(t)?wn:Ee.current,o.context=hr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(cl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Es.enqueueReplaceState(o,o.state,null),Wi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function vr(e,t){try{var n="",r=t;do n+=sy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ea(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function dl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var M1=typeof WeakMap=="function"?WeakMap:Map;function Vh(e,t,n){n=Tt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ki||(Ki=!0,wl=r),dl(e,t)},n}function Wh(e,t,n){n=Tt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){dl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){dl(e,t),typeof r!="function"&&(Yt===null?Yt=new Set([this]):Yt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Ef(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new M1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Z1.bind(null,e,t,n),t.then(e,e))}function Sf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function _f(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Tt(-1,1),t.tag=2,Xt(n,t,1))),n.lanes|=1),e)}var R1=Mt.ReactCurrentOwner,be=!1;function we(e,t,n,r){t.child=e===null?_h(t,null,n,r):gr(t,e.child,n,r)}function wf(e,t,n,r,o){n=n.render;var i=t.ref;return sr(t,o),r=Du(e,t,n,r,i,o),n=ju(),e!==null&&!be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,bt(e,t,o)):(Q&&n&&Tu(t),t.flags|=1,we(e,t,r,o),t.child)}function xf(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Xu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Zh(e,t,i,r,o)):(e=Ti(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:ho,n(s,r)&&e.ref===t.ref)return bt(e,t,o)}return t.flags|=1,e=Kt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Zh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ho(i,r)&&e.ref===t.ref)if(be=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(be=!0);else return t.lanes=e.lanes,bt(e,t,o)}return pl(e,t,n,r,o)}function Xh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(tr,Ae),Ae|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(tr,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,W(tr,Ae),Ae|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,W(tr,Ae),Ae|=r;return we(e,t,o,n),t.child}function Yh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function pl(e,t,n,r,o){var i=Me(n)?wn:Ee.current;return i=hr(t,i),sr(t,o),n=Du(e,t,n,r,i,o),r=ju(),e!==null&&!be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,bt(e,t,o)):(Q&&r&&Tu(t),t.flags|=1,we(e,t,n,o),t.child)}function Cf(e,t,n,r,o){if(Me(n)){var i=!0;Ui(t)}else i=!1;if(sr(t,o),t.stateNode===null)wi(e,t),Gh(t,n,r),fl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ke(u):(u=Me(n)?wn:Ee.current,u=hr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&vf(t,s,r,u),Dt=!1;var d=t.memoizedState;s.state=d,Wi(t,r,s,o),l=t.memoizedState,a!==r||d!==l||Oe.current||Dt?(typeof c=="function"&&(cl(t,n,c,r),l=t.memoizedState),(a=Dt||yf(t,n,a,r,d,l,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,xh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:rt(t.type,a),s.props=u,f=t.pendingProps,d=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Ke(l):(l=Me(n)?wn:Ee.current,l=hr(t,l));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||d!==l)&&vf(t,s,r,l),Dt=!1,d=t.memoizedState,s.state=d,Wi(t,r,s,o);var y=t.memoizedState;a!==f||d!==y||Oe.current||Dt?(typeof m=="function"&&(cl(t,n,m,r),y=t.memoizedState),(u=Dt||yf(t,n,u,r,d,y,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return hl(e,t,n,r,i,o)}function hl(e,t,n,r,o,i){Yh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&uf(t,n,!1),bt(e,t,i);r=t.stateNode,R1.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=gr(t,e.child,null,i),t.child=gr(t,null,a,i)):we(e,t,a,i),t.memoizedState=r.state,o&&uf(t,n,!0),t.child}function Qh(e){var t=e.stateNode;t.pendingContext?lf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&lf(e,t.context,!1),Mu(e,t.containerInfo)}function Tf(e,t,n,r,o){return mr(),Iu(o),t.flags|=256,we(e,t,n,r),t.child}var ml={dehydrated:null,treeContext:null,retryLane:0};function gl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Kh(e,t,n){var r=t.pendingProps,o=q.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),W(q,o&1),e===null)return ll(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=ws(s,r,0,null),e=Sn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=gl(n),t.memoizedState=ml,e):Uu(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return A1(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Kt(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Kt(a,i):(i=Sn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?gl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=ml,r}return i=e.child,e=i.sibling,r=Kt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Uu(e,t){return t=ws({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ei(e,t,n,r){return r!==null&&Iu(r),gr(t,e.child,null,n),e=Uu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function A1(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ea(Error(_(422))),ei(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ws({mode:"visible",children:r.children},o,0,null),i=Sn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&gr(t,e.child,null,s),t.child.memoizedState=gl(s),t.memoizedState=ml,i);if(!(t.mode&1))return ei(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(_(419)),r=Ea(i,r,void 0),ei(e,t,s,r)}if(a=(s&e.childLanes)!==0,be||a){if(r=ce,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Pt(e,o),at(r,e,o,-1))}return Zu(),r=Ea(Error(_(421))),ei(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=X1.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Fe=Zt(o.nextSibling),He=t,Q=!0,it=null,e!==null&&(We[Ze++]=xt,We[Ze++]=Ct,We[Ze++]=xn,xt=e.id,Ct=e.overflow,xn=t),t=Uu(t,r.children),t.flags|=4096,t)}function kf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ul(e.return,t,n)}function Sa(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function qh(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(we(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&kf(e,n,t);else if(e.tag===19)kf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(q,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Zi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Sa(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Zi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Sa(t,!0,n,null,i);break;case"together":Sa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function wi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function bt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Kt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Kt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function F1(e,t,n){switch(t.tag){case 3:Qh(t),mr();break;case 5:Ch(t);break;case 1:Me(t.type)&&Ui(t);break;case 4:Mu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;W(Gi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Kh(e,t,n):(W(q,q.current&1),e=bt(e,t,n),e!==null?e.sibling:null);W(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),W(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Xh(e,t,n)}return bt(e,t,n)}var Jh,yl,e0,t0;Jh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};yl=function(){};e0=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,yn(mt.current);var i=null;switch(n){case"input":o=Ha(e,o),r=Ha(e,r),i=[];break;case"select":o=ee({},o,{value:void 0}),r=ee({},r,{value:void 0}),i=[];break;case"textarea":o=$a(e,o),r=$a(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Hi)}Ga(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(so.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(so.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Z("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};t0=function(e,t,n,r){n!==r&&(t.flags|=4)};function $r(e,t){if(!Q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ge(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function D1(e,t,n){var r=t.pendingProps;switch(ku(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ge(t),null;case 1:return Me(t.type)&&Bi(),ge(t),null;case 3:return r=t.stateNode,yr(),Y(Oe),Y(Ee),Au(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(qo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,it!==null&&(Tl(it),it=null))),yl(e,t),ge(t),null;case 5:Ru(t);var o=yn(Eo.current);if(n=t.type,e!==null&&t.stateNode!=null)e0(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return ge(t),null}if(e=yn(mt.current),qo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[dt]=t,r[yo]=i,e=(t.mode&1)!==0,n){case"dialog":Z("cancel",r),Z("close",r);break;case"iframe":case"object":case"embed":Z("load",r);break;case"video":case"audio":for(o=0;o<Yr.length;o++)Z(Yr[o],r);break;case"source":Z("error",r);break;case"img":case"image":case"link":Z("error",r),Z("load",r);break;case"details":Z("toggle",r);break;case"input":Rc(r,i),Z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Z("invalid",r);break;case"textarea":Fc(r,i),Z("invalid",r)}Ga(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Ko(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Ko(r.textContent,a,e),o=["children",""+a]):so.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&Z("scroll",r)}switch(n){case"input":zo(r),Ac(r,i,!0);break;case"textarea":zo(r),Dc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Hi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Np(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[dt]=t,e[yo]=r,Jh(e,t,!1,!1),t.stateNode=e;e:{switch(s=Va(n,r),n){case"dialog":Z("cancel",e),Z("close",e),o=r;break;case"iframe":case"object":case"embed":Z("load",e),o=r;break;case"video":case"audio":for(o=0;o<Yr.length;o++)Z(Yr[o],e);o=r;break;case"source":Z("error",e),o=r;break;case"img":case"image":case"link":Z("error",e),Z("load",e),o=r;break;case"details":Z("toggle",e),o=r;break;case"input":Rc(e,r),o=Ha(e,r),Z("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ee({},r,{value:void 0}),Z("invalid",e);break;case"textarea":Fc(e,r),o=$a(e,r),Z("invalid",e);break;default:o=r}Ga(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Op(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Pp(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ao(e,l):typeof l=="number"&&ao(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(so.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Z("scroll",e):l!=null&&fu(e,i,l,s))}switch(n){case"input":zo(e),Ac(e,r,!1);break;case"textarea":zo(e),Dc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Jt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?nr(e,!!r.multiple,i,!1):r.defaultValue!=null&&nr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Hi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ge(t),null;case 6:if(e&&t.stateNode!=null)t0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=yn(Eo.current),yn(mt.current),qo(t)){if(r=t.stateNode,n=t.memoizedProps,r[dt]=t,(i=r.nodeValue!==n)&&(e=He,e!==null))switch(e.tag){case 3:Ko(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ko(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[dt]=t,t.stateNode=r}return ge(t),null;case 13:if(Y(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Q&&Fe!==null&&t.mode&1&&!(t.flags&128))Eh(),mr(),t.flags|=98560,i=!1;else if(i=qo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(_(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(_(317));i[dt]=t}else mr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ge(t),i=!1}else it!==null&&(Tl(it),it=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?ae===0&&(ae=3):Zu())),t.updateQueue!==null&&(t.flags|=4),ge(t),null);case 4:return yr(),yl(e,t),e===null&&mo(t.stateNode.containerInfo),ge(t),null;case 10:return Pu(t.type._context),ge(t),null;case 17:return Me(t.type)&&Bi(),ge(t),null;case 19:if(Y(q),i=t.memoizedState,i===null)return ge(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)$r(i,!1);else{if(ae!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Zi(e),s!==null){for(t.flags|=128,$r(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(q,q.current&1|2),t.child}e=e.sibling}i.tail!==null&&re()>Er&&(t.flags|=128,r=!0,$r(i,!1),t.lanes=4194304)}else{if(!r)if(e=Zi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),$r(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!Q)return ge(t),null}else 2*re()-i.renderingStartTime>Er&&n!==1073741824&&(t.flags|=128,r=!0,$r(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=re(),t.sibling=null,n=q.current,W(q,r?n&1|2:n&1),t):(ge(t),null);case 22:case 23:return Wu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(ge(t),t.subtreeFlags&6&&(t.flags|=8192)):ge(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function j1(e,t){switch(ku(t),t.tag){case 1:return Me(t.type)&&Bi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return yr(),Y(Oe),Y(Ee),Au(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ru(t),null;case 13:if(Y(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));mr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Y(q),null;case 4:return yr(),null;case 10:return Pu(t.type._context),null;case 22:case 23:return Wu(),null;case 24:return null;default:return null}}var ti=!1,ye=!1,H1=typeof WeakSet=="function"?WeakSet:Set,k=null;function er(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){te(e,t,r)}else n.current=null}function vl(e,t,n){try{n()}catch(r){te(e,t,r)}}var If=!1;function B1(e,t){if(tl=Fi,e=sh(),Cu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var m;f!==n||o!==0&&f.nodeType!==3||(a=s+o),f!==i||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===e)break t;if(d===n&&++u===o&&(a=s),d===i&&++c===r&&(l=s),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(nl={focusedElem:e,selectionRange:n},Fi=!1,k=t;k!==null;)if(t=k,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,k=e;else for(;k!==null;){t=k;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,w=y.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:rt(t.type,v),w);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(S){te(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,k=e;break}k=t.return}return y=If,If=!1,y}function ro(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&vl(t,n,i)}o=o.next}while(o!==r)}}function Ss(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function El(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function n0(e){var t=e.alternate;t!==null&&(e.alternate=null,n0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[dt],delete t[yo],delete t[il],delete t[w1],delete t[x1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function r0(e){return e.tag===5||e.tag===3||e.tag===4}function Lf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||r0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Sl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Hi));else if(r!==4&&(e=e.child,e!==null))for(Sl(e,t,n),e=e.sibling;e!==null;)Sl(e,t,n),e=e.sibling}function _l(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(_l(e,t,n),e=e.sibling;e!==null;)_l(e,t,n),e=e.sibling}var de=null,ot=!1;function Rt(e,t,n){for(n=n.child;n!==null;)o0(e,t,n),n=n.sibling}function o0(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(ds,n)}catch{}switch(n.tag){case 5:ye||er(n,t);case 6:var r=de,o=ot;de=null,Rt(e,t,n),de=r,ot=o,de!==null&&(ot?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(ot?(e=de,n=n.stateNode,e.nodeType===8?pa(e.parentNode,n):e.nodeType===1&&pa(e,n),fo(e)):pa(de,n.stateNode));break;case 4:r=de,o=ot,de=n.stateNode.containerInfo,ot=!0,Rt(e,t,n),de=r,ot=o;break;case 0:case 11:case 14:case 15:if(!ye&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&vl(n,t,s),o=o.next}while(o!==r)}Rt(e,t,n);break;case 1:if(!ye&&(er(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){te(n,t,a)}Rt(e,t,n);break;case 21:Rt(e,t,n);break;case 22:n.mode&1?(ye=(r=ye)||n.memoizedState!==null,Rt(e,t,n),ye=r):Rt(e,t,n);break;default:Rt(e,t,n)}}function Nf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new H1),t.forEach(function(r){var o=Y1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function nt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:de=a.stateNode,ot=!1;break e;case 3:de=a.stateNode.containerInfo,ot=!0;break e;case 4:de=a.stateNode.containerInfo,ot=!0;break e}a=a.return}if(de===null)throw Error(_(160));o0(i,s,o),de=null,ot=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){te(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)i0(t,e),t=t.sibling}function i0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(nt(t,e),ct(e),r&4){try{ro(3,e,e.return),Ss(3,e)}catch(v){te(e,e.return,v)}try{ro(5,e,e.return)}catch(v){te(e,e.return,v)}}break;case 1:nt(t,e),ct(e),r&512&&n!==null&&er(n,n.return);break;case 5:if(nt(t,e),ct(e),r&512&&n!==null&&er(n,n.return),e.flags&32){var o=e.stateNode;try{ao(o,"")}catch(v){te(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Ip(o,i),Va(a,s);var u=Va(a,i);for(s=0;s<l.length;s+=2){var c=l[s],f=l[s+1];c==="style"?Op(o,f):c==="dangerouslySetInnerHTML"?Pp(o,f):c==="children"?ao(o,f):fu(o,c,f,u)}switch(a){case"input":Ba(o,i);break;case"textarea":Lp(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?nr(o,!!i.multiple,m,!1):d!==!!i.multiple&&(i.defaultValue!=null?nr(o,!!i.multiple,i.defaultValue,!0):nr(o,!!i.multiple,i.multiple?[]:"",!1))}o[yo]=i}catch(v){te(e,e.return,v)}}break;case 6:if(nt(t,e),ct(e),r&4){if(e.stateNode===null)throw Error(_(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){te(e,e.return,v)}}break;case 3:if(nt(t,e),ct(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{fo(t.containerInfo)}catch(v){te(e,e.return,v)}break;case 4:nt(t,e),ct(e);break;case 13:nt(t,e),ct(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Gu=re())),r&4&&Nf(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(ye=(u=ye)||c,nt(t,e),ye=u):nt(t,e),ct(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(k=e,c=e.child;c!==null;){for(f=k=c;k!==null;){switch(d=k,m=d.child,d.tag){case 0:case 11:case 14:case 15:ro(4,d,d.return);break;case 1:er(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){te(r,n,v)}}break;case 5:er(d,d.return);break;case 22:if(d.memoizedState!==null){bf(f);continue}}m!==null?(m.return=d,k=m):bf(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=bp("display",s))}catch(v){te(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){te(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:nt(t,e),ct(e),r&4&&Nf(e);break;case 21:break;default:nt(t,e),ct(e)}}function ct(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(r0(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ao(o,""),r.flags&=-33);var i=Lf(e);_l(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Lf(e);Sl(e,a,s);break;default:throw Error(_(161))}}catch(l){te(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function U1(e,t,n){k=e,s0(e)}function s0(e,t,n){for(var r=(e.mode&1)!==0;k!==null;){var o=k,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||ti;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||ye;a=ti;var u=ye;if(ti=s,(ye=l)&&!u)for(k=o;k!==null;)s=k,l=s.child,s.tag===22&&s.memoizedState!==null?Of(o):l!==null?(l.return=s,k=l):Of(o);for(;i!==null;)k=i,s0(i),i=i.sibling;k=o,ti=a,ye=u}Pf(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,k=i):Pf(e)}}function Pf(e){for(;k!==null;){var t=k;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ye||Ss(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ye)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:rt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&hf(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}hf(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&fo(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}ye||t.flags&512&&El(t)}catch(d){te(t,t.return,d)}}if(t===e){k=null;break}if(n=t.sibling,n!==null){n.return=t.return,k=n;break}k=t.return}}function bf(e){for(;k!==null;){var t=k;if(t===e){k=null;break}var n=t.sibling;if(n!==null){n.return=t.return,k=n;break}k=t.return}}function Of(e){for(;k!==null;){var t=k;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ss(4,t)}catch(l){te(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){te(t,o,l)}}var i=t.return;try{El(t)}catch(l){te(t,i,l)}break;case 5:var s=t.return;try{El(t)}catch(l){te(t,s,l)}}}catch(l){te(t,t.return,l)}if(t===e){k=null;break}var a=t.sibling;if(a!==null){a.return=t.return,k=a;break}k=t.return}}var $1=Math.ceil,Qi=Mt.ReactCurrentDispatcher,$u=Mt.ReactCurrentOwner,Qe=Mt.ReactCurrentBatchConfig,H=0,ce=null,oe=null,pe=0,Ae=0,tr=on(0),ae=0,xo=null,Tn=0,_s=0,zu=0,oo=null,Pe=null,Gu=0,Er=1/0,Et=null,Ki=!1,wl=null,Yt=null,ni=!1,zt=null,qi=0,io=0,xl=null,xi=-1,Ci=0;function ke(){return H&6?re():xi!==-1?xi:xi=re()}function Qt(e){return e.mode&1?H&2&&pe!==0?pe&-pe:T1.transition!==null?(Ci===0&&(Ci=Gp()),Ci):(e=U,e!==0||(e=window.event,e=e===void 0?16:Kp(e.type)),e):1}function at(e,t,n,r){if(50<io)throw io=0,xl=null,Error(_(185));Lo(e,n,r),(!(H&2)||e!==ce)&&(e===ce&&(!(H&2)&&(_s|=n),ae===4&&Ut(e,pe)),Re(e,r),n===1&&H===0&&!(t.mode&1)&&(Er=re()+500,ys&&sn()))}function Re(e,t){var n=e.callbackNode;Ty(e,t);var r=Ai(e,e===ce?pe:0);if(r===0)n!==null&&Bc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Bc(n),t===1)e.tag===0?C1(Mf.bind(null,e)):gh(Mf.bind(null,e)),S1(function(){!(H&6)&&sn()}),n=null;else{switch(Vp(r)){case 1:n=gu;break;case 4:n=$p;break;case 16:n=Ri;break;case 536870912:n=zp;break;default:n=Ri}n=h0(n,a0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function a0(e,t){if(xi=-1,Ci=0,H&6)throw Error(_(327));var n=e.callbackNode;if(ar()&&e.callbackNode!==n)return null;var r=Ai(e,e===ce?pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ji(e,r);else{t=r;var o=H;H|=2;var i=u0();(ce!==e||pe!==t)&&(Et=null,Er=re()+500,En(e,t));do try{V1();break}catch(a){l0(e,a)}while(!0);Nu(),Qi.current=i,H=o,oe!==null?t=0:(ce=null,pe=0,t=ae)}if(t!==0){if(t===2&&(o=Qa(e),o!==0&&(r=o,t=Cl(e,o))),t===1)throw n=xo,En(e,0),Ut(e,r),Re(e,re()),n;if(t===6)Ut(e,r);else{if(o=e.current.alternate,!(r&30)&&!z1(o)&&(t=Ji(e,r),t===2&&(i=Qa(e),i!==0&&(r=i,t=Cl(e,i))),t===1))throw n=xo,En(e,0),Ut(e,r),Re(e,re()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:hn(e,Pe,Et);break;case 3:if(Ut(e,r),(r&130023424)===r&&(t=Gu+500-re(),10<t)){if(Ai(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){ke(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ol(hn.bind(null,e,Pe,Et),t);break}hn(e,Pe,Et);break;case 4:if(Ut(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-st(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=re()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*$1(r/1960))-r,10<r){e.timeoutHandle=ol(hn.bind(null,e,Pe,Et),r);break}hn(e,Pe,Et);break;case 5:hn(e,Pe,Et);break;default:throw Error(_(329))}}}return Re(e,re()),e.callbackNode===n?a0.bind(null,e):null}function Cl(e,t){var n=oo;return e.current.memoizedState.isDehydrated&&(En(e,t).flags|=256),e=Ji(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&Tl(t)),e}function Tl(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function z1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!ut(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ut(e,t){for(t&=~zu,t&=~_s,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function Mf(e){if(H&6)throw Error(_(327));ar();var t=Ai(e,0);if(!(t&1))return Re(e,re()),null;var n=Ji(e,t);if(e.tag!==0&&n===2){var r=Qa(e);r!==0&&(t=r,n=Cl(e,r))}if(n===1)throw n=xo,En(e,0),Ut(e,t),Re(e,re()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,hn(e,Pe,Et),Re(e,re()),null}function Vu(e,t){var n=H;H|=1;try{return e(t)}finally{H=n,H===0&&(Er=re()+500,ys&&sn())}}function kn(e){zt!==null&&zt.tag===0&&!(H&6)&&ar();var t=H;H|=1;var n=Qe.transition,r=U;try{if(Qe.transition=null,U=1,e)return e()}finally{U=r,Qe.transition=n,H=t,!(H&6)&&sn()}}function Wu(){Ae=tr.current,Y(tr)}function En(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,E1(n)),oe!==null)for(n=oe.return;n!==null;){var r=n;switch(ku(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Bi();break;case 3:yr(),Y(Oe),Y(Ee),Au();break;case 5:Ru(r);break;case 4:yr();break;case 13:Y(q);break;case 19:Y(q);break;case 10:Pu(r.type._context);break;case 22:case 23:Wu()}n=n.return}if(ce=e,oe=e=Kt(e.current,null),pe=Ae=t,ae=0,xo=null,zu=_s=Tn=0,Pe=oo=null,gn!==null){for(t=0;t<gn.length;t++)if(n=gn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}gn=null}return e}function l0(e,t){do{var n=oe;try{if(Nu(),Si.current=Yi,Xi){for(var r=J.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Xi=!1}if(Cn=0,ue=se=J=null,no=!1,So=0,$u.current=null,n===null||n.return===null){ae=1,xo=t,oe=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=pe,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=Sf(s);if(m!==null){m.flags&=-257,_f(m,s,a,i,t),m.mode&1&&Ef(i,u,t),t=m,l=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(l),t.updateQueue=v}else y.add(l);break e}else{if(!(t&1)){Ef(i,u,t),Zu();break e}l=Error(_(426))}}else if(Q&&a.mode&1){var w=Sf(s);if(w!==null){!(w.flags&65536)&&(w.flags|=256),_f(w,s,a,i,t),Iu(vr(l,a));break e}}i=l=vr(l,a),ae!==4&&(ae=2),oo===null?oo=[i]:oo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Vh(i,l,t);pf(i,h);break e;case 1:a=l;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Yt===null||!Yt.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=Wh(i,a,t);pf(i,S);break e}}i=i.return}while(i!==null)}f0(n)}catch(C){t=C,oe===n&&n!==null&&(oe=n=n.return);continue}break}while(!0)}function u0(){var e=Qi.current;return Qi.current=Yi,e===null?Yi:e}function Zu(){(ae===0||ae===3||ae===2)&&(ae=4),ce===null||!(Tn&268435455)&&!(_s&268435455)||Ut(ce,pe)}function Ji(e,t){var n=H;H|=2;var r=u0();(ce!==e||pe!==t)&&(Et=null,En(e,t));do try{G1();break}catch(o){l0(e,o)}while(!0);if(Nu(),H=n,Qi.current=r,oe!==null)throw Error(_(261));return ce=null,pe=0,ae}function G1(){for(;oe!==null;)c0(oe)}function V1(){for(;oe!==null&&!gy();)c0(oe)}function c0(e){var t=p0(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?f0(e):oe=t,$u.current=null}function f0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=j1(n,t),n!==null){n.flags&=32767,oe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ae=6,oe=null;return}}else if(n=D1(n,t,Ae),n!==null){oe=n;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);ae===0&&(ae=5)}function hn(e,t,n){var r=U,o=Qe.transition;try{Qe.transition=null,U=1,W1(e,t,n,r)}finally{Qe.transition=o,U=r}return null}function W1(e,t,n,r){do ar();while(zt!==null);if(H&6)throw Error(_(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(ky(e,i),e===ce&&(oe=ce=null,pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ni||(ni=!0,h0(Ri,function(){return ar(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Qe.transition,Qe.transition=null;var s=U;U=1;var a=H;H|=4,$u.current=null,B1(e,n),i0(n,e),d1(nl),Fi=!!tl,nl=tl=null,e.current=n,U1(n),yy(),H=a,U=s,Qe.transition=i}else e.current=n;if(ni&&(ni=!1,zt=e,qi=o),i=e.pendingLanes,i===0&&(Yt=null),Sy(n.stateNode),Re(e,re()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ki)throw Ki=!1,e=wl,wl=null,e;return qi&1&&e.tag!==0&&ar(),i=e.pendingLanes,i&1?e===xl?io++:(io=0,xl=e):io=0,sn(),null}function ar(){if(zt!==null){var e=Vp(qi),t=Qe.transition,n=U;try{if(Qe.transition=null,U=16>e?16:e,zt===null)var r=!1;else{if(e=zt,zt=null,qi=0,H&6)throw Error(_(331));var o=H;for(H|=4,k=e.current;k!==null;){var i=k,s=i.child;if(k.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(k=u;k!==null;){var c=k;switch(c.tag){case 0:case 11:case 15:ro(8,c,i)}var f=c.child;if(f!==null)f.return=c,k=f;else for(;k!==null;){c=k;var d=c.sibling,m=c.return;if(n0(c),c===u){k=null;break}if(d!==null){d.return=m,k=d;break}k=m}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}k=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,k=s;else e:for(;k!==null;){if(i=k,i.flags&2048)switch(i.tag){case 0:case 11:case 15:ro(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,k=h;break e}k=i.return}}var p=e.current;for(k=p;k!==null;){s=k;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,k=g;else e:for(s=p;k!==null;){if(a=k,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ss(9,a)}}catch(C){te(a,a.return,C)}if(a===s){k=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,k=S;break e}k=a.return}}if(H=o,sn(),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(ds,e)}catch{}r=!0}return r}finally{U=n,Qe.transition=t}}return!1}function Rf(e,t,n){t=vr(n,t),t=Vh(e,t,1),e=Xt(e,t,1),t=ke(),e!==null&&(Lo(e,1,t),Re(e,t))}function te(e,t,n){if(e.tag===3)Rf(e,e,n);else for(;t!==null;){if(t.tag===3){Rf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Yt===null||!Yt.has(r))){e=vr(n,e),e=Wh(t,e,1),t=Xt(t,e,1),e=ke(),t!==null&&(Lo(t,1,e),Re(t,e));break}}t=t.return}}function Z1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ke(),e.pingedLanes|=e.suspendedLanes&n,ce===e&&(pe&n)===n&&(ae===4||ae===3&&(pe&130023424)===pe&&500>re()-Gu?En(e,0):zu|=n),Re(e,t)}function d0(e,t){t===0&&(e.mode&1?(t=Wo,Wo<<=1,!(Wo&130023424)&&(Wo=4194304)):t=1);var n=ke();e=Pt(e,t),e!==null&&(Lo(e,t,n),Re(e,n))}function X1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),d0(e,n)}function Y1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),d0(e,n)}var p0;p0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Oe.current)be=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return be=!1,F1(e,t,n);be=!!(e.flags&131072)}else be=!1,Q&&t.flags&1048576&&yh(t,zi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;wi(e,t),e=t.pendingProps;var o=hr(t,Ee.current);sr(t,n),o=Du(null,t,r,e,o,n);var i=ju();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Me(r)?(i=!0,Ui(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Ou(t),o.updater=Es,t.stateNode=o,o._reactInternals=t,fl(t,r,e,n),t=hl(null,t,r,!0,i,n)):(t.tag=0,Q&&i&&Tu(t),we(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(wi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=K1(r),e=rt(r,e),o){case 0:t=pl(null,t,r,e,n);break e;case 1:t=Cf(null,t,r,e,n);break e;case 11:t=wf(null,t,r,e,n);break e;case 14:t=xf(null,t,r,rt(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:rt(r,o),pl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:rt(r,o),Cf(e,t,r,o,n);case 3:e:{if(Qh(t),e===null)throw Error(_(387));r=t.pendingProps,i=t.memoizedState,o=i.element,xh(e,t),Wi(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=vr(Error(_(423)),t),t=Tf(e,t,r,n,o);break e}else if(r!==o){o=vr(Error(_(424)),t),t=Tf(e,t,r,n,o);break e}else for(Fe=Zt(t.stateNode.containerInfo.firstChild),He=t,Q=!0,it=null,n=_h(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(mr(),r===o){t=bt(e,t,n);break e}we(e,t,r,n)}t=t.child}return t;case 5:return Ch(t),e===null&&ll(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,rl(r,o)?s=null:i!==null&&rl(r,i)&&(t.flags|=32),Yh(e,t),we(e,t,s,n),t.child;case 6:return e===null&&ll(t),null;case 13:return Kh(e,t,n);case 4:return Mu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=gr(t,null,r,n):we(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:rt(r,o),wf(e,t,r,o,n);case 7:return we(e,t,t.pendingProps,n),t.child;case 8:return we(e,t,t.pendingProps.children,n),t.child;case 12:return we(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,W(Gi,r._currentValue),r._currentValue=s,i!==null)if(ut(i.value,s)){if(i.children===o.children&&!Oe.current){t=bt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Tt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),ul(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(_(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ul(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}we(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,sr(t,n),o=Ke(o),r=r(o),t.flags|=1,we(e,t,r,n),t.child;case 14:return r=t.type,o=rt(r,t.pendingProps),o=rt(r.type,o),xf(e,t,r,o,n);case 15:return Zh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:rt(r,o),wi(e,t),t.tag=1,Me(r)?(e=!0,Ui(t)):e=!1,sr(t,n),Gh(t,r,o),fl(t,r,o,n),hl(null,t,r,!0,e,n);case 19:return qh(e,t,n);case 22:return Xh(e,t,n)}throw Error(_(156,t.tag))};function h0(e,t){return Up(e,t)}function Q1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ye(e,t,n,r){return new Q1(e,t,n,r)}function Xu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function K1(e){if(typeof e=="function")return Xu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===pu)return 11;if(e===hu)return 14}return 2}function Kt(e,t){var n=e.alternate;return n===null?(n=Ye(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ti(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Xu(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Vn:return Sn(n.children,o,i,t);case du:s=8,o|=8;break;case Aa:return e=Ye(12,n,t,o|2),e.elementType=Aa,e.lanes=i,e;case Fa:return e=Ye(13,n,t,o),e.elementType=Fa,e.lanes=i,e;case Da:return e=Ye(19,n,t,o),e.elementType=Da,e.lanes=i,e;case Cp:return ws(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wp:s=10;break e;case xp:s=9;break e;case pu:s=11;break e;case hu:s=14;break e;case Ft:s=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=Ye(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Sn(e,t,n,r){return e=Ye(7,e,r,t),e.lanes=n,e}function ws(e,t,n,r){return e=Ye(22,e,r,t),e.elementType=Cp,e.lanes=n,e.stateNode={isHidden:!1},e}function _a(e,t,n){return e=Ye(6,e,null,t),e.lanes=n,e}function wa(e,t,n){return t=Ye(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function q1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=na(0),this.expirationTimes=na(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=na(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Yu(e,t,n,r,o,i,s,a,l){return e=new q1(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ye(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ou(i),e}function J1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Gn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function m0(e){if(!e)return en;e=e._reactInternals;e:{if(An(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Me(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(Me(n))return mh(e,n,t)}return t}function g0(e,t,n,r,o,i,s,a,l){return e=Yu(n,r,!0,e,o,i,s,a,l),e.context=m0(null),n=e.current,r=ke(),o=Qt(n),i=Tt(r,o),i.callback=t??null,Xt(n,i,o),e.current.lanes=o,Lo(e,o,r),Re(e,r),e}function xs(e,t,n,r){var o=t.current,i=ke(),s=Qt(o);return n=m0(n),t.context===null?t.context=n:t.pendingContext=n,t=Tt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Xt(o,t,s),e!==null&&(at(e,o,s,i),Ei(e,o,s)),s}function es(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Af(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Qu(e,t){Af(e,t),(e=e.alternate)&&Af(e,t)}function ev(){return null}var y0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}Cs.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));xs(e,t,null,null)};Cs.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;kn(function(){xs(null,e,null,null)}),t[Nt]=null}};function Cs(e){this._internalRoot=e}Cs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Bt.length&&t!==0&&t<Bt[n].priority;n++);Bt.splice(n,0,e),n===0&&Qp(e)}};function qu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ts(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ff(){}function tv(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=es(s);i.call(u)}}var s=g0(t,r,e,0,null,!1,!1,"",Ff);return e._reactRootContainer=s,e[Nt]=s.current,mo(e.nodeType===8?e.parentNode:e),kn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=es(l);a.call(u)}}var l=Yu(e,0,!1,null,null,!1,!1,"",Ff);return e._reactRootContainer=l,e[Nt]=l.current,mo(e.nodeType===8?e.parentNode:e),kn(function(){xs(t,l,n,r)}),l}function ks(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=es(s);a.call(l)}}xs(t,s,e,o)}else s=tv(n,t,e,o,r);return es(s)}Wp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Xr(t.pendingLanes);n!==0&&(yu(t,n|1),Re(t,re()),!(H&6)&&(Er=re()+500,sn()))}break;case 13:kn(function(){var r=Pt(e,1);if(r!==null){var o=ke();at(r,e,1,o)}}),Qu(e,1)}};vu=function(e){if(e.tag===13){var t=Pt(e,134217728);if(t!==null){var n=ke();at(t,e,134217728,n)}Qu(e,134217728)}};Zp=function(e){if(e.tag===13){var t=Qt(e),n=Pt(e,t);if(n!==null){var r=ke();at(n,e,t,r)}Qu(e,t)}};Xp=function(){return U};Yp=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};Za=function(e,t,n){switch(t){case"input":if(Ba(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=gs(r);if(!o)throw Error(_(90));kp(r),Ba(r,o)}}}break;case"textarea":Lp(e,n);break;case"select":t=n.value,t!=null&&nr(e,!!n.multiple,t,!1)}};Ap=Vu;Fp=kn;var nv={usingClientEntryPoint:!1,Events:[Po,Yn,gs,Mp,Rp,Vu]},zr={findFiberByHostInstance:mn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},rv={bundleType:zr.bundleType,version:zr.version,rendererPackageName:zr.rendererPackageName,rendererConfig:zr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Mt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Hp(e),e===null?null:e.stateNode},findFiberByHostInstance:zr.findFiberByHostInstance||ev,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ri=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ri.isDisabled&&ri.supportsFiber)try{ds=ri.inject(rv),ht=ri}catch{}}$e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=nv;$e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!qu(t))throw Error(_(200));return J1(e,t,null,n)};$e.createRoot=function(e,t){if(!qu(e))throw Error(_(299));var n=!1,r="",o=y0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Yu(e,1,!1,null,null,n,!1,r,o),e[Nt]=t.current,mo(e.nodeType===8?e.parentNode:e),new Ku(t)};$e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Hp(t),e=e===null?null:e.stateNode,e};$e.flushSync=function(e){return kn(e)};$e.hydrate=function(e,t,n){if(!Ts(t))throw Error(_(200));return ks(null,e,t,!0,n)};$e.hydrateRoot=function(e,t,n){if(!qu(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=y0;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=g0(t,null,e,1,n??null,o,!1,i,s),e[Nt]=t.current,mo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Cs(t)};$e.render=function(e,t,n){if(!Ts(t))throw Error(_(200));return ks(null,e,t,!1,n)};$e.unmountComponentAtNode=function(e){if(!Ts(e))throw Error(_(40));return e._reactRootContainer?(kn(function(){ks(null,null,e,!1,function(){e._reactRootContainer=null,e[Nt]=null})}),!0):!1};$e.unstable_batchedUpdates=Vu;$e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ts(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return ks(e,t,n,!1,r)};$e.version="18.3.1-next-f1338f8080-20240426";function v0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(v0)}catch(e){console.error(e)}}v0(),vp.exports=$e;var ov=vp.exports,Ju,Df=ov;Ju=Df.createRoot,Df.hydrateRoot;const In=process.platform==="darwin",iv=process.platform==="win32";process.platform;process.type;process.type;const sv=In?0:36,av=In?28:36;var kl=function(e,t){return kl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])},kl(e,t)};function Je(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");kl(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}var N=function(){return N=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},N.apply(this,arguments)};function Sr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Te(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}function xe(e,t){var n=t&&t.cache?t.cache:pv,r=t&&t.serializer?t.serializer:dv,o=t&&t.strategy?t.strategy:uv;return o(e,{cache:n,serializer:r})}function lv(e){return e==null||typeof e=="number"||typeof e=="boolean"}function E0(e,t,n,r){var o=lv(r)?r:n(r),i=t.get(o);return typeof i>"u"&&(i=e.call(this,r),t.set(o,i)),i}function S0(e,t,n){var r=Array.prototype.slice.call(arguments,3),o=n(r),i=t.get(o);return typeof i>"u"&&(i=e.apply(this,r),t.set(o,i)),i}function ec(e,t,n,r,o){return n.bind(t,e,r,o)}function uv(e,t){var n=e.length===1?E0:S0;return ec(e,this,n,t.cache.create(),t.serializer)}function cv(e,t){return ec(e,this,S0,t.cache.create(),t.serializer)}function fv(e,t){return ec(e,this,E0,t.cache.create(),t.serializer)}var dv=function(){return JSON.stringify(arguments)};function tc(){this.cache=Object.create(null)}tc.prototype.get=function(e){return this.cache[e]};tc.prototype.set=function(e,t){this.cache[e]=t};var pv={create:function(){return new tc}},Ce={variadic:cv,monadic:fv};function _0(e,t,n){if(n===void 0&&(n=Error),!e)throw new n(t)}xe(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,Te([void 0],t,!1)))},{strategy:Ce.variadic});xe(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,Te([void 0],t,!1)))},{strategy:Ce.variadic});xe(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,Te([void 0],t,!1)))},{strategy:Ce.variadic});xe(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.Locale).bind.apply(e,Te([void 0],t,!1)))},{strategy:Ce.variadic});xe(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.ListFormat).bind.apply(e,Te([void 0],t,!1)))},{strategy:Ce.variadic});var D;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(D||(D={}));var X;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(X||(X={}));var _r;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(_r||(_r={}));function jf(e){return e.type===X.literal}function hv(e){return e.type===X.argument}function w0(e){return e.type===X.number}function x0(e){return e.type===X.date}function C0(e){return e.type===X.time}function T0(e){return e.type===X.select}function k0(e){return e.type===X.plural}function mv(e){return e.type===X.pound}function I0(e){return e.type===X.tag}function L0(e){return!!(e&&typeof e=="object"&&e.type===_r.number)}function Il(e){return!!(e&&typeof e=="object"&&e.type===_r.dateTime)}var N0=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,gv=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function yv(e){var t={};return e.replace(gv,function(n){var r=n.length;switch(n[0]){case"G":t.era=r===4?"long":r===5?"narrow":"short";break;case"y":t.year=r===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=r===4?"long":r===5?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var vv=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Ev(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(vv).filter(function(d){return d.length>0}),n=[],r=0,o=t;r<o.length;r++){var i=o[r],s=i.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var a=s[0],l=s.slice(1),u=0,c=l;u<c.length;u++){var f=c[u];if(f.length===0)throw new Error("Invalid number skeleton")}n.push({stem:a,options:l})}return n}function Sv(e){return e.replace(/^(.*?)-/,"")}var Hf=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,P0=/^(@+)?(\+|#+)?[rs]?$/g,_v=/(\*)(0+)|(#+)(0+)|(0+)/g,b0=/^(0+)$/;function Bf(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(P0,function(n,r,o){return typeof o!="string"?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):o==="+"?t.minimumSignificantDigits=r.length:r[0]==="#"?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+(typeof o=="string"?o.length:0)),""}),t}function O0(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function wv(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var n=e.slice(0,2);if(n==="+!"?(t.signDisplay="always",e=e.slice(2)):n==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!b0.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function Uf(e){var t={},n=O0(e);return n||t}function xv(e){for(var t={},n=0,r=e;n<r.length;n++){var o=r[n];switch(o.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=o.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=Sv(o.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=N(N(N({},t),{notation:"scientific"}),o.options.reduce(function(l,u){return N(N({},l),Uf(u))},{}));continue;case"engineering":t=N(N(N({},t),{notation:"engineering"}),o.options.reduce(function(l,u){return N(N({},l),Uf(u))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(o.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(o.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");o.options[0].replace(_v,function(l,u,c,f,d,m){if(u)t.minimumIntegerDigits=c.length;else{if(f&&d)throw new Error("We currently do not support maximum integer digits");if(m)throw new Error("We currently do not support exact integer digits")}return""});continue}if(b0.test(o.stem)){t.minimumIntegerDigits=o.stem.length;continue}if(Hf.test(o.stem)){if(o.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");o.stem.replace(Hf,function(l,u,c,f,d,m){return c==="*"?t.minimumFractionDigits=u.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:d&&m?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+m.length):(t.minimumFractionDigits=u.length,t.maximumFractionDigits=u.length),""});var i=o.options[0];i==="w"?t=N(N({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=N(N({},t),Bf(i)));continue}if(P0.test(o.stem)){t=N(N({},t),Bf(o.stem));continue}var s=O0(o.stem);s&&(t=N(N({},t),s));var a=wv(o.stem);a&&(t=N(N({},t),a))}return t}var oi={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Cv(e,t){for(var n="",r=0;r<e.length;r++){var o=e.charAt(r);if(o==="j"){for(var i=0;r+1<e.length&&e.charAt(r+1)===o;)i++,r++;var s=1+(i&1),a=i<2?1:3+(i>>1),l="a",u=Tv(t);for((u=="H"||u=="k")&&(a=0);a-- >0;)n+=l;for(;s-- >0;)n=u+n}else o==="J"?n+="H":n+=o}return n}function Tv(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n=e.language,r;n!=="root"&&(r=e.maximize().region);var o=oi[r||""]||oi[n||""]||oi["".concat(n,"-001")]||oi["001"];return o[0]}var xa,kv=new RegExp("^".concat(N0.source,"*")),Iv=new RegExp("".concat(N0.source,"*$"));function j(e,t){return{start:e,end:t}}var Lv=!!String.prototype.startsWith&&"_a".startsWith("a",1),Nv=!!String.fromCodePoint,Pv=!!Object.fromEntries,bv=!!String.prototype.codePointAt,Ov=!!String.prototype.trimStart,Mv=!!String.prototype.trimEnd,Rv=!!Number.isSafeInteger,Av=Rv?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},Ll=!0;try{var Fv=R0("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Ll=((xa=Fv.exec("a"))===null||xa===void 0?void 0:xa[0])==="a"}catch{Ll=!1}var $f=Lv?function(t,n,r){return t.startsWith(n,r)}:function(t,n,r){return t.slice(r,r+n.length)===n},Nl=Nv?String.fromCodePoint:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var r="",o=t.length,i=0,s;o>i;){if(s=t[i++],s>1114111)throw RangeError(s+" is not a valid code point");r+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return r},zf=Pv?Object.fromEntries:function(t){for(var n={},r=0,o=t;r<o.length;r++){var i=o[r],s=i[0],a=i[1];n[s]=a}return n},M0=bv?function(t,n){return t.codePointAt(n)}:function(t,n){var r=t.length;if(!(n<0||n>=r)){var o=t.charCodeAt(n),i;return o<55296||o>56319||n+1===r||(i=t.charCodeAt(n+1))<56320||i>57343?o:(o-55296<<10)+(i-56320)+65536}},Dv=Ov?function(t){return t.trimStart()}:function(t){return t.replace(kv,"")},jv=Mv?function(t){return t.trimEnd()}:function(t){return t.replace(Iv,"")};function R0(e,t){return new RegExp(e,t)}var Pl;if(Ll){var Gf=R0("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Pl=function(t,n){var r;Gf.lastIndex=n;var o=Gf.exec(t);return(r=o[1])!==null&&r!==void 0?r:""}}else Pl=function(t,n){for(var r=[];;){var o=M0(t,n);if(o===void 0||A0(o)||$v(o))break;r.push(o),n+=o>=65536?2:1}return Nl.apply(void 0,r)};var Hv=function(){function e(t,n){n===void 0&&(n={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!n.ignoreTag,this.locale=n.locale,this.requiresOtherClause=!!n.requiresOtherClause,this.shouldParseSkeletons=!!n.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,n,r){for(var o=[];!this.isEOF();){var i=this.char();if(i===123){var s=this.parseArgument(t,r);if(s.err)return s;o.push(s.val)}else{if(i===125&&t>0)break;if(i===35&&(n==="plural"||n==="selectordinal")){var a=this.clonePosition();this.bump(),o.push({type:X.pound,location:j(a,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(r)break;return this.error(D.UNMATCHED_CLOSING_TAG,j(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&bl(this.peek()||0)){var s=this.parseTag(t,n);if(s.err)return s;o.push(s.val)}else{var s=this.parseLiteral(t,n);if(s.err)return s;o.push(s.val)}}}return{val:o,err:null}},e.prototype.parseTag=function(t,n){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:X.literal,value:"<".concat(o,"/>"),location:j(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,n,!0);if(i.err)return i;var s=i.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!bl(this.char()))return this.error(D.INVALID_TAG,j(a,this.clonePosition()));var l=this.clonePosition(),u=this.parseTagName();return o!==u?this.error(D.UNMATCHED_CLOSING_TAG,j(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:X.tag,value:o,children:s,location:j(r,this.clonePosition())},err:null}:this.error(D.INVALID_TAG,j(a,this.clonePosition())))}else return this.error(D.UNCLOSED_TAG,j(r,this.clonePosition()))}else return this.error(D.INVALID_TAG,j(r,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Uv(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,n){for(var r=this.clonePosition(),o="";;){var i=this.tryParseQuote(n);if(i){o+=i;continue}var s=this.tryParseUnquoted(t,n);if(s){o+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){o+=a;continue}break}var l=j(r,this.clonePosition());return{val:{type:X.literal,value:o,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Bv(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var n=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(r===39)if(this.peek()===39)n.push(39),this.bump();else{this.bump();break}else n.push(r);this.bump()}return Nl.apply(void 0,n)},e.prototype.tryParseUnquoted=function(t,n){if(this.isEOF())return null;var r=this.char();return r===60||r===123||r===35&&(n==="plural"||n==="selectordinal")||r===125&&t>0?null:(this.bump(),Nl(r))},e.prototype.parseArgument=function(t,n){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,j(r,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(D.EMPTY_ARGUMENT,j(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(D.MALFORMED_ARGUMENT,j(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,j(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:X.argument,value:o,location:j(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,j(r,this.clonePosition())):this.parseArgumentOptions(t,n,o,r);default:return this.error(D.MALFORMED_ARGUMENT,j(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),n=this.offset(),r=Pl(this.message,n),o=n+r.length;this.bumpTo(o);var i=this.clonePosition(),s=j(t,i);return{value:r,location:s}},e.prototype.parseArgumentOptions=function(t,n,r,o){var i,s=this.clonePosition(),a=this.parseIdentifierIfPossible().value,l=this.clonePosition();switch(a){case"":return this.error(D.EXPECT_ARGUMENT_TYPE,j(s,l));case"number":case"date":case"time":{this.bumpSpace();var u=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var d=jv(f.val);if(d.length===0)return this.error(D.EXPECT_ARGUMENT_STYLE,j(this.clonePosition(),this.clonePosition()));var m=j(c,this.clonePosition());u={style:d,styleLocation:m}}var y=this.tryParseArgumentClose(o);if(y.err)return y;var v=j(o,this.clonePosition());if(u&&$f(u==null?void 0:u.style,"::",0)){var w=Dv(u.style.slice(2));if(a==="number"){var f=this.parseNumberSkeletonFromString(w,u.styleLocation);return f.err?f:{val:{type:X.number,value:r,location:v,style:f.val},err:null}}else{if(w.length===0)return this.error(D.EXPECT_DATE_TIME_SKELETON,v);var h=w;this.locale&&(h=Cv(w,this.locale));var d={type:_r.dateTime,pattern:h,location:u.styleLocation,parsedOptions:this.shouldParseSkeletons?yv(h):{}},p=a==="date"?X.date:X.time;return{val:{type:p,value:r,location:v,style:d},err:null}}}return{val:{type:a==="number"?X.number:a==="date"?X.date:X.time,value:r,location:v,style:(i=u==null?void 0:u.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var g=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(D.EXPECT_SELECT_ARGUMENT_OPTIONS,j(g,N({},g)));this.bumpSpace();var S=this.parseIdentifierIfPossible(),C=0;if(a!=="select"&&S.value==="offset"){if(!this.bumpIf(":"))return this.error(D.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,j(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(D.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,D.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),S=this.parseIdentifierIfPossible(),C=f.val}var T=this.tryParsePluralOrSelectOptions(t,a,n,S);if(T.err)return T;var y=this.tryParseArgumentClose(o);if(y.err)return y;var L=j(o,this.clonePosition());return a==="select"?{val:{type:X.select,value:r,options:zf(T.val),location:L},err:null}:{val:{type:X.plural,value:r,options:zf(T.val),offset:C,pluralType:a==="plural"?"cardinal":"ordinal",location:L},err:null}}default:return this.error(D.INVALID_ARGUMENT_TYPE,j(s,l))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,j(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,n=this.clonePosition();!this.isEOF();){var r=this.char();switch(r){case 39:{this.bump();var o=this.clonePosition();if(!this.bumpUntil("'"))return this.error(D.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,j(o,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(n.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(n.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,n){var r=[];try{r=Ev(t)}catch{return this.error(D.INVALID_NUMBER_SKELETON,n)}return{val:{type:_r.number,tokens:r,location:n,parsedOptions:this.shouldParseSkeletons?xv(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,n,r,o){for(var i,s=!1,a=[],l=new Set,u=o.value,c=o.location;;){if(u.length===0){var f=this.clonePosition();if(n!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(D.EXPECT_PLURAL_ARGUMENT_SELECTOR,D.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=j(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}else break}if(l.has(u))return this.error(n==="select"?D.DUPLICATE_SELECT_ARGUMENT_SELECTOR:D.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);u==="other"&&(s=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error(n==="select"?D.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:D.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,j(this.clonePosition(),this.clonePosition()));var y=this.parseMessage(t+1,n,r);if(y.err)return y;var v=this.tryParseArgumentClose(m);if(v.err)return v;a.push([u,{value:y.val,location:j(m,this.clonePosition())}]),l.add(u),this.bumpSpace(),i=this.parseIdentifierIfPossible(),u=i.value,c=i.location}return a.length===0?this.error(n==="select"?D.EXPECT_SELECT_ARGUMENT_SELECTOR:D.EXPECT_PLURAL_ARGUMENT_SELECTOR,j(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(D.MISSING_OTHER_CLAUSE,j(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(t,n){var r=1,o=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,s=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)i=!0,s=s*10+(a-48),this.bump();else break}var l=j(o,this.clonePosition());return i?(s*=r,Av(s)?{val:s,err:null}:this.error(n,l)):this.error(t,l)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var n=M0(this.message,t);if(n===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return n},e.prototype.error=function(t,n){return{val:null,err:{kind:t,message:this.message,location:n}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if($f(this.message,t,this.offset())){for(var n=0;n<t.length;n++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var n=this.offset(),r=this.message.indexOf(t,n);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var n=this.offset();if(n===t)break;if(n>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&A0(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),n=this.offset(),r=this.message.charCodeAt(n+(t>=65536?2:1));return r??null},e}();function bl(e){return e>=97&&e<=122||e>=65&&e<=90}function Bv(e){return bl(e)||e===47}function Uv(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function A0(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function $v(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function Ol(e){e.forEach(function(t){if(delete t.location,T0(t)||k0(t))for(var n in t.options)delete t.options[n].location,Ol(t.options[n].value);else w0(t)&&L0(t.style)||(x0(t)||C0(t))&&Il(t.style)?delete t.style.location:I0(t)&&Ol(t.children)})}function zv(e,t){t===void 0&&(t={}),t=N({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var n=new Hv(e,t).parse();if(n.err){var r=SyntaxError(D[n.err.kind]);throw r.location=n.err.location,r.originalMessage=n.err.message,r}return t!=null&&t.captureLocation||Ol(n.val),n.val}var gt;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(gt||(gt={}));var an=function(e){Je(t,e);function t(n,r,o){var i=e.call(this,n)||this;return i.code=r,i.originalMessage=o,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),Vf=function(e){Je(t,e);function t(n,r,o,i){return e.call(this,'Invalid values for "'.concat(n,'": "').concat(r,'". Options are "').concat(Object.keys(o).join('", "'),'"'),gt.INVALID_VALUE,i)||this}return t}(an),Gv=function(e){Je(t,e);function t(n,r,o){return e.call(this,'Value for "'.concat(n,'" must be of type ').concat(r),gt.INVALID_VALUE,o)||this}return t}(an),Vv=function(e){Je(t,e);function t(n,r){return e.call(this,'The intl string context variable "'.concat(n,'" was not provided to the string "').concat(r,'"'),gt.MISSING_VALUE,r)||this}return t}(an),_e;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(_e||(_e={}));function Wv(e){return e.length<2?e:e.reduce(function(t,n){var r=t[t.length-1];return!r||r.type!==_e.literal||n.type!==_e.literal?t.push(n):r.value+=n.value,t},[])}function F0(e){return typeof e=="function"}function ki(e,t,n,r,o,i,s){if(e.length===1&&jf(e[0]))return[{type:_e.literal,value:e[0].value}];for(var a=[],l=0,u=e;l<u.length;l++){var c=u[l];if(jf(c)){a.push({type:_e.literal,value:c.value});continue}if(mv(c)){typeof i=="number"&&a.push({type:_e.literal,value:n.getNumberFormat(t).format(i)});continue}var f=c.value;if(!(o&&f in o))throw new Vv(f,s);var d=o[f];if(hv(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),a.push({type:typeof d=="string"?_e.literal:_e.object,value:d});continue}if(x0(c)){var m=typeof c.style=="string"?r.date[c.style]:Il(c.style)?c.style.parsedOptions:void 0;a.push({type:_e.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(C0(c)){var m=typeof c.style=="string"?r.time[c.style]:Il(c.style)?c.style.parsedOptions:r.time.medium;a.push({type:_e.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(w0(c)){var m=typeof c.style=="string"?r.number[c.style]:L0(c.style)?c.style.parsedOptions:void 0;m&&m.scale&&(d=d*(m.scale||1)),a.push({type:_e.literal,value:n.getNumberFormat(t,m).format(d)});continue}if(I0(c)){var y=c.children,v=c.value,w=o[v];if(!F0(w))throw new Gv(v,"function",s);var h=ki(y,t,n,r,o,i),p=w(h.map(function(C){return C.value}));Array.isArray(p)||(p=[p]),a.push.apply(a,p.map(function(C){return{type:typeof C=="string"?_e.literal:_e.object,value:C}}))}if(T0(c)){var g=c.options[d]||c.options.other;if(!g)throw new Vf(c.value,d,Object.keys(c.options),s);a.push.apply(a,ki(g.value,t,n,r,o));continue}if(k0(c)){var g=c.options["=".concat(d)];if(!g){if(!Intl.PluralRules)throw new an(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,gt.MISSING_INTL_API,s);var S=n.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));g=c.options[S]||c.options.other}if(!g)throw new Vf(c.value,d,Object.keys(c.options),s);a.push.apply(a,ki(g.value,t,n,r,o,d-(c.offset||0)));continue}}return Wv(a)}function Zv(e,t){return t?N(N(N({},e||{}),t||{}),Object.keys(e).reduce(function(n,r){return n[r]=N(N({},e[r]),t[r]||{}),n},{})):e}function Xv(e,t){return t?Object.keys(e).reduce(function(n,r){return n[r]=Zv(e[r],t[r]),n},N({},e)):e}function Ca(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function Yv(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:xe(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.NumberFormat).bind.apply(t,Te([void 0],n,!1)))},{cache:Ca(e.number),strategy:Ce.variadic}),getDateTimeFormat:xe(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.DateTimeFormat).bind.apply(t,Te([void 0],n,!1)))},{cache:Ca(e.dateTime),strategy:Ce.variadic}),getPluralRules:xe(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.PluralRules).bind.apply(t,Te([void 0],n,!1)))},{cache:Ca(e.pluralRules),strategy:Ce.variadic})}}var D0=function(){function e(t,n,r,o){var i=this;if(n===void 0&&(n=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(l){var u=i.formatToParts(l);if(u.length===1)return u[0].value;var c=u.reduce(function(f,d){return!f.length||d.type!==_e.literal||typeof f[f.length-1]!="string"?f.push(d.value):f[f.length-1]+=d.value,f},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(l){return ki(i.ast,i.locales,i.formatters,i.formats,l,void 0,i.message)},this.resolvedOptions=function(){var l;return{locale:((l=i.resolvedLocale)===null||l===void 0?void 0:l.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=n,this.resolvedLocale=e.resolveLocale(n),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=o||{};s.formatters;var a=Sr(s,["formatters"]);this.ast=e.__parse(t,N(N({},a),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Xv(e.formats,r),this.formatters=o&&o.formatters||Yv(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var n=Intl.NumberFormat.supportedLocalesOf(t);return n.length>0?new Intl.Locale(n[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=zv,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),Ln;(function(e){e.FORMAT_ERROR="FORMAT_ERROR",e.UNSUPPORTED_FORMATTER="UNSUPPORTED_FORMATTER",e.INVALID_CONFIG="INVALID_CONFIG",e.MISSING_DATA="MISSING_DATA",e.MISSING_TRANSLATION="MISSING_TRANSLATION"})(Ln||(Ln={}));var Oo=function(e){Je(t,e);function t(n,r,o){var i=this,s=o?o instanceof Error?o:new Error(String(o)):void 0;return i=e.call(this,"[@formatjs/intl Error ".concat(n,"] ").concat(r,`
`).concat(s?`
`.concat(s.message,`
`).concat(s.stack):""))||this,i.code=n,typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(i,t),i}return t}(Error),Qv=function(e){Je(t,e);function t(n,r){return e.call(this,Ln.UNSUPPORTED_FORMATTER,n,r)||this}return t}(Oo),Kv=function(e){Je(t,e);function t(n,r){return e.call(this,Ln.INVALID_CONFIG,n,r)||this}return t}(Oo),Wf=function(e){Je(t,e);function t(n,r){return e.call(this,Ln.MISSING_DATA,n,r)||this}return t}(Oo),et=function(e){Je(t,e);function t(n,r,o){var i=e.call(this,Ln.FORMAT_ERROR,"".concat(n,`
Locale: `).concat(r,`
`),o)||this;return i.locale=r,i}return t}(Oo),Ta=function(e){Je(t,e);function t(n,r,o,i){var s=e.call(this,"".concat(n,`
MessageID: `).concat(o==null?void 0:o.id,`
Default Message: `).concat(o==null?void 0:o.defaultMessage,`
Description: `).concat(o==null?void 0:o.description,`
`),r,i)||this;return s.descriptor=o,s.locale=r,s}return t}(et),qv=function(e){Je(t,e);function t(n,r){var o=e.call(this,Ln.MISSING_TRANSLATION,'Missing message: "'.concat(n.id,'" for locale "').concat(r,'", using ').concat(n.defaultMessage?"default message (".concat(typeof n.defaultMessage=="string"?n.defaultMessage:n.defaultMessage.map(function(i){var s;return(s=i.value)!==null&&s!==void 0?s:JSON.stringify(i)}).join(),")"):"id"," as fallback."))||this;return o.descriptor=n,o}return t}(Oo);function Fn(e,t,n){return n===void 0&&(n={}),t.reduce(function(r,o){return o in e?r[o]=e[o]:o in n&&(r[o]=n[o]),r},{})}var Jv=function(e){},e2=function(e){},j0={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:Jv,onWarn:e2};function H0(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function dn(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function t2(e){e===void 0&&(e=H0());var t=Intl.RelativeTimeFormat,n=Intl.ListFormat,r=Intl.DisplayNames,o=xe(function(){for(var a,l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return new((a=Intl.DateTimeFormat).bind.apply(a,Te([void 0],l,!1)))},{cache:dn(e.dateTime),strategy:Ce.variadic}),i=xe(function(){for(var a,l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return new((a=Intl.NumberFormat).bind.apply(a,Te([void 0],l,!1)))},{cache:dn(e.number),strategy:Ce.variadic}),s=xe(function(){for(var a,l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return new((a=Intl.PluralRules).bind.apply(a,Te([void 0],l,!1)))},{cache:dn(e.pluralRules),strategy:Ce.variadic});return{getDateTimeFormat:o,getNumberFormat:i,getMessageFormat:xe(function(a,l,u,c){return new D0(a,l,u,N({formatters:{getNumberFormat:i,getDateTimeFormat:o,getPluralRules:s}},c||{}))},{cache:dn(e.message),strategy:Ce.variadic}),getRelativeTimeFormat:xe(function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new(t.bind.apply(t,Te([void 0],a,!1)))},{cache:dn(e.relativeTime),strategy:Ce.variadic}),getPluralRules:s,getListFormat:xe(function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new(n.bind.apply(n,Te([void 0],a,!1)))},{cache:dn(e.list),strategy:Ce.variadic}),getDisplayNames:xe(function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new(r.bind.apply(r,Te([void 0],a,!1)))},{cache:dn(e.displayNames),strategy:Ce.variadic})}}function nc(e,t,n,r){var o=e&&e[t],i;if(o&&(i=o[n]),i)return i;r(new Qv("No ".concat(t," format named: ").concat(n)))}function ii(e,t){return Object.keys(e).reduce(function(n,r){return n[r]=N({timeZone:t},e[r]),n},{})}function Zf(e,t){var n=Object.keys(N(N({},e),t));return n.reduce(function(r,o){return r[o]=N(N({},e[o]||{}),t[o]||{}),r},{})}function Xf(e,t){if(!t)return e;var n=D0.formats;return N(N(N({},n),e),{date:Zf(ii(n.date,t),ii(e.date||{},t)),time:Zf(ii(n.time,t),ii(e.time||{},t))})}var Ml=function(e,t,n,r,o){var i=e.locale,s=e.formats,a=e.messages,l=e.defaultLocale,u=e.defaultFormats,c=e.fallbackOnEmptyString,f=e.onError,d=e.timeZone,m=e.defaultRichTextElements;n===void 0&&(n={id:""});var y=n.id,v=n.defaultMessage;_0(!!y,"[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");var w=String(y),h=a&&Object.prototype.hasOwnProperty.call(a,w)&&a[w];if(Array.isArray(h)&&h.length===1&&h[0].type===X.literal)return h[0].value;if(!r&&h&&typeof h=="string"&&!m)return h.replace(/'\{(.*?)\}'/gi,"{$1}");if(r=N(N({},m),r||{}),s=Xf(s,d),u=Xf(u,d),!h){if(c===!1&&h==="")return h;if((!v||i&&i.toLowerCase()!==l.toLowerCase())&&f(new qv(n,i)),v)try{var p=t.getMessageFormat(v,l,u,o);return p.format(r)}catch(g){return f(new Ta('Error formatting default message for: "'.concat(w,'", rendering default message verbatim'),i,n,g)),typeof v=="string"?v:w}return w}try{var p=t.getMessageFormat(h,i,s,N({formatters:t},o||{}));return p.format(r)}catch(g){f(new Ta('Error formatting message: "'.concat(w,'", using ').concat(v?"default message":"id"," as fallback."),i,n,g))}if(v)try{var p=t.getMessageFormat(v,l,u,o);return p.format(r)}catch(g){f(new Ta('Error formatting the default message for: "'.concat(w,'", rendering message verbatim'),i,n,g))}return typeof h=="string"?h:typeof v=="string"?v:w},B0=["formatMatcher","timeZone","hour12","weekday","era","year","month","day","hour","minute","second","timeZoneName","hourCycle","dateStyle","timeStyle","calendar","numberingSystem","fractionalSecondDigits"];function Is(e,t,n,r){var o=e.locale,i=e.formats,s=e.onError,a=e.timeZone;r===void 0&&(r={});var l=r.format,u=N(N({},a&&{timeZone:a}),l&&nc(i,t,l,s)),c=Fn(r,B0,u);return t==="time"&&!c.hour&&!c.minute&&!c.second&&!c.timeStyle&&!c.dateStyle&&(c=N(N({},c),{hour:"numeric",minute:"numeric"})),n(o,c)}function n2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,a=typeof o=="string"?new Date(o||0):o;try{return Is(e,"date",t,s).format(a)}catch(l){e.onError(new et("Error formatting date.",e.locale,l))}return String(a)}function r2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,a=typeof o=="string"?new Date(o||0):o;try{return Is(e,"time",t,s).format(a)}catch(l){e.onError(new et("Error formatting time.",e.locale,l))}return String(a)}function o2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=n[2],a=s===void 0?{}:s,l=e.timeZone,u=e.locale,c=e.onError,f=Fn(a,B0,l?{timeZone:l}:{});try{return t(u,f).formatRange(o,i)}catch(d){c(new et("Error formatting date time range.",e.locale,d))}return String(o)}function i2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,a=typeof o=="string"?new Date(o||0):o;try{return Is(e,"date",t,s).formatToParts(a)}catch(l){e.onError(new et("Error formatting date.",e.locale,l))}return[]}function s2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,a=typeof o=="string"?new Date(o||0):o;try{return Is(e,"time",t,s).formatToParts(a)}catch(l){e.onError(new et("Error formatting time.",e.locale,l))}return[]}var a2=["style","type","fallback","languageDisplay"];function l2(e,t,n,r){var o=e.locale,i=e.onError,s=Intl.DisplayNames;s||i(new an(`Intl.DisplayNames is not available in this environment.
Try polyfilling it using "@formatjs/intl-displaynames"
`,gt.MISSING_INTL_API));var a=Fn(r,a2);try{return t(o,a).of(n)}catch(l){i(new et("Error formatting display name.",o,l))}}var u2=["type","style"],Yf=Date.now();function c2(e){return"".concat(Yf,"_").concat(e,"_").concat(Yf)}function f2(e,t,n,r){r===void 0&&(r={});var o=U0(e,t,n,r).reduce(function(i,s){var a=s.value;return typeof a!="string"?i.push(a):typeof i[i.length-1]=="string"?i[i.length-1]+=a:i.push(a),i},[]);return o.length===1?o[0]:o.length===0?"":o}function U0(e,t,n,r){var o=e.locale,i=e.onError;r===void 0&&(r={});var s=Intl.ListFormat;s||i(new an(`Intl.ListFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-listformat"
`,gt.MISSING_INTL_API));var a=Fn(r,u2);try{var l={},u=n.map(function(c,f){if(typeof c=="object"){var d=c2(f);return l[d]=c,d}return String(c)});return t(o,a).formatToParts(u).map(function(c){return c.type==="literal"?c:N(N({},c),{value:l[c.value]||c.value})})}catch(c){i(new et("Error formatting list.",o,c))}return n}var d2=["type"];function p2(e,t,n,r){var o=e.locale,i=e.onError;r===void 0&&(r={}),Intl.PluralRules||i(new an(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,gt.MISSING_INTL_API));var s=Fn(r,d2);try{return t(o,s).select(n)}catch(a){i(new et("Error formatting plural.",o,a))}return"other"}var h2=["numeric","style"];function m2(e,t,n){var r=e.locale,o=e.formats,i=e.onError;n===void 0&&(n={});var s=n.format,a=!!s&&nc(o,"relative",s,i)||{},l=Fn(n,h2,a);return t(r,l)}function g2(e,t,n,r,o){o===void 0&&(o={}),r||(r="second");var i=Intl.RelativeTimeFormat;i||e.onError(new an(`Intl.RelativeTimeFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-relativetimeformat"
`,gt.MISSING_INTL_API));try{return m2(e,t,o).format(n,r)}catch(s){e.onError(new et("Error formatting relative time.",e.locale,s))}return String(n)}var y2=["style","currency","unit","unitDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","currencyDisplay","currencySign","notation","signDisplay","unit","unitDisplay","numberingSystem","trailingZeroDisplay","roundingPriority","roundingIncrement","roundingMode"];function $0(e,t,n){var r=e.locale,o=e.formats,i=e.onError;n===void 0&&(n={});var s=n.format,a=s&&nc(o,"number",s,i)||{},l=Fn(n,y2,a);return t(r,l)}function v2(e,t,n,r){r===void 0&&(r={});try{return $0(e,t,r).format(n)}catch(o){e.onError(new et("Error formatting number.",e.locale,o))}return String(n)}function E2(e,t,n,r){r===void 0&&(r={});try{return $0(e,t,r).formatToParts(n)}catch(o){e.onError(new et("Error formatting number.",e.locale,o))}return[]}function S2(e){var t=e?e[Object.keys(e)[0]]:void 0;return typeof t=="string"}function _2(e){e.onWarn&&e.defaultRichTextElements&&S2(e.messages||{})&&e.onWarn(`[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. 
Please consider using "@formatjs/cli" to pre-compile your messages for performance.
For more details see https://formatjs.io/docs/getting-started/message-distribution`)}function w2(e,t){var n=t2(t),r=N(N({},j0),e),o=r.locale,i=r.defaultLocale,s=r.onError;return o?!Intl.NumberFormat.supportedLocalesOf(o).length&&s?s(new Wf('Missing locale data for locale: "'.concat(o,'" in Intl.NumberFormat. Using default locale: "').concat(i,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):!Intl.DateTimeFormat.supportedLocalesOf(o).length&&s&&s(new Wf('Missing locale data for locale: "'.concat(o,'" in Intl.DateTimeFormat. Using default locale: "').concat(i,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):(s&&s(new Kv('"locale" was not configured, using "'.concat(i,'" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details'))),r.locale=r.defaultLocale||"en"),_2(r),N(N({},r),{formatters:n,formatNumber:v2.bind(null,r,n.getNumberFormat),formatNumberToParts:E2.bind(null,r,n.getNumberFormat),formatRelativeTime:g2.bind(null,r,n.getRelativeTimeFormat),formatDate:n2.bind(null,r,n.getDateTimeFormat),formatDateToParts:i2.bind(null,r,n.getDateTimeFormat),formatTime:r2.bind(null,r,n.getDateTimeFormat),formatDateTimeRange:o2.bind(null,r,n.getDateTimeFormat),formatTimeToParts:s2.bind(null,r,n.getDateTimeFormat),formatPlural:p2.bind(null,r,n.getPluralRules),formatMessage:Ml.bind(null,r,n),$t:Ml.bind(null,r,n),formatList:f2.bind(null,r,n.getListFormat),formatListToParts:U0.bind(null,r,n.getListFormat),formatDisplayName:l2.bind(null,r,n.getDisplayNames)})}function z0(e){_0(e,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}var G0=N(N({},j0),{textComponent:b.Fragment});function x2(e){return function(t){return e(b.Children.toArray(t))}}function Rl(e,t){if(e===t)return!0;if(!e||!t)return!1;var n=Object.keys(e),r=Object.keys(t),o=n.length;if(r.length!==o)return!1;for(var i=0;i<o;i++){var s=n[i];if(e[s]!==t[s]||!Object.prototype.hasOwnProperty.call(t,s))return!1}return!0}var V0={exports:{}},$={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fe=typeof Symbol=="function"&&Symbol.for,rc=fe?Symbol.for("react.element"):60103,oc=fe?Symbol.for("react.portal"):60106,Ls=fe?Symbol.for("react.fragment"):60107,Ns=fe?Symbol.for("react.strict_mode"):60108,Ps=fe?Symbol.for("react.profiler"):60114,bs=fe?Symbol.for("react.provider"):60109,Os=fe?Symbol.for("react.context"):60110,ic=fe?Symbol.for("react.async_mode"):60111,Ms=fe?Symbol.for("react.concurrent_mode"):60111,Rs=fe?Symbol.for("react.forward_ref"):60112,As=fe?Symbol.for("react.suspense"):60113,C2=fe?Symbol.for("react.suspense_list"):60120,Fs=fe?Symbol.for("react.memo"):60115,Ds=fe?Symbol.for("react.lazy"):60116,T2=fe?Symbol.for("react.block"):60121,k2=fe?Symbol.for("react.fundamental"):60117,I2=fe?Symbol.for("react.responder"):60118,L2=fe?Symbol.for("react.scope"):60119;function Ge(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case rc:switch(e=e.type,e){case ic:case Ms:case Ls:case Ps:case Ns:case As:return e;default:switch(e=e&&e.$$typeof,e){case Os:case Rs:case Ds:case Fs:case bs:return e;default:return t}}case oc:return t}}}function W0(e){return Ge(e)===Ms}$.AsyncMode=ic;$.ConcurrentMode=Ms;$.ContextConsumer=Os;$.ContextProvider=bs;$.Element=rc;$.ForwardRef=Rs;$.Fragment=Ls;$.Lazy=Ds;$.Memo=Fs;$.Portal=oc;$.Profiler=Ps;$.StrictMode=Ns;$.Suspense=As;$.isAsyncMode=function(e){return W0(e)||Ge(e)===ic};$.isConcurrentMode=W0;$.isContextConsumer=function(e){return Ge(e)===Os};$.isContextProvider=function(e){return Ge(e)===bs};$.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===rc};$.isForwardRef=function(e){return Ge(e)===Rs};$.isFragment=function(e){return Ge(e)===Ls};$.isLazy=function(e){return Ge(e)===Ds};$.isMemo=function(e){return Ge(e)===Fs};$.isPortal=function(e){return Ge(e)===oc};$.isProfiler=function(e){return Ge(e)===Ps};$.isStrictMode=function(e){return Ge(e)===Ns};$.isSuspense=function(e){return Ge(e)===As};$.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Ls||e===Ms||e===Ps||e===Ns||e===As||e===C2||typeof e=="object"&&e!==null&&(e.$$typeof===Ds||e.$$typeof===Fs||e.$$typeof===bs||e.$$typeof===Os||e.$$typeof===Rs||e.$$typeof===k2||e.$$typeof===I2||e.$$typeof===L2||e.$$typeof===T2)};$.typeOf=Ge;V0.exports=$;var N2=V0.exports,Z0=N2,P2={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},b2={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},X0={};X0[Z0.ForwardRef]=P2;X0[Z0.Memo]=b2;var sc=typeof window<"u"&&!window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=b.createContext(null)):b.createContext(null);sc.Consumer;var O2=sc.Provider,M2=O2,R2=sc;function Dn(){var e=b.useContext(R2);return z0(e),e}var Al;(function(e){e.formatDate="FormattedDate",e.formatTime="FormattedTime",e.formatNumber="FormattedNumber",e.formatList="FormattedList",e.formatDisplayName="FormattedDisplayName"})(Al||(Al={}));var Fl;(function(e){e.formatDate="FormattedDateParts",e.formatTime="FormattedTimeParts",e.formatNumber="FormattedNumberParts",e.formatList="FormattedListParts"})(Fl||(Fl={}));function Y0(e){var t=function(n){var r=Dn(),o=n.value,i=n.children,s=Sr(n,["value","children"]),a=typeof o=="string"?new Date(o||0):o,l=e==="formatDate"?r.formatDateToParts(a,s):r.formatTimeToParts(a,s);return i(l)};return t.displayName=Fl[e],t}function Mo(e){var t=function(n){var r=Dn(),o=n.value,i=n.children,s=Sr(n,["value","children"]),a=r[e](o,s);if(typeof i=="function")return i(a);var l=r.textComponent||b.Fragment;return b.createElement(l,null,a)};return t.displayName=Al[e],t}function Q0(e){return e&&Object.keys(e).reduce(function(t,n){var r=e[n];return t[n]=F0(r)?x2(r):r,t},{})}var Qf=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];var s=Q0(r),a=Ml.apply(void 0,Te([e,t,n,s],o,!1));return Array.isArray(a)?b.Children.toArray(a):a},Kf=function(e,t){var n=e.defaultRichTextElements,r=Sr(e,["defaultRichTextElements"]),o=Q0(n),i=w2(N(N(N({},G0),r),{defaultRichTextElements:o}),t),s={locale:i.locale,timeZone:i.timeZone,fallbackOnEmptyString:i.fallbackOnEmptyString,formats:i.formats,defaultLocale:i.defaultLocale,defaultFormats:i.defaultFormats,messages:i.messages,onError:i.onError,defaultRichTextElements:o};return N(N({},i),{formatMessage:Qf.bind(null,s,i.formatters),$t:Qf.bind(null,s,i.formatters)})};function ka(e){return{locale:e.locale,timeZone:e.timeZone,fallbackOnEmptyString:e.fallbackOnEmptyString,formats:e.formats,textComponent:e.textComponent,messages:e.messages,defaultLocale:e.defaultLocale,defaultFormats:e.defaultFormats,onError:e.onError,onWarn:e.onWarn,wrapRichTextChunksInFragment:e.wrapRichTextChunksInFragment,defaultRichTextElements:e.defaultRichTextElements}}var A2=function(e){Je(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.cache=H0(),n.state={cache:n.cache,intl:Kf(ka(n.props),n.cache),prevConfig:ka(n.props)},n}return t.getDerivedStateFromProps=function(n,r){var o=r.prevConfig,i=r.cache,s=ka(n);return Rl(o,s)?null:{intl:Kf(s,i),prevConfig:s}},t.prototype.render=function(){return z0(this.state.intl),b.createElement(M2,{value:this.state.intl},this.props.children)},t.displayName="IntlProvider",t.defaultProps=G0,t}(b.PureComponent);function F2(e,t){var n=e.values,r=Sr(e,["values"]),o=t.values,i=Sr(t,["values"]);return Rl(o,n)&&Rl(r,i)}function K0(e){var t=Dn(),n=t.formatMessage,r=t.textComponent,o=r===void 0?b.Fragment:r,i=e.id,s=e.description,a=e.defaultMessage,l=e.values,u=e.children,c=e.tagName,f=c===void 0?o:c,d=e.ignoreTag,m={id:i,description:s,defaultMessage:a},y=n(m,l,{ignoreTag:d});return typeof u=="function"?u(Array.isArray(y)?y:[y]):f?b.createElement(f,null,b.Children.toArray(y)):b.createElement(b.Fragment,null,y)}K0.displayName="FormattedMessage";var z=b.memo(K0,F2);z.displayName="MemoizedFormattedMessage";Mo("formatDate");Mo("formatTime");Mo("formatNumber");Mo("formatList");Mo("formatDisplayName");Y0("formatDate");Y0("formatTime");var Dl=function(e,t){return Dl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])},Dl(e,t)};function br(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Dl(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}function D2(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(c){try{u(r.next(c))}catch(f){s(f)}}function l(c){try{u(r.throw(c))}catch(f){s(f)}}function u(c){c.done?i(c.value):o(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})}function q0(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,o,i,s=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(u){return function(c){return l([u,c])}}function l(u){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,u[0]&&(n=0)),n;)try{if(r=1,o&&(i=u[0]&2?o.return:u[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,u[1])).done)return i;switch(o=0,i&&(u=[u[0]&2,i.value]),u[0]){case 0:case 1:i=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,o=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!i||u[1]>i[0]&&u[1]<i[3])){n.label=u[1];break}if(u[0]===6&&n.label<i[1]){n.label=i[1],i=u;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(u);break}i[2]&&n.ops.pop(),n.trys.pop();continue}u=t.call(e,n)}catch(c){u=[6,c],o=0}finally{r=i=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function Co(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function ts(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=r.next()).done;)i.push(o.value)}catch(a){s={error:a}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(s)throw s.error}}return i}function ns(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}function lr(e){return this instanceof lr?(this.v=e,this):new lr(e)}function j2(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(m){return function(y){return Promise.resolve(y).then(m,f)}}function a(m,y){r[m]&&(o[m]=function(v){return new Promise(function(w,h){i.push([m,v,w,h])>1||l(m,v)})},y&&(o[m]=y(o[m])))}function l(m,y){try{u(r[m](y))}catch(v){d(i[0][3],v)}}function u(m){m.value instanceof lr?Promise.resolve(m.value.v).then(c,f):d(i[0][2],m)}function c(m){l("next",m)}function f(m){l("throw",m)}function d(m,y){m(y),i.shift(),i.length&&l(i[0][0],i[0][1])}}function H2(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Co=="function"?Co(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,l){s=e[i](s),o(a,l,s.done,s.value)})}}function o(i,s,a,l){Promise.resolve(l).then(function(u){i({value:u,done:a})},s)}}function ie(e){return typeof e=="function"}function ac(e){var t=function(r){Error.call(r),r.stack=new Error().stack},n=e(t);return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ia=ac(function(e){return function(n){e(this),this.message=n?n.length+` errors occurred during unsubscription:
`+n.map(function(r,o){return o+1+") "+r.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=n}});function jl(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var js=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,n,r,o,i;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var a=Co(s),l=a.next();!l.done;l=a.next()){var u=l.value;u.remove(this)}}catch(v){t={error:v}}finally{try{l&&!l.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}else s.remove(this);var c=this.initialTeardown;if(ie(c))try{c()}catch(v){i=v instanceof Ia?v.errors:[v]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var d=Co(f),m=d.next();!m.done;m=d.next()){var y=m.value;try{qf(y)}catch(v){i=i??[],v instanceof Ia?i=ns(ns([],ts(i)),ts(v.errors)):i.push(v)}}}catch(v){r={error:v}}finally{try{m&&!m.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}}if(i)throw new Ia(i)}},e.prototype.add=function(t){var n;if(t&&t!==this)if(this.closed)qf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}},e.prototype._hasParent=function(t){var n=this._parentage;return n===t||Array.isArray(n)&&n.includes(t)},e.prototype._addParent=function(t){var n=this._parentage;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t},e.prototype._removeParent=function(t){var n=this._parentage;n===t?this._parentage=null:Array.isArray(n)&&jl(n,t)},e.prototype.remove=function(t){var n=this._finalizers;n&&jl(n,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}();js.EMPTY;function J0(e){return e instanceof js||e&&"closed"in e&&ie(e.remove)&&ie(e.add)&&ie(e.unsubscribe)}function qf(e){ie(e)?e():e.unsubscribe()}var em={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},tm={setTimeout:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,ns([e,t],ts(n)))},clearTimeout:function(e){var t=tm.delegate;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function nm(e){tm.setTimeout(function(){throw e})}function rs(){}function B2(e){e()}var lc=function(e){br(t,e);function t(n){var r=e.call(this)||this;return r.isStopped=!1,n?(r.destination=n,J0(n)&&n.add(r)):r.destination=G2,r}return t.create=function(n,r,o){return new os(n,r,o)},t.prototype.next=function(n){this.isStopped||this._next(n)},t.prototype.error=function(n){this.isStopped||(this.isStopped=!0,this._error(n))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(n){this.destination.next(n)},t.prototype._error=function(n){try{this.destination.error(n)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(js),U2=Function.prototype.bind;function La(e,t){return U2.call(e,t)}var $2=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var n=this.partialObserver;if(n.next)try{n.next(t)}catch(r){si(r)}},e.prototype.error=function(t){var n=this.partialObserver;if(n.error)try{n.error(t)}catch(r){si(r)}else si(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(n){si(n)}},e}(),os=function(e){br(t,e);function t(n,r,o){var i=e.call(this)||this,s;if(ie(n)||!n)s={next:n??void 0,error:r??void 0,complete:o??void 0};else{var a;i&&em.useDeprecatedNextContext?(a=Object.create(n),a.unsubscribe=function(){return i.unsubscribe()},s={next:n.next&&La(n.next,a),error:n.error&&La(n.error,a),complete:n.complete&&La(n.complete,a)}):s=n}return i.destination=new $2(s),i}return t}(lc);function si(e){nm(e)}function z2(e){throw e}var G2={closed:!0,next:rs,error:z2,complete:rs},uc=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function V2(e){return e}function W2(e){return e.length===0?V2:e.length===1?e[0]:function(n){return e.reduce(function(r,o){return o(r)},n)}}var Ne=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(t,n,r){var o=this,i=X2(t)?t:new os(t,n,r);return B2(function(){var s=o,a=s.operator,l=s.source;i.add(a?a.call(i,l):l?o._subscribe(i):o._trySubscribe(i))}),i},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(n){t.error(n)}},e.prototype.forEach=function(t,n){var r=this;return n=Jf(n),new n(function(o,i){var s=new os({next:function(a){try{t(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});r.subscribe(s)})},e.prototype._subscribe=function(t){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(t)},e.prototype[uc]=function(){return this},e.prototype.pipe=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return W2(t)(this)},e.prototype.toPromise=function(t){var n=this;return t=Jf(t),new t(function(r,o){var i;n.subscribe(function(s){return i=s},function(s){return o(s)},function(){return r(i)})})},e.create=function(t){return new e(t)},e}();function Jf(e){var t;return(t=e??em.Promise)!==null&&t!==void 0?t:Promise}function Z2(e){return e&&ie(e.next)&&ie(e.error)&&ie(e.complete)}function X2(e){return e&&e instanceof lc||Z2(e)&&J0(e)}function Y2(e){return ie(e==null?void 0:e.lift)}function yt(e){return function(t){if(Y2(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Ot(e,t,n,r,o){return new Q2(e,t,n,r,o)}var Q2=function(e){br(t,e);function t(n,r,o,i,s,a){var l=e.call(this,n)||this;return l.onFinalize=s,l.shouldUnsubscribe=a,l._next=r?function(u){try{r(u)}catch(c){n.error(c)}}:e.prototype._next,l._error=i?function(u){try{i(u)}catch(c){n.error(c)}finally{this.unsubscribe()}}:e.prototype._error,l._complete=o?function(){try{o()}catch(u){n.error(u)}finally{this.unsubscribe()}}:e.prototype._complete,l}return t.prototype.unsubscribe=function(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&((n=this.onFinalize)===null||n===void 0||n.call(this))}},t}(lc),K2={now:function(){return Date.now()},delegate:void 0},q2=function(e){br(t,e);function t(n,r){return e.call(this)||this}return t.prototype.schedule=function(n,r){return this},t}(js),ed={setInterval:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setInterval.apply(void 0,ns([e,t],ts(n)))},clearInterval:function(e){return clearInterval(e)},delegate:void 0},J2=function(e){br(t,e);function t(n,r){var o=e.call(this,n,r)||this;return o.scheduler=n,o.work=r,o.pending=!1,o}return t.prototype.schedule=function(n,r){var o;if(r===void 0&&(r=0),this.closed)return this;this.state=n;var i=this.id,s=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(s,i,r)),this.pending=!0,this.delay=r,this.id=(o=this.id)!==null&&o!==void 0?o:this.requestAsyncId(s,this.id,r),this},t.prototype.requestAsyncId=function(n,r,o){return o===void 0&&(o=0),ed.setInterval(n.flush.bind(n,this),o)},t.prototype.recycleAsyncId=function(n,r,o){if(o===void 0&&(o=0),o!=null&&this.delay===o&&this.pending===!1)return r;r!=null&&ed.clearInterval(r)},t.prototype.execute=function(n,r){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var o=this._execute(n,r);if(o)return o;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(n,r){var o=!1,i;try{this.work(n)}catch(s){o=!0,i=s||new Error("Scheduled action threw falsy error")}if(o)return this.unsubscribe(),i},t.prototype.unsubscribe=function(){if(!this.closed){var n=this,r=n.id,o=n.scheduler,i=o.actions;this.work=this.state=this.scheduler=null,this.pending=!1,jl(i,this),r!=null&&(this.id=this.recycleAsyncId(o,r,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(q2),td=function(){function e(t,n){n===void 0&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(t,n,r){return n===void 0&&(n=0),new this.schedulerActionCtor(this,t).schedule(r,n)},e.now=K2.now,e}(),eE=function(e){br(t,e);function t(n,r){r===void 0&&(r=td.now);var o=e.call(this,n,r)||this;return o.actions=[],o._active=!1,o}return t.prototype.flush=function(n){var r=this.actions;if(this._active){r.push(n);return}var o;this._active=!0;do if(o=n.execute(n.state,n.delay))break;while(n=r.shift());if(this._active=!1,o){for(;n=r.shift();)n.unsubscribe();throw o}},t}(td),tE=new eE(J2);function nE(e){return e&&ie(e.schedule)}function rE(e){return e[e.length-1]}function oE(e){return nE(rE(e))?e.pop():void 0}var rm=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function om(e){return ie(e==null?void 0:e.then)}function im(e){return ie(e[uc])}function sm(e){return Symbol.asyncIterator&&ie(e==null?void 0:e[Symbol.asyncIterator])}function am(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function iE(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var lm=iE();function um(e){return ie(e==null?void 0:e[lm])}function cm(e){return j2(this,arguments,function(){var n,r,o,i;return q0(this,function(s){switch(s.label){case 0:n=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,lr(n.read())];case 3:return r=s.sent(),o=r.value,i=r.done,i?[4,lr(void 0)]:[3,5];case 4:return[2,s.sent()];case 5:return[4,lr(o)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return n.releaseLock(),[7];case 10:return[2]}})})}function fm(e){return ie(e==null?void 0:e.getReader)}function ln(e){if(e instanceof Ne)return e;if(e!=null){if(im(e))return sE(e);if(rm(e))return aE(e);if(om(e))return lE(e);if(sm(e))return dm(e);if(um(e))return uE(e);if(fm(e))return cE(e)}throw am(e)}function sE(e){return new Ne(function(t){var n=e[uc]();if(ie(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function aE(e){return new Ne(function(t){for(var n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function lE(e){return new Ne(function(t){e.then(function(n){t.closed||(t.next(n),t.complete())},function(n){return t.error(n)}).then(null,nm)})}function uE(e){return new Ne(function(t){var n,r;try{for(var o=Co(e),i=o.next();!i.done;i=o.next()){var s=i.value;if(t.next(s),t.closed)return}}catch(a){n={error:a}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}t.complete()})}function dm(e){return new Ne(function(t){fE(e,t).catch(function(n){return t.error(n)})})}function cE(e){return dm(cm(e))}function fE(e,t){var n,r,o,i;return D2(this,void 0,void 0,function(){var s,a;return q0(this,function(l){switch(l.label){case 0:l.trys.push([0,5,6,11]),n=H2(e),l.label=1;case 1:return[4,n.next()];case 2:if(r=l.sent(),!!r.done)return[3,4];if(s=r.value,t.next(s),t.closed)return[2];l.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return a=l.sent(),o={error:a},[3,11];case 6:return l.trys.push([6,,9,10]),r&&!r.done&&(i=n.return)?[4,i.call(n)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function qt(e,t,n,r,o){r===void 0&&(r=0),o===void 0&&(o=!1);var i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function pm(e,t){return t===void 0&&(t=0),yt(function(n,r){n.subscribe(Ot(r,function(o){return qt(r,e,function(){return r.next(o)},t)},function(){return qt(r,e,function(){return r.complete()},t)},function(o){return qt(r,e,function(){return r.error(o)},t)}))})}function hm(e,t){return t===void 0&&(t=0),yt(function(n,r){r.add(e.schedule(function(){return n.subscribe(r)},t))})}function dE(e,t){return ln(e).pipe(hm(t),pm(t))}function pE(e,t){return ln(e).pipe(hm(t),pm(t))}function hE(e,t){return new Ne(function(n){var r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function mE(e,t){return new Ne(function(n){var r;return qt(n,t,function(){r=e[lm](),qt(n,t,function(){var o,i,s;try{o=r.next(),i=o.value,s=o.done}catch(a){n.error(a);return}s?n.complete():n.next(i)},0,!0)}),function(){return ie(r==null?void 0:r.return)&&r.return()}})}function mm(e,t){if(!e)throw new Error("Iterable cannot be null");return new Ne(function(n){qt(n,t,function(){var r=e[Symbol.asyncIterator]();qt(n,t,function(){r.next().then(function(o){o.done?n.complete():n.next(o.value)})},0,!0)})})}function gE(e,t){return mm(cm(e),t)}function yE(e,t){if(e!=null){if(im(e))return dE(e,t);if(rm(e))return hE(e,t);if(om(e))return pE(e,t);if(sm(e))return mm(e,t);if(um(e))return mE(e,t);if(fm(e))return gE(e,t)}throw am(e)}function gm(e,t){return t?yE(e,t):ln(e)}function Ii(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=oE(e);return gm(e,n)}function vE(e,t){var n=ie(e)?e:function(){return e},r=function(o){return o.error(n())};return new Ne(r)}var EE=ac(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function SE(e,t){return new Promise(function(n,r){var o=new os({next:function(i){n(i),o.unsubscribe()},error:r,complete:function(){r(new EE)}});e.subscribe(o)})}function _E(e){return e instanceof Date&&!isNaN(e)}var wE=ac(function(e){return function(n){n===void 0&&(n=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n}});function xE(e,t){var n=_E(e)?{first:e}:typeof e=="number"?{each:e}:e,r=n.first,o=n.each,i=n.with,s=i===void 0?CE:i,a=n.scheduler,l=a===void 0?tE:a,u=n.meta,c=u===void 0?null:u;if(r==null&&o==null)throw new TypeError("No timeout provided.");return yt(function(f,d){var m,y,v=null,w=0,h=function(p){y=qt(d,l,function(){try{m.unsubscribe(),ln(s({meta:c,lastValue:v,seen:w})).subscribe(d)}catch(g){d.error(g)}},p)};m=f.subscribe(Ot(d,function(p){y==null||y.unsubscribe(),w++,d.next(v=p),o>0&&h(o)},void 0,void 0,function(){y!=null&&y.closed||y==null||y.unsubscribe(),v=null})),!w&&h(r!=null?typeof r=="number"?r:+r-l.now():o)})}function CE(e){throw new wE(e)}function ym(e,t){return yt(function(n,r){var o=0;n.subscribe(Ot(r,function(i){r.next(e.call(t,i,o++))}))})}function TE(e,t,n,r,o,i,s,a){var l=[],u=0,c=0,f=!1,d=function(){f&&!l.length&&!u&&t.complete()},m=function(v){return u<r?y(v):l.push(v)},y=function(v){u++;var w=!1;ln(n(v,c++)).subscribe(Ot(t,function(h){t.next(h)},function(){w=!0},void 0,function(){if(w)try{u--;for(var h=function(){var p=l.shift();s||y(p)};l.length&&u<r;)h();d()}catch(p){t.error(p)}}))};return e.subscribe(Ot(t,m,function(){f=!0,d()})),function(){}}function cc(e,t,n){return n===void 0&&(n=1/0),ie(t)?cc(function(r,o){return ym(function(i,s){return t(r,i,o,s)})(ln(e(r,o)))},n):(typeof t=="number"&&(n=t),yt(function(r,o){return TE(r,o,e,n)}))}var vm=new Ne(rs);function Hl(e,t){return yt(function(n,r){var o=0;n.subscribe(Ot(r,function(i){return e.call(t,i,o++)&&r.next(i)}))})}function Em(e){return yt(function(t,n){var r=null,o=!1,i;r=t.subscribe(Ot(n,void 0,void 0,function(s){i=ln(e(s,Em(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function kE(e,t,n,r,o){return function(i,s){var a=n,l=t,u=0;i.subscribe(Ot(s,function(c){var f=u++;l=a?e(l,c,f):(a=!0,c)},function(){a&&s.next(l),s.complete()}))}}function IE(e,t){return yt(kE(e,t,arguments.length>=2,!1,!0))}var LE=function(e,t){return e.push(t),e};function NE(){return yt(function(e,t){IE(LE,[])(e).subscribe(t)})}function fc(e){return yt(function(t,n){ln(e).subscribe(Ot(n,function(){return n.complete()},rs)),!n.closed&&t.subscribe(n)})}var Bl={exports:{}},Na,nd;function PE(){if(nd)return Na;nd=1;var e=1e3,t=e*60,n=t*60,r=n*24,o=r*7,i=r*365.25;Na=function(c,f){f=f||{};var d=typeof c;if(d==="string"&&c.length>0)return s(c);if(d==="number"&&isFinite(c))return f.long?l(c):a(c);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(c))};function s(c){if(c=String(c),!(c.length>100)){var f=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(f){var d=parseFloat(f[1]),m=(f[2]||"ms").toLowerCase();switch(m){case"years":case"year":case"yrs":case"yr":case"y":return d*i;case"weeks":case"week":case"w":return d*o;case"days":case"day":case"d":return d*r;case"hours":case"hour":case"hrs":case"hr":case"h":return d*n;case"minutes":case"minute":case"mins":case"min":case"m":return d*t;case"seconds":case"second":case"secs":case"sec":case"s":return d*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return d;default:return}}}}function a(c){var f=Math.abs(c);return f>=r?Math.round(c/r)+"d":f>=n?Math.round(c/n)+"h":f>=t?Math.round(c/t)+"m":f>=e?Math.round(c/e)+"s":c+"ms"}function l(c){var f=Math.abs(c);return f>=r?u(c,f,r,"day"):f>=n?u(c,f,n,"hour"):f>=t?u(c,f,t,"minute"):f>=e?u(c,f,e,"second"):c+" ms"}function u(c,f,d,m){var y=f>=d*1.5;return Math.round(c/d)+" "+m+(y?"s":"")}return Na}function bE(e){n.debug=n,n.default=n,n.coerce=l,n.disable=s,n.enable=o,n.enabled=a,n.humanize=PE(),n.destroy=u,Object.keys(e).forEach(c=>{n[c]=e[c]}),n.names=[],n.skips=[],n.formatters={};function t(c){let f=0;for(let d=0;d<c.length;d++)f=(f<<5)-f+c.charCodeAt(d),f|=0;return n.colors[Math.abs(f)%n.colors.length]}n.selectColor=t;function n(c){let f,d=null,m,y;function v(...w){if(!v.enabled)return;const h=v,p=Number(new Date),g=p-(f||p);h.diff=g,h.prev=f,h.curr=p,f=p,w[0]=n.coerce(w[0]),typeof w[0]!="string"&&w.unshift("%O");let S=0;w[0]=w[0].replace(/%([a-zA-Z%])/g,(T,L)=>{if(T==="%%")return"%";S++;const P=n.formatters[L];if(typeof P=="function"){const K=w[S];T=P.call(h,K),w.splice(S,1),S--}return T}),n.formatArgs.call(h,w),(h.log||n.log).apply(h,w)}return v.namespace=c,v.useColors=n.useColors(),v.color=n.selectColor(c),v.extend=r,v.destroy=n.destroy,Object.defineProperty(v,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==n.namespaces&&(m=n.namespaces,y=n.enabled(c)),y),set:w=>{d=w}}),typeof n.init=="function"&&n.init(v),v}function r(c,f){const d=n(this.namespace+(typeof f>"u"?":":f)+c);return d.log=this.log,d}function o(c){n.save(c),n.namespaces=c,n.names=[],n.skips=[];const f=(typeof c=="string"?c:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const d of f)d[0]==="-"?n.skips.push(d.slice(1)):n.names.push(d)}function i(c,f){let d=0,m=0,y=-1,v=0;for(;d<c.length;)if(m<f.length&&(f[m]===c[d]||f[m]==="*"))f[m]==="*"?(y=m,v=d,m++):(d++,m++);else if(y!==-1)m=y+1,v++,d=v;else return!1;for(;m<f.length&&f[m]==="*";)m++;return m===f.length}function s(){const c=[...n.names,...n.skips.map(f=>"-"+f)].join(",");return n.enable(""),c}function a(c){for(const f of n.skips)if(i(c,f))return!1;for(const f of n.names)if(i(c,f))return!0;return!1}function l(c){return c instanceof Error?c.stack||c.message:c}function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}var OE=bE;(function(e,t){var n={};t.formatArgs=o,t.save=i,t.load=s,t.useColors=r,t.storage=a(),t.destroy=(()=>{let u=!1;return()=>{u||(u=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function r(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let u;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(u=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(u[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function o(u){if(u[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+u[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const c="color: "+this.color;u.splice(1,0,c,"color: inherit");let f=0,d=0;u[0].replace(/%[a-zA-Z%]/g,m=>{m!=="%%"&&(f++,m==="%c"&&(d=f))}),u.splice(d,0,c)}t.log=console.debug||console.log||(()=>{});function i(u){try{u?t.storage.setItem("debug",u):t.storage.removeItem("debug")}catch{}}function s(){let u;try{u=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch{}return!u&&typeof process<"u"&&"env"in process&&(u=n.DEBUG),u}function a(){try{return localStorage}catch{}}e.exports=OE(t);const{formatters:l}=e.exports;l.j=function(u){try{return JSON.stringify(u)}catch(c){return"[UnexpectedJSONParseError]: "+c.message}}})(Bl,Bl.exports);var ME=Bl.exports;const Sm=ip(ME),rd=Sm("claude:ipc");class RE{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(fc(this.onDestroy)).subscribe(t)}}function AE(e){return new Ne(t=>e.subscribe(t))}function FE(e){return e==null?!1:typeof e=="object"&&"then"in e}function DE(e){return e!==null&&(typeof e=="object"||typeof e=="function")&&typeof e.subscribe=="function"}let Pa={};function jE(e,t,n,r){var s,a;const o=HE(e,t,n,r),i=_m(t);return(a=(s=Pa[e])==null?void 0:s.subscription)==null||a.unsubscribe(),Pa[e]={subscription:o,metadata:i},o.add(()=>delete Pa[e]),o}function HE(e,t,n,r){return AE(n).pipe(Hl(o=>o.methodChain.split(".")[0]===e),Hl(o=>r(o)?!0:(console.error(`Invalid message received: ${JSON.stringify(o)}`),!1)),cc(o=>{const i=o.methodChain.split(".").splice(1),s=i.pop(),a=i.reduce((f,d)=>f[d],t),l=o.customSendMethod??n.next.bind(n);let u;try{const f=a[s];rd('Calling method "%s" with args %o',s,o.argList),u=f.call(a,...o.argList)}catch(f){return rd(`Error in API call for message: %o
%o`,o,f),Ii({sendMethod:l,result:{error:f,callId:o.callId}})}let c=Ii(u);return FE(u)?c=gm(u):DE(u)&&(c=u.pipe(NE())),c.pipe(ym(f=>({sendMethod:l,result:{result:f,callId:o.callId}})),Em(f=>Ii({sendMethod:l,result:{result:null,callId:o.callId,error:f}})))}),fc(n.onDestroy)).subscribe({next:o=>o.sendMethod(o.result),error:o=>{console.error(`Error in API Handler - this should not happen! ${o}
${o.stack}`)}})}function _m(e){return BE(e).reduce((t,n)=>(typeof e[n]=="function"&&(t[n]=!0),typeof e[n]=="object"&&e[n]!==null&&(t[n]=_m(e[n])),t),{})}function BE(e){const t=Object.keys(e),n=Object.getOwnPropertyNames(e).filter(s=>!t.includes(s)),r=[];let o=Object.getPrototypeOf(e);for(;o&&o!==Object.prototype;)Object.getOwnPropertyNames(o).filter(s=>!["constructor"].includes(s)).forEach(s=>{!r.includes(s)&&!t.includes(s)&&!n.includes(s)&&r.push(s)}),o=Object.getPrototypeOf(o);const i=Object.getOwnPropertySymbols(e).map(s=>s.toString());return[...t,...n,...r,...i]}function UE(){return""}function $E(e){return new Ne(t=>e.subscribe(t))}const wm=5e3,zE=Sm("claude:ipc");function GE(e,t,n=wm){return Ul.create(e,(r,o)=>WE(t,r.join("."),o,n))}let VE=0;function WE(e,t,n,r=wm){const o=++VE,i={sender:{},callId:`${o}`,methodChain:t,argList:n};zE("sending message %o, stack: %s",i,UE());let s=SE($E(e).pipe(Hl(a=>a.callId===`${o}`),cc(a=>a.error?vE(()=>a.error):Ii(a.result)),xE(r)));return e.next(i),s}var cr,fr,dr,vn,ko,Ir,xm,Cm;const cs=class cs{constructor(t,n,r=null,o=null){fn(this,Ir);fn(this,cr);fn(this,fr);fn(this,dr);fn(this,vn);fn(this,ko,{});Ar(this,cr,t),Ar(this,fr,n),Ar(this,dr,r),Ar(this,vn,o)}static create(t,n,r=null){return new Proxy(()=>{},new cs(t,n,null,r))}get(t,n){return Ve(this,vn)&&n in Ve(this,vn)?Ve(this,vn)[n]:new Proxy(()=>{},Qs(this,Ir,xm).call(this,n))}apply(t,n,r){const o=[Qs(this,Ir,Cm).call(this,Ve(this,cr))];let i=Ve(this,dr);for(;i;)o.unshift(Ve(i,cr)),i=Ve(i,dr);return Ve(this,fr).call(this,o,r)}};cr=new WeakMap,fr=new WeakMap,dr=new WeakMap,vn=new WeakMap,ko=new WeakMap,Ir=new WeakSet,xm=function(t){let n=Ve(this,ko)[t];return n||(n=new cs(t.toString(),Ve(this,fr),this),Ve(this,ko)[t]=n,n)},Cm=function(t){return t.replace(/_get$/,"")};let Ul=cs;class ZE{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(fc(this.onDestroy)).subscribe(t)}}function Tm(){return new ZE({next:e=>window.rpcInternal.rpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new Ne(e=>window.rpcInternal.rpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),vm)}function XE(){return new RE({next:e=>window.rpcInternal.reverseRpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new Ne(e=>window.rpcInternal.reverseRpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),vm)}function YE(e,t){return jE(e,t,window.replyPort,()=>!0)}window.sendPort=Tm();window.replyPort=XE();var dc=(e=>(e.QuickWindow="QuickWindow",e.Find="Find",e.StartupSettings="StartupSettings",e.Filesystem="Filesystem",e.Intl="Intl",e.IntlSync="IntlSync",e.AboutWindow="AboutWindow",e.WindowControl="WindowControl",e))(dc||{});function km(e){const[t,n]=b.useState(window.initialLocale),[r,o]=b.useState(window.initialMessages);return b.useEffect(()=>{const i=YE(dc.Intl,{localeChanged:(s,a)=>{n(s),o(a)}});return()=>i.unsubscribe()},[n,o]),E.jsx(A2,{locale:t,messages:r,...e})}async function QE(e,t,n){const r=await t,o="default"in r?r.default:r,i=Ju(e),s=n??{};return i.render(E.jsx(km,{children:E.jsx(o,{...s})})),()=>{i.unmount()}}window.attachReactToElement=QE;const Im=Object.prototype.toString;function pc(e){switch(Im.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Nn(e,Error)}}function Or(e,t){return Im.call(e)===`[object ${t}]`}function Lm(e){return Or(e,"ErrorEvent")}function od(e){return Or(e,"DOMError")}function KE(e){return Or(e,"DOMException")}function kt(e){return Or(e,"String")}function hc(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function mc(e){return e===null||hc(e)||typeof e!="object"&&typeof e!="function"}function wr(e){return Or(e,"Object")}function Hs(e){return typeof Event<"u"&&Nn(e,Event)}function qE(e){return typeof Element<"u"&&Nn(e,Element)}function JE(e){return Or(e,"RegExp")}function Bs(e){return!!(e&&e.then&&typeof e.then=="function")}function eS(e){return wr(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function Nn(e,t){try{return e instanceof t}catch{return!1}}function Nm(e){return!!(typeof e=="object"&&e!==null&&(e.__isVue||e._isVue))}function ur(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function id(e,t){if(!Array.isArray(e))return"";const n=[];for(let r=0;r<e.length;r++){const o=e[r];try{Nm(o)?n.push("[VueViewModel]"):n.push(String(o))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function tS(e,t,n=!1){return kt(e)?JE(t)?t.test(e):kt(t)?n?e===t:e.includes(t):!1:!1}function Us(e,t=[],n=!1){return t.some(r=>tS(e,r,n))}function nS(e,t,n=250,r,o,i,s){if(!i.exception||!i.exception.values||!s||!Nn(s.originalException,Error))return;const a=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;a&&(i.exception.values=rS($l(e,t,o,s.originalException,r,i.exception.values,a,0),n))}function $l(e,t,n,r,o,i,s,a){if(i.length>=n+1)return i;let l=[...i];if(Nn(r[o],Error)){sd(s,a);const u=e(t,r[o]),c=l.length;ad(u,o,c,a),l=$l(e,t,n,r[o],o,[u,...l],u,c)}return Array.isArray(r.errors)&&r.errors.forEach((u,c)=>{if(Nn(u,Error)){sd(s,a);const f=e(t,u),d=l.length;ad(f,`errors[${c}]`,d,a),l=$l(e,t,n,u,o,[f,...l],f,d)}}),l}function sd(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function ad(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function rS(e,t){return e.map(n=>(n.value&&(n.value=ur(n.value,t)),n))}function Pm(e){if(e!==void 0)return e>=400&&e<500?"warning":e>=500?"error":void 0}const _n="8.33.1",B=globalThis;function $s(e,t,n){const r=n||B,o=r.__SENTRY__=r.__SENTRY__||{},i=o[_n]=o[_n]||{};return i[e]||(i[e]=t())}const gc=B,oS=80;function bm(e,t={}){if(!e)return"<unknown>";try{let n=e;const r=5,o=[];let i=0,s=0;const a=" > ",l=a.length;let u;const c=Array.isArray(t)?t:t.keyAttrs,f=!Array.isArray(t)&&t.maxStringLength||oS;for(;n&&i++<r&&(u=iS(n,c),!(u==="html"||i>1&&s+o.length*l+u.length>=f));)o.push(u),s+=u.length,n=n.parentNode;return o.reverse().join(a)}catch{return"<unknown>"}}function iS(e,t){const n=e,r=[];if(!n||!n.tagName)return"";if(gc.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const o=t&&t.length?t.filter(s=>n.getAttribute(s)).map(s=>[s,n.getAttribute(s)]):null;if(o&&o.length)o.forEach(s=>{r.push(`[${s[0]}="${s[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const s=n.className;if(s&&kt(s)){const a=s.split(/\s+/);for(const l of a)r.push(`.${l}`)}}const i=["aria-label","type","name","title","alt"];for(const s of i){const a=n.getAttribute(s);a&&r.push(`[${s}="${a}"]`)}return r.join("")}function sS(){try{return gc.document.location.href}catch{return""}}function aS(e){if(!gc.HTMLElement)return null;let t=e;const n=5;for(let r=0;r<n;r++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}const Ro=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,lS="Sentry Logger ",zl=["debug","info","warn","error","log","assert","trace"],is={};function Ao(e){if(!("console"in B))return e();const t=B.console,n={},r=Object.keys(is);r.forEach(o=>{const i=is[o];n[o]=t[o],t[o]=i});try{return e()}finally{r.forEach(o=>{t[o]=n[o]})}}function uS(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return Ro?zl.forEach(n=>{t[n]=(...r)=>{e&&Ao(()=>{B.console[n](`${lS}[${n}]:`,...r)})}}):zl.forEach(n=>{t[n]=()=>{}}),t}const O=$s("logger",uS),cS=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function fS(e){return e==="http"||e==="https"}function zs(e,t=!1){const{host:n,path:r,pass:o,port:i,projectId:s,protocol:a,publicKey:l}=e;return`${a}://${l}${t&&o?`:${o}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${s}`}function dS(e){const t=cS.exec(e);if(!t){Ao(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});return}const[n,r,o="",i="",s="",a=""]=t.slice(1);let l="",u=a;const c=u.split("/");if(c.length>1&&(l=c.slice(0,-1).join("/"),u=c.pop()),u){const f=u.match(/^\d+/);f&&(u=f[0])}return Om({host:i,pass:o,path:l,projectId:u,port:s,protocol:n,publicKey:r})}function Om(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function pS(e){if(!Ro)return!0;const{port:t,projectId:n,protocol:r}=e;return["protocol","publicKey","host","projectId"].find(s=>e[s]?!1:(O.error(`Invalid Sentry Dsn: ${s} missing`),!0))?!1:n.match(/^\d+$/)?fS(r)?t&&isNaN(parseInt(t,10))?(O.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):!0:(O.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(O.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function hS(e){const t=typeof e=="string"?dS(e):Om(e);if(!(!t||!pS(t)))return t}class pt extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}function De(e,t,n){if(!(t in e))return;const r=e[t],o=n(r);typeof o=="function"&&Mm(o,r),e[t]=o}function Pn(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{Ro&&O.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function Mm(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,Pn(e,"__sentry_original__",t)}catch{}}function yc(e){return e.__sentry_original__}function mS(e){return Object.keys(e).map(t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`).join("&")}function Rm(e){if(pc(e))return{message:e.message,name:e.name,stack:e.stack,...ud(e)};if(Hs(e)){const t={type:e.type,target:ld(e.target),currentTarget:ld(e.currentTarget),...ud(e)};return typeof CustomEvent<"u"&&Nn(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function ld(e){try{return qE(e)?bm(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function ud(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}else return{}}function gS(e,t=40){const n=Object.keys(Rm(e));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return ur(r,t);for(let o=n.length;o>0;o--){const i=n.slice(0,o).join(", ");if(!(i.length>t))return o===n.length?i:ur(i,t)}return""}function je(e){return Gl(e,new Map)}function Gl(e,t){if(yS(e)){const n=t.get(e);if(n!==void 0)return n;const r={};t.set(e,r);for(const o of Object.getOwnPropertyNames(e))typeof e[o]<"u"&&(r[o]=Gl(e[o],t));return r}if(Array.isArray(e)){const n=t.get(e);if(n!==void 0)return n;const r=[];return t.set(e,r),e.forEach(o=>{r.push(Gl(o,t))}),r}return e}function yS(e){if(!wr(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||t==="Object"}catch{return!0}}const Am=50,tn="?",cd=/\(error: (.*)\)/,fd=/captureMessage|captureException/;function Fm(...e){const t=e.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,o=0)=>{const i=[],s=n.split(`
`);for(let a=r;a<s.length;a++){const l=s[a];if(l.length>1024)continue;const u=cd.test(l)?l.replace(cd,"$1"):l;if(!u.match(/\S*Error: /)){for(const c of t){const f=c(u);if(f){i.push(f);break}}if(i.length>=Am+o)break}}return Dm(i.slice(o))}}function vS(e){return Array.isArray(e)?Fm(...e):e}function Dm(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test(ai(t).function||"")&&t.pop(),t.reverse(),fd.test(ai(t).function||"")&&(t.pop(),fd.test(ai(t).function||"")&&t.pop()),t.slice(0,Am).map(n=>({...n,filename:n.filename||ai(t).filename,function:n.function||tn}))}function ai(e){return e[e.length-1]||{}}const ba="<anonymous>";function nn(e){try{return!e||typeof e!="function"?ba:e.name||ba}catch{return ba}}function dd(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Li={},pd={};function jn(e,t){Li[e]=Li[e]||[],Li[e].push(t)}function Hn(e,t){pd[e]||(t(),pd[e]=!0)}function lt(e,t){const n=e&&Li[e];if(n)for(const r of n)try{r(t)}catch(o){Ro&&O.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${nn(r)}
Error:`,o)}}function ES(e){const t="console";jn(t,e),Hn(t,SS)}function SS(){"console"in B&&zl.forEach(function(e){e in B.console&&De(B.console,e,function(t){return is[e]=t,function(...n){lt("console",{args:n,level:e});const o=is[e];o&&o.apply(B.console,n)}})})}const Vl=B;function jm(){if(!("fetch"in Vl))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Wl(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function _S(){if(typeof EdgeRuntime=="string")return!0;if(!jm())return!1;if(Wl(Vl.fetch))return!0;let e=!1;const t=Vl.document;if(t&&typeof t.createElement=="function")try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=Wl(n.contentWindow.fetch)),t.head.removeChild(n)}catch(n){Ro&&O.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return e}const Hm=1e3;function Fo(){return Date.now()/Hm}function wS(){const{performance:e}=B;if(!e||!e.now)return Fo;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/Hm}const It=wS();(()=>{const{performance:e}=B;if(!e||!e.now)return;const t=3600*1e3,n=e.now(),r=Date.now(),o=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,i=o<t,s=e.timing&&e.timing.navigationStart,l=typeof s=="number"?Math.abs(s+n-r):t,u=l<t;return i||u?o<=l?e.timeOrigin:s:r})();function xS(e,t){const n="fetch";jn(n,e),Hn(n,()=>CS(void 0,t))}function CS(e,t=!1){t&&!_S()||De(B,"fetch",function(n){return function(...r){const{method:o,url:i}=TS(r),s={args:r,fetchData:{method:o,url:i},startTimestamp:It()*1e3};lt("fetch",{...s});const a=new Error().stack;return n.apply(B,r).then(async l=>(lt("fetch",{...s,endTimestamp:It()*1e3,response:l}),l),l=>{throw lt("fetch",{...s,endTimestamp:It()*1e3,error:l}),pc(l)&&l.stack===void 0&&(l.stack=a,Pn(l,"framesToPop",1)),l})}})}function Zl(e,t){return!!e&&typeof e=="object"&&!!e[t]}function hd(e){return typeof e=="string"?e:e?Zl(e,"url")?e.url:e.toString?e.toString():"":""}function TS(e){if(e.length===0)return{method:"GET",url:""};if(e.length===2){const[n,r]=e;return{url:hd(n),method:Zl(r,"method")?String(r.method).toUpperCase():"GET"}}const t=e[0];return{url:hd(t),method:Zl(t,"method")?String(t.method).toUpperCase():"GET"}}let li=null;function kS(e){const t="error";jn(t,e),Hn(t,IS)}function IS(){li=B.onerror,B.onerror=function(e,t,n,r,o){return lt("error",{column:r,error:o,line:n,msg:e,url:t}),li&&!li.__SENTRY_LOADER__?li.apply(this,arguments):!1},B.onerror.__SENTRY_INSTRUMENTED__=!0}let ui=null;function LS(e){const t="unhandledrejection";jn(t,e),Hn(t,NS)}function NS(){ui=B.onunhandledrejection,B.onunhandledrejection=function(e){return lt("unhandledrejection",e),ui&&!ui.__SENTRY_LOADER__?ui.apply(this,arguments):!0},B.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function PS(){return"npm"}function bS(){const e=typeof WeakSet=="function",t=e?new WeakSet:[];function n(o){if(e)return t.has(o)?!0:(t.add(o),!1);for(let i=0;i<t.length;i++)if(t[i]===o)return!0;return t.push(o),!1}function r(o){if(e)t.delete(o);else for(let i=0;i<t.length;i++)if(t[i]===o){t.splice(i,1);break}}return[n,r]}function Be(){const e=B,t=e.crypto||e.msCrypto;let n=()=>Math.random()*16;try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(n=()=>{const r=new Uint8Array(1);return t.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function Bm(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function jt(e){const{message:t,event_id:n}=e;if(t)return t;const r=Bm(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Xl(e,t,n){const r=e.exception=e.exception||{},o=r.values=r.values||[],i=o[0]=o[0]||{};i.value||(i.value=t||""),i.type||(i.type="Error")}function To(e,t){const n=Bm(e);if(!n)return;const r={type:"generic",handled:!0},o=n.mechanism;if(n.mechanism={...r,...o,...t},t&&"data"in t){const i={...o&&o.data,...t.data};n.mechanism.data=i}}function md(e){if(e&&e.__sentry_captured__)return!0;try{Pn(e,"__sentry_captured__",!0)}catch{}return!1}function Um(e){return Array.isArray(e)?e:[e]}function wt(e,t=100,n=1/0){try{return Yl("",e,t,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function $m(e,t=3,n=100*1024){const r=wt(e,t);return AS(r)>n?$m(e,t-1,n):r}function Yl(e,t,n=1/0,r=1/0,o=bS()){const[i,s]=o;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const a=OS(e,t);if(!a.startsWith("[object "))return a;if(t.__sentry_skip_normalization__)return t;const l=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(l===0)return a.replace("object ","");if(i(t))return"[Circular ~]";const u=t;if(u&&typeof u.toJSON=="function")try{const m=u.toJSON();return Yl("",m,l-1,r,o)}catch{}const c=Array.isArray(t)?[]:{};let f=0;const d=Rm(t);for(const m in d){if(!Object.prototype.hasOwnProperty.call(d,m))continue;if(f>=r){c[m]="[MaxProperties ~]";break}const y=d[m];c[m]=Yl(m,y,l-1,r,o),f++}return s(t),c}function OS(e,t){try{if(e==="domain"&&t&&typeof t=="object"&&t._events)return"[Domain]";if(e==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&t===global)return"[Global]";if(typeof window<"u"&&t===window)return"[Window]";if(typeof document<"u"&&t===document)return"[Document]";if(Nm(t))return"[VueViewModel]";if(eS(t))return"[SyntheticEvent]";if(typeof t=="number"&&!Number.isFinite(t))return`[${t}]`;if(typeof t=="function")return`[Function: ${nn(t)}]`;if(typeof t=="symbol")return`[${String(t)}]`;if(typeof t=="bigint")return`[BigInt: ${String(t)}]`;const n=MS(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function MS(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}function RS(e){return~-encodeURI(e).split(/%..|./).length}function AS(e){return RS(JSON.stringify(e))}var St;(function(e){e[e.PENDING=0]="PENDING";const n=1;e[e.RESOLVED=n]="RESOLVED";const r=2;e[e.REJECTED=r]="REJECTED"})(St||(St={}));function bn(e){return new Xe(t=>{t(e)})}function ss(e){return new Xe((t,n)=>{n(e)})}class Xe{constructor(t){Xe.prototype.__init.call(this),Xe.prototype.__init2.call(this),Xe.prototype.__init3.call(this),Xe.prototype.__init4.call(this),this._state=St.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(n){this._reject(n)}}then(t,n){return new Xe((r,o)=>{this._handlers.push([!1,i=>{if(!t)r(i);else try{r(t(i))}catch(s){o(s)}},i=>{if(!n)o(i);else try{r(n(i))}catch(s){o(s)}}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new Xe((n,r)=>{let o,i;return this.then(s=>{i=!1,o=s,t&&t()},s=>{i=!0,o=s,t&&t()}).then(()=>{if(i){r(o);return}n(o)})})}__init(){this._resolve=t=>{this._setResult(St.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(St.REJECTED,t)}}__init3(){this._setResult=(t,n)=>{if(this._state===St.PENDING){if(Bs(n)){n.then(this._resolve,this._reject);return}this._state=t,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===St.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===St.RESOLVED&&n[1](this._value),this._state===St.REJECTED&&n[2](this._value),n[0]=!0)})}}}function FS(e){const t=[];function n(){return e===void 0||t.length<e}function r(s){return t.splice(t.indexOf(s),1)[0]||Promise.resolve(void 0)}function o(s){if(!n())return ss(new pt("Not adding Promise because buffer limit was reached."));const a=s();return t.indexOf(a)===-1&&t.push(a),a.then(()=>r(a)).then(null,()=>r(a).then(null,()=>{})),a}function i(s){return new Xe((a,l)=>{let u=t.length;if(!u)return a(!0);const c=setTimeout(()=>{s&&s>0&&a(!1)},s);t.forEach(f=>{bn(f).then(()=>{--u||(clearTimeout(c),a(!0))},l)})})}return{$:t,add:o,drain:i}}function Oa(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}const DS=["fatal","error","warning","log","info","debug"];function jS(e){return e==="warn"?"warning":DS.includes(e)?e:"log"}function HS(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&e!==void 0&&!e.includes("node_modules/")}function BS(e){const t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{const o=r.match(n);if(o){let i,s,a,l,u;if(o[1]){a=o[1];let d=a.lastIndexOf(".");if(a[d-1]==="."&&d--,d>0){i=a.slice(0,d),s=a.slice(d+1);const m=i.indexOf(".Module");m>0&&(a=a.slice(m+1),i=i.slice(0,m))}l=void 0}s&&(l=i,u=s),s==="<anonymous>"&&(u=void 0,a=void 0),a===void 0&&(u=u||tn,a=l?`${l}.${u}`:u);let c=o[2]&&o[2].startsWith("file://")?o[2].slice(7):o[2];const f=o[5]==="native";return c&&c.match(/\/[A-Z]:/)&&(c=c.slice(1)),!c&&o[5]&&!f&&(c=o[5]),{filename:c,module:void 0,function:a,lineno:gd(o[3]),colno:gd(o[4]),in_app:HS(c||"",f)}}if(r.match(t))return{filename:r}}}function US(e){return[90,BS()]}function gd(e){return parseInt(e||"",10)||void 0}const $S="sentry-",zS=/^sentry-/;function GS(e){const t=VS(e);if(!t)return;const n=Object.entries(t).reduce((r,[o,i])=>{if(o.match(zS)){const s=o.slice($S.length);r[s]=i}return r},{});if(Object.keys(n).length>0)return n}function VS(e){if(!(!e||!kt(e)&&!Array.isArray(e)))return Array.isArray(e)?e.reduce((t,n)=>{const r=yd(n);return Object.entries(r).forEach(([o,i])=>{t[o]=i}),t},{}):yd(e)}function yd(e){return e.split(",").map(t=>t.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((t,[n,r])=>(n&&r&&(t[n]=r),t),{})}function Do(e,t=[]){return[e,t]}function WS(e,t){const[n,r]=e;return[n,[...r,t]]}function vd(e,t){const n=e[1];for(const r of n){const o=r[0].type;if(t(r,o))return!0}return!1}function Ql(e){return B.__SENTRY__&&B.__SENTRY__.encodePolyfill?B.__SENTRY__.encodePolyfill(e):new TextEncoder().encode(e)}function ZS(e){const[t,n]=e;let r=JSON.stringify(t);function o(i){typeof r=="string"?r=typeof i=="string"?r+i:[Ql(r),i]:r.push(typeof i=="string"?Ql(i):i)}for(const i of n){const[s,a]=i;if(o(`
${JSON.stringify(s)}
`),typeof a=="string"||a instanceof Uint8Array)o(a);else{let l;try{l=JSON.stringify(a)}catch{l=JSON.stringify(wt(a))}o(l)}}return typeof r=="string"?r:XS(r)}function XS(e){const t=e.reduce((o,i)=>o+i.length,0),n=new Uint8Array(t);let r=0;for(const o of e)n.set(o,r),r+=o.length;return n}function YS(e){const t=typeof e.data=="string"?Ql(e.data):e.data;return[je({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const QS={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function Ed(e){return QS[e]}function zm(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function KS(e,t,n,r){const o=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:zs(r)},...o&&{trace:je({...o})}}}function qS(e,t,n){const r=[{type:"client_report"},{timestamp:Fo(),discarded_events:e}];return Do(t?{dsn:t}:{},[r])}const JS=60*1e3;function e_(e,t=Date.now()){const n=parseInt(`${e}`,10);if(!isNaN(n))return n*1e3;const r=Date.parse(`${e}`);return isNaN(r)?JS:r-t}function t_(e,t){return e[t]||e.all||0}function n_(e,t,n=Date.now()){return t_(e,t)>n}function r_(e,{statusCode:t,headers:n},r=Date.now()){const o={...e},i=n&&n["x-sentry-rate-limits"],s=n&&n["retry-after"];if(i)for(const a of i.trim().split(",")){const[l,u,,,c]=a.split(":",5),f=parseInt(l,10),d=(isNaN(f)?60:f)*1e3;if(!u)o.all=r+d;else for(const m of u.split(";"))m==="metric_bucket"?(!c||c.split(";").includes("custom"))&&(o[m]=r+d):o[m]=r+d}else s?o.all=r+e_(s,r):t===429&&(o.all=r+60*1e3);return o}function Sd(){return{traceId:Be(),spanId:Be().substring(16)}}const ci=B;function o_(){const e=ci.chrome,t=e&&e.app&&e.app.runtime,n="history"in ci&&!!ci.history.pushState&&!!ci.history.replaceState;return!t&&n}const G=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Gs(){return vc(B),B}function vc(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||_n,t[_n]=t[_n]||{}}function i_(e){const t=It(),n={sid:Be(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>a_(n)};return e&&xr(n,e),n}function xr(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||It(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:Be()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function s_(e,t){let n={};e.status==="ok"&&(n={status:"exited"}),xr(e,n)}function a_(e){return je({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}const Kl="_sentrySpan";function _d(e,t){t?Pn(e,Kl,t):delete e[Kl]}function wd(e){return e[Kl]}const l_=100;class Ec{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=Sd()}clone(){const t=new Ec;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,_d(t,wd(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&xr(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,[r,o]=n instanceof On?[n.getScopeData(),n.getRequestSession()]:wr(n)?[t,t.requestSession]:[],{tags:i,extra:s,user:a,contexts:l,level:u,fingerprint:c=[],propagationContext:f}=r||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...s},this._contexts={...this._contexts,...l},a&&Object.keys(a).length&&(this._user=a),u&&(this._level=u),c.length&&(this._fingerprint=c),f&&(this._propagationContext=f),o&&(this._requestSession=o),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,_d(this,void 0),this._attachments=[],this._propagationContext=Sd(),this._notifyScopeListeners(),this}addBreadcrumb(t,n){const r=typeof n=="number"?n:l_;if(r<=0)return this;const o={timestamp:Fo(),...t},i=this._breadcrumbs;return i.push(o),this._breadcrumbs=i.length>r?i.slice(-r):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:wd(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,n){const r=n&&n.event_id?n.event_id:Be();if(!this._client)return O.warn("No client configured on scope - will not capture exception!"),r;const o=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:o,...n,event_id:r},this),r}captureMessage(t,n,r){const o=r&&r.event_id?r.event_id:Be();if(!this._client)return O.warn("No client configured on scope - will not capture message!"),o;const i=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:i,...r,event_id:o},this),o}captureEvent(t,n){const r=n&&n.event_id?n.event_id:Be();return this._client?(this._client.captureEvent(t,{...n,event_id:r},this),r):(O.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}const On=Ec;function u_(){return $s("defaultCurrentScope",()=>new On)}function c_(){return $s("defaultIsolationScope",()=>new On)}class f_{constructor(t,n){let r;t?r=t:r=new On;let o;n?o=n:o=new On,this._stack=[{scope:r}],this._isolationScope=o}withScope(t){const n=this._pushScope();let r;try{r=t(n)}catch(o){throw this._popScope(),o}return Bs(r)?r.then(o=>(this._popScope(),o),o=>{throw this._popScope(),o}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function Cr(){const e=Gs(),t=vc(e);return t.stack=t.stack||new f_(u_(),c_())}function d_(e){return Cr().withScope(e)}function p_(e,t){const n=Cr();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function xd(e){return Cr().withScope(()=>e(Cr().getIsolationScope()))}function h_(){return{withIsolationScope:xd,withScope:d_,withSetScope:p_,withSetIsolationScope:(e,t)=>xd(t),getCurrentScope:()=>Cr().getScope(),getIsolationScope:()=>Cr().getIsolationScope()}}function Sc(e){const t=vc(e);return t.acs?t.acs:h_()}function vt(){const e=Gs();return Sc(e).getCurrentScope()}function Bn(){const e=Gs();return Sc(e).getIsolationScope()}function m_(){return $s("globalScope",()=>new On)}function g_(...e){const t=Gs(),n=Sc(t);if(e.length===2){const[r,o]=e;return r?n.withSetScope(r,o):n.withScope(o)}return n.withScope(e[0])}function Se(){return vt().getClient()}const y_="_sentryMetrics";function v_(e){const t=e[y_];if(!t)return;const n={};for(const[,[r,o]]of t)(n[r]||(n[r]=[])).push(je(o));return n}const E_="sentry.source",S_="sentry.sample_rate",__="sentry.op",w_="sentry.origin",x_=0,C_=1,T_=1;function k_(e){const{spanId:t,traceId:n}=e.spanContext(),{parent_span_id:r}=as(e);return je({parent_span_id:r,span_id:t,trace_id:n})}function Cd(e){return typeof e=="number"?Td(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?Td(e.getTime()):It()}function Td(e){return e>9999999999?e/1e3:e}function as(e){if(L_(e))return e.getSpanJSON();try{const{spanId:t,traceId:n}=e.spanContext();if(I_(e)){const{attributes:r,startTime:o,name:i,endTime:s,parentSpanId:a,status:l}=e;return je({span_id:t,trace_id:n,data:r,description:i,parent_span_id:a,start_timestamp:Cd(o),timestamp:Cd(s)||void 0,status:P_(l),op:r[__],origin:r[w_],_metrics_summary:v_(e)})}return{span_id:t,trace_id:n}}catch{return{}}}function I_(e){const t=e;return!!t.attributes&&!!t.startTime&&!!t.name&&!!t.endTime&&!!t.status}function L_(e){return typeof e.getSpanJSON=="function"}function N_(e){const{traceFlags:t}=e.spanContext();return t===T_}function P_(e){if(!(!e||e.code===x_))return e.code===C_?"ok":e.message||"unknown_error"}const b_="_sentryRootSpan";function Gm(e){return e[b_]||e}function O_(e){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=Se(),n=t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const _c="production",M_="_frozenDsc";function Vm(e,t){const n=t.getOptions(),{publicKey:r}=t.getDsn()||{},o=je({environment:n.environment||_c,release:n.release,public_key:r,trace_id:e});return t.emit("createDsc",o),o}function R_(e){const t=Se();if(!t)return{};const n=Vm(as(e).trace_id||"",t),r=Gm(e),o=r[M_];if(o)return o;const i=r.spanContext().traceState,s=i&&i.get("sentry.dsc"),a=s&&GS(s);if(a)return a;const l=as(r),u=l.data||{},c=u[S_];c!=null&&(n.sample_rate=`${c}`);const f=u[E_],d=l.description;return f!=="url"&&d&&(n.transaction=d),O_()&&(n.sampled=String(N_(r))),t.emit("createDsc",n,r),n}function A_(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;if(typeof t!="number"||isNaN(t)||t<0||t>1){G&&O.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`);return}return t}function F_(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function D_(e,t,n,r){const o=zm(n),i={sent_at:new Date().toISOString(),...o&&{sdk:o},...!!r&&t&&{dsn:zs(t)}},s="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return Do(i,[s])}function j_(e,t,n,r){const o=zm(n),i=e.type&&e.type!=="replay_event"?e.type:"event";F_(e,n&&n.sdk);const s=KS(e,o,r,t);return delete e.sdkProcessingMetadata,Do(s,[[{type:i},e]])}function ql(e,t,n,r=0){return new Xe((o,i)=>{const s=e[r];if(t===null||typeof s!="function")o(t);else{const a=s({...t},n);G&&s.id&&a===null&&O.log(`Event processor "${s.id}" dropped event`),Bs(a)?a.then(l=>ql(e,l,n,r+1).then(o)).then(null,i):ql(e,a,n,r+1).then(o).then(null,i)}})}function H_(e,t){const{fingerprint:n,span:r,breadcrumbs:o,sdkProcessingMetadata:i}=t;B_(e,t),r&&z_(e,r),G_(e,n),U_(e,o),$_(e,i)}function Jl(e,t){const{extra:n,tags:r,user:o,contexts:i,level:s,sdkProcessingMetadata:a,breadcrumbs:l,fingerprint:u,eventProcessors:c,attachments:f,propagationContext:d,transactionName:m,span:y}=t;Gr(e,"extra",n),Gr(e,"tags",r),Gr(e,"user",o),Gr(e,"contexts",i),Gr(e,"sdkProcessingMetadata",a),s&&(e.level=s),m&&(e.transactionName=m),y&&(e.span=y),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),f.length&&(e.attachments=[...e.attachments,...f]),e.propagationContext={...e.propagationContext,...d}}function Gr(e,t,n){if(n&&Object.keys(n).length){e[t]={...e[t]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[t][r]=n[r])}}function B_(e,t){const{extra:n,tags:r,user:o,contexts:i,level:s,transactionName:a}=t,l=je(n);l&&Object.keys(l).length&&(e.extra={...l,...e.extra});const u=je(r);u&&Object.keys(u).length&&(e.tags={...u,...e.tags});const c=je(o);c&&Object.keys(c).length&&(e.user={...c,...e.user});const f=je(i);f&&Object.keys(f).length&&(e.contexts={...f,...e.contexts}),s&&(e.level=s),a&&e.type!=="transaction"&&(e.transaction=a)}function U_(e,t){const n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}function $_(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}function z_(e,t){e.contexts={trace:k_(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:R_(t),...e.sdkProcessingMetadata};const n=Gm(t),r=as(n).description;r&&!e.transaction&&e.type==="transaction"&&(e.transaction=r)}function G_(e,t){e.fingerprint=e.fingerprint?Um(e.fingerprint):[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}function V_(e,t,n,r,o,i){const{normalizeDepth:s=3,normalizeMaxBreadth:a=1e3}=e,l={...t,event_id:t.event_id||n.event_id||Be(),timestamp:t.timestamp||Fo()},u=n.integrations||e.integrations.map(w=>w.name);W_(l,e),Y_(l,u),o&&o.emit("applyFrameMetadata",t),t.type===void 0&&Z_(l,e.stackParser);const c=K_(r,n.captureContext);n.mechanism&&To(l,n.mechanism);const f=o?o.getEventProcessors():[],d=m_().getScopeData();if(i){const w=i.getScopeData();Jl(d,w)}if(c){const w=c.getScopeData();Jl(d,w)}const m=[...n.attachments||[],...d.attachments];m.length&&(n.attachments=m),H_(l,d);const y=[...f,...d.eventProcessors];return ql(y,l,n).then(w=>(w&&X_(w),typeof s=="number"&&s>0?Q_(w,s,a):w))}function W_(e,t){const{environment:n,release:r,dist:o,maxValueLength:i=250}=t;"environment"in e||(e.environment="environment"in t?n:_c),e.release===void 0&&r!==void 0&&(e.release=r),e.dist===void 0&&o!==void 0&&(e.dist=o),e.message&&(e.message=ur(e.message,i));const s=e.exception&&e.exception.values&&e.exception.values[0];s&&s.value&&(s.value=ur(s.value,i));const a=e.request;a&&a.url&&(a.url=ur(a.url,i))}const kd=new WeakMap;function Z_(e,t){const n=B._sentryDebugIds;if(!n)return;let r;const o=kd.get(t);o?r=o:(r=new Map,kd.set(t,r));const i=Object.entries(n).reduce((s,[a,l])=>{let u;const c=r.get(a);c?u=c:(u=t(a),r.set(a,u));for(let f=u.length-1;f>=0;f--){const d=u[f];if(d.filename){s[d.filename]=l;break}}return s},{});try{e.exception.values.forEach(s=>{s.stacktrace.frames.forEach(a=>{a.filename&&(a.debug_id=i[a.filename])})})}catch{}}function X_(e){const t={};try{e.exception.values.forEach(r=>{r.stacktrace.frames.forEach(o=>{o.debug_id&&(o.abs_path?t[o.abs_path]=o.debug_id:o.filename&&(t[o.filename]=o.debug_id),delete o.debug_id)})})}catch{}if(Object.keys(t).length===0)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.entries(t).forEach(([r,o])=>{n.push({type:"sourcemap",code_file:r,debug_id:o})})}function Y_(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}function Q_(e,t,n){if(!e)return null;const r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(o=>({...o,...o.data&&{data:wt(o.data,t,n)}}))},...e.user&&{user:wt(e.user,t,n)},...e.contexts&&{contexts:wt(e.contexts,t,n)},...e.extra&&{extra:wt(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=wt(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(o=>({...o,...o.data&&{data:wt(o.data,t,n)}}))),r}function K_(e,t){if(!t)return e;const n=e?e.clone():new On;return n.update(t),n}function q_(e,t){return vt().captureException(e,void 0)}function Wm(e,t){return vt().captureEvent(e,t)}function Id(e){const t=Se(),n=Bn(),r=vt(),{release:o,environment:i=_c}=t&&t.getOptions()||{},{userAgent:s}=B.navigator||{},a=i_({release:o,environment:i,user:r.getUser()||n.getUser(),...s&&{userAgent:s},...e}),l=n.getSession();return l&&l.status==="ok"&&xr(l,{status:"exited"}),Zm(),n.setSession(a),r.setSession(a),a}function Zm(){const e=Bn(),t=vt(),n=t.getSession()||e.getSession();n&&s_(n),Xm(),e.setSession(),t.setSession()}function Xm(){const e=Bn(),t=vt(),n=Se(),r=t.getSession()||e.getSession();r&&n&&n.captureSession(r)}function Ld(e=!1){if(e){Zm();return}Xm()}const J_="7";function ew(e){const t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function tw(e){return`${ew(e)}${e.projectId}/envelope/`}function nw(e,t){return mS({sentry_key:e.publicKey,sentry_version:J_,...t&&{sentry_client:`${t.name}/${t.version}`}})}function rw(e,t,n){return t||`${tw(e)}?${nw(e,n)}`}const Nd=[];function ow(e){const t={};return e.forEach(n=>{const{name:r}=n,o=t[r];o&&!o.isDefaultInstance&&n.isDefaultInstance||(t[r]=n)}),Object.values(t)}function iw(e){const t=e.defaultIntegrations||[],n=e.integrations;t.forEach(s=>{s.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...t,...n]:typeof n=="function"?r=Um(n(t)):r=t;const o=ow(r),i=o.findIndex(s=>s.name==="Debug");if(i>-1){const[s]=o.splice(i,1);o.push(s)}return o}function sw(e,t){const n={};return t.forEach(r=>{r&&Ym(e,r,n)}),n}function Pd(e,t){for(const n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function Ym(e,t,n){if(n[t.name]){G&&O.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,Nd.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),Nd.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(o,i)=>r(o,i,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),o=Object.assign((i,s)=>r(i,s,e),{id:t.name});e.addEventProcessor(o)}G&&O.log(`Integration installed: ${t.name}`)}const bd="Not capturing exception because it's already been captured.";class aw{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=hS(t.dsn):G&&O.warn("No DSN provided, client will not send events."),this._dsn){const n=rw(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const o=Be();if(md(t))return G&&O.log(bd),o;const i={event_id:o,...n};return this._process(this.eventFromException(t,i).then(s=>this._captureEvent(s,i,r))),i.event_id}captureMessage(t,n,r,o){const i={event_id:Be(),...r},s=hc(t)?t:String(t),a=mc(t)?this.eventFromMessage(s,n,i):this.eventFromException(t,i);return this._process(a.then(l=>this._captureEvent(l,i,o))),i.event_id}captureEvent(t,n,r){const o=Be();if(n&&n.originalException&&md(n.originalException))return G&&O.log(bd),o;const i={event_id:o,...n},a=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,i,a||r)),i.event_id}captureSession(t){typeof t.release!="string"?G&&O.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),xr(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(o=>r&&o))):bn(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];Ym(this,t,this._integrations),n||Pd(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=j_(t,this._dsn,this._options._metadata,this._options.tunnel);for(const i of n.attachments||[])r=WS(r,YS(i));const o=this.sendEnvelope(r);o&&o.then(i=>this.emit("afterSendEvent",t,i),null)}sendSession(t){const n=D_(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(t,n,r){if(this._options.sendClientReports){const o=typeof r=="number"?r:1,i=`${t}:${n}`;G&&O.log(`Recording outcome: "${i}"${o>1?` (${o} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+o}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const o=r.indexOf(n);o>-1&&r.splice(o,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(o=>o(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(G&&O.error("Error while sending event:",n),n)):(G&&O.error("Transport disabled"),bn({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=sw(this,t),Pd(this,t)}_updateSessionFromEvent(t,n){let r=!1,o=!1;const i=n.exception&&n.exception.values;if(i){o=!0;for(const l of i){const u=l.mechanism;if(u&&u.handled===!1){r=!0;break}}}const s=t.status==="ok";(s&&t.errors===0||s&&r)&&(xr(t,{...r&&{status:"crashed"},errors:t.errors||Number(o||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new Xe(n=>{let r=0;const o=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=o,t&&r>=t&&(clearInterval(i),n(!1)))},o)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,o=Bn()){const i=this.getOptions(),s=Object.keys(this._integrations);return!n.integrations&&s.length>0&&(n.integrations=s),this.emit("preprocessEvent",t,n),t.type||o.setLastEventId(t.event_id||n.event_id),V_(i,t,n,r,this,o).then(a=>{if(a===null)return a;const l={...o.getPropagationContext(),...r?r.getPropagationContext():void 0};if(!(a.contexts&&a.contexts.trace)&&l){const{traceId:c,spanId:f,parentSpanId:d,dsc:m}=l;a.contexts={trace:je({trace_id:c,span_id:f,parent_span_id:d}),...a.contexts};const y=m||Vm(c,this);a.sdkProcessingMetadata={dynamicSamplingContext:y,...a.sdkProcessingMetadata}}return a})}_captureEvent(t,n={},r){return this._processEvent(t,n,r).then(o=>o.event_id,o=>{if(G){const i=o;i.logLevel==="log"?O.log(i.message):O.warn(i)}})}_processEvent(t,n,r){const o=this.getOptions(),{sampleRate:i}=o,s=Km(t),a=Qm(t),l=t.type||"error",u=`before send for type \`${l}\``,c=typeof i>"u"?void 0:A_(i);if(a&&typeof c=="number"&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",t),ss(new pt(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));const f=l==="replay_event"?"replay":l,m=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,n,r,m).then(y=>{if(y===null)throw this.recordDroppedEvent("event_processor",f,t),new pt("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return y;const w=uw(this,o,y,n);return lw(w,u)}).then(y=>{if(y===null){if(this.recordDroppedEvent("before_send",f,t),s){const p=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",p)}throw new pt(`${u} returned \`null\`, will not send event.`,"log")}const v=r&&r.getSession();if(!s&&v&&this._updateSessionFromEvent(v,y),s){const h=y.sdkProcessingMetadata&&y.sdkProcessingMetadata.spanCountBeforeProcessing||0,p=y.spans?y.spans.length:0,g=h-p;g>0&&this.recordDroppedEvent("before_send","span",g)}const w=y.transaction_info;if(s&&w&&y.transaction!==t.transaction){const h="custom";y.transaction_info={...w,source:h}}return this.sendEvent(y,n),y}).then(null,y=>{throw y instanceof pt?y:(this.captureException(y,{data:{__sentry__:!0},originalException:y}),new pt(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${y}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[o,i]=n.split(":");return{reason:o,category:i,quantity:r}})}_flushOutcomes(){G&&O.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0){G&&O.log("No outcomes to send");return}if(!this._dsn){G&&O.log("No dsn provided, will not send outcomes");return}G&&O.log("Sending outcomes:",t);const n=qS(t,this._options.tunnel&&zs(this._dsn));this.sendEnvelope(n)}}function lw(e,t){const n=`${t} must return \`null\` or a valid event.`;if(Bs(e))return e.then(r=>{if(!wr(r)&&r!==null)throw new pt(n);return r},r=>{throw new pt(`${t} rejected with ${r}`)});if(!wr(e)&&e!==null)throw new pt(n);return e}function uw(e,t,n,r){const{beforeSend:o,beforeSendTransaction:i,beforeSendSpan:s}=t;if(Qm(n)&&o)return o(n,r);if(Km(n)){if(n.spans&&s){const a=[];for(const l of n.spans){const u=s(l);u?a.push(u):e.recordDroppedEvent("before_send","span")}n.spans=a}if(i){if(n.spans){const a=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:a}}return i(n,r)}}return n}function Qm(e){return e.type===void 0}function Km(e){return e.type==="transaction"}function cw(e,t){t.debug===!0&&(G?O.enable():Ao(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),vt().update(t.initialScope);const r=new e(t);return fw(r),r.init(),r}function fw(e){vt().setClient(e)}const dw=64;function qm(e,t,n=FS(e.bufferSize||dw)){let r={};const o=s=>n.drain(s);function i(s){const a=[];if(vd(s,(f,d)=>{const m=Ed(d);if(n_(r,m)){const y=Od(f,d);e.recordDroppedEvent("ratelimit_backoff",m,y)}else a.push(f)}),a.length===0)return bn({});const l=Do(s[0],a),u=f=>{vd(l,(d,m)=>{const y=Od(d,m);e.recordDroppedEvent(f,Ed(m),y)})},c=()=>t({body:ZS(l)}).then(f=>(f.statusCode!==void 0&&(f.statusCode<200||f.statusCode>=300)&&G&&O.warn(`Sentry responded with status code ${f.statusCode} to sent event.`),r=r_(r,f),f),f=>{throw u("network_error"),f});return n.add(c).then(f=>f,f=>{if(f instanceof pt)return G&&O.error("Skipped sending event because buffer is full."),u("queue_overflow"),bn({});throw f})}return{send:i,flush:o}}function Od(e,t){if(!(t!=="event"&&t!=="transaction"))return Array.isArray(e)?e[1]:void 0}function pw(e,t,n=[t],r="npm"){const o=e._metadata||{};o.sdk||(o.sdk={name:`sentry.javascript.${t}`,packages:n.map(i=>({name:`${r}:@sentry/${i}`,version:_n})),version:_n}),e._metadata=o}const hw=100;function Mn(e,t){const n=Se(),r=Bn();if(!n)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:i=hw}=n.getOptions();if(i<=0)return;const a={timestamp:Fo(),...e},l=o?Ao(()=>o(a,t)):a;l!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",l,t),r.addBreadcrumb(l,i))}let Md;const mw="FunctionToString",Rd=new WeakMap,gw=()=>({name:mw,setupOnce(){Md=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=yc(this),n=Rd.has(Se())&&t!==void 0?t:this;return Md.apply(n,e)}}catch{}},setup(e){Rd.set(e,!0)}}),yw=gw,vw=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"],Ew="InboundFilters",Sw=(e={})=>({name:Ew,processEvent(t,n,r){const o=r.getOptions(),i=ww(e,o);return xw(t,i)?null:t}}),_w=Sw;function ww(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:vw],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function xw(e,t){return t.ignoreInternal&&Nw(e)?(G&&O.warn(`Event dropped due to being internal Sentry Error.
Event: ${jt(e)}`),!0):Cw(e,t.ignoreErrors)?(G&&O.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${jt(e)}`),!0):bw(e)?(G&&O.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${jt(e)}`),!0):Tw(e,t.ignoreTransactions)?(G&&O.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${jt(e)}`),!0):kw(e,t.denyUrls)?(G&&O.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${jt(e)}.
Url: ${ls(e)}`),!0):Iw(e,t.allowUrls)?!1:(G&&O.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${jt(e)}.
Url: ${ls(e)}`),!0)}function Cw(e,t){return e.type||!t||!t.length?!1:Lw(e).some(n=>Us(n,t))}function Tw(e,t){if(e.type!=="transaction"||!t||!t.length)return!1;const n=e.transaction;return n?Us(n,t):!1}function kw(e,t){if(!t||!t.length)return!1;const n=ls(e);return n?Us(n,t):!1}function Iw(e,t){if(!t||!t.length)return!0;const n=ls(e);return n?Us(n,t):!0}function Lw(e){const t=[];e.message&&t.push(e.message);let n;try{n=e.exception.values[e.exception.values.length-1]}catch{}return n&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`)),t}function Nw(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function Pw(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function ls(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?Pw(t):null}catch{return G&&O.error(`Cannot extract url for event ${jt(e)}`),null}}function bw(e){return e.type||!e.exception||!e.exception.values||e.exception.values.length===0?!1:!e.message&&!e.exception.values.some(t=>t.stacktrace||t.type&&t.type!=="Error"||t.value)}const Ow="Dedupe",Mw=()=>{let e;return{name:Ow,processEvent(t){if(t.type)return t;try{if(Aw(t,e))return G&&O.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}},Rw=Mw;function Aw(e,t){return t?!!(Fw(e,t)||Dw(e,t)):!1}function Fw(e,t){const n=e.message,r=t.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!eg(e,t)||!Jm(e,t))}function Dw(e,t){const n=Ad(t),r=Ad(e);return!(!n||!r||n.type!==r.type||n.value!==r.value||!eg(e,t)||!Jm(e,t))}function Jm(e,t){let n=dd(e),r=dd(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let o=0;o<r.length;o++){const i=r[o],s=n[o];if(i.filename!==s.filename||i.lineno!==s.lineno||i.colno!==s.colno||i.function!==s.function)return!1}return!0}function eg(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function Ad(e){return e.exception&&e.exception.values&&e.exception.values[0]}const V=B;let eu=0;function tg(){return eu>0}function jw(){eu++,setTimeout(()=>{eu--})}function Tr(e,t={},n){if(typeof e!="function")return e;try{const o=e.__sentry_wrapped__;if(o)return typeof o=="function"?o:e;if(yc(e))return e}catch{return e}const r=function(){const o=Array.prototype.slice.call(arguments);try{const i=o.map(s=>Tr(s,t));return e.apply(this,i)}catch(i){throw jw(),g_(s=>{s.addEventProcessor(a=>(t.mechanism&&(Xl(a,void 0),To(a,t.mechanism)),a.extra={...a.extra,arguments:o},a)),q_(i)}),i}};try{for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o])}catch{}Mm(r,e),Pn(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return e.name}})}catch{}return r}const jo=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function wc(e,t){const n=xc(e,t),r={type:t&&t.name,value:zw(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Hw(e,t,n,r){const o=Se(),i=o&&o.getOptions().normalizeDepth,s=Xw(t),a={__serialized__:$m(t,i)};if(s)return{exception:{values:[wc(e,s)]},extra:a};const l={exception:{values:[{type:Hs(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:Ww(t,{isUnhandledRejection:r})}]},extra:a};if(n){const u=xc(e,n);u.length&&(l.exception.values[0].stacktrace={frames:u})}return l}function Ma(e,t){return{exception:{values:[wc(e,t)]}}}function xc(e,t){const n=t.stacktrace||t.stack||"",r=Uw(t),o=$w(t);try{return e(n,r,o)}catch{}return[]}const Bw=/Minified React error #\d+;/i;function Uw(e){return e&&Bw.test(e.message)?1:0}function $w(e){return typeof e.framesToPop=="number"?e.framesToPop:0}function zw(e){const t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function Gw(e,t,n,r){const o=n&&n.syntheticException||void 0,i=Cc(e,t,o,r);return To(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),bn(i)}function Vw(e,t,n="info",r,o){const i=r&&r.syntheticException||void 0,s=tu(e,t,i,o);return s.level=n,r&&r.event_id&&(s.event_id=r.event_id),bn(s)}function Cc(e,t,n,r,o){let i;if(Lm(t)&&t.error)return Ma(e,t.error);if(od(t)||KE(t)){const s=t;if("stack"in t)i=Ma(e,t);else{const a=s.name||(od(s)?"DOMError":"DOMException"),l=s.message?`${a}: ${s.message}`:a;i=tu(e,l,n,r),Xl(i,l)}return"code"in s&&(i.tags={...i.tags,"DOMException.code":`${s.code}`}),i}return pc(t)?Ma(e,t):wr(t)||Hs(t)?(i=Hw(e,t,n,o),To(i,{synthetic:!0}),i):(i=tu(e,t,n,r),Xl(i,`${t}`),To(i,{synthetic:!0}),i)}function tu(e,t,n,r){const o={};if(r&&n){const i=xc(e,n);i.length&&(o.exception={values:[{value:t,stacktrace:{frames:i}}]})}if(hc(t)){const{__sentry_template_string__:i,__sentry_template_values__:s}=t;return o.logentry={message:i,params:s},o}return o.message=t,o}function Ww(e,{isUnhandledRejection:t}){const n=gS(e),r=t?"promise rejection":"exception";return Lm(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:Hs(e)?`Event \`${Zw(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function Zw(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch{}}function Xw(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const n=e[t];if(n instanceof Error)return n}}function Yw(e,{metadata:t,tunnel:n,dsn:r}){const o={event_id:e.event_id,sent_at:new Date().toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!r&&{dsn:zs(r)}},i=Qw(e);return Do(o,[i])}function Qw(e){return[{type:"user_report"},e]}class Kw extends aw{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t},r=V.SENTRY_SDK_SOURCE||PS();pw(n,"browser",["browser"],r),super(n),n.sendClientReports&&V.document&&V.document.addEventListener("visibilitychange",()=>{V.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,n){return Gw(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return Vw(this._options.stackParser,t,n,r,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled()){jo&&O.warn("SDK not enabled, will not capture user feedback.");return}const n=Yw(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}_prepareEvent(t,n,r){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r)}}const qw=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,ve=B,Jw=1e3;let Fd,nu,ru;function ex(e){const t="dom";jn(t,e),Hn(t,tx)}function tx(){if(!ve.document)return;const e=lt.bind(null,"dom"),t=Dd(e,!0);ve.document.addEventListener("click",t,!1),ve.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{const r=ve[n]&&ve[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(De(r,"addEventListener",function(o){return function(i,s,a){if(i==="click"||i=="keypress")try{const l=this,u=l.__sentry_instrumentation_handlers__=l.__sentry_instrumentation_handlers__||{},c=u[i]=u[i]||{refCount:0};if(!c.handler){const f=Dd(e);c.handler=f,o.call(this,i,f,a)}c.refCount++}catch{}return o.call(this,i,s,a)}}),De(r,"removeEventListener",function(o){return function(i,s,a){if(i==="click"||i=="keypress")try{const l=this,u=l.__sentry_instrumentation_handlers__||{},c=u[i];c&&(c.refCount--,c.refCount<=0&&(o.call(this,i,c.handler,a),c.handler=void 0,delete u[i]),Object.keys(u).length===0&&delete l.__sentry_instrumentation_handlers__)}catch{}return o.call(this,i,s,a)}}))})}function nx(e){if(e.type!==nu)return!1;try{if(!e.target||e.target._sentryId!==ru)return!1}catch{}return!0}function rx(e,t){return e!=="keypress"?!1:!t||!t.tagName?!0:!(t.tagName==="INPUT"||t.tagName==="TEXTAREA"||t.isContentEditable)}function Dd(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=ox(n);if(rx(n.type,r))return;Pn(n,"_sentryCaptured",!0),r&&!r._sentryId&&Pn(r,"_sentryId",Be());const o=n.type==="keypress"?"input":n.type;nx(n)||(e({event:n,name:o,global:t}),nu=n.type,ru=r?r._sentryId:void 0),clearTimeout(Fd),Fd=ve.setTimeout(()=>{ru=void 0,nu=void 0},Jw)}}function ox(e){try{return e.target}catch{return null}}let fi;function ng(e){const t="history";jn(t,e),Hn(t,ix)}function ix(){if(!o_())return;const e=ve.onpopstate;ve.onpopstate=function(...n){const r=ve.location.href,o=fi;if(fi=r,lt("history",{from:o,to:r}),e)try{return e.apply(this,n)}catch{}};function t(n){return function(...r){const o=r.length>2?r[2]:void 0;if(o){const i=fi,s=String(o);fi=s,lt("history",{from:i,to:s})}return n.apply(this,r)}}De(ve.history,"pushState",t),De(ve.history,"replaceState",t)}const Ni={};function sx(e){const t=Ni[e];if(t)return t;let n=ve[e];if(Wl(n))return Ni[e]=n.bind(ve);const r=ve.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o);const i=o.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(o)}catch(o){qw&&O.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,o)}return n&&(Ni[e]=n.bind(ve))}function jd(e){Ni[e]=void 0}const Qr="__sentry_xhr_v3__";function ax(e){const t="xhr";jn(t,e),Hn(t,lx)}function lx(){if(!ve.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const o=It()*1e3,i=kt(r[0])?r[0].toUpperCase():void 0,s=ux(r[1]);if(!i||!s)return t.apply(n,r);n[Qr]={method:i,url:s,request_headers:{}},i==="POST"&&s.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const a=()=>{const l=n[Qr];if(l&&n.readyState===4){try{l.status_code=n.status}catch{}const u={endTimestamp:It()*1e3,startTimestamp:o,xhr:n};lt("xhr",u)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(l,u,c){return a(),l.apply(u,c)}}):n.addEventListener("readystatechange",a),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(l,u,c){const[f,d]=c,m=u[Qr];return m&&kt(f)&&kt(d)&&(m.request_headers[f.toLowerCase()]=d),l.apply(u,c)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const o=n[Qr];if(!o)return t.apply(n,r);r[0]!==void 0&&(o.body=r[0]);const i={startTimestamp:It()*1e3,xhr:n};return lt("xhr",i),t.apply(n,r)}})}function ux(e){if(kt(e))return e;try{return e.toString()}catch{}}function cx(e,t=sx("fetch")){let n=0,r=0;function o(i){const s=i.body.length;n+=s,r++;const a={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return jd("fetch"),ss("No fetch implementation available");try{return t(e.url,a).then(l=>(n-=s,r--,{statusCode:l.status,headers:{"x-sentry-rate-limits":l.headers.get("X-Sentry-Rate-Limits"),"retry-after":l.headers.get("Retry-After")}}))}catch(l){return jd("fetch"),n-=s,r--,ss(l)}}return qm(e,o)}const fx=30,dx=50;function ou(e,t,n,r){const o={filename:e,function:t==="<anonymous>"?tn:t,in_app:!0};return n!==void 0&&(o.lineno=n),r!==void 0&&(o.colno=r),o}const px=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,hx=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,mx=/\((\S*)(?::(\d+))(?::(\d+))\)/,gx=e=>{const t=px.exec(e);if(t){const[,r,o,i]=t;return ou(r,tn,+o,+i)}const n=hx.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const s=mx.exec(n[2]);s&&(n[2]=s[1],n[3]=s[2],n[4]=s[3])}const[o,i]=og(n[1]||tn,n[2]);return ou(i,o,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},rg=[fx,gx],yx=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,vx=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Ex=e=>{const t=yx.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const i=vx.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}let r=t[3],o=t[1]||tn;return[o,r]=og(o,r),ou(r,o,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},Sx=[dx,Ex],_x=[rg,Sx],wx=Fm(..._x),og=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:tn,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},di=1024,xx="Breadcrumbs",Cx=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:xx,setup(n){t.console&&ES(Lx(n)),t.dom&&ex(Ix(n,t.dom)),t.xhr&&ax(Nx(n)),t.fetch&&xS(Px(n)),t.history&&ng(bx(n)),t.sentry&&n.on("beforeSendEvent",kx(n))}}},Tx=Cx;function kx(e){return function(n){Se()===e&&Mn({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:jt(n)},{event:n})}}function Ix(e,t){return function(r){if(Se()!==e)return;let o,i,s=typeof t=="object"?t.serializeAttribute:void 0,a=typeof t=="object"&&typeof t.maxStringLength=="number"?t.maxStringLength:void 0;a&&a>di&&(jo&&O.warn(`\`dom.maxStringLength\` cannot exceed ${di}, but a value of ${a} was configured. Sentry will use ${di} instead.`),a=di),typeof s=="string"&&(s=[s]);try{const u=r.event,c=Ox(u)?u.target:u;o=bm(c,{keyAttrs:s,maxStringLength:a}),i=aS(c)}catch{o="<unknown>"}if(o.length===0)return;const l={category:`ui.${r.name}`,message:o};i&&(l.data={"ui.component_name":i}),Mn(l,{event:r.event,name:r.name,global:r.global})}}function Lx(e){return function(n){if(Se()!==e)return;const r={category:"console",data:{arguments:n.args,logger:"console"},level:jS(n.level),message:id(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)r.message=`Assertion failed: ${id(n.args.slice(1)," ")||"console.assert"}`,r.data.arguments=n.args.slice(1);else return;Mn(r,{input:n.args,level:n.level})}}function Nx(e){return function(n){if(Se()!==e)return;const{startTimestamp:r,endTimestamp:o}=n,i=n.xhr[Qr];if(!r||!o||!i)return;const{method:s,url:a,status_code:l,body:u}=i,c={method:s,url:a,status_code:l},f={xhr:n.xhr,input:u,startTimestamp:r,endTimestamp:o},d=Pm(l);Mn({category:"xhr",data:c,type:"http",level:d},f)}}function Px(e){return function(n){if(Se()!==e)return;const{startTimestamp:r,endTimestamp:o}=n;if(o&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.error){const i=n.fetchData,s={data:n.error,input:n.args,startTimestamp:r,endTimestamp:o};Mn({category:"fetch",data:i,level:"error",type:"http"},s)}else{const i=n.response,s={...n.fetchData,status_code:i&&i.status},a={input:n.args,response:i,startTimestamp:r,endTimestamp:o},l=Pm(s.status_code);Mn({category:"fetch",data:s,type:"http",level:l},a)}}}function bx(e){return function(n){if(Se()!==e)return;let r=n.from,o=n.to;const i=Oa(V.location.href);let s=r?Oa(r):void 0;const a=Oa(o);(!s||!s.path)&&(s=i),i.protocol===a.protocol&&i.host===a.host&&(o=a.relative),i.protocol===s.protocol&&i.host===s.host&&(r=s.relative),Mn({category:"navigation",data:{from:r,to:o}})}}function Ox(e){return!!e&&!!e.target}const Mx=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Rx="BrowserApiErrors",Ax=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:Rx,setupOnce(){t.setTimeout&&De(V,"setTimeout",Hd),t.setInterval&&De(V,"setInterval",Hd),t.requestAnimationFrame&&De(V,"requestAnimationFrame",Dx),t.XMLHttpRequest&&"XMLHttpRequest"in V&&De(XMLHttpRequest.prototype,"send",jx);const n=t.eventTarget;n&&(Array.isArray(n)?n:Mx).forEach(Hx)}}},Fx=Ax;function Hd(e){return function(...t){const n=t[0];return t[0]=Tr(n,{mechanism:{data:{function:nn(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function Dx(e){return function(t){return e.apply(this,[Tr(t,{mechanism:{data:{function:"requestAnimationFrame",handler:nn(e)},handled:!1,type:"instrument"}})])}}function jx(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(o=>{o in n&&typeof n[o]=="function"&&De(n,o,function(i){const s={mechanism:{data:{function:o,handler:nn(i)},handled:!1,type:"instrument"}},a=yc(i);return a&&(s.mechanism.data.handler=nn(a)),Tr(i,s)})}),e.apply(this,t)}}function Hx(e){const t=V,n=t[e]&&t[e].prototype;!n||!n.hasOwnProperty||!n.hasOwnProperty("addEventListener")||(De(n,"addEventListener",function(r){return function(o,i,s){try{typeof i.handleEvent=="function"&&(i.handleEvent=Tr(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:nn(i),target:e},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[o,Tr(i,{mechanism:{data:{function:"addEventListener",handler:nn(i),target:e},handled:!1,type:"instrument"}}),s])}}),De(n,"removeEventListener",function(r){return function(o,i,s){const a=i;try{const l=a&&a.__sentry_wrapped__;l&&r.call(this,o,l,s)}catch{}return r.call(this,o,a,s)}}))}const Bx="GlobalHandlers",Ux=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:Bx,setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(zx(n),Bd("onerror")),t.onunhandledrejection&&(Gx(n),Bd("onunhandledrejection"))}}},$x=Ux;function zx(e){kS(t=>{const{stackParser:n,attachStacktrace:r}=ig();if(Se()!==e||tg())return;const{msg:o,url:i,line:s,column:a,error:l}=t,u=Zx(Cc(n,l||o,void 0,r,!1),i,s,a);u.level="error",Wm(u,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})}function Gx(e){LS(t=>{const{stackParser:n,attachStacktrace:r}=ig();if(Se()!==e||tg())return;const o=Vx(t),i=mc(o)?Wx(o):Cc(n,o,void 0,r,!0);i.level="error",Wm(i,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function Vx(e){if(mc(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}function Wx(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function Zx(e,t,n,r){const o=e.exception=e.exception||{},i=o.values=o.values||[],s=i[0]=i[0]||{},a=s.stacktrace=s.stacktrace||{},l=a.frames=a.frames||[],u=isNaN(parseInt(r,10))?void 0:r,c=isNaN(parseInt(n,10))?void 0:n,f=kt(t)&&t.length>0?t:sS();return l.length===0&&l.push({colno:u,filename:f,function:tn,in_app:!0,lineno:c}),e}function Bd(e){jo&&O.log(`Global Handler attached: ${e}`)}function ig(){const e=Se();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const Xx=()=>({name:"HttpContext",preprocessEvent(e){if(!V.navigator&&!V.location&&!V.document)return;const t=e.request&&e.request.url||V.location&&V.location.href,{referrer:n}=V.document||{},{userAgent:r}=V.navigator||{},o={...e.request&&e.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},i={...e.request,...t&&{url:t},headers:o};e.request=i}}),Yx="cause",Qx=5,Kx="LinkedErrors",qx=(e={})=>{const t=e.limit||Qx,n=e.key||Yx;return{name:Kx,preprocessEvent(r,o,i){const s=i.getOptions();nS(wc,s.stackParser,s.maxValueLength,n,t,r,o)}}},Jx=qx;function sg(e){return[_w(),yw(),Fx(),Tx(),$x(),Jx(),Rw(),Xx()]}function eC(e={}){const t={defaultIntegrations:sg(),release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:V.SENTRY_RELEASE&&V.SENTRY_RELEASE.id?V.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return e.defaultIntegrations==null&&delete e.defaultIntegrations,{...t,...e}}function tC(){const e=typeof V.window<"u"&&V;if(!e)return!1;const t=e.chrome?"chrome":"browser",n=e[t],r=n&&n.runtime&&n.runtime.id,o=V.location&&V.location.href||"",i=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],s=!!r&&V===V.top&&i.some(l=>o.startsWith(`${l}//`)),a=typeof e.nw<"u";return!!r&&!s&&!a}function nC(e={}){const t=eC(e);if(tC()){Ao(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});return}jo&&(jm()||O.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...t,stackParser:vS(t.stackParser||wx),integrations:iw(t),transport:t.transport||cx},r=cw(Kw,n);return t.autoSessionTracking&&rC(),r}function rC(){if(typeof V.document>"u"){jo&&O.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}Id({ignoreDuration:!0}),Ld(),ng(({from:e,to:t})=>{e!==void 0&&e!==t&&(Id({ignoreDuration:!0}),Ld())})}function Ud(){const e=Bn().getScopeData();return Jl(e,vt().getScopeData()),e.eventProcessors=[],e}function oC(e){Bn().addScopeListener(t=>{const n=Ud();e(n,t)}),vt().addScopeListener(t=>{const n=Ud();e(n,t)})}var $d;(function(e){e[e.Classic=1]="Classic",e[e.Protocol=2]="Protocol",e[e.Both=3]="Both"})($d||($d={}));const iC="sentry-ipc";var Ht;(function(e){e.RENDERER_START="sentry-electron.renderer-start",e.EVENT="sentry-electron.event",e.SCOPE="sentry-electron.scope",e.ENVELOPE="sentry-electron.envelope",e.STATUS="sentry-electron.status",e.ADD_METRIC="sentry-electron.add-metric"})(Ht||(Ht={}));const sC="sentry-electron-renderer-id";function $n(e){return`${iC}://${e}/sentry_key`}function aC(){if(window.__SENTRY_IPC__)return window.__SENTRY_IPC__;{O.log("IPC was not configured in preload script, falling back to custom protocol and fetch");const e=window.__SENTRY_RENDERER_ID__=Be(),t={[sC]:e};return{sendRendererStart:()=>{fetch($n(Ht.RENDERER_START),{method:"POST",body:"",headers:t}).catch(()=>{console.error(`Sentry SDK failed to establish connection with the Electron main process.
  - Ensure you have initialized the SDK in the main process
  - If your renderers use custom sessions, be sure to set 'getSessions' in the main process options
  - If you are bundling your main process code and using Electron < v5, you'll need to manually configure a preload script`)})},sendScope:n=>{fetch($n(Ht.SCOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEvent:n=>{fetch($n(Ht.EVENT),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEnvelope:n=>{fetch($n(Ht.ENVELOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendStatus:n=>{fetch($n(Ht.STATUS),{method:"POST",body:JSON.stringify({status:n}),headers:t}).catch(()=>{})},sendAddMetric:n=>{fetch($n(Ht.ADD_METRIC),{method:"POST",body:JSON.stringify(n),headers:t}).catch(()=>{})}}}}let pi;function Tc(){return pi||(pi=aC(),pi.sendRendererStart()),pi}const lC=()=>({name:"ScopeToMain",setup(){const e=Tc();oC((t,n)=>{e.sendScope(JSON.stringify(wt(t,20,2e3))),n.clearBreadcrumbs(),n.clearAttachments()})}});function uC(e){const t=Tc();return qm(e,async n=>(t.sendEnvelope(n.body),{statusCode:200}))}function cC(e){const t={pollInterval:1e3,anrThreshold:5e3,captureStackTrace:!1,...e},n=Tc();document.addEventListener("visibilitychange",()=>{n.sendStatus({status:document.visibilityState,config:t})}),n.sendStatus({status:document.visibilityState,config:t}),setInterval(()=>{n.sendStatus({status:"alive",config:t})},t.pollInterval)}const fC=50,[,dC]=rg,[,pC]=US(),hC=(e,t=0)=>{const n=[];for(const r of e.split(`
`).slice(t)){const o=dC(r),i=pC(r);if(o&&(i==null?void 0:i.in_app)!==!1?n.push(o):i&&n.push(je(i)),n.length>=fC)break}return Dm(n)};function mC(e){return[...sg(),lC()]}function gC(e={},t=nC){if(window!=null&&window.__SENTRY__RENDERER_INIT__){O.warn(`The browser SDK has already been initialized.
If init has been called in the preload and contextIsolation is disabled, is not required to call init in the renderer`);return}window.__SENTRY__RENDERER_INIT__=!0,e.autoSessionTracking===void 0&&(e.autoSessionTracking=!1),e.sendClientReports=!1,e.defaultIntegrations===void 0&&(e.defaultIntegrations=mC()),e.stackParser===void 0&&(e.stackParser=hC),e.dsn===void 0&&(e.dsn="https://<EMAIL>/12345"),e.transport===void 0&&(e.transport=uC),e.anrDetection&&cC(e.anrDetection===!0?{}:e.anrDetection),delete e.initialScope,t(e)}gC();window.mainProcess.titleBarApi.onUpdateDarkMode(e=>{document.body.className=e=="dark"?"darkTheme":""});function yC({width:e=16,height:t=16,style:n}){return E.jsx("svg",{width:e,height:t,style:n,viewBox:"0 0 248 248",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:E.jsx("path",{d:"M52.4285 162.873L98.7844 136.879L99.5485 134.602L98.7844 133.334H96.4921L88.7237 132.862L62.2346 132.153L39.3113 131.207L17.0249 130.026L11.4214 128.844L6.2 121.873L6.7094 118.447L11.4214 115.257L18.171 115.847L33.0711 116.911L55.485 118.447L71.6586 119.392L95.728 121.873H99.5485L100.058 120.337L98.7844 119.392L97.7656 118.447L74.5877 102.732L49.4995 86.1905L36.3823 76.62L29.3779 71.7757L25.8121 67.2858L24.2839 57.3608L30.6515 50.2716L39.3113 50.8623L41.4763 51.4531L50.2636 58.1879L68.9842 72.7209L93.4357 90.6804L97.0015 93.6343L98.4374 92.6652L98.6571 91.9801L97.0015 89.2625L83.757 65.2772L69.621 40.8192L63.2534 30.6579L61.5978 24.632C60.9565 22.1032 60.579 20.0111 60.579 17.4246L67.8381 7.49965L71.9133 6.19995L81.7193 7.49965L85.7946 11.0443L91.9074 24.9865L101.714 46.8451L116.996 76.62L121.453 85.4816L123.873 93.6343L124.764 96.1155H126.292V94.6976L127.566 77.9197L129.858 57.3608L132.15 30.8942L132.915 23.4505L136.608 14.4708L143.994 9.62643L149.725 12.344L154.437 19.0788L153.8 23.4505L150.998 41.6463L145.522 70.1215L141.957 89.2625H143.994L146.414 86.7813L156.093 74.0206L172.266 53.698L179.398 45.6635L187.803 36.802L193.152 32.5484H203.34L210.726 43.6549L207.415 55.1159L196.972 68.3492L188.312 79.5739L175.896 96.2095L168.191 109.585L168.882 110.689L170.738 110.53L198.755 104.504L213.91 101.787L231.994 98.7149L240.144 102.496L241.036 106.395L237.852 114.311L218.495 119.037L195.826 123.645L162.07 131.592L161.696 131.893L162.137 132.547L177.36 133.925L183.855 134.279H199.774L229.447 136.524L237.215 141.605L241.8 147.867L241.036 152.711L229.065 158.737L213.019 154.956L175.45 145.977L162.587 142.787H160.805V143.85L171.502 154.366L191.242 172.089L215.82 195.011L217.094 200.682L213.91 205.172L210.599 204.699L188.949 188.394L180.544 181.069L161.696 165.118H160.422V166.772L164.752 173.152L187.803 207.771L188.949 218.405L187.294 221.832L181.308 223.959L174.813 222.777L161.187 203.754L147.305 182.486L136.098 163.345L134.745 164.2L128.075 235.42L125.019 239.082L117.887 241.8L111.902 237.31L108.718 229.984L111.902 215.452L115.722 196.547L118.779 181.541L121.58 162.873L123.291 156.636L123.14 156.219L121.773 156.449L107.699 175.752L86.304 204.699L69.3663 222.777L65.291 224.431L58.2867 220.768L58.9235 214.27L62.8713 208.48L86.304 178.705L100.44 160.155L109.551 149.507L109.462 147.967L108.959 147.924L46.6977 188.512L35.6182 189.93L30.7788 185.44L31.4156 178.115L33.7079 175.752L52.4285 162.873Z",fill:"#D97757"})})}function vC({isMainWindow:e,windowTitle:t,titleBarHeight:n=e?sv:av}){if(!iv&&e)return null;const r=e?E.jsxs("div",{className:"items-center ms-3 flex nc-no-drag",style:{height:n},id:"app-icon-container",children:[E.jsx("svg",{id:"hamburger-menu",width:"16",height:"16",viewBox:"0 0 24 24",onClick:()=>{window.mainProcess.titleBarApi.requestMainMenuPopup()},fill:"none",xmlns:"http://www.w3.org/2000/svg",children:E.jsxs("g",{style:{stroke:"var(--claude-foreground-color)"},children:[E.jsx("path",{d:"M4 18L20 18",strokeWidth:"2",strokeLinecap:"round"}),E.jsx("path",{d:"M4 12L20 12",strokeWidth:"2",strokeLinecap:"round"}),E.jsx("path",{d:"M4 6L20 6",strokeWidth:"2",strokeLinecap:"round"})]})}),E.jsx(yC,{width:16,height:16,style:{marginLeft:12,height:16}})]}):null,o=E.jsx("div",{className:"flex flex-row items-center justify-center select-none nc-drag",style:{height:`${n}px`},children:E.jsx("h1",{className:"text-xs text-center self-center opacity-40 font-bold select-none",id:"titleBar",children:t})}),i=e?E.jsx(E.Fragment,{children:r}):E.jsx("div",{});return E.jsxs(E.Fragment,{children:[o,E.jsx("div",{className:"absolute top-0 left-0 right-0 flex flex-row items-center select-none nc-drag",style:{height:`${n+1}px`,borderBottom:"1px solid rgba(0,0,0,0.1)"},children:i})]})}var ag={exports:{}},Vr={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zd;function EC(){if(zd)return Vr;zd=1;var e=gp,t=Symbol.for("react.element"),n=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function s(a,l,u){var c,f={},d=null,m=null;u!==void 0&&(d=""+u),l.key!==void 0&&(d=""+l.key),l.ref!==void 0&&(m=l.ref);for(c in l)r.call(l,c)&&!i.hasOwnProperty(c)&&(f[c]=l[c]);if(a&&a.defaultProps)for(c in l=a.defaultProps,l)f[c]===void 0&&(f[c]=l[c]);return{$$typeof:t,type:a,key:d,ref:m,props:f,_owner:o.current}}return Vr.Fragment=n,Vr.jsx=s,Vr.jsxs=s,Vr}ag.exports=EC();var x=ag.exports;const SC=new Map([["bold",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M204,64V168a12,12,0,0,1-24,0V93L72.49,200.49a12,12,0,0,1-17-17L163,76H88a12,12,0,0,1,0-24H192A12,12,0,0,1,204,64Z"})})],["duotone",x.jsxs(x.Fragment,{children:[x.jsx("path",{d:"M192,64V168L88,64Z",opacity:"0.2"}),x.jsx("path",{d:"M192,56H88a8,8,0,0,0-5.66,13.66L128.69,116,58.34,186.34a8,8,0,0,0,11.32,11.32L140,127.31l46.34,46.35A8,8,0,0,0,200,168V64A8,8,0,0,0,192,56Zm-8,92.69-38.34-38.34h0L107.31,72H184Z"})]})],["fill",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M200,64V168a8,8,0,0,1-13.66,5.66L140,127.31,69.66,197.66a8,8,0,0,1-11.32-11.32L128.69,116,82.34,69.66A8,8,0,0,1,88,56H192A8,8,0,0,1,200,64Z"})})],["light",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M198,64V168a6,6,0,0,1-12,0V78.48L68.24,196.24a6,6,0,0,1-8.48-8.48L177.52,70H88a6,6,0,0,1,0-12H192A6,6,0,0,1,198,64Z"})})],["regular",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"})})],["thin",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M196,64V168a4,4,0,0,1-8,0V73.66L66.83,194.83a4,4,0,0,1-5.66-5.66L182.34,68H88a4,4,0,0,1,0-8H192A4,4,0,0,1,196,64Z"})})]]),_C=new Map([["bold",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M184.49,136.49l-80,80a12,12,0,0,1-17-17L159,128,87.51,56.49a12,12,0,1,1,17-17l80,80A12,12,0,0,1,184.49,136.49Z"})})],["duotone",x.jsxs(x.Fragment,{children:[x.jsx("path",{d:"M176,128,96,208V48Z",opacity:"0.2"}),x.jsx("path",{d:"M181.66,122.34l-80-80A8,8,0,0,0,88,48V208a8,8,0,0,0,13.66,5.66l80-80A8,8,0,0,0,181.66,122.34ZM104,188.69V67.31L164.69,128Z"})]})],["fill",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M181.66,133.66l-80,80A8,8,0,0,1,88,208V48a8,8,0,0,1,13.66-5.66l80,80A8,8,0,0,1,181.66,133.66Z"})})],["light",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M180.24,132.24l-80,80a6,6,0,0,1-8.48-8.48L167.51,128,91.76,52.24a6,6,0,0,1,8.48-8.48l80,80A6,6,0,0,1,180.24,132.24Z"})})],["regular",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"})})],["thin",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M178.83,130.83l-80,80a4,4,0,0,1-5.66-5.66L170.34,128,93.17,50.83a4,4,0,0,1,5.66-5.66l80,80A4,4,0,0,1,178.83,130.83Z"})})]]),wC=new Map([["bold",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M216,48H180V36A28,28,0,0,0,152,8H104A28,28,0,0,0,76,36V48H40a12,12,0,0,0,0,24h4V208a20,20,0,0,0,20,20H192a20,20,0,0,0,20-20V72h4a12,12,0,0,0,0-24ZM100,36a4,4,0,0,1,4-4h48a4,4,0,0,1,4,4V48H100Zm88,168H68V72H188ZM116,104v64a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm48,0v64a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Z"})})],["duotone",x.jsxs(x.Fragment,{children:[x.jsx("path",{d:"M200,56V208a8,8,0,0,1-8,8H64a8,8,0,0,1-8-8V56Z",opacity:"0.2"}),x.jsx("path",{d:"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96Zm96,168H64V64H192ZM112,104v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Z"})]})],["fill",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM112,168a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm0-120H96V40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8Z"})})],["light",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M216,50H174V40a22,22,0,0,0-22-22H104A22,22,0,0,0,82,40V50H40a6,6,0,0,0,0,12H50V208a14,14,0,0,0,14,14H192a14,14,0,0,0,14-14V62h10a6,6,0,0,0,0-12ZM94,40a10,10,0,0,1,10-10h48a10,10,0,0,1,10,10V50H94ZM194,208a2,2,0,0,1-2,2H64a2,2,0,0,1-2-2V62H194ZM110,104v64a6,6,0,0,1-12,0V104a6,6,0,0,1,12,0Zm48,0v64a6,6,0,0,1-12,0V104a6,6,0,0,1,12,0Z"})})],["regular",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96Zm96,168H64V64H192ZM112,104v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Z"})})],["thin",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M216,52H172V40a20,20,0,0,0-20-20H104A20,20,0,0,0,84,40V52H40a4,4,0,0,0,0,8H52V208a12,12,0,0,0,12,12H192a12,12,0,0,0,12-12V60h12a4,4,0,0,0,0-8ZM92,40a12,12,0,0,1,12-12h48a12,12,0,0,1,12,12V52H92ZM196,208a4,4,0,0,1-4,4H64a4,4,0,0,1-4-4V60H196ZM108,104v64a4,4,0,0,1-8,0V104a4,4,0,0,1,8,0Zm48,0v64a4,4,0,0,1-8,0V104a4,4,0,0,1,8,0Z"})})]]),xC=new Map([["bold",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M240.26,186.1,152.81,34.23h0a28.74,28.74,0,0,0-49.62,0L15.74,186.1a27.45,27.45,0,0,0,0,27.71A28.31,28.31,0,0,0,40.55,228h174.9a28.31,28.31,0,0,0,24.79-14.19A27.45,27.45,0,0,0,240.26,186.1Zm-20.8,15.7a4.46,4.46,0,0,1-4,2.2H40.55a4.46,4.46,0,0,1-4-2.2,3.56,3.56,0,0,1,0-3.73L124,46.2a4.77,4.77,0,0,1,8,0l87.44,151.87A3.56,3.56,0,0,1,219.46,201.8ZM116,136V104a12,12,0,0,1,24,0v32a12,12,0,0,1-24,0Zm28,40a16,16,0,1,1-16-16A16,16,0,0,1,144,176Z"})})],["duotone",x.jsxs(x.Fragment,{children:[x.jsx("path",{d:"M215.46,216H40.54C27.92,216,20,202.79,26.13,192.09L113.59,40.22c6.3-11,22.52-11,28.82,0l87.46,151.87C236,202.79,228.08,216,215.46,216Z",opacity:"0.2"}),x.jsx("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"})]})],["fill",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM120,104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm8,88a12,12,0,1,1,12-12A12,12,0,0,1,128,192Z"})})],["light",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M235.07,189.09,147.61,37.22h0a22.75,22.75,0,0,0-39.22,0L20.93,189.09a21.53,21.53,0,0,0,0,21.72A22.35,22.35,0,0,0,40.55,222h174.9a22.35,22.35,0,0,0,19.6-11.19A21.53,21.53,0,0,0,235.07,189.09ZM224.66,204.8a10.46,10.46,0,0,1-9.21,5.2H40.55a10.46,10.46,0,0,1-9.21-5.2,9.51,9.51,0,0,1,0-9.72L118.79,43.21a10.75,10.75,0,0,1,18.42,0l87.46,151.87A9.51,9.51,0,0,1,224.66,204.8ZM122,144V104a6,6,0,0,1,12,0v40a6,6,0,0,1-12,0Zm16,36a10,10,0,1,1-10-10A10,10,0,0,1,138,180Z"})})],["regular",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"})})],["thin",x.jsx(x.Fragment,{children:x.jsx("path",{d:"M233.34,190.09,145.88,38.22h0a20.75,20.75,0,0,0-35.76,0L22.66,190.09a19.52,19.52,0,0,0,0,19.71A20.36,20.36,0,0,0,40.54,220H215.46a20.36,20.36,0,0,0,17.86-10.2A19.52,19.52,0,0,0,233.34,190.09ZM226.4,205.8a12.47,12.47,0,0,1-10.94,6.2H40.54a12.47,12.47,0,0,1-10.94-6.2,11.45,11.45,0,0,1,0-11.72L117.05,42.21a12.76,12.76,0,0,1,21.9,0L226.4,194.08A11.45,11.45,0,0,1,226.4,205.8ZM124,144V104a4,4,0,0,1,8,0v40a4,4,0,0,1-8,0Zm12,36a8,8,0,1,1-8-8A8,8,0,0,1,136,180Z"})})]]),CC=b.createContext({color:"currentColor",size:"1em",weight:"regular",mirrored:!1});var TC=Object.defineProperty,kC=Object.defineProperties,IC=Object.getOwnPropertyDescriptors,us=Object.getOwnPropertySymbols,lg=Object.prototype.hasOwnProperty,ug=Object.prototype.propertyIsEnumerable,Gd=(e,t,n)=>t in e?TC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Vd=(e,t)=>{for(var n in t||(t={}))lg.call(t,n)&&Gd(e,n,t[n]);if(us)for(var n of us(t))ug.call(t,n)&&Gd(e,n,t[n]);return e},LC=(e,t)=>kC(e,IC(t)),Wd=(e,t)=>{var n={};for(var r in e)lg.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&us)for(var r of us(e))t.indexOf(r)<0&&ug.call(e,r)&&(n[r]=e[r]);return n};const cg=b.forwardRef((e,t)=>{const n=e,{alt:r,color:o,size:i,weight:s,mirrored:a,children:l,weights:u}=n,c=Wd(n,["alt","color","size","weight","mirrored","children","weights"]),f=b.useContext(CC),{color:d="currentColor",size:m,weight:y="regular",mirrored:v=!1}=f,w=Wd(f,["color","size","weight","mirrored"]);return x.jsxs("svg",LC(Vd(Vd({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i??m,height:i??m,fill:o??d,viewBox:"0 0 256 256",transform:a||v?"scale(-1, 1)":void 0},w),c),{children:[!!r&&x.jsx("title",{children:r}),l,u.get(s??y)]}))});cg.displayName="IconBase";const Vs=cg;var NC=Object.defineProperty,PC=Object.defineProperties,bC=Object.getOwnPropertyDescriptors,Zd=Object.getOwnPropertySymbols,OC=Object.prototype.hasOwnProperty,MC=Object.prototype.propertyIsEnumerable,Xd=(e,t,n)=>t in e?NC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,RC=(e,t)=>{for(var n in t||(t={}))OC.call(t,n)&&Xd(e,n,t[n]);if(Zd)for(var n of Zd(t))MC.call(t,n)&&Xd(e,n,t[n]);return e},AC=(e,t)=>PC(e,bC(t));const fg=b.forwardRef((e,t)=>x.jsx(Vs,AC(RC({ref:t},e),{weights:SC})));fg.displayName="ArrowUpRight";var FC=Object.defineProperty,DC=Object.defineProperties,jC=Object.getOwnPropertyDescriptors,Yd=Object.getOwnPropertySymbols,HC=Object.prototype.hasOwnProperty,BC=Object.prototype.propertyIsEnumerable,Qd=(e,t,n)=>t in e?FC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,UC=(e,t)=>{for(var n in t||(t={}))HC.call(t,n)&&Qd(e,n,t[n]);if(Yd)for(var n of Yd(t))BC.call(t,n)&&Qd(e,n,t[n]);return e},$C=(e,t)=>DC(e,jC(t));const dg=b.forwardRef((e,t)=>x.jsx(Vs,$C(UC({ref:t},e),{weights:_C})));dg.displayName="CaretRight";var zC=Object.defineProperty,GC=Object.defineProperties,VC=Object.getOwnPropertyDescriptors,Kd=Object.getOwnPropertySymbols,WC=Object.prototype.hasOwnProperty,ZC=Object.prototype.propertyIsEnumerable,qd=(e,t,n)=>t in e?zC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,XC=(e,t)=>{for(var n in t||(t={}))WC.call(t,n)&&qd(e,n,t[n]);if(Kd)for(var n of Kd(t))ZC.call(t,n)&&qd(e,n,t[n]);return e},YC=(e,t)=>GC(e,VC(t));const pg=b.forwardRef((e,t)=>x.jsx(Vs,YC(XC({ref:t},e),{weights:wC})));pg.displayName="Trash";var QC=Object.defineProperty,KC=Object.defineProperties,qC=Object.getOwnPropertyDescriptors,Jd=Object.getOwnPropertySymbols,JC=Object.prototype.hasOwnProperty,e4=Object.prototype.propertyIsEnumerable,ep=(e,t,n)=>t in e?QC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,t4=(e,t)=>{for(var n in t||(t={}))JC.call(t,n)&&ep(e,n,t[n]);if(Jd)for(var n of Jd(t))e4.call(t,n)&&ep(e,n,t[n]);return e},n4=(e,t)=>KC(e,qC(t));const hg=b.forwardRef((e,t)=>x.jsx(Vs,n4(t4({ref:t},e),{weights:xC})));hg.displayName="Warning";function mg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=mg(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function tp(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=mg(e))&&(r&&(r+=" "),r+=t);return r}function np({className:e,variant:t,...n}){return E.jsxs("div",{className:tp("relative",e),children:[E.jsx("input",{type:"checkbox",className:"peer sr-only",...n}),E.jsx("div",{className:tp("border-border-300 peer-focus:ring-offset-bg-300 peer h-5 w-[36px] rounded-full after:absolute after:start-[2px] after:top-[2px] after:h-4 after:w-4 after:rounded-full after:border after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-focus:outline-none peer-focus:ring-1 peer-focus:ring-offset-2 peer-disabled:opacity-50 rtl:peer-checked:after:-translate-x-full",r4[t||"default"])})]})}const r4={default:`
    bg-bg-500
    after:border-border-300
    after:bg-white
    peer-focus:ring-accent-secondary-100
    peer-checked:bg-accent-secondary-000
    peer-checked:after:border-white
    `,secondary:`
    bg-bg-000
    border
    after:border-bg-000
    after:bg-text-500
    peer-focus:ring-accent-secondary-100
    peer-checked:bg-accent-secondary-900
    peer-checked:after:border-accent-secondary-900
    peer-checked:after:bg-accent-secondary-100
    `},kr=({variant:e,prepend:t,children:n,style:r,className:o,...i})=>{const s=e==="secondary"?{backgroundImage:"radial-gradient(ellipse, hsl(var(--bg-500) / 0.1) 50%, hsl(var(--bg-500) / 0.3))",backgroundColor:"transparent",borderColor:"hsl(var(--border-400) / .1)",color:"hsl(var(--text-100) / .9)"}:{backgroundImage:"linear-gradient(to right, hsl(var(--accent-main-100)), hsl(var(--accent-main-200)), hsl(var(--accent-main-200)))",backgroundColor:"hsl(var(--accent-main-100))",borderColor:"hsla(var(--border-300-rgb), 0.25)",color:"hsl(var(--oncolor-100))",textShadow:"0 1px 2px rgba(0, 0, 0, 0.1)"},a=t?{paddingRight:"0.75rem"}:{};return E.jsxs("button",{className:o,...i,style:{transition:"all 0.15s cubic-bezier(0.4, 0, 0.2, 1)",filter:"drop-shadow(0 1px 1px rgba(0, 0, 0, 0.05))",boxShadow:"inset 0 0.5px 0px rgba(255, 255, 0, 0.15)",fontWeight:500,fontSize:"14px",fontFamily:'var(--font-styrene-b), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',gap:"6px",padding:"0.5rem 1rem",backgroundSize:"200% 100%",borderWidth:"0.5px",borderRadius:"6px",whiteSpace:"nowrap",justifyContent:"center",alignItems:"center",flexShrink:0,height:"32px",display:"inline-flex",position:"relative",cursor:"pointer",...s,...a,...r},children:[t,n]})},o4={CommandOrControl:{char:"⌘",order:3},CmdOrCtrl:{char:"⌘",order:3},AltGr:{char:"⌥",order:1},Command:{char:"⌘",order:3},Cmd:{char:"⌘",order:3},Control:{char:"⌃",order:0},Ctrl:{char:"⌃",order:0},Alt:{char:"⌥",order:1},Option:{char:"⌥",order:1},Shift:{char:"⇧",order:2}},i4={CommandOrControl:{char:"Control",order:3},CmdOrCtrl:{char:"Control",order:3},AltGr:{char:"Alt",order:1},Command:{char:"Windows",order:3},Cmd:{char:"Windows",order:3},Control:{char:"Control",order:0},Ctrl:{char:"Control",order:0},Alt:{char:"Alt",order:1},Option:{char:"Alt",order:1},Shift:{char:"Shift",order:2}},rp=In?o4:i4,s4=In?"":"+";function a4(e,t=null){const n=e.split(/\s*\+\s*/),r=[],o=[];return n.forEach(s=>{s in rp?r.push(rp[s]):o.push(s)}),r.sort((s,a)=>s.order-a.order).map(s=>s.char).concat(o).join(t??s4)}const l4=["ControlLeft","ControlRight","AltLeft","AltRight","ShiftLeft","ShiftRight","MetaLeft","MetaRight"];function u4(){const t=Dn().formatMessage({defaultMessage:"Set shortcut",description:"Placeholder text shown in the keyboard shortcut input field when no shortcut is set",id:"WBvq3HlPae"}),n=b.useCallback(a=>{const u=a4(a,In?" ":"+"),c=document.getElementById("capture-area-content");a===""?(c.innerText=t,c.classList.add("placeholder")):(c.innerText=u,c.classList.remove("placeholder")),console.log("Electron Accelerator:",a)},[t]),r=b.useCallback(a=>{n(a),window.mainProcess.settingsWindowApi.setGlobalShortcut(a)},[n]),o=b.useCallback((a,l)=>{const u=l.toUpperCase().startsWith("F")&&l.length>1;if(a.length===0&&!u)return!0;const c=["Cmd+Q","Cmd+W","Cmd+H","Cmd+M","Cmd+,","Cmd+N","Cmd+O","Cmd+T","Cmd+S","Cmd+C","Cmd+V","Ctrl+W","Ctrl+N","Ctrl+O","Ctrl+T","Ctrl+S","Ctrl+C","Ctrl+V"],f=[...a,l].join("+");return c.includes(f)},[]),i=b.useCallback(a=>{const l=a.altKey||a.ctrlKey||a.metaKey;if(a.key==="Tab"&&!l||(a.preventDefault(),l4.includes(a.code)))return;const u=[];a.shiftKey&&u.push("Shift"),a.altKey&&u.push("Alt"),a.ctrlKey&&u.push("Ctrl"),a.metaKey&&u.push("Cmd");let c="";if(a.code.startsWith("Key")?c=a.code.slice(-1):a.code==="Space"?c="Space":a.key.length===1?c=a.key.toUpperCase():c=a.code,!c||o(u,c))return;const f=[...u,c].join("+");r(f)},[o,r]);b.useEffect(()=>{window.mainProcess.settingsWindowApi.getGlobalShortcut(a=>{n(a)})},[n]);const s=b.useCallback(()=>{r("")},[r]);return E.jsxs("div",{id:"capture-area",tabIndex:0,onKeyDown:i,children:[E.jsx("div",{id:"capture-area-content",className:"placeholder",children:t}),E.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",id:"clear-button",viewBox:"0 0 100 100",onClick:s,children:[E.jsx("defs",{children:E.jsxs("mask",{id:"xMask",children:[E.jsx("rect",{width:"100",height:"100",fill:"white"}),E.jsx("path",{d:"M35 35 L65 65 M65 35 L35 65",stroke:"black",strokeWidth:"8",strokeLinecap:"round"})]})}),E.jsx("circle",{cx:"50",cy:"50",r:"40",fill:"currentColor",mask:"url(#xMask)"}),E.jsx("title",{children:E.jsx(z,{defaultMessage:"Clear shortcut",description:"Title text for the button that clears the current keyboard shortcut",id:"zSP70MVzIo"})})]})]})}const c4=Tm(),zn=GE(dc.StartupSettings,c4),f4=()=>{const[e,t]=b.useState(!1),[n,r]=b.useState(!0);b.useEffect(()=>{zn.isStartupOnLoginEnabled().then(t),zn.isMenuBarEnabled().then(r)},[]);const o=b.useCallback(()=>{zn.setStartupOnLoginEnabled(!e).then(()=>zn.isStartupOnLoginEnabled()).then(s=>{t(s)},s=>{console.error("Failed to toggle run on startup",s)})},[e,t]),i=b.useCallback(()=>{zn.setMenuBarEnabled(!n).then(()=>zn.isMenuBarEnabled()).then(s=>{r(s)},s=>{console.error("Failed to toggle run on startup",s)})},[n,r]);return E.jsxs("ul",{className:"settings-page-grid",children:[E.jsxs("li",{className:"settings-item highlighted",children:[E.jsx("div",{style:{display:"flex",gap:"8px"},children:E.jsxs("p",{style:{fontSize:"14px",lineHeight:"19.6px"},children:[E.jsx("strong",{style:{fontWeight:500},children:E.jsx(z,{defaultMessage:"Claude Desktop is in beta.",id:"kYwW0OsI4M",description:"Title text indicating that the application is in beta status"})})," ",E.jsx(z,{defaultMessage:"You're an early explorer, so let us know your feedback.",id:"jd5ZNrRMNP",description:"Message encouraging users to provide feedback during the beta phase"})]})}),E.jsx(kr,{onClick:()=>{window.mainProcess.settingsWindowApi.openFeedback()},children:E.jsx(z,{defaultMessage:"Give feedback",id:"sZxWXq9BzJ",description:"Button text for opening the feedback form"})})]}),E.jsxs("li",{className:"settings-item",children:[E.jsxs("div",{className:"col-left",children:[E.jsx("h2",{children:E.jsx(z,{defaultMessage:"Run on Startup",id:"GSG5S0ysrR",description:"Title for the setting that controls whether Claude starts automatically on system login"})}),E.jsx("p",{className:"description",children:E.jsx(z,{defaultMessage:"Automatically start Claude when you log in",id:"25aCMlTDUq",description:"Description explaining the run on startup setting's functionality"})})]}),E.jsx("div",{className:"col-right flex flex-row justify-end",children:E.jsx("div",{className:"inline-block",onClick:o,children:E.jsx(np,{checked:e,readOnly:!0})})})]}),E.jsxs("li",{className:"settings-item",children:[E.jsxs("div",{className:"col-left",children:[E.jsx("h2",{children:E.jsx(z,{defaultMessage:"Quick Entry Keyboard Shortcut",id:"u1/hT7oRQY",description:"Title for the setting that configures the global keyboard shortcut"})}),E.jsx("p",{className:"description",children:E.jsx(z,{defaultMessage:"Quickly open Claude from anywhere",id:"7gSC+rZzXX",description:"Description explaining the purpose of the quick entry keyboard shortcut"})})]}),E.jsx("div",{className:"col-right",children:E.jsx(u4,{})})]}),E.jsxs("li",{className:"settings-item",children:[E.jsxs("div",{className:"col-left",children:[E.jsx("h2",{children:In?E.jsx(z,{defaultMessage:"Menu Bar",id:"fWDSQQgRO5",description:"Title for the setting that controls whether to show the menu bar icon"}):E.jsx(z,{defaultMessage:"System Tray",id:"E9jYTa7AbX",description:"Title for the setting that controls whether to show the icon in the system tray (Windows)"})}),E.jsx("p",{className:"description",children:In?E.jsx(z,{defaultMessage:"Show Claude in the menu bar",id:"YTdYCYAf/Z",description:"Description explaining the menu bar setting's functionality"}):E.jsx(z,{defaultMessage:"Show Claude in the notifications area",id:"k+06oXbIas",description:"Description explaining the system tray setting's functionality (Windows)"})})]}),E.jsx("div",{className:"col-right flex flex-row justify-end",children:E.jsx("div",{className:"inline-block",onClick:i,children:E.jsx(np,{checked:n,readOnly:!0})})})]}),E.jsxs("li",{className:"settings-item",children:[E.jsxs("div",{className:"col-left",children:[E.jsx("h2",{children:E.jsx(z,{defaultMessage:"Claude Settings",id:"D43DeqP+2t",description:"Title for the section that links to Claude's web settings"})}),E.jsx("p",{className:"description",children:E.jsx(z,{defaultMessage:"Go to claude.ai/settings to configure your profile, team, and more.",id:"0g8/VVdNuN",description:"Description explaining where users can find additional Claude settings on the web"})})]}),E.jsx("div",{className:"col-right",children:E.jsx(kr,{style:{width:"100%"},variant:"secondary",prepend:E.jsx(fg,{}),onClick:()=>{window.mainProcess.settingsWindowApi.openWebSettings()},children:E.jsx(z,{defaultMessage:"Configure",id:"xKRKzVVy9c",description:"Button text for opening Claude's web settings"})})})]})]})};var Pi=(e=>(e[e.OpenConfigFile=0]="OpenConfigFile",e[e.Installer=1]="Installer",e))(Pi||{}),At=(e=>(e[e.General=0]="General",e[e.MCP=1]="MCP",e))(At||{});function gg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=gg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function d4(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=gg(e))&&(r&&(r+=" "),r+=t);return r}const p4=()=>E.jsx(kr,{className:"w-36",variant:"secondary",onClick:()=>{window.mainProcess.settingsWindowApi.revealMcpLogs()},children:E.jsx(z,{defaultMessage:"Open Logs Folder",description:"Button text to open the folder containing MCP server logs",id:"WF1HSu0jAC"})}),h4=({configKey:e,config:t,deleteCallback:n})=>{var d;const[r,o]=b.useState(!1),i=Dn(),s=((d=t.args)==null?void 0:d.join(" "))||void 0,a=t.env?Object.keys(t.env).map(m=>{var y;return E.jsx("p",{children:`${m}=${(y=t.env)==null?void 0:y[m]}`},m)}):null,l="bg-accent-secondary-200 text-text-000",u="bg-danger-200 text-text-000",c="bg-accent-main-200 text-text-000",f=!!a;return E.jsxs("div",{className:"mcp-settings-pane flex-1 h-full min-w-0 overflow-auto break-words",children:[E.jsxs("header",{className:"flex items-center justify-between",children:[E.jsxs("div",{className:"flex items-center gap-2",children:[E.jsx("h2",{className:"text-xl font-medium",children:e}),E.jsx("div",{className:`rounded px-2 py-0.5 text-xs ${t.status==="running"?l:t.status==="failed"?u:c}`,children:t.status})]}),E.jsx("button",{onClick:n,className:"appearance-none border-0 bg-transparent",title:i.formatMessage({defaultMessage:"Delete",description:"Title text for the delete button that removes an MCP server configuration",id:"L32WRR6NOL"}),children:E.jsx(pg,{})})]}),E.jsxs("section",{className:"mt-4",children:[E.jsx("h3",{className:"text-lg font-medium",children:E.jsx(z,{defaultMessage:"Command",description:"Label for the command used to start the MCP server",id:"urCd4k/cE0"})}),E.jsx("p",{className:"mt-1",children:t.command})]}),s&&E.jsxs("section",{className:"mt-4",children:[E.jsx("h3",{className:"text-lg font-medium",children:E.jsx(z,{defaultMessage:"Arguments",description:"Label for the command-line arguments passed to the MCP server",id:"pgaCSv2/6H"})}),E.jsx("p",{className:"mt-1",children:s})]}),t.error&&E.jsxs("section",{className:"mt-4",children:[E.jsx("h3",{className:"text-lg font-medium text-danger-100",children:E.jsx(z,{defaultMessage:"Error",description:"Label for the error message when an MCP server fails",id:"zCIK9K8J4a"})}),E.jsx("p",{className:"mt-1 text-danger-100",children:t.error}),E.jsx(p4,{})]}),f&&E.jsxs(E.Fragment,{children:[E.jsxs("button",{className:"mt-4 flex items-center gap-1 text-text-500 hover:text-text-100",onClick:()=>o(!r),children:[E.jsx(dg,{className:`transition-transform ${r?"rotate-90":""}`}),E.jsx(z,{defaultMessage:"Advanced options",description:"Button text to show or hide advanced MCP server configuration options",id:"CZwl8X2D85"})]}),r&&a&&E.jsxs("section",{className:"mt-4",children:[E.jsx("h3",{className:"text-lg font-medium",children:E.jsx(z,{defaultMessage:"Environment variables",description:"Label for the environment variables section of MCP server configuration",id:"4qP7MjrQfC"})}),a]})]})]})},m4=({installerMode:e=Pi.OpenConfigFile,selectedConfigKey:t,setSelectedConfigKey:n})=>{const[r,o]=b.useState({}),i=Dn();b.useEffect(()=>((async()=>{try{const v=await window.mainProcess.settingsWindowApi.getMcpConfigs();v!==void 0&&o(v)}catch(v){console.error("Failed to load MCP configs:",v)}})(),window.mainProcess.settingsWindowApi.onMcpStatusChange((v,w,h)=>{o(p=>({...p,[v]:{...p[v],status:w,error:h}}))}),()=>{window.mainProcess.settingsWindowApi.removeMcpStatusChangeListeners()}),[]);const[s,a]=b.useState(new Set);b.useEffect(()=>{window.mainProcess.settingsWindowApi.onMcpConfigChange(v=>{const w=new Set;for(const h of Object.keys(v))Object.hasOwn(r,h)||w.add(h);return a(w),o(Object.fromEntries(Object.entries(v).map(([h,p])=>[h,{...p,status:"initializing",error:void 0}]))),()=>{window.mainProcess.settingsWindowApi.removeMcpConfigChangeListeners()}})},[r]);const l=Object.keys(r),u=b.useRef(null),c=E.jsx("ul",{className:"mcp-config-list",ref:u,children:l.map(v=>E.jsx("li",{className:d4({selected:t===v,"newly-added":s.has(v)}),"data-config-key":v,onClick:()=>n(v),children:E.jsxs("div",{className:"flex items-center justify-between w-full pr-2",children:[E.jsx("span",{className:"min-w-0 truncate",children:v}),r[v].status==="failed"&&E.jsx(hg,{className:"text-danger-100 flex-shrink-0",size:16})]})},v))});b.useEffect(()=>{if(s.size<1)return;const v=u.current;if(!v)return;let w=Number.POSITIVE_INFINITY;for(const h of s){const p=v.querySelector(`[data-config-key="${h}"]`);p instanceof HTMLElement&&(w=Math.min(p.offsetTop,w))}if(w<Number.POSITIVE_INFINITY){const h=u.current;if(h){const p=w-h.offsetTop;h.scrollTo({top:p,behavior:"smooth"})}}},[s,u]);const f=b.useCallback(()=>{if(window.confirm(i.formatMessage({defaultMessage:'Are you sure you want to remove the MCP server "{serverKey}"?',id:"NZIwKxgxJ+",description:"Confirmation message shown when deleting an MCP server configuration"},{serverKey:t}))){const v={...r};delete v[t],window.mainProcess.settingsWindowApi.setMcpConfigs(v),o(v)}},[r,t,i]),d=(()=>{switch(e){case Pi.OpenConfigFile:return E.jsx(g4,{});case Pi.Installer:return E.jsx(y4,{})}})(),m=Object.hasOwn(r,t)?E.jsx(h4,{configKey:t,config:r[t],deleteCallback:f}):E.jsx("div",{className:"mcp-settings-pane"}),y=l.length>0?E.jsxs("div",{className:"flex flex-row flex-1 min-h-0 min-w-0",children:[E.jsxs("div",{className:"mcp-config-list-wrapper flex-shrink-0",children:[c,E.jsx("div",{className:"mcp-config-list-footer",children:d})]}),m]}):E.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px",alignItems:"center",justifyContent:"center",textAlign:"center"},children:[E.jsx("p",{style:{maxWidth:"60%"},children:E.jsx(z,{defaultMessage:"MCP is a protocol that enables secure connections between clients, such as the Claude Desktop app, and local services.",id:"wS64bVG2CO",description:"Description of the Model Context Protocol (MCP) shown when no servers are configured"})}),E.jsxs("div",{style:{display:"inline-flex",gap:"12px"},children:[d,E.jsx(v4,{})]})]});return E.jsxs("div",{className:"mcp-settings",children:[E.jsx("header",{className:"mcp-settings-header",children:E.jsxs("p",{children:[E.jsx(z,{defaultMessage:"Claude can receive information like prompts and attachments from specialized servers using Model Context Protocol.",id:"HeHYq6bbS2",description:"Header text explaining MCP functionality with a link to learn more"})," ",E.jsx("a",{href:"https://modelcontextprotocol.io/",target:"_blank",children:E.jsx(z,{defaultMessage:"Learn more",id:"XinCguXCgN",description:"Link text to learn more about Model Context Protocol"})})]})}),E.jsx("div",{className:"mcp-settings-wrapper flex flex-1 min-h-0 min-w-0 overflow-hidden",children:y})]})},g4=()=>E.jsx(kr,{variant:"secondary",onClick:()=>{window.mainProcess.settingsWindowApi.revealMcpConfig()},children:E.jsx(z,{defaultMessage:"Edit Config",id:"Vvus2ifAny",description:"Button text to open and edit the MCP configuration file"})}),y4=()=>E.jsx(kr,{variant:"secondary",onClick:()=>{window.mainProcess.settingsWindowApi.openMcpInstaller()},children:E.jsx(z,{defaultMessage:"Install",id:"9uNxNtcrFI",description:"Button text to open the MCP installer"})}),v4=()=>E.jsx(kr,{variant:"primary",onClick:()=>{window.open("https://modelcontextprotocol.io/quickstart","_blank")},children:E.jsx(z,{defaultMessage:"Get Started",id:"I5O68ogAtr",description:"Button text to open the MCP quick start guide"})}),E4=()=>{const e=Dn(),t=new Map([[At.General,{title:e.formatMessage({defaultMessage:"General",id:"1PfZLi/OV7",description:"Title for the general settings page"})}],[At.MCP,{title:e.formatMessage({defaultMessage:"Developer",id:"dLyz0Srosd",description:"Title for the developer settings page which contains MCP configuration"})}]]);if(!t.has(At.General))throw new Error("SettingsPageType.General must be provided");const[n,r]=b.useState(At.General),[o,i]=b.useState(""),s=(()=>{switch(n){case At.General:return E.jsx(f4,{});case At.MCP:return E.jsx(m4,{selectedConfigKey:o,setSelectedConfigKey:i})}})(),a=Array.from(t.keys()),l=a.length<2?null:E.jsx("ul",{className:"settings-pages",children:a.map(u=>{var f;const c=(f=t.get(u))==null?void 0:f.title;return c?E.jsx("li",{className:n===u?"selected":"",onClick:()=>r(u),children:c},u):null})});return b.useEffect(()=>(window.mainProcess.settingsWindowApi.onRevealMcpServerSettingsRequested(u=>{r(At.MCP),i(u)}),()=>{window.mainProcess.settingsWindowApi.removeRevealMcpServerSettingsRequestedListeners()})),E.jsxs("div",{className:"flex flex-col h-full",children:[E.jsx(vC,{windowTitle:e.formatMessage({defaultMessage:"Settings",id:"L717supPIA",description:"Title shown in the window title bar of the settings window"}),isMainWindow:!1}),E.jsxs("div",{className:"settings flex-1 flex flex-row",children:[l,s&&E.jsx("div",{className:"flex-1 min-h-0 flex flex-col min-w-0",children:s})]})]})},S4=Ju(document.getElementById("react-root"));S4.render(E.jsx(gp.StrictMode,{children:E.jsx(km,{children:E.jsx(E4,{})})}));
