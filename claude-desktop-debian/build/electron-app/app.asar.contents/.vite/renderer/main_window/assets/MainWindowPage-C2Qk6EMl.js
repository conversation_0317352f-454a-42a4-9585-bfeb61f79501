import{r as d,_ as y,s as b,u as _,R as H,j as a,m as Z,i as T,a as z}from"./main-G8n7PHcx.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new e.Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="7beba091-2ac8-4940-b990-d4333c09b2c0",e._sentryDebugIdIdentifier="sentry-dbid-7beba091-2ac8-4940-b990-d4333c09b2c0")}catch{}})();function W(e,r){var t=e.values,n=y(e,["values"]),i=r.values,o=y(r,["values"]);return b(i,t)&&b(n,o)}function k(e){var r=_(),t=r.formatMessage,n=r.textComponent,i=n===void 0?d.Fragment:n,o=e.id,h=e.description,L=e.defaultMessage,l=e.values,f=e.children,c=e.tagName,u=c===void 0?i:c,g=e.ignoreTag,p={id:o,description:h,defaultMessage:L},x=t(p,l,{ignoreTag:g});return typeof f=="function"?f(Array.isArray(x)?x:[x]):u?d.createElement(u,null,d.Children.toArray(x)):d.createElement(d.Fragment,null,x)}k.displayName="FormattedMessage";var w=d.memo(k,W);w.displayName="MemoizedFormattedMessage";var S={exports:{}},m={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var j;function $(){if(j)return m;j=1;var e=H,r=Symbol.for("react.element"),t=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function h(L,l,f){var c,u={},g=null,p=null;f!==void 0&&(g=""+f),l.key!==void 0&&(g=""+l.key),l.ref!==void 0&&(p=l.ref);for(c in l)n.call(l,c)&&!o.hasOwnProperty(c)&&(u[c]=l[c]);if(L&&L.defaultProps)for(c in l=L.defaultProps,l)u[c]===void 0&&(u[c]=l[c]);return{$$typeof:r,type:L,key:g,ref:p,props:u,_owner:i.current}}return m.Fragment=t,m.jsx=h,m.jsxs=h,m}S.exports=$();var s=S.exports;const D=new Map([["bold",s.jsx(s.Fragment,{children:s.jsx("path",{d:"M240.26,186.1,152.81,34.23h0a28.74,28.74,0,0,0-49.62,0L15.74,186.1a27.45,27.45,0,0,0,0,27.71A28.31,28.31,0,0,0,40.55,228h174.9a28.31,28.31,0,0,0,24.79-14.19A27.45,27.45,0,0,0,240.26,186.1Zm-20.8,15.7a4.46,4.46,0,0,1-4,2.2H40.55a4.46,4.46,0,0,1-4-2.2,3.56,3.56,0,0,1,0-3.73L124,46.2a4.77,4.77,0,0,1,8,0l87.44,151.87A3.56,3.56,0,0,1,219.46,201.8ZM116,136V104a12,12,0,0,1,24,0v32a12,12,0,0,1-24,0Zm28,40a16,16,0,1,1-16-16A16,16,0,0,1,144,176Z"})})],["duotone",s.jsxs(s.Fragment,{children:[s.jsx("path",{d:"M215.46,216H40.54C27.92,216,20,202.79,26.13,192.09L113.59,40.22c6.3-11,22.52-11,28.82,0l87.46,151.87C236,202.79,228.08,216,215.46,216Z",opacity:"0.2"}),s.jsx("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"})]})],["fill",s.jsx(s.Fragment,{children:s.jsx("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM120,104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm8,88a12,12,0,1,1,12-12A12,12,0,0,1,128,192Z"})})],["light",s.jsx(s.Fragment,{children:s.jsx("path",{d:"M235.07,189.09,147.61,37.22h0a22.75,22.75,0,0,0-39.22,0L20.93,189.09a21.53,21.53,0,0,0,0,21.72A22.35,22.35,0,0,0,40.55,222h174.9a22.35,22.35,0,0,0,19.6-11.19A21.53,21.53,0,0,0,235.07,189.09ZM224.66,204.8a10.46,10.46,0,0,1-9.21,5.2H40.55a10.46,10.46,0,0,1-9.21-5.2,9.51,9.51,0,0,1,0-9.72L118.79,43.21a10.75,10.75,0,0,1,18.42,0l87.46,151.87A9.51,9.51,0,0,1,224.66,204.8ZM122,144V104a6,6,0,0,1,12,0v40a6,6,0,0,1-12,0Zm16,36a10,10,0,1,1-10-10A10,10,0,0,1,138,180Z"})})],["regular",s.jsx(s.Fragment,{children:s.jsx("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"})})],["thin",s.jsx(s.Fragment,{children:s.jsx("path",{d:"M233.34,190.09,145.88,38.22h0a20.75,20.75,0,0,0-35.76,0L22.66,190.09a19.52,19.52,0,0,0,0,19.71A20.36,20.36,0,0,0,40.54,220H215.46a20.36,20.36,0,0,0,17.86-10.2A19.52,19.52,0,0,0,233.34,190.09ZM226.4,205.8a12.47,12.47,0,0,1-10.94,6.2H40.54a12.47,12.47,0,0,1-10.94-6.2,11.45,11.45,0,0,1,0-11.72L117.05,42.21a12.76,12.76,0,0,1,21.9,0L226.4,194.08A11.45,11.45,0,0,1,226.4,205.8ZM124,144V104a4,4,0,0,1,8,0v40a4,4,0,0,1-8,0Zm12,36a8,8,0,1,1-8-8A8,8,0,0,1,136,180Z"})})]]),V=d.createContext({color:"currentColor",size:"1em",weight:"regular",mirrored:!1});var U=Object.defineProperty,q=Object.defineProperties,Y=Object.getOwnPropertyDescriptors,v=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable,M=(e,r,t)=>r in e?U(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,A=(e,r)=>{for(var t in r||(r={}))N.call(r,t)&&M(e,t,r[t]);if(v)for(var t of v(r))P.call(r,t)&&M(e,t,r[t]);return e},K=(e,r)=>q(e,Y(r)),O=(e,r)=>{var t={};for(var n in e)N.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&v)for(var n of v(e))r.indexOf(n)<0&&P.call(e,n)&&(t[n]=e[n]);return t};const B=d.forwardRef((e,r)=>{const t=e,{alt:n,color:i,size:o,weight:h,mirrored:L,children:l,weights:f}=t,c=O(t,["alt","color","size","weight","mirrored","children","weights"]),u=d.useContext(V),{color:g="currentColor",size:p,weight:x="regular",mirrored:I=!1}=u,R=O(u,["color","size","weight","mirrored"]);return s.jsxs("svg",K(A(A({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o??p,height:o??p,fill:i??g,viewBox:"0 0 256 256",transform:L||I?"scale(-1, 1)":void 0},R),c),{children:[!!n&&s.jsx("title",{children:n}),l,f.get(h??x)]}))});B.displayName="IconBase";const G=B;var J=Object.defineProperty,Q=Object.defineProperties,X=Object.getOwnPropertyDescriptors,C=Object.getOwnPropertySymbols,e1=Object.prototype.hasOwnProperty,r1=Object.prototype.propertyIsEnumerable,E=(e,r,t)=>r in e?J(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,t1=(e,r)=>{for(var t in r||(r={}))e1.call(r,t)&&E(e,t,r[t]);if(C)for(var t of C(r))r1.call(r,t)&&E(e,t,r[t]);return e},a1=(e,r)=>Q(e,X(r));const F=d.forwardRef((e,r)=>s.jsx(G,a1(t1({ref:r},e),{weights:D})));F.displayName="Warning";const n1=({variant:e,prepend:r,children:t,style:n,className:i,...o})=>{const h=e==="secondary"?{backgroundImage:"radial-gradient(ellipse, hsl(var(--bg-500) / 0.1) 50%, hsl(var(--bg-500) / 0.3))",backgroundColor:"transparent",borderColor:"hsl(var(--border-400) / .1)",color:"hsl(var(--text-100) / .9)"}:{backgroundImage:"linear-gradient(to right, hsl(var(--accent-main-100)), hsl(var(--accent-main-200)), hsl(var(--accent-main-200)))",backgroundColor:"hsl(var(--accent-main-100))",borderColor:"hsla(var(--border-300-rgb), 0.25)",color:"hsl(var(--oncolor-100))",textShadow:"0 1px 2px rgba(0, 0, 0, 0.1)"},L=r?{paddingRight:"0.75rem"}:{};return a.jsxs("button",{className:i,...o,style:{transition:"all 0.15s cubic-bezier(0.4, 0, 0.2, 1)",filter:"drop-shadow(0 1px 1px rgba(0, 0, 0, 0.05))",boxShadow:"inset 0 0.5px 0px rgba(255, 255, 0, 0.15)",fontWeight:500,fontSize:"14px",fontFamily:'var(--font-styrene-b), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',gap:"6px",padding:"0.5rem 1rem",backgroundSize:"200% 100%",borderWidth:"0.5px",borderRadius:"6px",whiteSpace:"nowrap",justifyContent:"center",alignItems:"center",flexShrink:0,height:"32px",display:"inline-flex",position:"relative",cursor:"pointer",...h,...L,...n},children:[r,t]})},s1=({details:e,onRefresh:r})=>{const t=_(),n=()=>t.formatMessage({id:"6yv8ytK4El",defaultMessage:"Check your network connection",description:"Error message suggesting the user to check their internet connection"});return a.jsx("div",{className:"nc-drag absolute z-50 flex flex-col items-center justify-center",style:{top:`${Z}px`,left:0,right:0,bottom:0,fontFamily:"Styrene B LC"},children:a.jsxs("div",{className:"nc-no-drag select-none flex flex-col items-center justify-center max-w-md p-6 space-y-2",children:[a.jsx("div",{className:"flex items-center justify-center w-16 h-16 rounded-full bg-danger-200/10 text-danger-100",children:a.jsx(F,{size:32})}),a.jsx("h1",{className:"text-l font-bold text-center",children:a.jsx(w,{id:"Nmvo1ufAY5",defaultMessage:"Couldn't connect to Claude",description:"Error title shown when the app fails to connect to Claude's servers"})}),a.jsx("p",{className:"text-xs text-center text-text-400 dark:text-text-100 select-text !mb-2",children:e.errorDescription||n()}),a.jsx(n1,{onClick:r,variant:"secondary",children:a.jsx(w,{id:"ilE9e0uxNN",defaultMessage:"Refresh",description:"Button label to retry the connection"})})]})})};function o1({width:e=16,height:r=16,style:t}){return a.jsx("svg",{width:e,height:r,style:t,viewBox:"0 0 248 248",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{d:"M52.4285 162.873L98.7844 136.879L99.5485 134.602L98.7844 133.334H96.4921L88.7237 132.862L62.2346 132.153L39.3113 131.207L17.0249 130.026L11.4214 128.844L6.2 121.873L6.7094 118.447L11.4214 115.257L18.171 115.847L33.0711 116.911L55.485 118.447L71.6586 119.392L95.728 121.873H99.5485L100.058 120.337L98.7844 119.392L97.7656 118.447L74.5877 102.732L49.4995 86.1905L36.3823 76.62L29.3779 71.7757L25.8121 67.2858L24.2839 57.3608L30.6515 50.2716L39.3113 50.8623L41.4763 51.4531L50.2636 58.1879L68.9842 72.7209L93.4357 90.6804L97.0015 93.6343L98.4374 92.6652L98.6571 91.9801L97.0015 89.2625L83.757 65.2772L69.621 40.8192L63.2534 30.6579L61.5978 24.632C60.9565 22.1032 60.579 20.0111 60.579 17.4246L67.8381 7.49965L71.9133 6.19995L81.7193 7.49965L85.7946 11.0443L91.9074 24.9865L101.714 46.8451L116.996 76.62L121.453 85.4816L123.873 93.6343L124.764 96.1155H126.292V94.6976L127.566 77.9197L129.858 57.3608L132.15 30.8942L132.915 23.4505L136.608 14.4708L143.994 9.62643L149.725 12.344L154.437 19.0788L153.8 23.4505L150.998 41.6463L145.522 70.1215L141.957 89.2625H143.994L146.414 86.7813L156.093 74.0206L172.266 53.698L179.398 45.6635L187.803 36.802L193.152 32.5484H203.34L210.726 43.6549L207.415 55.1159L196.972 68.3492L188.312 79.5739L175.896 96.2095L168.191 109.585L168.882 110.689L170.738 110.53L198.755 104.504L213.91 101.787L231.994 98.7149L240.144 102.496L241.036 106.395L237.852 114.311L218.495 119.037L195.826 123.645L162.07 131.592L161.696 131.893L162.137 132.547L177.36 133.925L183.855 134.279H199.774L229.447 136.524L237.215 141.605L241.8 147.867L241.036 152.711L229.065 158.737L213.019 154.956L175.45 145.977L162.587 142.787H160.805V143.85L171.502 154.366L191.242 172.089L215.82 195.011L217.094 200.682L213.91 205.172L210.599 204.699L188.949 188.394L180.544 181.069L161.696 165.118H160.422V166.772L164.752 173.152L187.803 207.771L188.949 218.405L187.294 221.832L181.308 223.959L174.813 222.777L161.187 203.754L147.305 182.486L136.098 163.345L134.745 164.2L128.075 235.42L125.019 239.082L117.887 241.8L111.902 237.31L108.718 229.984L111.902 215.452L115.722 196.547L118.779 181.541L121.58 162.873L123.291 156.636L123.14 156.219L121.773 156.449L107.699 175.752L86.304 204.699L69.3663 222.777L65.291 224.431L58.2867 220.768L58.9235 214.27L62.8713 208.48L86.304 178.705L100.44 160.155L109.551 149.507L109.462 147.967L108.959 147.924L46.6977 188.512L35.6182 189.93L30.7788 185.44L31.4156 178.115L33.7079 175.752L52.4285 162.873Z",fill:"#D97757"})})}function i1({isMainWindow:e,windowTitle:r,titleBarHeight:t=e?Z:z}){if(T && e)return null;const n=e?a.jsxs("div",{className:"items-center ms-3 flex nc-no-drag",style:{height:t},id:"app-icon-container",children:[a.jsx("svg",{id:"hamburger-menu",width:"16",height:"16",viewBox:"0 0 24 24",onClick:()=>{window.mainProcess.titleBarApi.requestMainMenuPopup()},fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsxs("g",{style:{stroke:"var(--claude-foreground-color)"},children:[a.jsx("path",{d:"M4 18L20 18",strokeWidth:"2",strokeLinecap:"round"}),a.jsx("path",{d:"M4 12L20 12",strokeWidth:"2",strokeLinecap:"round"}),a.jsx("path",{d:"M4 6L20 6",strokeWidth:"2",strokeLinecap:"round"})]})}),a.jsx(o1,{width:16,height:16,style:{marginLeft:12,height:16}})]}):null,i=a.jsx("div",{className:"flex flex-row items-center justify-center select-none nc-drag",style:{height:`${t}px`},children:a.jsx("h1",{className:"text-xs text-center self-center opacity-40 font-bold select-none",id:"titleBar",children:r})}),o=e?a.jsx(a.Fragment,{children:n}):a.jsx("div",{});return a.jsxs(a.Fragment,{children:[i,a.jsx("div",{className:"absolute top-0 left-0 right-0 flex flex-row items-center select-none nc-drag",style:{height:`${t+1}px`,borderBottom:"1px solid rgba(0,0,0,0.1)"},children:o})]})}function c1(){const[e,r]=d.useState("Claude"),[t,n]=d.useState(null);d.useEffect(()=>{window.mainProcess.titleBarApi.onUpdateTitleBar(o=>{r(o)}),window.mainProcess.titleBarApi.onShowLoadError(o=>{n(o)}),window.mainProcess.titleBarApi.onHideLoadError(()=>{n(null)}),window.mainProcess.titleBarApi.titleBarReady()},[]);const i=()=>{window.mainProcess.titleBarApi.requestReloadWebview()};return a.jsxs(a.Fragment,{children:[a.jsx(i1,{windowTitle:e,isMainWindow:!0}),t&&a.jsx(s1,{details:t,onRefresh:i})]})}export{c1 as default};
