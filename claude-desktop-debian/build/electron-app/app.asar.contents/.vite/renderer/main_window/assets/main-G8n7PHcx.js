(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="89000cb9-c23c-45d7-a0e6-fea230ca8331",e._sentryDebugIdIdentifier="sentry-dbid-89000cb9-c23c-45d7-a0e6-fea230ca8331")}catch{}})();var p0=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};p0.SENTRY_RELEASE={id:"27cc6f763724a1af75b35c386a6b8d014eedc334"};(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();const h0=""+new URL("Copernicus-Book-Dqxs7atU.otf",import.meta.url).href,m0=""+new URL("Copernicus-BookItalic-DE-FEDdC.otf",import.meta.url).href,g0=""+new URL("Copernicus-Medium-BDFrxFZK.otf",import.meta.url).href,y0=""+new URL("Copernicus-MediumItalic-DwCSBWW0.otf",import.meta.url).href,v0=""+new URL("Copernicus-Semibold-CN_XmW6o.otf",import.meta.url).href,E0=""+new URL("StyreneBLC-Medium-Cw-IvyMy.otf",import.meta.url).href,_0=""+new URL("StyreneBLC-MediumItalic-CKHvGCIz.otf",import.meta.url).href,S0=""+new URL("StyreneBLC-Regular-DLVQLT8g.otf",import.meta.url).href,w0=""+new URL("StyreneBLC-RegularItalic-hJCoPVD5.otf",import.meta.url).href,x0=""+new URL("TiemposText-Medium-vqMEr0TH.otf",import.meta.url).href,T0=""+new URL("TiemposText-MediumItalic-CIeY-CUo.otf",import.meta.url).href,C0=""+new URL("TiemposText-Regular-CoJqehkj.otf",import.meta.url).href,I0=""+new URL("TiemposText-RegularItalic-C4EVGPqi.otf",import.meta.url).href,k0=`
@font-face {
    font-family: 'Copernicus Book';
    src: url('${h0}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus Book';
    src: url('${m0}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${g0}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${y0}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${v0}') format('opentype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${E0}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${_0}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${S0}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${w0}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${x0}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${T0}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${C0}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${I0}') format('opentype');
    font-weight: normal;
    font-style: italic;
}
`,dd=document.createElement("style");dd.textContent=k0;document.head.appendChild(dd);function pd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var hd={exports:{}},Hi={},md={exports:{}},L={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var so=Symbol.for("react.element"),N0=Symbol.for("react.portal"),P0=Symbol.for("react.fragment"),R0=Symbol.for("react.strict_mode"),O0=Symbol.for("react.profiler"),L0=Symbol.for("react.provider"),A0=Symbol.for("react.context"),D0=Symbol.for("react.forward_ref"),b0=Symbol.for("react.suspense"),M0=Symbol.for("react.memo"),F0=Symbol.for("react.lazy"),Kl=Symbol.iterator;function H0(e){return e===null||typeof e!="object"?null:(e=Kl&&e[Kl]||e["@@iterator"],typeof e=="function"?e:null)}var gd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},yd=Object.assign,vd={};function dr(e,t,n){this.props=e,this.context=t,this.refs=vd,this.updater=n||gd}dr.prototype.isReactComponent={};dr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};dr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ed(){}Ed.prototype=dr.prototype;function Ra(e,t,n){this.props=e,this.context=t,this.refs=vd,this.updater=n||gd}var Oa=Ra.prototype=new Ed;Oa.constructor=Ra;yd(Oa,dr.prototype);Oa.isPureReactComponent=!0;var Zl=Array.isArray,_d=Object.prototype.hasOwnProperty,La={current:null},Sd={key:!0,ref:!0,__self:!0,__source:!0};function wd(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)_d.call(t,r)&&!Sd.hasOwnProperty(r)&&(o[r]=t[r]);var u=arguments.length-2;if(u===1)o.children=n;else if(1<u){for(var a=Array(u),l=0;l<u;l++)a[l]=arguments[l+2];o.children=a}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)o[r]===void 0&&(o[r]=u[r]);return{$$typeof:so,type:e,key:i,ref:s,props:o,_owner:La.current}}function B0(e,t){return{$$typeof:so,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Aa(e){return typeof e=="object"&&e!==null&&e.$$typeof===so}function U0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ql=/\/+/g;function Ss(e,t){return typeof e=="object"&&e!==null&&e.key!=null?U0(""+e.key):t.toString(36)}function Yo(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case so:case N0:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Ss(s,0):r,Zl(o)?(n="",e!=null&&(n=e.replace(ql,"$&/")+"/"),Yo(o,t,n,"",function(l){return l})):o!=null&&(Aa(o)&&(o=B0(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(ql,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Zl(e))for(var u=0;u<e.length;u++){i=e[u];var a=r+Ss(i,u);s+=Yo(i,t,n,a,o)}else if(a=H0(e),typeof a=="function")for(e=a.call(e),u=0;!(i=e.next()).done;)i=i.value,a=r+Ss(i,u++),s+=Yo(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function So(e,t,n){if(e==null)return e;var r=[],o=0;return Yo(e,r,"","",function(i){return t.call(n,i,o++)}),r}function $0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},Qo={transition:null},j0={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:Qo,ReactCurrentOwner:La};function xd(){throw Error("act(...) is not supported in production builds of React.")}L.Children={map:So,forEach:function(e,t,n){So(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return So(e,function(){t++}),t},toArray:function(e){return So(e,function(t){return t})||[]},only:function(e){if(!Aa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};L.Component=dr;L.Fragment=P0;L.Profiler=O0;L.PureComponent=Ra;L.StrictMode=R0;L.Suspense=b0;L.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=j0;L.act=xd;L.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=yd({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=La.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in t)_d.call(t,a)&&!Sd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&u!==void 0?u[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){u=Array(a);for(var l=0;l<a;l++)u[l]=arguments[l+2];r.children=u}return{$$typeof:so,type:e.type,key:o,ref:i,props:r,_owner:s}};L.createContext=function(e){return e={$$typeof:A0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:L0,_context:e},e.Consumer=e};L.createElement=wd;L.createFactory=function(e){var t=wd.bind(null,e);return t.type=e,t};L.createRef=function(){return{current:null}};L.forwardRef=function(e){return{$$typeof:D0,render:e}};L.isValidElement=Aa;L.lazy=function(e){return{$$typeof:F0,_payload:{_status:-1,_result:e},_init:$0}};L.memo=function(e,t){return{$$typeof:M0,type:e,compare:t===void 0?null:t}};L.startTransition=function(e){var t=Qo.transition;Qo.transition={};try{e()}finally{Qo.transition=t}};L.unstable_act=xd;L.useCallback=function(e,t){return Te.current.useCallback(e,t)};L.useContext=function(e){return Te.current.useContext(e)};L.useDebugValue=function(){};L.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};L.useEffect=function(e,t){return Te.current.useEffect(e,t)};L.useId=function(){return Te.current.useId()};L.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};L.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};L.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};L.useMemo=function(e,t){return Te.current.useMemo(e,t)};L.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};L.useRef=function(e){return Te.current.useRef(e)};L.useState=function(e){return Te.current.useState(e)};L.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};L.useTransition=function(){return Te.current.useTransition()};L.version="18.3.1";md.exports=L;var me=md.exports;const qx=pd(me);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var z0=me,G0=Symbol.for("react.element"),V0=Symbol.for("react.fragment"),W0=Object.prototype.hasOwnProperty,X0=z0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Y0={key:!0,ref:!0,__self:!0,__source:!0};function Td(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)W0.call(t,r)&&!Y0.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:G0,type:e,key:i,ref:s,props:o,_owner:X0.current}}Hi.Fragment=V0;Hi.jsx=Td;Hi.jsxs=Td;hd.exports=Hi;var iu=hd.exports,Cd={exports:{}},He={},Id={exports:{}},kd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,R){var O=C.length;C.push(R);e:for(;0<O;){var q=O-1>>>1,oe=C[q];if(0<o(oe,R))C[q]=R,C[O]=oe,O=q;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var R=C[0],O=C.pop();if(O!==R){C[0]=O;e:for(var q=0,oe=C.length,Eo=oe>>>1;q<Eo;){var tn=2*(q+1)-1,_s=C[tn],nn=tn+1,_o=C[nn];if(0>o(_s,O))nn<oe&&0>o(_o,_s)?(C[q]=_o,C[nn]=O,q=nn):(C[q]=_s,C[tn]=O,q=tn);else if(nn<oe&&0>o(_o,O))C[q]=_o,C[nn]=O,q=nn;else break e}}return R}function o(C,R){var O=C.sortIndex-R.sortIndex;return O!==0?O:C.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,u=s.now();e.unstable_now=function(){return s.now()-u}}var a=[],l=[],c=1,f=null,d=3,m=!1,y=!1,v=!1,S=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(C){for(var R=n(l);R!==null;){if(R.callback===null)r(l);else if(R.startTime<=C)r(l),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(l)}}function E(C){if(v=!1,g(C),!y)if(n(a)!==null)y=!0,vs(w);else{var R=n(l);R!==null&&Es(E,R.startTime-C)}}function w(C,R){y=!1,v&&(v=!1,h(N),N=-1),m=!0;var O=d;try{for(g(R),f=n(a);f!==null&&(!(f.expirationTime>R)||C&&!Ze());){var q=f.callback;if(typeof q=="function"){f.callback=null,d=f.priorityLevel;var oe=q(f.expirationTime<=R);R=e.unstable_now(),typeof oe=="function"?f.callback=oe:f===n(a)&&r(a),g(R)}else r(a);f=n(a)}if(f!==null)var Eo=!0;else{var tn=n(l);tn!==null&&Es(E,tn.startTime-R),Eo=!1}return Eo}finally{f=null,d=O,m=!1}}var x=!1,I=null,N=-1,X=5,A=-1;function Ze(){return!(e.unstable_now()-A<X)}function gr(){if(I!==null){var C=e.unstable_now();A=C;var R=!0;try{R=I(!0,C)}finally{R?yr():(x=!1,I=null)}}else x=!1}var yr;if(typeof p=="function")yr=function(){p(gr)};else if(typeof MessageChannel<"u"){var Ql=new MessageChannel,d0=Ql.port2;Ql.port1.onmessage=gr,yr=function(){d0.postMessage(null)}}else yr=function(){S(gr,0)};function vs(C){I=C,x||(x=!0,yr())}function Es(C,R){N=S(function(){C(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,vs(w))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):X=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(C){switch(d){case 1:case 2:case 3:var R=3;break;default:R=d}var O=d;d=R;try{return C()}finally{d=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,R){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var O=d;d=C;try{return R()}finally{d=O}},e.unstable_scheduleCallback=function(C,R,O){var q=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?q+O:q):O=q,C){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=**********;break;case 4:oe=1e4;break;default:oe=5e3}return oe=O+oe,C={id:c++,callback:R,priorityLevel:C,startTime:O,expirationTime:oe,sortIndex:-1},O>q?(C.sortIndex=O,t(l,C),n(a)===null&&C===n(l)&&(v?(h(N),N=-1):v=!0,Es(E,O-q))):(C.sortIndex=oe,t(a,C),y||m||(y=!0,vs(w))),C},e.unstable_shouldYield=Ze,e.unstable_wrapCallback=function(C){var R=d;return function(){var O=d;d=R;try{return C.apply(this,arguments)}finally{d=O}}}})(kd);Id.exports=kd;var Q0=Id.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var K0=me,Fe=Q0;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Nd=new Set,$r={};function Cn(e,t){er(e,t),er(e+"Capture",t)}function er(e,t){for($r[e]=t,e=0;e<t.length;e++)Nd.add(t[e])}var xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),su=Object.prototype.hasOwnProperty,Z0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Jl={},ec={};function q0(e){return su.call(ec,e)?!0:su.call(Jl,e)?!1:Z0.test(e)?ec[e]=!0:(Jl[e]=!0,!1)}function J0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function eg(e,t,n,r){if(t===null||typeof t>"u"||J0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ce(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new Ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new Ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new Ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new Ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new Ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new Ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new Ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new Ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new Ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var Da=/[\-:]([a-z])/g;function ba(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Da,ba);ce[t]=new Ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Da,ba);ce[t]=new Ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Da,ba);ce[t]=new Ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new Ce(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new Ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new Ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ma(e,t,n,r){var o=ce.hasOwnProperty(t)?ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(eg(t,n,o,r)&&(n=null),r||o===null?q0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var kt=K0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,wo=Symbol.for("react.element"),Dn=Symbol.for("react.portal"),bn=Symbol.for("react.fragment"),Fa=Symbol.for("react.strict_mode"),uu=Symbol.for("react.profiler"),Pd=Symbol.for("react.provider"),Rd=Symbol.for("react.context"),Ha=Symbol.for("react.forward_ref"),au=Symbol.for("react.suspense"),lu=Symbol.for("react.suspense_list"),Ba=Symbol.for("react.memo"),Rt=Symbol.for("react.lazy"),Od=Symbol.for("react.offscreen"),tc=Symbol.iterator;function vr(e){return e===null||typeof e!="object"?null:(e=tc&&e[tc]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,ws;function kr(e){if(ws===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ws=t&&t[1]||""}return`
`+ws+e}var xs=!1;function Ts(e,t){if(!e||xs)return"";xs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(l){var r=l}Reflect.construct(e,[],t)}else{try{t.call()}catch(l){r=l}e.call(t.prototype)}else{try{throw Error()}catch(l){r=l}e()}}catch(l){if(l&&r&&typeof l.stack=="string"){for(var o=l.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,u=i.length-1;1<=s&&0<=u&&o[s]!==i[u];)u--;for(;1<=s&&0<=u;s--,u--)if(o[s]!==i[u]){if(s!==1||u!==1)do if(s--,u--,0>u||o[s]!==i[u]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=u);break}}}finally{xs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?kr(e):""}function tg(e){switch(e.tag){case 5:return kr(e.type);case 16:return kr("Lazy");case 13:return kr("Suspense");case 19:return kr("SuspenseList");case 0:case 2:case 15:return e=Ts(e.type,!1),e;case 11:return e=Ts(e.type.render,!1),e;case 1:return e=Ts(e.type,!0),e;default:return""}}function cu(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case bn:return"Fragment";case Dn:return"Portal";case uu:return"Profiler";case Fa:return"StrictMode";case au:return"Suspense";case lu:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Rd:return(e.displayName||"Context")+".Consumer";case Pd:return(e._context.displayName||"Context")+".Provider";case Ha:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ba:return t=e.displayName||null,t!==null?t:cu(e.type)||"Memo";case Rt:t=e._payload,e=e._init;try{return cu(e(t))}catch{}}return null}function ng(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return cu(t);case 8:return t===Fa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Wt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ld(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function rg(e){var t=Ld(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xo(e){e._valueTracker||(e._valueTracker=rg(e))}function Ad(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ld(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function li(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function fu(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function nc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Wt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Dd(e,t){t=t.checked,t!=null&&Ma(e,"checked",t,!1)}function du(e,t){Dd(e,t);var n=Wt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?pu(e,t.type,n):t.hasOwnProperty("defaultValue")&&pu(e,t.type,Wt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function pu(e,t,n){(t!=="number"||li(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Nr=Array.isArray;function Wn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Wt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function hu(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(Nr(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Wt(n)}}function bd(e,t){var n=Wt(t.value),r=Wt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ic(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Md(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function mu(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Md(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var To,Fd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(To=To||document.createElement("div"),To.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=To.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function jr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Lr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},og=["Webkit","ms","Moz","O"];Object.keys(Lr).forEach(function(e){og.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Lr[t]=Lr[e]})});function Hd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Lr.hasOwnProperty(e)&&Lr[e]?(""+t).trim():t+"px"}function Bd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Hd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var ig=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function gu(e,t){if(t){if(ig[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function yu(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var vu=null;function Ua(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Eu=null,Xn=null,Yn=null;function sc(e){if(e=lo(e)){if(typeof Eu!="function")throw Error(_(280));var t=e.stateNode;t&&(t=zi(t),Eu(e.stateNode,e.type,t))}}function Ud(e){Xn?Yn?Yn.push(e):Yn=[e]:Xn=e}function $d(){if(Xn){var e=Xn,t=Yn;if(Yn=Xn=null,sc(e),t)for(e=0;e<t.length;e++)sc(t[e])}}function jd(e,t){return e(t)}function zd(){}var Cs=!1;function Gd(e,t,n){if(Cs)return e(t,n);Cs=!0;try{return jd(e,t,n)}finally{Cs=!1,(Xn!==null||Yn!==null)&&(zd(),$d())}}function zr(e,t){var n=e.stateNode;if(n===null)return null;var r=zi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var _u=!1;if(xt)try{var Er={};Object.defineProperty(Er,"passive",{get:function(){_u=!0}}),window.addEventListener("test",Er,Er),window.removeEventListener("test",Er,Er)}catch{_u=!1}function sg(e,t,n,r,o,i,s,u,a){var l=Array.prototype.slice.call(arguments,3);try{t.apply(n,l)}catch(c){this.onError(c)}}var Ar=!1,ci=null,fi=!1,Su=null,ug={onError:function(e){Ar=!0,ci=e}};function ag(e,t,n,r,o,i,s,u,a){Ar=!1,ci=null,sg.apply(ug,arguments)}function lg(e,t,n,r,o,i,s,u,a){if(ag.apply(this,arguments),Ar){if(Ar){var l=ci;Ar=!1,ci=null}else throw Error(_(198));fi||(fi=!0,Su=l)}}function In(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Vd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function uc(e){if(In(e)!==e)throw Error(_(188))}function cg(e){var t=e.alternate;if(!t){if(t=In(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return uc(o),e;if(i===r)return uc(o),t;i=i.sibling}throw Error(_(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,u=o.child;u;){if(u===n){s=!0,n=o,r=i;break}if(u===r){s=!0,r=o,n=i;break}u=u.sibling}if(!s){for(u=i.child;u;){if(u===n){s=!0,n=i,r=o;break}if(u===r){s=!0,r=i,n=o;break}u=u.sibling}if(!s)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Wd(e){return e=cg(e),e!==null?Xd(e):null}function Xd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Xd(e);if(t!==null)return t;e=e.sibling}return null}var Yd=Fe.unstable_scheduleCallback,ac=Fe.unstable_cancelCallback,fg=Fe.unstable_shouldYield,dg=Fe.unstable_requestPaint,J=Fe.unstable_now,pg=Fe.unstable_getCurrentPriorityLevel,$a=Fe.unstable_ImmediatePriority,Qd=Fe.unstable_UserBlockingPriority,di=Fe.unstable_NormalPriority,hg=Fe.unstable_LowPriority,Kd=Fe.unstable_IdlePriority,Bi=null,ct=null;function mg(e){if(ct&&typeof ct.onCommitFiberRoot=="function")try{ct.onCommitFiberRoot(Bi,e,void 0,(e.current.flags&128)===128)}catch{}}var nt=Math.clz32?Math.clz32:vg,gg=Math.log,yg=Math.LN2;function vg(e){return e>>>=0,e===0?32:31-(gg(e)/yg|0)|0}var Co=64,Io=4194304;function Pr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var u=s&~o;u!==0?r=Pr(u):(i&=s,i!==0&&(r=Pr(i)))}else s=n&~o,s!==0?r=Pr(s):i!==0&&(r=Pr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-nt(t),o=1<<n,r|=e[n],t&=~o;return r}function Eg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function _g(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-nt(i),u=1<<s,a=o[s];a===-1?(!(u&n)||u&r)&&(o[s]=Eg(u,t)):a<=t&&(e.expiredLanes|=u),i&=~u}}function wu(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Zd(){var e=Co;return Co<<=1,!(Co&4194240)&&(Co=64),e}function Is(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function uo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-nt(t),e[t]=n}function Sg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-nt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function ja(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-nt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var H=0;function qd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Jd,za,ep,tp,np,xu=!1,ko=[],Ht=null,Bt=null,Ut=null,Gr=new Map,Vr=new Map,Dt=[],wg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function lc(e,t){switch(e){case"focusin":case"focusout":Ht=null;break;case"dragenter":case"dragleave":Bt=null;break;case"mouseover":case"mouseout":Ut=null;break;case"pointerover":case"pointerout":Gr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vr.delete(t.pointerId)}}function _r(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=lo(t),t!==null&&za(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function xg(e,t,n,r,o){switch(t){case"focusin":return Ht=_r(Ht,e,t,n,r,o),!0;case"dragenter":return Bt=_r(Bt,e,t,n,r,o),!0;case"mouseover":return Ut=_r(Ut,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Gr.set(i,_r(Gr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Vr.set(i,_r(Vr.get(i)||null,e,t,n,r,o)),!0}return!1}function rp(e){var t=un(e.target);if(t!==null){var n=In(t);if(n!==null){if(t=n.tag,t===13){if(t=Vd(n),t!==null){e.blockedOn=t,np(e.priority,function(){ep(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ko(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Tu(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);vu=r,n.target.dispatchEvent(r),vu=null}else return t=lo(n),t!==null&&za(t),e.blockedOn=n,!1;t.shift()}return!0}function cc(e,t,n){Ko(e)&&n.delete(t)}function Tg(){xu=!1,Ht!==null&&Ko(Ht)&&(Ht=null),Bt!==null&&Ko(Bt)&&(Bt=null),Ut!==null&&Ko(Ut)&&(Ut=null),Gr.forEach(cc),Vr.forEach(cc)}function Sr(e,t){e.blockedOn===t&&(e.blockedOn=null,xu||(xu=!0,Fe.unstable_scheduleCallback(Fe.unstable_NormalPriority,Tg)))}function Wr(e){function t(o){return Sr(o,e)}if(0<ko.length){Sr(ko[0],e);for(var n=1;n<ko.length;n++){var r=ko[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ht!==null&&Sr(Ht,e),Bt!==null&&Sr(Bt,e),Ut!==null&&Sr(Ut,e),Gr.forEach(t),Vr.forEach(t),n=0;n<Dt.length;n++)r=Dt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Dt.length&&(n=Dt[0],n.blockedOn===null);)rp(n),n.blockedOn===null&&Dt.shift()}var Qn=kt.ReactCurrentBatchConfig,hi=!0;function Cg(e,t,n,r){var o=H,i=Qn.transition;Qn.transition=null;try{H=1,Ga(e,t,n,r)}finally{H=o,Qn.transition=i}}function Ig(e,t,n,r){var o=H,i=Qn.transition;Qn.transition=null;try{H=4,Ga(e,t,n,r)}finally{H=o,Qn.transition=i}}function Ga(e,t,n,r){if(hi){var o=Tu(e,t,n,r);if(o===null)Ms(e,t,r,mi,n),lc(e,r);else if(xg(o,e,t,n,r))r.stopPropagation();else if(lc(e,r),t&4&&-1<wg.indexOf(e)){for(;o!==null;){var i=lo(o);if(i!==null&&Jd(i),i=Tu(e,t,n,r),i===null&&Ms(e,t,r,mi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ms(e,t,r,null,n)}}var mi=null;function Tu(e,t,n,r){if(mi=null,e=Ua(r),e=un(e),e!==null)if(t=In(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Vd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return mi=e,null}function op(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(pg()){case $a:return 1;case Qd:return 4;case di:case hg:return 16;case Kd:return 536870912;default:return 16}default:return 16}}var Mt=null,Va=null,Zo=null;function ip(){if(Zo)return Zo;var e,t=Va,n=t.length,r,o="value"in Mt?Mt.value:Mt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Zo=o.slice(e,1<r?1-r:void 0)}function qo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function No(){return!0}function fc(){return!1}function Be(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(i):i[u]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?No:fc,this.isPropagationStopped=fc,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=No)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=No)},persist:function(){},isPersistent:No}),t}var pr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Wa=Be(pr),ao=K({},pr,{view:0,detail:0}),kg=Be(ao),ks,Ns,wr,Ui=K({},ao,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Xa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==wr&&(wr&&e.type==="mousemove"?(ks=e.screenX-wr.screenX,Ns=e.screenY-wr.screenY):Ns=ks=0,wr=e),ks)},movementY:function(e){return"movementY"in e?e.movementY:Ns}}),dc=Be(Ui),Ng=K({},Ui,{dataTransfer:0}),Pg=Be(Ng),Rg=K({},ao,{relatedTarget:0}),Ps=Be(Rg),Og=K({},pr,{animationName:0,elapsedTime:0,pseudoElement:0}),Lg=Be(Og),Ag=K({},pr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Dg=Be(Ag),bg=K({},pr,{data:0}),pc=Be(bg),Mg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Bg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Hg[e])?!!t[e]:!1}function Xa(){return Bg}var Ug=K({},ao,{key:function(e){if(e.key){var t=Mg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Xa,charCode:function(e){return e.type==="keypress"?qo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),$g=Be(Ug),jg=K({},Ui,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hc=Be(jg),zg=K({},ao,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Xa}),Gg=Be(zg),Vg=K({},pr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Wg=Be(Vg),Xg=K({},Ui,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Yg=Be(Xg),Qg=[9,13,27,32],Ya=xt&&"CompositionEvent"in window,Dr=null;xt&&"documentMode"in document&&(Dr=document.documentMode);var Kg=xt&&"TextEvent"in window&&!Dr,sp=xt&&(!Ya||Dr&&8<Dr&&11>=Dr),mc=" ",gc=!1;function up(e,t){switch(e){case"keyup":return Qg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ap(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Mn=!1;function Zg(e,t){switch(e){case"compositionend":return ap(t);case"keypress":return t.which!==32?null:(gc=!0,mc);case"textInput":return e=t.data,e===mc&&gc?null:e;default:return null}}function qg(e,t){if(Mn)return e==="compositionend"||!Ya&&up(e,t)?(e=ip(),Zo=Va=Mt=null,Mn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return sp&&t.locale!=="ko"?null:t.data;default:return null}}var Jg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jg[e.type]:t==="textarea"}function lp(e,t,n,r){Ud(r),t=gi(t,"onChange"),0<t.length&&(n=new Wa("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var br=null,Xr=null;function ey(e){_p(e,0)}function $i(e){var t=Bn(e);if(Ad(t))return e}function ty(e,t){if(e==="change")return t}var cp=!1;if(xt){var Rs;if(xt){var Os="oninput"in document;if(!Os){var vc=document.createElement("div");vc.setAttribute("oninput","return;"),Os=typeof vc.oninput=="function"}Rs=Os}else Rs=!1;cp=Rs&&(!document.documentMode||9<document.documentMode)}function Ec(){br&&(br.detachEvent("onpropertychange",fp),Xr=br=null)}function fp(e){if(e.propertyName==="value"&&$i(Xr)){var t=[];lp(t,Xr,e,Ua(e)),Gd(ey,t)}}function ny(e,t,n){e==="focusin"?(Ec(),br=t,Xr=n,br.attachEvent("onpropertychange",fp)):e==="focusout"&&Ec()}function ry(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return $i(Xr)}function oy(e,t){if(e==="click")return $i(t)}function iy(e,t){if(e==="input"||e==="change")return $i(t)}function sy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var it=typeof Object.is=="function"?Object.is:sy;function Yr(e,t){if(it(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!su.call(t,o)||!it(e[o],t[o]))return!1}return!0}function _c(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sc(e,t){var n=_c(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=_c(n)}}function dp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?dp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function pp(){for(var e=window,t=li();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=li(e.document)}return t}function Qa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function uy(e){var t=pp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dp(n.ownerDocument.documentElement,n)){if(r!==null&&Qa(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Sc(n,i);var s=Sc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ay=xt&&"documentMode"in document&&11>=document.documentMode,Fn=null,Cu=null,Mr=null,Iu=!1;function wc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Iu||Fn==null||Fn!==li(r)||(r=Fn,"selectionStart"in r&&Qa(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Mr&&Yr(Mr,r)||(Mr=r,r=gi(Cu,"onSelect"),0<r.length&&(t=new Wa("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Fn)))}function Po(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Hn={animationend:Po("Animation","AnimationEnd"),animationiteration:Po("Animation","AnimationIteration"),animationstart:Po("Animation","AnimationStart"),transitionend:Po("Transition","TransitionEnd")},Ls={},hp={};xt&&(hp=document.createElement("div").style,"AnimationEvent"in window||(delete Hn.animationend.animation,delete Hn.animationiteration.animation,delete Hn.animationstart.animation),"TransitionEvent"in window||delete Hn.transitionend.transition);function ji(e){if(Ls[e])return Ls[e];if(!Hn[e])return e;var t=Hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in hp)return Ls[e]=t[n];return e}var mp=ji("animationend"),gp=ji("animationiteration"),yp=ji("animationstart"),vp=ji("transitionend"),Ep=new Map,xc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zt(e,t){Ep.set(e,t),Cn(t,[e])}for(var As=0;As<xc.length;As++){var Ds=xc[As],ly=Ds.toLowerCase(),cy=Ds[0].toUpperCase()+Ds.slice(1);Zt(ly,"on"+cy)}Zt(mp,"onAnimationEnd");Zt(gp,"onAnimationIteration");Zt(yp,"onAnimationStart");Zt("dblclick","onDoubleClick");Zt("focusin","onFocus");Zt("focusout","onBlur");Zt(vp,"onTransitionEnd");er("onMouseEnter",["mouseout","mouseover"]);er("onMouseLeave",["mouseout","mouseover"]);er("onPointerEnter",["pointerout","pointerover"]);er("onPointerLeave",["pointerout","pointerover"]);Cn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Cn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Cn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Cn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Cn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Cn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fy=new Set("cancel close invalid load scroll toggle".split(" ").concat(Rr));function Tc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,lg(r,t,void 0,e),e.currentTarget=null}function _p(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var u=r[s],a=u.instance,l=u.currentTarget;if(u=u.listener,a!==i&&o.isPropagationStopped())break e;Tc(o,u,l),i=a}else for(s=0;s<r.length;s++){if(u=r[s],a=u.instance,l=u.currentTarget,u=u.listener,a!==i&&o.isPropagationStopped())break e;Tc(o,u,l),i=a}}}if(fi)throw e=Su,fi=!1,Su=null,e}function z(e,t){var n=t[Ou];n===void 0&&(n=t[Ou]=new Set);var r=e+"__bubble";n.has(r)||(Sp(t,e,2,!1),n.add(r))}function bs(e,t,n){var r=0;t&&(r|=4),Sp(n,e,r,t)}var Ro="_reactListening"+Math.random().toString(36).slice(2);function Qr(e){if(!e[Ro]){e[Ro]=!0,Nd.forEach(function(n){n!=="selectionchange"&&(fy.has(n)||bs(n,!1,e),bs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ro]||(t[Ro]=!0,bs("selectionchange",!1,t))}}function Sp(e,t,n,r){switch(op(t)){case 1:var o=Cg;break;case 4:o=Ig;break;default:o=Ga}n=o.bind(null,t,n,e),o=void 0,!_u||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ms(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var u=r.stateNode.containerInfo;if(u===o||u.nodeType===8&&u.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;u!==null;){if(s=un(u),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}u=u.parentNode}}r=r.return}Gd(function(){var l=i,c=Ua(n),f=[];e:{var d=Ep.get(e);if(d!==void 0){var m=Wa,y=e;switch(e){case"keypress":if(qo(n)===0)break e;case"keydown":case"keyup":m=$g;break;case"focusin":y="focus",m=Ps;break;case"focusout":y="blur",m=Ps;break;case"beforeblur":case"afterblur":m=Ps;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=dc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Pg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Gg;break;case mp:case gp:case yp:m=Lg;break;case vp:m=Wg;break;case"scroll":m=kg;break;case"wheel":m=Yg;break;case"copy":case"cut":case"paste":m=Dg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=hc}var v=(t&4)!==0,S=!v&&e==="scroll",h=v?d!==null?d+"Capture":null:d;v=[];for(var p=l,g;p!==null;){g=p;var E=g.stateNode;if(g.tag===5&&E!==null&&(g=E,h!==null&&(E=zr(p,h),E!=null&&v.push(Kr(p,E,g)))),S)break;p=p.return}0<v.length&&(d=new m(d,y,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",d&&n!==vu&&(y=n.relatedTarget||n.fromElement)&&(un(y)||y[Tt]))break e;if((m||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=l,y=y?un(y):null,y!==null&&(S=In(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=l),m!==y)){if(v=dc,E="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=hc,E="onPointerLeave",h="onPointerEnter",p="pointer"),S=m==null?d:Bn(m),g=y==null?d:Bn(y),d=new v(E,p+"leave",m,n,c),d.target=S,d.relatedTarget=g,E=null,un(c)===l&&(v=new v(h,p+"enter",y,n,c),v.target=g,v.relatedTarget=S,E=v),S=E,m&&y)t:{for(v=m,h=y,p=0,g=v;g;g=Ln(g))p++;for(g=0,E=h;E;E=Ln(E))g++;for(;0<p-g;)v=Ln(v),p--;for(;0<g-p;)h=Ln(h),g--;for(;p--;){if(v===h||h!==null&&v===h.alternate)break t;v=Ln(v),h=Ln(h)}v=null}else v=null;m!==null&&Cc(f,d,m,v,!1),y!==null&&S!==null&&Cc(f,S,y,v,!0)}}e:{if(d=l?Bn(l):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var w=ty;else if(yc(d))if(cp)w=iy;else{w=ry;var x=ny}else(m=d.nodeName)&&m.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(w=oy);if(w&&(w=w(e,l))){lp(f,w,n,c);break e}x&&x(e,d,l),e==="focusout"&&(x=d._wrapperState)&&x.controlled&&d.type==="number"&&pu(d,"number",d.value)}switch(x=l?Bn(l):window,e){case"focusin":(yc(x)||x.contentEditable==="true")&&(Fn=x,Cu=l,Mr=null);break;case"focusout":Mr=Cu=Fn=null;break;case"mousedown":Iu=!0;break;case"contextmenu":case"mouseup":case"dragend":Iu=!1,wc(f,n,c);break;case"selectionchange":if(ay)break;case"keydown":case"keyup":wc(f,n,c)}var I;if(Ya)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else Mn?up(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(sp&&n.locale!=="ko"&&(Mn||N!=="onCompositionStart"?N==="onCompositionEnd"&&Mn&&(I=ip()):(Mt=c,Va="value"in Mt?Mt.value:Mt.textContent,Mn=!0)),x=gi(l,N),0<x.length&&(N=new pc(N,e,null,n,c),f.push({event:N,listeners:x}),I?N.data=I:(I=ap(n),I!==null&&(N.data=I)))),(I=Kg?Zg(e,n):qg(e,n))&&(l=gi(l,"onBeforeInput"),0<l.length&&(c=new pc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:l}),c.data=I))}_p(f,t)})}function Kr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function gi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=zr(e,n),i!=null&&r.unshift(Kr(e,i,o)),i=zr(e,t),i!=null&&r.push(Kr(e,i,o))),e=e.return}return r}function Ln(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Cc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var u=n,a=u.alternate,l=u.stateNode;if(a!==null&&a===r)break;u.tag===5&&l!==null&&(u=l,o?(a=zr(n,i),a!=null&&s.unshift(Kr(n,a,u))):o||(a=zr(n,i),a!=null&&s.push(Kr(n,a,u)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var dy=/\r\n?/g,py=/\u0000|\uFFFD/g;function Ic(e){return(typeof e=="string"?e:""+e).replace(dy,`
`).replace(py,"")}function Oo(e,t,n){if(t=Ic(t),Ic(e)!==t&&n)throw Error(_(425))}function yi(){}var ku=null,Nu=null;function Pu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ru=typeof setTimeout=="function"?setTimeout:void 0,hy=typeof clearTimeout=="function"?clearTimeout:void 0,kc=typeof Promise=="function"?Promise:void 0,my=typeof queueMicrotask=="function"?queueMicrotask:typeof kc<"u"?function(e){return kc.resolve(null).then(e).catch(gy)}:Ru;function gy(e){setTimeout(function(){throw e})}function Fs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Wr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Wr(t)}function $t(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Nc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var hr=Math.random().toString(36).slice(2),at="__reactFiber$"+hr,Zr="__reactProps$"+hr,Tt="__reactContainer$"+hr,Ou="__reactEvents$"+hr,yy="__reactListeners$"+hr,vy="__reactHandles$"+hr;function un(e){var t=e[at];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Tt]||n[at]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Nc(e);e!==null;){if(n=e[at])return n;e=Nc(e)}return t}e=n,n=e.parentNode}return null}function lo(e){return e=e[at]||e[Tt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Bn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function zi(e){return e[Zr]||null}var Lu=[],Un=-1;function qt(e){return{current:e}}function V(e){0>Un||(e.current=Lu[Un],Lu[Un]=null,Un--)}function j(e,t){Un++,Lu[Un]=e.current,e.current=t}var Xt={},ge=qt(Xt),Ne=qt(!1),hn=Xt;function tr(e,t){var n=e.type.contextTypes;if(!n)return Xt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Pe(e){return e=e.childContextTypes,e!=null}function vi(){V(Ne),V(ge)}function Pc(e,t,n){if(ge.current!==Xt)throw Error(_(168));j(ge,t),j(Ne,n)}function wp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(_(108,ng(e)||"Unknown",o));return K({},n,r)}function Ei(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Xt,hn=ge.current,j(ge,e),j(Ne,Ne.current),!0}function Rc(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=wp(e,t,hn),r.__reactInternalMemoizedMergedChildContext=e,V(Ne),V(ge),j(ge,e)):V(Ne),j(Ne,n)}var gt=null,Gi=!1,Hs=!1;function xp(e){gt===null?gt=[e]:gt.push(e)}function Ey(e){Gi=!0,xp(e)}function Jt(){if(!Hs&&gt!==null){Hs=!0;var e=0,t=H;try{var n=gt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}gt=null,Gi=!1}catch(o){throw gt!==null&&(gt=gt.slice(e+1)),Yd($a,Jt),o}finally{H=t,Hs=!1}}return null}var $n=[],jn=0,_i=null,Si=0,je=[],ze=0,mn=null,vt=1,Et="";function on(e,t){$n[jn++]=Si,$n[jn++]=_i,_i=e,Si=t}function Tp(e,t,n){je[ze++]=vt,je[ze++]=Et,je[ze++]=mn,mn=e;var r=vt;e=Et;var o=32-nt(r)-1;r&=~(1<<o),n+=1;var i=32-nt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,vt=1<<32-nt(t)+o|n<<o|r,Et=i+e}else vt=1<<i|n<<o|r,Et=e}function Ka(e){e.return!==null&&(on(e,1),Tp(e,1,0))}function Za(e){for(;e===_i;)_i=$n[--jn],$n[jn]=null,Si=$n[--jn],$n[jn]=null;for(;e===mn;)mn=je[--ze],je[ze]=null,Et=je[--ze],je[ze]=null,vt=je[--ze],je[ze]=null}var be=null,Le=null,W=!1,tt=null;function Cp(e,t){var n=Ve(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Oc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,be=e,Le=$t(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,be=e,Le=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=mn!==null?{id:vt,overflow:Et}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ve(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,be=e,Le=null,!0):!1;default:return!1}}function Au(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Du(e){if(W){var t=Le;if(t){var n=t;if(!Oc(e,t)){if(Au(e))throw Error(_(418));t=$t(n.nextSibling);var r=be;t&&Oc(e,t)?Cp(r,n):(e.flags=e.flags&-4097|2,W=!1,be=e)}}else{if(Au(e))throw Error(_(418));e.flags=e.flags&-4097|2,W=!1,be=e}}}function Lc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;be=e}function Lo(e){if(e!==be)return!1;if(!W)return Lc(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pu(e.type,e.memoizedProps)),t&&(t=Le)){if(Au(e))throw Ip(),Error(_(418));for(;t;)Cp(e,t),t=$t(t.nextSibling)}if(Lc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Le=$t(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Le=null}}else Le=be?$t(e.stateNode.nextSibling):null;return!0}function Ip(){for(var e=Le;e;)e=$t(e.nextSibling)}function nr(){Le=be=null,W=!1}function qa(e){tt===null?tt=[e]:tt.push(e)}var _y=kt.ReactCurrentBatchConfig;function xr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var u=o.refs;s===null?delete u[i]:u[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function Ao(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ac(e){var t=e._init;return t(e._payload)}function kp(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=Vt(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function u(h,p,g,E){return p===null||p.tag!==6?(p=Vs(g,h.mode,E),p.return=h,p):(p=o(p,g),p.return=h,p)}function a(h,p,g,E){var w=g.type;return w===bn?c(h,p,g.props.children,E,g.key):p!==null&&(p.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Rt&&Ac(w)===p.type)?(E=o(p,g.props),E.ref=xr(h,p,g),E.return=h,E):(E=ii(g.type,g.key,g.props,null,h.mode,E),E.ref=xr(h,p,g),E.return=h,E)}function l(h,p,g,E){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=Ws(g,h.mode,E),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function c(h,p,g,E,w){return p===null||p.tag!==7?(p=fn(g,h.mode,E,w),p.return=h,p):(p=o(p,g),p.return=h,p)}function f(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Vs(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case wo:return g=ii(p.type,p.key,p.props,null,h.mode,g),g.ref=xr(h,null,p),g.return=h,g;case Dn:return p=Ws(p,h.mode,g),p.return=h,p;case Rt:var E=p._init;return f(h,E(p._payload),g)}if(Nr(p)||vr(p))return p=fn(p,h.mode,g,null),p.return=h,p;Ao(h,p)}return null}function d(h,p,g,E){var w=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return w!==null?null:u(h,p,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case wo:return g.key===w?a(h,p,g,E):null;case Dn:return g.key===w?l(h,p,g,E):null;case Rt:return w=g._init,d(h,p,w(g._payload),E)}if(Nr(g)||vr(g))return w!==null?null:c(h,p,g,E,null);Ao(h,g)}return null}function m(h,p,g,E,w){if(typeof E=="string"&&E!==""||typeof E=="number")return h=h.get(g)||null,u(p,h,""+E,w);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case wo:return h=h.get(E.key===null?g:E.key)||null,a(p,h,E,w);case Dn:return h=h.get(E.key===null?g:E.key)||null,l(p,h,E,w);case Rt:var x=E._init;return m(h,p,g,x(E._payload),w)}if(Nr(E)||vr(E))return h=h.get(g)||null,c(p,h,E,w,null);Ao(p,E)}return null}function y(h,p,g,E){for(var w=null,x=null,I=p,N=p=0,X=null;I!==null&&N<g.length;N++){I.index>N?(X=I,I=null):X=I.sibling;var A=d(h,I,g[N],E);if(A===null){I===null&&(I=X);break}e&&I&&A.alternate===null&&t(h,I),p=i(A,p,N),x===null?w=A:x.sibling=A,x=A,I=X}if(N===g.length)return n(h,I),W&&on(h,N),w;if(I===null){for(;N<g.length;N++)I=f(h,g[N],E),I!==null&&(p=i(I,p,N),x===null?w=I:x.sibling=I,x=I);return W&&on(h,N),w}for(I=r(h,I);N<g.length;N++)X=m(I,h,N,g[N],E),X!==null&&(e&&X.alternate!==null&&I.delete(X.key===null?N:X.key),p=i(X,p,N),x===null?w=X:x.sibling=X,x=X);return e&&I.forEach(function(Ze){return t(h,Ze)}),W&&on(h,N),w}function v(h,p,g,E){var w=vr(g);if(typeof w!="function")throw Error(_(150));if(g=w.call(g),g==null)throw Error(_(151));for(var x=w=null,I=p,N=p=0,X=null,A=g.next();I!==null&&!A.done;N++,A=g.next()){I.index>N?(X=I,I=null):X=I.sibling;var Ze=d(h,I,A.value,E);if(Ze===null){I===null&&(I=X);break}e&&I&&Ze.alternate===null&&t(h,I),p=i(Ze,p,N),x===null?w=Ze:x.sibling=Ze,x=Ze,I=X}if(A.done)return n(h,I),W&&on(h,N),w;if(I===null){for(;!A.done;N++,A=g.next())A=f(h,A.value,E),A!==null&&(p=i(A,p,N),x===null?w=A:x.sibling=A,x=A);return W&&on(h,N),w}for(I=r(h,I);!A.done;N++,A=g.next())A=m(I,h,N,A.value,E),A!==null&&(e&&A.alternate!==null&&I.delete(A.key===null?N:A.key),p=i(A,p,N),x===null?w=A:x.sibling=A,x=A);return e&&I.forEach(function(gr){return t(h,gr)}),W&&on(h,N),w}function S(h,p,g,E){if(typeof g=="object"&&g!==null&&g.type===bn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case wo:e:{for(var w=g.key,x=p;x!==null;){if(x.key===w){if(w=g.type,w===bn){if(x.tag===7){n(h,x.sibling),p=o(x,g.props.children),p.return=h,h=p;break e}}else if(x.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Rt&&Ac(w)===x.type){n(h,x.sibling),p=o(x,g.props),p.ref=xr(h,x,g),p.return=h,h=p;break e}n(h,x);break}else t(h,x);x=x.sibling}g.type===bn?(p=fn(g.props.children,h.mode,E,g.key),p.return=h,h=p):(E=ii(g.type,g.key,g.props,null,h.mode,E),E.ref=xr(h,p,g),E.return=h,h=E)}return s(h);case Dn:e:{for(x=g.key;p!==null;){if(p.key===x)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=Ws(g,h.mode,E),p.return=h,h=p}return s(h);case Rt:return x=g._init,S(h,p,x(g._payload),E)}if(Nr(g))return y(h,p,g,E);if(vr(g))return v(h,p,g,E);Ao(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=Vs(g,h.mode,E),p.return=h,h=p),s(h)):n(h,p)}return S}var rr=kp(!0),Np=kp(!1),wi=qt(null),xi=null,zn=null,Ja=null;function el(){Ja=zn=xi=null}function tl(e){var t=wi.current;V(wi),e._currentValue=t}function bu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Kn(e,t){xi=e,Ja=zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Xe(e){var t=e._currentValue;if(Ja!==e)if(e={context:e,memoizedValue:t,next:null},zn===null){if(xi===null)throw Error(_(308));zn=e,xi.dependencies={lanes:0,firstContext:e}}else zn=zn.next=e;return t}var an=null;function nl(e){an===null?an=[e]:an.push(e)}function Pp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,nl(t)):(n.next=o.next,o.next=n),t.interleaved=n,Ct(e,r)}function Ct(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ot=!1;function rl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Rp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function _t(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function jt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,M&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Ct(e,n)}return o=r.interleaved,o===null?(t.next=t,nl(r)):(t.next=o.next,o.next=t),r.interleaved=t,Ct(e,n)}function Jo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ja(e,n)}}function Dc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ti(e,t,n,r){var o=e.updateQueue;Ot=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,u=o.shared.pending;if(u!==null){o.shared.pending=null;var a=u,l=a.next;a.next=null,s===null?i=l:s.next=l,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,u=c.lastBaseUpdate,u!==s&&(u===null?c.firstBaseUpdate=l:u.next=l,c.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;s=0,c=l=a=null,u=i;do{var d=u.lane,m=u.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:m,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var y=e,v=u;switch(d=t,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(m,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(m,f,d):y,d==null)break e;f=K({},f,d);break e;case 2:Ot=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[u]:d.push(u))}else m={eventTime:m,lane:d,tag:u.tag,payload:u.payload,callback:u.callback,next:null},c===null?(l=c=m,a=f):c=c.next=m,s|=d;if(u=u.next,u===null){if(u=o.shared.pending,u===null)break;d=u,u=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(c===null&&(a=f),o.baseState=a,o.firstBaseUpdate=l,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);yn|=s,e.lanes=s,e.memoizedState=f}}function bc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(_(191,o));o.call(r)}}}var co={},ft=qt(co),qr=qt(co),Jr=qt(co);function ln(e){if(e===co)throw Error(_(174));return e}function ol(e,t){switch(j(Jr,t),j(qr,e),j(ft,co),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:mu(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=mu(t,e)}V(ft),j(ft,t)}function or(){V(ft),V(qr),V(Jr)}function Op(e){ln(Jr.current);var t=ln(ft.current),n=mu(t,e.type);t!==n&&(j(qr,e),j(ft,n))}function il(e){qr.current===e&&(V(ft),V(qr))}var Y=qt(0);function Ci(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Bs=[];function sl(){for(var e=0;e<Bs.length;e++)Bs[e]._workInProgressVersionPrimary=null;Bs.length=0}var ei=kt.ReactCurrentDispatcher,Us=kt.ReactCurrentBatchConfig,gn=0,Q=null,te=null,ie=null,Ii=!1,Fr=!1,eo=0,Sy=0;function fe(){throw Error(_(321))}function ul(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!it(e[n],t[n]))return!1;return!0}function al(e,t,n,r,o,i){if(gn=i,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ei.current=e===null||e.memoizedState===null?Cy:Iy,e=n(r,o),Fr){i=0;do{if(Fr=!1,eo=0,25<=i)throw Error(_(301));i+=1,ie=te=null,t.updateQueue=null,ei.current=ky,e=n(r,o)}while(Fr)}if(ei.current=ki,t=te!==null&&te.next!==null,gn=0,ie=te=Q=null,Ii=!1,t)throw Error(_(300));return e}function ll(){var e=eo!==0;return eo=0,e}function ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?Q.memoizedState=ie=e:ie=ie.next=e,ie}function Ye(){if(te===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=ie===null?Q.memoizedState:ie.next;if(t!==null)ie=t,te=e;else{if(e===null)throw Error(_(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},ie===null?Q.memoizedState=ie=e:ie=ie.next=e}return ie}function to(e,t){return typeof t=="function"?t(e):t}function $s(e){var t=Ye(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=te,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var u=s=null,a=null,l=i;do{var c=l.lane;if((gn&c)===c)a!==null&&(a=a.next={lane:0,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null}),r=l.hasEagerState?l.eagerState:e(r,l.action);else{var f={lane:c,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null};a===null?(u=a=f,s=r):a=a.next=f,Q.lanes|=c,yn|=c}l=l.next}while(l!==null&&l!==i);a===null?s=r:a.next=u,it(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Q.lanes|=i,yn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function js(e){var t=Ye(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);it(i,t.memoizedState)||(ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Lp(){}function Ap(e,t){var n=Q,r=Ye(),o=t(),i=!it(r.memoizedState,o);if(i&&(r.memoizedState=o,ke=!0),r=r.queue,cl(Mp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ie!==null&&ie.memoizedState.tag&1){if(n.flags|=2048,no(9,bp.bind(null,n,r,o,t),void 0,null),se===null)throw Error(_(349));gn&30||Dp(n,t,o)}return o}function Dp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function bp(e,t,n,r){t.value=n,t.getSnapshot=r,Fp(t)&&Hp(e)}function Mp(e,t,n){return n(function(){Fp(t)&&Hp(e)})}function Fp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!it(e,n)}catch{return!0}}function Hp(e){var t=Ct(e,1);t!==null&&rt(t,e,1,-1)}function Mc(e){var t=ut();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:to,lastRenderedState:e},t.queue=e,e=e.dispatch=Ty.bind(null,Q,e),[t.memoizedState,e]}function no(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Bp(){return Ye().memoizedState}function ti(e,t,n,r){var o=ut();Q.flags|=e,o.memoizedState=no(1|t,n,void 0,r===void 0?null:r)}function Vi(e,t,n,r){var o=Ye();r=r===void 0?null:r;var i=void 0;if(te!==null){var s=te.memoizedState;if(i=s.destroy,r!==null&&ul(r,s.deps)){o.memoizedState=no(t,n,i,r);return}}Q.flags|=e,o.memoizedState=no(1|t,n,i,r)}function Fc(e,t){return ti(8390656,8,e,t)}function cl(e,t){return Vi(2048,8,e,t)}function Up(e,t){return Vi(4,2,e,t)}function $p(e,t){return Vi(4,4,e,t)}function jp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function zp(e,t,n){return n=n!=null?n.concat([e]):null,Vi(4,4,jp.bind(null,t,e),n)}function fl(){}function Gp(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ul(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vp(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ul(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wp(e,t,n){return gn&21?(it(n,t)||(n=Zd(),Q.lanes|=n,yn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function wy(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=Us.transition;Us.transition={};try{e(!1),t()}finally{H=n,Us.transition=r}}function Xp(){return Ye().memoizedState}function xy(e,t,n){var r=Gt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yp(e))Qp(t,n);else if(n=Pp(e,t,n,r),n!==null){var o=xe();rt(n,e,r,o),Kp(n,t,r)}}function Ty(e,t,n){var r=Gt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yp(e))Qp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,u=i(s,n);if(o.hasEagerState=!0,o.eagerState=u,it(u,s)){var a=t.interleaved;a===null?(o.next=o,nl(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Pp(e,t,o,r),n!==null&&(o=xe(),rt(n,e,r,o),Kp(n,t,r))}}function Yp(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function Qp(e,t){Fr=Ii=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ja(e,n)}}var ki={readContext:Xe,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},Cy={readContext:Xe,useCallback:function(e,t){return ut().memoizedState=[e,t===void 0?null:t],e},useContext:Xe,useEffect:Fc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ti(4194308,4,jp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ti(4194308,4,e,t)},useInsertionEffect:function(e,t){return ti(4,2,e,t)},useMemo:function(e,t){var n=ut();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ut();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=xy.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=ut();return e={current:e},t.memoizedState=e},useState:Mc,useDebugValue:fl,useDeferredValue:function(e){return ut().memoizedState=e},useTransition:function(){var e=Mc(!1),t=e[0];return e=wy.bind(null,e[1]),ut().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,o=ut();if(W){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),se===null)throw Error(_(349));gn&30||Dp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Fc(Mp.bind(null,r,i,e),[e]),r.flags|=2048,no(9,bp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=ut(),t=se.identifierPrefix;if(W){var n=Et,r=vt;n=(r&~(1<<32-nt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=eo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Sy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Iy={readContext:Xe,useCallback:Gp,useContext:Xe,useEffect:cl,useImperativeHandle:zp,useInsertionEffect:Up,useLayoutEffect:$p,useMemo:Vp,useReducer:$s,useRef:Bp,useState:function(){return $s(to)},useDebugValue:fl,useDeferredValue:function(e){var t=Ye();return Wp(t,te.memoizedState,e)},useTransition:function(){var e=$s(to)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:Lp,useSyncExternalStore:Ap,useId:Xp,unstable_isNewReconciler:!1},ky={readContext:Xe,useCallback:Gp,useContext:Xe,useEffect:cl,useImperativeHandle:zp,useInsertionEffect:Up,useLayoutEffect:$p,useMemo:Vp,useReducer:js,useRef:Bp,useState:function(){return js(to)},useDebugValue:fl,useDeferredValue:function(e){var t=Ye();return te===null?t.memoizedState=e:Wp(t,te.memoizedState,e)},useTransition:function(){var e=js(to)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:Lp,useSyncExternalStore:Ap,useId:Xp,unstable_isNewReconciler:!1};function Je(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Mu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Wi={isMounted:function(e){return(e=e._reactInternals)?In(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),o=Gt(e),i=_t(r,o);i.payload=t,n!=null&&(i.callback=n),t=jt(e,i,o),t!==null&&(rt(t,e,o,r),Jo(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),o=Gt(e),i=_t(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=jt(e,i,o),t!==null&&(rt(t,e,o,r),Jo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Gt(e),o=_t(n,r);o.tag=2,t!=null&&(o.callback=t),t=jt(e,o,r),t!==null&&(rt(t,e,r,n),Jo(t,e,r))}};function Hc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Yr(n,r)||!Yr(o,i):!0}function Zp(e,t,n){var r=!1,o=Xt,i=t.contextType;return typeof i=="object"&&i!==null?i=Xe(i):(o=Pe(t)?hn:ge.current,r=t.contextTypes,i=(r=r!=null)?tr(e,o):Xt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Wi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Bc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Wi.enqueueReplaceState(t,t.state,null)}function Fu(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},rl(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Xe(i):(i=Pe(t)?hn:ge.current,o.context=tr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Mu(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Wi.enqueueReplaceState(o,o.state,null),Ti(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function ir(e,t){try{var n="",r=t;do n+=tg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function zs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Hu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ny=typeof WeakMap=="function"?WeakMap:Map;function qp(e,t,n){n=_t(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Pi||(Pi=!0,Yu=r),Hu(e,t)},n}function Jp(e,t,n){n=_t(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Hu(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Hu(e,t),typeof r!="function"&&(zt===null?zt=new Set([this]):zt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Uc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ny;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=jy.bind(null,e,t,n),t.then(e,e))}function $c(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function jc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=_t(-1,1),t.tag=2,jt(n,t,1))),n.lanes|=1),e)}var Py=kt.ReactCurrentOwner,ke=!1;function Ee(e,t,n,r){t.child=e===null?Np(t,null,n,r):rr(t,e.child,n,r)}function zc(e,t,n,r,o){n=n.render;var i=t.ref;return Kn(t,o),r=al(e,t,n,r,i,o),n=ll(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,It(e,t,o)):(W&&n&&Ka(t),t.flags|=1,Ee(e,t,r,o),t.child)}function Gc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!El(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,eh(e,t,i,r,o)):(e=ii(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Yr,n(s,r)&&e.ref===t.ref)return It(e,t,o)}return t.flags|=1,e=Vt(i,r),e.ref=t.ref,e.return=t,t.child=e}function eh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Yr(i,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,It(e,t,o)}return Bu(e,t,n,r,o)}function th(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},j(Vn,Oe),Oe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,j(Vn,Oe),Oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,j(Vn,Oe),Oe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,j(Vn,Oe),Oe|=r;return Ee(e,t,o,n),t.child}function nh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Bu(e,t,n,r,o){var i=Pe(n)?hn:ge.current;return i=tr(t,i),Kn(t,o),n=al(e,t,n,r,i,o),r=ll(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,It(e,t,o)):(W&&r&&Ka(t),t.flags|=1,Ee(e,t,n,o),t.child)}function Vc(e,t,n,r,o){if(Pe(n)){var i=!0;Ei(t)}else i=!1;if(Kn(t,o),t.stateNode===null)ni(e,t),Zp(t,n,r),Fu(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,u=t.memoizedProps;s.props=u;var a=s.context,l=n.contextType;typeof l=="object"&&l!==null?l=Xe(l):(l=Pe(n)?hn:ge.current,l=tr(t,l));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u!==r||a!==l)&&Bc(t,s,r,l),Ot=!1;var d=t.memoizedState;s.state=d,Ti(t,r,s,o),a=t.memoizedState,u!==r||d!==a||Ne.current||Ot?(typeof c=="function"&&(Mu(t,n,c,r),a=t.memoizedState),(u=Ot||Hc(t,n,u,r,d,a,l))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=l,r=u):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Rp(e,t),u=t.memoizedProps,l=t.type===t.elementType?u:Je(t.type,u),s.props=l,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Xe(a):(a=Pe(n)?hn:ge.current,a=tr(t,a));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u!==f||d!==a)&&Bc(t,s,r,a),Ot=!1,d=t.memoizedState,s.state=d,Ti(t,r,s,o);var y=t.memoizedState;u!==f||d!==y||Ne.current||Ot?(typeof m=="function"&&(Mu(t,n,m,r),y=t.memoizedState),(l=Ot||Hc(t,n,l,r,d,y,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=l):(typeof s.componentDidUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Uu(e,t,n,r,i,o)}function Uu(e,t,n,r,o,i){nh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Rc(t,n,!1),It(e,t,i);r=t.stateNode,Py.current=t;var u=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=rr(t,e.child,null,i),t.child=rr(t,null,u,i)):Ee(e,t,u,i),t.memoizedState=r.state,o&&Rc(t,n,!0),t.child}function rh(e){var t=e.stateNode;t.pendingContext?Pc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Pc(e,t.context,!1),ol(e,t.containerInfo)}function Wc(e,t,n,r,o){return nr(),qa(o),t.flags|=256,Ee(e,t,n,r),t.child}var $u={dehydrated:null,treeContext:null,retryLane:0};function ju(e){return{baseLanes:e,cachePool:null,transitions:null}}function oh(e,t,n){var r=t.pendingProps,o=Y.current,i=!1,s=(t.flags&128)!==0,u;if((u=s)||(u=e!==null&&e.memoizedState===null?!1:(o&2)!==0),u?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),j(Y,o&1),e===null)return Du(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Qi(s,r,0,null),e=fn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ju(n),t.memoizedState=$u,e):dl(t,s));if(o=e.memoizedState,o!==null&&(u=o.dehydrated,u!==null))return Ry(e,t,s,r,u,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,u=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Vt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),u!==null?i=Vt(u,i):(i=fn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?ju(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=$u,r}return i=e.child,e=i.sibling,r=Vt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function dl(e,t){return t=Qi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Do(e,t,n,r){return r!==null&&qa(r),rr(t,e.child,null,n),e=dl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ry(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=zs(Error(_(422))),Do(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Qi({mode:"visible",children:r.children},o,0,null),i=fn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&rr(t,e.child,null,s),t.child.memoizedState=ju(s),t.memoizedState=$u,i);if(!(t.mode&1))return Do(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var u=r.dgst;return r=u,i=Error(_(419)),r=zs(i,r,void 0),Do(e,t,s,r)}if(u=(s&e.childLanes)!==0,ke||u){if(r=se,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Ct(e,o),rt(r,e,o,-1))}return vl(),r=zs(Error(_(421))),Do(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=zy.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Le=$t(o.nextSibling),be=t,W=!0,tt=null,e!==null&&(je[ze++]=vt,je[ze++]=Et,je[ze++]=mn,vt=e.id,Et=e.overflow,mn=t),t=dl(t,r.children),t.flags|=4096,t)}function Xc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),bu(e.return,t,n)}function Gs(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function ih(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ee(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Xc(e,n,t);else if(e.tag===19)Xc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(j(Y,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ci(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Gs(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ci(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Gs(t,!0,n,null,i);break;case"together":Gs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ni(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function It(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),yn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Vt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Vt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Oy(e,t,n){switch(t.tag){case 3:rh(t),nr();break;case 5:Op(t);break;case 1:Pe(t.type)&&Ei(t);break;case 4:ol(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;j(wi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(j(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?oh(e,t,n):(j(Y,Y.current&1),e=It(e,t,n),e!==null?e.sibling:null);j(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ih(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),j(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,th(e,t,n)}return It(e,t,n)}var sh,zu,uh,ah;sh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};zu=function(){};uh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,ln(ft.current);var i=null;switch(n){case"input":o=fu(e,o),r=fu(e,r),i=[];break;case"select":o=K({},o,{value:void 0}),r=K({},r,{value:void 0}),i=[];break;case"textarea":o=hu(e,o),r=hu(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=yi)}gu(n,r);var s;n=null;for(l in o)if(!r.hasOwnProperty(l)&&o.hasOwnProperty(l)&&o[l]!=null)if(l==="style"){var u=o[l];for(s in u)u.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else l!=="dangerouslySetInnerHTML"&&l!=="children"&&l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&($r.hasOwnProperty(l)?i||(i=[]):(i=i||[]).push(l,null));for(l in r){var a=r[l];if(u=o!=null?o[l]:void 0,r.hasOwnProperty(l)&&a!==u&&(a!=null||u!=null))if(l==="style")if(u){for(s in u)!u.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&u[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(l,n)),n=a;else l==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,u=u?u.__html:void 0,a!=null&&u!==a&&(i=i||[]).push(l,a)):l==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(l,""+a):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&($r.hasOwnProperty(l)?(a!=null&&l==="onScroll"&&z("scroll",e),i||u===a||(i=[])):(i=i||[]).push(l,a))}n&&(i=i||[]).push("style",n);var l=i;(t.updateQueue=l)&&(t.flags|=4)}};ah=function(e,t,n,r){n!==r&&(t.flags|=4)};function Tr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ly(e,t,n){var r=t.pendingProps;switch(Za(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Pe(t.type)&&vi(),de(t),null;case 3:return r=t.stateNode,or(),V(Ne),V(ge),sl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Lo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,tt!==null&&(Zu(tt),tt=null))),zu(e,t),de(t),null;case 5:il(t);var o=ln(Jr.current);if(n=t.type,e!==null&&t.stateNode!=null)uh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return de(t),null}if(e=ln(ft.current),Lo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[at]=t,r[Zr]=i,e=(t.mode&1)!==0,n){case"dialog":z("cancel",r),z("close",r);break;case"iframe":case"object":case"embed":z("load",r);break;case"video":case"audio":for(o=0;o<Rr.length;o++)z(Rr[o],r);break;case"source":z("error",r);break;case"img":case"image":case"link":z("error",r),z("load",r);break;case"details":z("toggle",r);break;case"input":nc(r,i),z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},z("invalid",r);break;case"textarea":oc(r,i),z("invalid",r)}gu(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var u=i[s];s==="children"?typeof u=="string"?r.textContent!==u&&(i.suppressHydrationWarning!==!0&&Oo(r.textContent,u,e),o=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(i.suppressHydrationWarning!==!0&&Oo(r.textContent,u,e),o=["children",""+u]):$r.hasOwnProperty(s)&&u!=null&&s==="onScroll"&&z("scroll",r)}switch(n){case"input":xo(r),rc(r,i,!0);break;case"textarea":xo(r),ic(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=yi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Md(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[at]=t,e[Zr]=r,sh(e,t,!1,!1),t.stateNode=e;e:{switch(s=yu(n,r),n){case"dialog":z("cancel",e),z("close",e),o=r;break;case"iframe":case"object":case"embed":z("load",e),o=r;break;case"video":case"audio":for(o=0;o<Rr.length;o++)z(Rr[o],e);o=r;break;case"source":z("error",e),o=r;break;case"img":case"image":case"link":z("error",e),z("load",e),o=r;break;case"details":z("toggle",e),o=r;break;case"input":nc(e,r),o=fu(e,r),z("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=K({},r,{value:void 0}),z("invalid",e);break;case"textarea":oc(e,r),o=hu(e,r),z("invalid",e);break;default:o=r}gu(n,o),u=o;for(i in u)if(u.hasOwnProperty(i)){var a=u[i];i==="style"?Bd(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Fd(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&jr(e,a):typeof a=="number"&&jr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&($r.hasOwnProperty(i)?a!=null&&i==="onScroll"&&z("scroll",e):a!=null&&Ma(e,i,a,s))}switch(n){case"input":xo(e),rc(e,r,!1);break;case"textarea":xo(e),ic(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Wt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Wn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=yi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)ah(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=ln(Jr.current),ln(ft.current),Lo(t)){if(r=t.stateNode,n=t.memoizedProps,r[at]=t,(i=r.nodeValue!==n)&&(e=be,e!==null))switch(e.tag){case 3:Oo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Oo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[at]=t,t.stateNode=r}return de(t),null;case 13:if(V(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Le!==null&&t.mode&1&&!(t.flags&128))Ip(),nr(),t.flags|=98560,i=!1;else if(i=Lo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(_(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(_(317));i[at]=t}else nr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),i=!1}else tt!==null&&(Zu(tt),tt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?ne===0&&(ne=3):vl())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return or(),zu(e,t),e===null&&Qr(t.stateNode.containerInfo),de(t),null;case 10:return tl(t.type._context),de(t),null;case 17:return Pe(t.type)&&vi(),de(t),null;case 19:if(V(Y),i=t.memoizedState,i===null)return de(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Tr(i,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Ci(e),s!==null){for(t.flags|=128,Tr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return j(Y,Y.current&1|2),t.child}e=e.sibling}i.tail!==null&&J()>sr&&(t.flags|=128,r=!0,Tr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ci(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Tr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!W)return de(t),null}else 2*J()-i.renderingStartTime>sr&&n!==1073741824&&(t.flags|=128,r=!0,Tr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=J(),t.sibling=null,n=Y.current,j(Y,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return yl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Oe&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function Ay(e,t){switch(Za(t),t.tag){case 1:return Pe(t.type)&&vi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return or(),V(Ne),V(ge),sl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return il(t),null;case 13:if(V(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));nr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(Y),null;case 4:return or(),null;case 10:return tl(t.type._context),null;case 22:case 23:return yl(),null;case 24:return null;default:return null}}var bo=!1,pe=!1,Dy=typeof WeakSet=="function"?WeakSet:Set,T=null;function Gn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function Gu(e,t,n){try{n()}catch(r){Z(e,t,r)}}var Yc=!1;function by(e,t){if(ku=hi,e=pp(),Qa(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,u=-1,a=-1,l=0,c=0,f=e,d=null;t:for(;;){for(var m;f!==n||o!==0&&f.nodeType!==3||(u=s+o),f!==i||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===e)break t;if(d===n&&++l===o&&(u=s),d===i&&++c===r&&(a=s),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=u===-1||a===-1?null:{start:u,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Nu={focusedElem:e,selectionRange:n},hi=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:Je(t.type,v),S);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(E){Z(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return y=Yc,Yc=!1,y}function Hr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Gu(t,n,i)}o=o.next}while(o!==r)}}function Xi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Vu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function lh(e){var t=e.alternate;t!==null&&(e.alternate=null,lh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[at],delete t[Zr],delete t[Ou],delete t[yy],delete t[vy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ch(e){return e.tag===5||e.tag===3||e.tag===4}function Qc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ch(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Wu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=yi));else if(r!==4&&(e=e.child,e!==null))for(Wu(e,t,n),e=e.sibling;e!==null;)Wu(e,t,n),e=e.sibling}function Xu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Xu(e,t,n),e=e.sibling;e!==null;)Xu(e,t,n),e=e.sibling}var ae=null,et=!1;function Pt(e,t,n){for(n=n.child;n!==null;)fh(e,t,n),n=n.sibling}function fh(e,t,n){if(ct&&typeof ct.onCommitFiberUnmount=="function")try{ct.onCommitFiberUnmount(Bi,n)}catch{}switch(n.tag){case 5:pe||Gn(n,t);case 6:var r=ae,o=et;ae=null,Pt(e,t,n),ae=r,et=o,ae!==null&&(et?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(et?(e=ae,n=n.stateNode,e.nodeType===8?Fs(e.parentNode,n):e.nodeType===1&&Fs(e,n),Wr(e)):Fs(ae,n.stateNode));break;case 4:r=ae,o=et,ae=n.stateNode.containerInfo,et=!0,Pt(e,t,n),ae=r,et=o;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Gu(n,t,s),o=o.next}while(o!==r)}Pt(e,t,n);break;case 1:if(!pe&&(Gn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){Z(n,t,u)}Pt(e,t,n);break;case 21:Pt(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,Pt(e,t,n),pe=r):Pt(e,t,n);break;default:Pt(e,t,n)}}function Kc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Dy),t.forEach(function(r){var o=Gy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function qe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,u=s;e:for(;u!==null;){switch(u.tag){case 5:ae=u.stateNode,et=!1;break e;case 3:ae=u.stateNode.containerInfo,et=!0;break e;case 4:ae=u.stateNode.containerInfo,et=!0;break e}u=u.return}if(ae===null)throw Error(_(160));fh(i,s,o),ae=null,et=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(l){Z(o,t,l)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)dh(t,e),t=t.sibling}function dh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(qe(t,e),st(e),r&4){try{Hr(3,e,e.return),Xi(3,e)}catch(v){Z(e,e.return,v)}try{Hr(5,e,e.return)}catch(v){Z(e,e.return,v)}}break;case 1:qe(t,e),st(e),r&512&&n!==null&&Gn(n,n.return);break;case 5:if(qe(t,e),st(e),r&512&&n!==null&&Gn(n,n.return),e.flags&32){var o=e.stateNode;try{jr(o,"")}catch(v){Z(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,u=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{u==="input"&&i.type==="radio"&&i.name!=null&&Dd(o,i),yu(u,s);var l=yu(u,i);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Bd(o,f):c==="dangerouslySetInnerHTML"?Fd(o,f):c==="children"?jr(o,f):Ma(o,c,f,l)}switch(u){case"input":du(o,i);break;case"textarea":bd(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?Wn(o,!!i.multiple,m,!1):d!==!!i.multiple&&(i.defaultValue!=null?Wn(o,!!i.multiple,i.defaultValue,!0):Wn(o,!!i.multiple,i.multiple?[]:"",!1))}o[Zr]=i}catch(v){Z(e,e.return,v)}}break;case 6:if(qe(t,e),st(e),r&4){if(e.stateNode===null)throw Error(_(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){Z(e,e.return,v)}}break;case 3:if(qe(t,e),st(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Wr(t.containerInfo)}catch(v){Z(e,e.return,v)}break;case 4:qe(t,e),st(e);break;case 13:qe(t,e),st(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ml=J())),r&4&&Kc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(l=pe)||c,qe(t,e),pe=l):qe(t,e),st(e),r&8192){if(l=e.memoizedState!==null,(e.stateNode.isHidden=l)&&!c&&e.mode&1)for(T=e,c=e.child;c!==null;){for(f=T=c;T!==null;){switch(d=T,m=d.child,d.tag){case 0:case 11:case 14:case 15:Hr(4,d,d.return);break;case 1:Gn(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){Z(r,n,v)}}break;case 5:Gn(d,d.return);break;case 22:if(d.memoizedState!==null){qc(f);continue}}m!==null?(m.return=d,T=m):qc(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,l?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(u=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,u.style.display=Hd("display",s))}catch(v){Z(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=l?"":f.memoizedProps}catch(v){Z(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:qe(t,e),st(e),r&4&&Kc(e);break;case 21:break;default:qe(t,e),st(e)}}function st(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ch(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(jr(o,""),r.flags&=-33);var i=Qc(e);Xu(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,u=Qc(e);Wu(e,u,s);break;default:throw Error(_(161))}}catch(a){Z(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function My(e,t,n){T=e,ph(e)}function ph(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var o=T,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||bo;if(!s){var u=o.alternate,a=u!==null&&u.memoizedState!==null||pe;u=bo;var l=pe;if(bo=s,(pe=a)&&!l)for(T=o;T!==null;)s=T,a=s.child,s.tag===22&&s.memoizedState!==null?Jc(o):a!==null?(a.return=s,T=a):Jc(o);for(;i!==null;)T=i,ph(i),i=i.sibling;T=o,bo=u,pe=l}Zc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,T=i):Zc(e)}}function Zc(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||Xi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Je(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&bc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}bc(t,s,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var l=t.alternate;if(l!==null){var c=l.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Wr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}pe||t.flags&512&&Vu(t)}catch(d){Z(t,t.return,d)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function qc(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function Jc(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Xi(4,t)}catch(a){Z(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){Z(t,o,a)}}var i=t.return;try{Vu(t)}catch(a){Z(t,i,a)}break;case 5:var s=t.return;try{Vu(t)}catch(a){Z(t,s,a)}}}catch(a){Z(t,t.return,a)}if(t===e){T=null;break}var u=t.sibling;if(u!==null){u.return=t.return,T=u;break}T=t.return}}var Fy=Math.ceil,Ni=kt.ReactCurrentDispatcher,pl=kt.ReactCurrentOwner,We=kt.ReactCurrentBatchConfig,M=0,se=null,ee=null,le=0,Oe=0,Vn=qt(0),ne=0,ro=null,yn=0,Yi=0,hl=0,Br=null,Ie=null,ml=0,sr=1/0,ht=null,Pi=!1,Yu=null,zt=null,Mo=!1,Ft=null,Ri=0,Ur=0,Qu=null,ri=-1,oi=0;function xe(){return M&6?J():ri!==-1?ri:ri=J()}function Gt(e){return e.mode&1?M&2&&le!==0?le&-le:_y.transition!==null?(oi===0&&(oi=Zd()),oi):(e=H,e!==0||(e=window.event,e=e===void 0?16:op(e.type)),e):1}function rt(e,t,n,r){if(50<Ur)throw Ur=0,Qu=null,Error(_(185));uo(e,n,r),(!(M&2)||e!==se)&&(e===se&&(!(M&2)&&(Yi|=n),ne===4&&bt(e,le)),Re(e,r),n===1&&M===0&&!(t.mode&1)&&(sr=J()+500,Gi&&Jt()))}function Re(e,t){var n=e.callbackNode;_g(e,t);var r=pi(e,e===se?le:0);if(r===0)n!==null&&ac(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ac(n),t===1)e.tag===0?Ey(ef.bind(null,e)):xp(ef.bind(null,e)),my(function(){!(M&6)&&Jt()}),n=null;else{switch(qd(r)){case 1:n=$a;break;case 4:n=Qd;break;case 16:n=di;break;case 536870912:n=Kd;break;default:n=di}n=Sh(n,hh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function hh(e,t){if(ri=-1,oi=0,M&6)throw Error(_(327));var n=e.callbackNode;if(Zn()&&e.callbackNode!==n)return null;var r=pi(e,e===se?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Oi(e,r);else{t=r;var o=M;M|=2;var i=gh();(se!==e||le!==t)&&(ht=null,sr=J()+500,cn(e,t));do try{Uy();break}catch(u){mh(e,u)}while(!0);el(),Ni.current=i,M=o,ee!==null?t=0:(se=null,le=0,t=ne)}if(t!==0){if(t===2&&(o=wu(e),o!==0&&(r=o,t=Ku(e,o))),t===1)throw n=ro,cn(e,0),bt(e,r),Re(e,J()),n;if(t===6)bt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Hy(o)&&(t=Oi(e,r),t===2&&(i=wu(e),i!==0&&(r=i,t=Ku(e,i))),t===1))throw n=ro,cn(e,0),bt(e,r),Re(e,J()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:sn(e,Ie,ht);break;case 3:if(bt(e,r),(r&130023424)===r&&(t=ml+500-J(),10<t)){if(pi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ru(sn.bind(null,e,Ie,ht),t);break}sn(e,Ie,ht);break;case 4:if(bt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-nt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Fy(r/1960))-r,10<r){e.timeoutHandle=Ru(sn.bind(null,e,Ie,ht),r);break}sn(e,Ie,ht);break;case 5:sn(e,Ie,ht);break;default:throw Error(_(329))}}}return Re(e,J()),e.callbackNode===n?hh.bind(null,e):null}function Ku(e,t){var n=Br;return e.current.memoizedState.isDehydrated&&(cn(e,t).flags|=256),e=Oi(e,t),e!==2&&(t=Ie,Ie=n,t!==null&&Zu(t)),e}function Zu(e){Ie===null?Ie=e:Ie.push.apply(Ie,e)}function Hy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!it(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function bt(e,t){for(t&=~hl,t&=~Yi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-nt(t),r=1<<n;e[n]=-1,t&=~r}}function ef(e){if(M&6)throw Error(_(327));Zn();var t=pi(e,0);if(!(t&1))return Re(e,J()),null;var n=Oi(e,t);if(e.tag!==0&&n===2){var r=wu(e);r!==0&&(t=r,n=Ku(e,r))}if(n===1)throw n=ro,cn(e,0),bt(e,t),Re(e,J()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,sn(e,Ie,ht),Re(e,J()),null}function gl(e,t){var n=M;M|=1;try{return e(t)}finally{M=n,M===0&&(sr=J()+500,Gi&&Jt())}}function vn(e){Ft!==null&&Ft.tag===0&&!(M&6)&&Zn();var t=M;M|=1;var n=We.transition,r=H;try{if(We.transition=null,H=1,e)return e()}finally{H=r,We.transition=n,M=t,!(M&6)&&Jt()}}function yl(){Oe=Vn.current,V(Vn)}function cn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,hy(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(Za(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&vi();break;case 3:or(),V(Ne),V(ge),sl();break;case 5:il(r);break;case 4:or();break;case 13:V(Y);break;case 19:V(Y);break;case 10:tl(r.type._context);break;case 22:case 23:yl()}n=n.return}if(se=e,ee=e=Vt(e.current,null),le=Oe=t,ne=0,ro=null,hl=Yi=yn=0,Ie=Br=null,an!==null){for(t=0;t<an.length;t++)if(n=an[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}an=null}return e}function mh(e,t){do{var n=ee;try{if(el(),ei.current=ki,Ii){for(var r=Q.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ii=!1}if(gn=0,ie=te=Q=null,Fr=!1,eo=0,pl.current=null,n===null||n.return===null){ne=1,ro=t,ee=null;break}e:{var i=e,s=n.return,u=n,a=t;if(t=le,u.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var l=a,c=u,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=$c(s);if(m!==null){m.flags&=-257,jc(m,s,u,i,t),m.mode&1&&Uc(i,l,t),t=m,a=l;var y=t.updateQueue;if(y===null){var v=new Set;v.add(a),t.updateQueue=v}else y.add(a);break e}else{if(!(t&1)){Uc(i,l,t),vl();break e}a=Error(_(426))}}else if(W&&u.mode&1){var S=$c(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),jc(S,s,u,i,t),qa(ir(a,u));break e}}i=a=ir(a,u),ne!==4&&(ne=2),Br===null?Br=[i]:Br.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=qp(i,a,t);Dc(i,h);break e;case 1:u=a;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(zt===null||!zt.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=Jp(i,u,t);Dc(i,E);break e}}i=i.return}while(i!==null)}vh(n)}catch(w){t=w,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function gh(){var e=Ni.current;return Ni.current=ki,e===null?ki:e}function vl(){(ne===0||ne===3||ne===2)&&(ne=4),se===null||!(yn&268435455)&&!(Yi&268435455)||bt(se,le)}function Oi(e,t){var n=M;M|=2;var r=gh();(se!==e||le!==t)&&(ht=null,cn(e,t));do try{By();break}catch(o){mh(e,o)}while(!0);if(el(),M=n,Ni.current=r,ee!==null)throw Error(_(261));return se=null,le=0,ne}function By(){for(;ee!==null;)yh(ee)}function Uy(){for(;ee!==null&&!fg();)yh(ee)}function yh(e){var t=_h(e.alternate,e,Oe);e.memoizedProps=e.pendingProps,t===null?vh(e):ee=t,pl.current=null}function vh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ay(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,ee=null;return}}else if(n=Ly(n,t,Oe),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);ne===0&&(ne=5)}function sn(e,t,n){var r=H,o=We.transition;try{We.transition=null,H=1,$y(e,t,n,r)}finally{We.transition=o,H=r}return null}function $y(e,t,n,r){do Zn();while(Ft!==null);if(M&6)throw Error(_(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Sg(e,i),e===se&&(ee=se=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Mo||(Mo=!0,Sh(di,function(){return Zn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=We.transition,We.transition=null;var s=H;H=1;var u=M;M|=4,pl.current=null,by(e,n),dh(n,e),uy(Nu),hi=!!ku,Nu=ku=null,e.current=n,My(n),dg(),M=u,H=s,We.transition=i}else e.current=n;if(Mo&&(Mo=!1,Ft=e,Ri=o),i=e.pendingLanes,i===0&&(zt=null),mg(n.stateNode),Re(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Pi)throw Pi=!1,e=Yu,Yu=null,e;return Ri&1&&e.tag!==0&&Zn(),i=e.pendingLanes,i&1?e===Qu?Ur++:(Ur=0,Qu=e):Ur=0,Jt(),null}function Zn(){if(Ft!==null){var e=qd(Ri),t=We.transition,n=H;try{if(We.transition=null,H=16>e?16:e,Ft===null)var r=!1;else{if(e=Ft,Ft=null,Ri=0,M&6)throw Error(_(331));var o=M;for(M|=4,T=e.current;T!==null;){var i=T,s=i.child;if(T.flags&16){var u=i.deletions;if(u!==null){for(var a=0;a<u.length;a++){var l=u[a];for(T=l;T!==null;){var c=T;switch(c.tag){case 0:case 11:case 15:Hr(8,c,i)}var f=c.child;if(f!==null)f.return=c,T=f;else for(;T!==null;){c=T;var d=c.sibling,m=c.return;if(lh(c),c===l){T=null;break}if(d!==null){d.return=m,T=d;break}T=m}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}T=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,T=s;else e:for(;T!==null;){if(i=T,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Hr(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,T=h;break e}T=i.return}}var p=e.current;for(T=p;T!==null;){s=T;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,T=g;else e:for(s=p;T!==null;){if(u=T,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:Xi(9,u)}}catch(w){Z(u,u.return,w)}if(u===s){T=null;break e}var E=u.sibling;if(E!==null){E.return=u.return,T=E;break e}T=u.return}}if(M=o,Jt(),ct&&typeof ct.onPostCommitFiberRoot=="function")try{ct.onPostCommitFiberRoot(Bi,e)}catch{}r=!0}return r}finally{H=n,We.transition=t}}return!1}function tf(e,t,n){t=ir(n,t),t=qp(e,t,1),e=jt(e,t,1),t=xe(),e!==null&&(uo(e,1,t),Re(e,t))}function Z(e,t,n){if(e.tag===3)tf(e,e,n);else for(;t!==null;){if(t.tag===3){tf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(zt===null||!zt.has(r))){e=ir(n,e),e=Jp(t,e,1),t=jt(t,e,1),e=xe(),t!==null&&(uo(t,1,e),Re(t,e));break}}t=t.return}}function jy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(le&n)===n&&(ne===4||ne===3&&(le&130023424)===le&&500>J()-ml?cn(e,0):hl|=n),Re(e,t)}function Eh(e,t){t===0&&(e.mode&1?(t=Io,Io<<=1,!(Io&130023424)&&(Io=4194304)):t=1);var n=xe();e=Ct(e,t),e!==null&&(uo(e,t,n),Re(e,n))}function zy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Eh(e,n)}function Gy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),Eh(e,n)}var _h;_h=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,Oy(e,t,n);ke=!!(e.flags&131072)}else ke=!1,W&&t.flags&1048576&&Tp(t,Si,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ni(e,t),e=t.pendingProps;var o=tr(t,ge.current);Kn(t,n),o=al(null,t,r,e,o,n);var i=ll();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(i=!0,Ei(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,rl(t),o.updater=Wi,t.stateNode=o,o._reactInternals=t,Fu(t,r,e,n),t=Uu(null,t,r,!0,i,n)):(t.tag=0,W&&i&&Ka(t),Ee(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ni(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Wy(r),e=Je(r,e),o){case 0:t=Bu(null,t,r,e,n);break e;case 1:t=Vc(null,t,r,e,n);break e;case 11:t=zc(null,t,r,e,n);break e;case 14:t=Gc(null,t,r,Je(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Je(r,o),Bu(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Je(r,o),Vc(e,t,r,o,n);case 3:e:{if(rh(t),e===null)throw Error(_(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Rp(e,t),Ti(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=ir(Error(_(423)),t),t=Wc(e,t,r,n,o);break e}else if(r!==o){o=ir(Error(_(424)),t),t=Wc(e,t,r,n,o);break e}else for(Le=$t(t.stateNode.containerInfo.firstChild),be=t,W=!0,tt=null,n=Np(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(nr(),r===o){t=It(e,t,n);break e}Ee(e,t,r,n)}t=t.child}return t;case 5:return Op(t),e===null&&Du(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Pu(r,o)?s=null:i!==null&&Pu(r,i)&&(t.flags|=32),nh(e,t),Ee(e,t,s,n),t.child;case 6:return e===null&&Du(t),null;case 13:return oh(e,t,n);case 4:return ol(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=rr(t,null,r,n):Ee(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Je(r,o),zc(e,t,r,o,n);case 7:return Ee(e,t,t.pendingProps,n),t.child;case 8:return Ee(e,t,t.pendingProps.children,n),t.child;case 12:return Ee(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,j(wi,r._currentValue),r._currentValue=s,i!==null)if(it(i.value,s)){if(i.children===o.children&&!Ne.current){t=It(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var u=i.dependencies;if(u!==null){s=i.child;for(var a=u.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=_t(-1,n&-n),a.tag=2;var l=i.updateQueue;if(l!==null){l=l.shared;var c=l.pending;c===null?a.next=a:(a.next=c.next,c.next=a),l.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),bu(i.return,n,t),u.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(_(341));s.lanes|=n,u=s.alternate,u!==null&&(u.lanes|=n),bu(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Ee(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Kn(t,n),o=Xe(o),r=r(o),t.flags|=1,Ee(e,t,r,n),t.child;case 14:return r=t.type,o=Je(r,t.pendingProps),o=Je(r.type,o),Gc(e,t,r,o,n);case 15:return eh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Je(r,o),ni(e,t),t.tag=1,Pe(r)?(e=!0,Ei(t)):e=!1,Kn(t,n),Zp(t,r,o),Fu(t,r,o,n),Uu(null,t,r,!0,e,n);case 19:return ih(e,t,n);case 22:return th(e,t,n)}throw Error(_(156,t.tag))};function Sh(e,t){return Yd(e,t)}function Vy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ve(e,t,n,r){return new Vy(e,t,n,r)}function El(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wy(e){if(typeof e=="function")return El(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ha)return 11;if(e===Ba)return 14}return 2}function Vt(e,t){var n=e.alternate;return n===null?(n=Ve(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ii(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")El(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case bn:return fn(n.children,o,i,t);case Fa:s=8,o|=8;break;case uu:return e=Ve(12,n,t,o|2),e.elementType=uu,e.lanes=i,e;case au:return e=Ve(13,n,t,o),e.elementType=au,e.lanes=i,e;case lu:return e=Ve(19,n,t,o),e.elementType=lu,e.lanes=i,e;case Od:return Qi(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Pd:s=10;break e;case Rd:s=9;break e;case Ha:s=11;break e;case Ba:s=14;break e;case Rt:s=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=Ve(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function fn(e,t,n,r){return e=Ve(7,e,r,t),e.lanes=n,e}function Qi(e,t,n,r){return e=Ve(22,e,r,t),e.elementType=Od,e.lanes=n,e.stateNode={isHidden:!1},e}function Vs(e,t,n){return e=Ve(6,e,null,t),e.lanes=n,e}function Ws(e,t,n){return t=Ve(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Xy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Is(0),this.expirationTimes=Is(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Is(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function _l(e,t,n,r,o,i,s,u,a){return e=new Xy(e,t,n,u,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ve(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},rl(i),e}function Yy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Dn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function wh(e){if(!e)return Xt;e=e._reactInternals;e:{if(In(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(Pe(n))return wp(e,n,t)}return t}function xh(e,t,n,r,o,i,s,u,a){return e=_l(n,r,!0,e,o,i,s,u,a),e.context=wh(null),n=e.current,r=xe(),o=Gt(n),i=_t(r,o),i.callback=t??null,jt(n,i,o),e.current.lanes=o,uo(e,o,r),Re(e,r),e}function Ki(e,t,n,r){var o=t.current,i=xe(),s=Gt(o);return n=wh(n),t.context===null?t.context=n:t.pendingContext=n,t=_t(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=jt(o,t,s),e!==null&&(rt(e,o,s,i),Jo(e,o,s)),s}function Li(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Sl(e,t){nf(e,t),(e=e.alternate)&&nf(e,t)}function Qy(){return null}var Th=typeof reportError=="function"?reportError:function(e){console.error(e)};function wl(e){this._internalRoot=e}Zi.prototype.render=wl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));Ki(e,t,null,null)};Zi.prototype.unmount=wl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;vn(function(){Ki(null,e,null,null)}),t[Tt]=null}};function Zi(e){this._internalRoot=e}Zi.prototype.unstable_scheduleHydration=function(e){if(e){var t=tp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dt.length&&t!==0&&t<Dt[n].priority;n++);Dt.splice(n,0,e),n===0&&rp(e)}};function xl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function qi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rf(){}function Ky(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var l=Li(s);i.call(l)}}var s=xh(t,r,e,0,null,!1,!1,"",rf);return e._reactRootContainer=s,e[Tt]=s.current,Qr(e.nodeType===8?e.parentNode:e),vn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var u=r;r=function(){var l=Li(a);u.call(l)}}var a=_l(e,0,!1,null,null,!1,!1,"",rf);return e._reactRootContainer=a,e[Tt]=a.current,Qr(e.nodeType===8?e.parentNode:e),vn(function(){Ki(t,a,n,r)}),a}function Ji(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var u=o;o=function(){var a=Li(s);u.call(a)}}Ki(t,s,e,o)}else s=Ky(n,t,e,o,r);return Li(s)}Jd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Pr(t.pendingLanes);n!==0&&(ja(t,n|1),Re(t,J()),!(M&6)&&(sr=J()+500,Jt()))}break;case 13:vn(function(){var r=Ct(e,1);if(r!==null){var o=xe();rt(r,e,1,o)}}),Sl(e,1)}};za=function(e){if(e.tag===13){var t=Ct(e,134217728);if(t!==null){var n=xe();rt(t,e,134217728,n)}Sl(e,134217728)}};ep=function(e){if(e.tag===13){var t=Gt(e),n=Ct(e,t);if(n!==null){var r=xe();rt(n,e,t,r)}Sl(e,t)}};tp=function(){return H};np=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};Eu=function(e,t,n){switch(t){case"input":if(du(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=zi(r);if(!o)throw Error(_(90));Ad(r),du(r,o)}}}break;case"textarea":bd(e,n);break;case"select":t=n.value,t!=null&&Wn(e,!!n.multiple,t,!1)}};jd=gl;zd=vn;var Zy={usingClientEntryPoint:!1,Events:[lo,Bn,zi,Ud,$d,gl]},Cr={findFiberByHostInstance:un,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},qy={bundleType:Cr.bundleType,version:Cr.version,rendererPackageName:Cr.rendererPackageName,rendererConfig:Cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Wd(e),e===null?null:e.stateNode},findFiberByHostInstance:Cr.findFiberByHostInstance||Qy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Fo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Fo.isDisabled&&Fo.supportsFiber)try{Bi=Fo.inject(qy),ct=Fo}catch{}}He.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zy;He.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xl(t))throw Error(_(200));return Yy(e,t,null,n)};He.createRoot=function(e,t){if(!xl(e))throw Error(_(299));var n=!1,r="",o=Th;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=_l(e,1,!1,null,null,n,!1,r,o),e[Tt]=t.current,Qr(e.nodeType===8?e.parentNode:e),new wl(t)};He.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Wd(t),e=e===null?null:e.stateNode,e};He.flushSync=function(e){return vn(e)};He.hydrate=function(e,t,n){if(!qi(t))throw Error(_(200));return Ji(null,e,t,!0,n)};He.hydrateRoot=function(e,t,n){if(!xl(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Th;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=xh(t,null,e,1,n??null,o,!1,i,s),e[Tt]=t.current,Qr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Zi(t)};He.render=function(e,t,n){if(!qi(t))throw Error(_(200));return Ji(null,e,t,!1,n)};He.unmountComponentAtNode=function(e){if(!qi(e))throw Error(_(40));return e._reactRootContainer?(vn(function(){Ji(null,null,e,!1,function(){e._reactRootContainer=null,e[Tt]=null})}),!0):!1};He.unstable_batchedUpdates=gl;He.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!qi(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return Ji(e,t,n,!1,r)};He.version="18.3.1-next-f1338f8080-20240426";function Ch(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ch)}catch(e){console.error(e)}}Ch(),Cd.exports=He;var Jy=Cd.exports,Ih,of=Jy;Ih=of.createRoot,of.hydrateRoot;const kh=process.platform==="darwin",Jx=process.platform==="win32";process.platform;process.type;process.type;const eT=kh?0:36,tT=kh?28:36;var qu=function(e,t){return qu=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])},qu(e,t)};function Qe(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");qu(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}var k=function(){return k=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},k.apply(this,arguments)};function es(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function we(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}function _e(e,t){var n=t&&t.cache?t.cache:iv,r=t&&t.serializer?t.serializer:ov,o=t&&t.strategy?t.strategy:tv;return o(e,{cache:n,serializer:r})}function ev(e){return e==null||typeof e=="number"||typeof e=="boolean"}function Nh(e,t,n,r){var o=ev(r)?r:n(r),i=t.get(o);return typeof i>"u"&&(i=e.call(this,r),t.set(o,i)),i}function Ph(e,t,n){var r=Array.prototype.slice.call(arguments,3),o=n(r),i=t.get(o);return typeof i>"u"&&(i=e.apply(this,r),t.set(o,i)),i}function Tl(e,t,n,r,o){return n.bind(t,e,r,o)}function tv(e,t){var n=e.length===1?Nh:Ph;return Tl(e,this,n,t.cache.create(),t.serializer)}function nv(e,t){return Tl(e,this,Ph,t.cache.create(),t.serializer)}function rv(e,t){return Tl(e,this,Nh,t.cache.create(),t.serializer)}var ov=function(){return JSON.stringify(arguments)};function Cl(){this.cache=Object.create(null)}Cl.prototype.get=function(e){return this.cache[e]};Cl.prototype.set=function(e,t){this.cache[e]=t};var iv={create:function(){return new Cl}},Se={variadic:nv,monadic:rv};function Rh(e,t,n){if(n===void 0&&(n=Error),!e)throw new n(t)}_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.Locale).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.ListFormat).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});var D;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(D||(D={}));var G;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(G||(G={}));var ur;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(ur||(ur={}));function sf(e){return e.type===G.literal}function sv(e){return e.type===G.argument}function Oh(e){return e.type===G.number}function Lh(e){return e.type===G.date}function Ah(e){return e.type===G.time}function Dh(e){return e.type===G.select}function bh(e){return e.type===G.plural}function uv(e){return e.type===G.pound}function Mh(e){return e.type===G.tag}function Fh(e){return!!(e&&typeof e=="object"&&e.type===ur.number)}function Ju(e){return!!(e&&typeof e=="object"&&e.type===ur.dateTime)}var Hh=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,av=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function lv(e){var t={};return e.replace(av,function(n){var r=n.length;switch(n[0]){case"G":t.era=r===4?"long":r===5?"narrow":"short";break;case"y":t.year=r===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=r===4?"long":r===5?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var cv=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function fv(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(cv).filter(function(d){return d.length>0}),n=[],r=0,o=t;r<o.length;r++){var i=o[r],s=i.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var u=s[0],a=s.slice(1),l=0,c=a;l<c.length;l++){var f=c[l];if(f.length===0)throw new Error("Invalid number skeleton")}n.push({stem:u,options:a})}return n}function dv(e){return e.replace(/^(.*?)-/,"")}var uf=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,Bh=/^(@+)?(\+|#+)?[rs]?$/g,pv=/(\*)(0+)|(#+)(0+)|(0+)/g,Uh=/^(0+)$/;function af(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(Bh,function(n,r,o){return typeof o!="string"?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):o==="+"?t.minimumSignificantDigits=r.length:r[0]==="#"?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+(typeof o=="string"?o.length:0)),""}),t}function $h(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function hv(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var n=e.slice(0,2);if(n==="+!"?(t.signDisplay="always",e=e.slice(2)):n==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!Uh.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function lf(e){var t={},n=$h(e);return n||t}function mv(e){for(var t={},n=0,r=e;n<r.length;n++){var o=r[n];switch(o.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=o.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=dv(o.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=k(k(k({},t),{notation:"scientific"}),o.options.reduce(function(a,l){return k(k({},a),lf(l))},{}));continue;case"engineering":t=k(k(k({},t),{notation:"engineering"}),o.options.reduce(function(a,l){return k(k({},a),lf(l))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(o.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(o.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");o.options[0].replace(pv,function(a,l,c,f,d,m){if(l)t.minimumIntegerDigits=c.length;else{if(f&&d)throw new Error("We currently do not support maximum integer digits");if(m)throw new Error("We currently do not support exact integer digits")}return""});continue}if(Uh.test(o.stem)){t.minimumIntegerDigits=o.stem.length;continue}if(uf.test(o.stem)){if(o.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");o.stem.replace(uf,function(a,l,c,f,d,m){return c==="*"?t.minimumFractionDigits=l.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:d&&m?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+m.length):(t.minimumFractionDigits=l.length,t.maximumFractionDigits=l.length),""});var i=o.options[0];i==="w"?t=k(k({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=k(k({},t),af(i)));continue}if(Bh.test(o.stem)){t=k(k({},t),af(o.stem));continue}var s=$h(o.stem);s&&(t=k(k({},t),s));var u=hv(o.stem);u&&(t=k(k({},t),u))}return t}var Ho={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function gv(e,t){for(var n="",r=0;r<e.length;r++){var o=e.charAt(r);if(o==="j"){for(var i=0;r+1<e.length&&e.charAt(r+1)===o;)i++,r++;var s=1+(i&1),u=i<2?1:3+(i>>1),a="a",l=yv(t);for((l=="H"||l=="k")&&(u=0);u-- >0;)n+=a;for(;s-- >0;)n=l+n}else o==="J"?n+="H":n+=o}return n}function yv(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n=e.language,r;n!=="root"&&(r=e.maximize().region);var o=Ho[r||""]||Ho[n||""]||Ho["".concat(n,"-001")]||Ho["001"];return o[0]}var Xs,vv=new RegExp("^".concat(Hh.source,"*")),Ev=new RegExp("".concat(Hh.source,"*$"));function b(e,t){return{start:e,end:t}}var _v=!!String.prototype.startsWith&&"_a".startsWith("a",1),Sv=!!String.fromCodePoint,wv=!!Object.fromEntries,xv=!!String.prototype.codePointAt,Tv=!!String.prototype.trimStart,Cv=!!String.prototype.trimEnd,Iv=!!Number.isSafeInteger,kv=Iv?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},ea=!0;try{var Nv=zh("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");ea=((Xs=Nv.exec("a"))===null||Xs===void 0?void 0:Xs[0])==="a"}catch{ea=!1}var cf=_v?function(t,n,r){return t.startsWith(n,r)}:function(t,n,r){return t.slice(r,r+n.length)===n},ta=Sv?String.fromCodePoint:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var r="",o=t.length,i=0,s;o>i;){if(s=t[i++],s>1114111)throw RangeError(s+" is not a valid code point");r+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return r},ff=wv?Object.fromEntries:function(t){for(var n={},r=0,o=t;r<o.length;r++){var i=o[r],s=i[0],u=i[1];n[s]=u}return n},jh=xv?function(t,n){return t.codePointAt(n)}:function(t,n){var r=t.length;if(!(n<0||n>=r)){var o=t.charCodeAt(n),i;return o<55296||o>56319||n+1===r||(i=t.charCodeAt(n+1))<56320||i>57343?o:(o-55296<<10)+(i-56320)+65536}},Pv=Tv?function(t){return t.trimStart()}:function(t){return t.replace(vv,"")},Rv=Cv?function(t){return t.trimEnd()}:function(t){return t.replace(Ev,"")};function zh(e,t){return new RegExp(e,t)}var na;if(ea){var df=zh("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");na=function(t,n){var r;df.lastIndex=n;var o=df.exec(t);return(r=o[1])!==null&&r!==void 0?r:""}}else na=function(t,n){for(var r=[];;){var o=jh(t,n);if(o===void 0||Gh(o)||Dv(o))break;r.push(o),n+=o>=65536?2:1}return ta.apply(void 0,r)};var Ov=function(){function e(t,n){n===void 0&&(n={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!n.ignoreTag,this.locale=n.locale,this.requiresOtherClause=!!n.requiresOtherClause,this.shouldParseSkeletons=!!n.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,n,r){for(var o=[];!this.isEOF();){var i=this.char();if(i===123){var s=this.parseArgument(t,r);if(s.err)return s;o.push(s.val)}else{if(i===125&&t>0)break;if(i===35&&(n==="plural"||n==="selectordinal")){var u=this.clonePosition();this.bump(),o.push({type:G.pound,location:b(u,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(r)break;return this.error(D.UNMATCHED_CLOSING_TAG,b(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&ra(this.peek()||0)){var s=this.parseTag(t,n);if(s.err)return s;o.push(s.val)}else{var s=this.parseLiteral(t,n);if(s.err)return s;o.push(s.val)}}}return{val:o,err:null}},e.prototype.parseTag=function(t,n){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:G.literal,value:"<".concat(o,"/>"),location:b(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,n,!0);if(i.err)return i;var s=i.val,u=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!ra(this.char()))return this.error(D.INVALID_TAG,b(u,this.clonePosition()));var a=this.clonePosition(),l=this.parseTagName();return o!==l?this.error(D.UNMATCHED_CLOSING_TAG,b(a,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:G.tag,value:o,children:s,location:b(r,this.clonePosition())},err:null}:this.error(D.INVALID_TAG,b(u,this.clonePosition())))}else return this.error(D.UNCLOSED_TAG,b(r,this.clonePosition()))}else return this.error(D.INVALID_TAG,b(r,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Av(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,n){for(var r=this.clonePosition(),o="";;){var i=this.tryParseQuote(n);if(i){o+=i;continue}var s=this.tryParseUnquoted(t,n);if(s){o+=s;continue}var u=this.tryParseLeftAngleBracket();if(u){o+=u;continue}break}var a=b(r,this.clonePosition());return{val:{type:G.literal,value:o,location:a},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Lv(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var n=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(r===39)if(this.peek()===39)n.push(39),this.bump();else{this.bump();break}else n.push(r);this.bump()}return ta.apply(void 0,n)},e.prototype.tryParseUnquoted=function(t,n){if(this.isEOF())return null;var r=this.char();return r===60||r===123||r===35&&(n==="plural"||n==="selectordinal")||r===125&&t>0?null:(this.bump(),ta(r))},e.prototype.parseArgument=function(t,n){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,b(r,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(D.EMPTY_ARGUMENT,b(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(D.MALFORMED_ARGUMENT,b(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,b(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:G.argument,value:o,location:b(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,b(r,this.clonePosition())):this.parseArgumentOptions(t,n,o,r);default:return this.error(D.MALFORMED_ARGUMENT,b(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),n=this.offset(),r=na(this.message,n),o=n+r.length;this.bumpTo(o);var i=this.clonePosition(),s=b(t,i);return{value:r,location:s}},e.prototype.parseArgumentOptions=function(t,n,r,o){var i,s=this.clonePosition(),u=this.parseIdentifierIfPossible().value,a=this.clonePosition();switch(u){case"":return this.error(D.EXPECT_ARGUMENT_TYPE,b(s,a));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var d=Rv(f.val);if(d.length===0)return this.error(D.EXPECT_ARGUMENT_STYLE,b(this.clonePosition(),this.clonePosition()));var m=b(c,this.clonePosition());l={style:d,styleLocation:m}}var y=this.tryParseArgumentClose(o);if(y.err)return y;var v=b(o,this.clonePosition());if(l&&cf(l==null?void 0:l.style,"::",0)){var S=Pv(l.style.slice(2));if(u==="number"){var f=this.parseNumberSkeletonFromString(S,l.styleLocation);return f.err?f:{val:{type:G.number,value:r,location:v,style:f.val},err:null}}else{if(S.length===0)return this.error(D.EXPECT_DATE_TIME_SKELETON,v);var h=S;this.locale&&(h=gv(S,this.locale));var d={type:ur.dateTime,pattern:h,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?lv(h):{}},p=u==="date"?G.date:G.time;return{val:{type:p,value:r,location:v,style:d},err:null}}}return{val:{type:u==="number"?G.number:u==="date"?G.date:G.time,value:r,location:v,style:(i=l==null?void 0:l.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var g=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(D.EXPECT_SELECT_ARGUMENT_OPTIONS,b(g,k({},g)));this.bumpSpace();var E=this.parseIdentifierIfPossible(),w=0;if(u!=="select"&&E.value==="offset"){if(!this.bumpIf(":"))return this.error(D.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,b(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(D.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,D.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),E=this.parseIdentifierIfPossible(),w=f.val}var x=this.tryParsePluralOrSelectOptions(t,u,n,E);if(x.err)return x;var y=this.tryParseArgumentClose(o);if(y.err)return y;var I=b(o,this.clonePosition());return u==="select"?{val:{type:G.select,value:r,options:ff(x.val),location:I},err:null}:{val:{type:G.plural,value:r,options:ff(x.val),offset:w,pluralType:u==="plural"?"cardinal":"ordinal",location:I},err:null}}default:return this.error(D.INVALID_ARGUMENT_TYPE,b(s,a))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(D.EXPECT_ARGUMENT_CLOSING_BRACE,b(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,n=this.clonePosition();!this.isEOF();){var r=this.char();switch(r){case 39:{this.bump();var o=this.clonePosition();if(!this.bumpUntil("'"))return this.error(D.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,b(o,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(n.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(n.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,n){var r=[];try{r=fv(t)}catch{return this.error(D.INVALID_NUMBER_SKELETON,n)}return{val:{type:ur.number,tokens:r,location:n,parsedOptions:this.shouldParseSkeletons?mv(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,n,r,o){for(var i,s=!1,u=[],a=new Set,l=o.value,c=o.location;;){if(l.length===0){var f=this.clonePosition();if(n!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(D.EXPECT_PLURAL_ARGUMENT_SELECTOR,D.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=b(f,this.clonePosition()),l=this.message.slice(f.offset,this.offset())}else break}if(a.has(l))return this.error(n==="select"?D.DUPLICATE_SELECT_ARGUMENT_SELECTOR:D.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);l==="other"&&(s=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error(n==="select"?D.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:D.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,b(this.clonePosition(),this.clonePosition()));var y=this.parseMessage(t+1,n,r);if(y.err)return y;var v=this.tryParseArgumentClose(m);if(v.err)return v;u.push([l,{value:y.val,location:b(m,this.clonePosition())}]),a.add(l),this.bumpSpace(),i=this.parseIdentifierIfPossible(),l=i.value,c=i.location}return u.length===0?this.error(n==="select"?D.EXPECT_SELECT_ARGUMENT_SELECTOR:D.EXPECT_PLURAL_ARGUMENT_SELECTOR,b(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(D.MISSING_OTHER_CLAUSE,b(this.clonePosition(),this.clonePosition())):{val:u,err:null}},e.prototype.tryParseDecimalInteger=function(t,n){var r=1,o=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,s=0;!this.isEOF();){var u=this.char();if(u>=48&&u<=57)i=!0,s=s*10+(u-48),this.bump();else break}var a=b(o,this.clonePosition());return i?(s*=r,kv(s)?{val:s,err:null}:this.error(n,a)):this.error(t,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var n=jh(this.message,t);if(n===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return n},e.prototype.error=function(t,n){return{val:null,err:{kind:t,message:this.message,location:n}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(cf(this.message,t,this.offset())){for(var n=0;n<t.length;n++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var n=this.offset(),r=this.message.indexOf(t,n);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var n=this.offset();if(n===t)break;if(n>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&Gh(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),n=this.offset(),r=this.message.charCodeAt(n+(t>=65536?2:1));return r??null},e}();function ra(e){return e>=97&&e<=122||e>=65&&e<=90}function Lv(e){return ra(e)||e===47}function Av(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function Gh(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function Dv(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function oa(e){e.forEach(function(t){if(delete t.location,Dh(t)||bh(t))for(var n in t.options)delete t.options[n].location,oa(t.options[n].value);else Oh(t)&&Fh(t.style)||(Lh(t)||Ah(t))&&Ju(t.style)?delete t.style.location:Mh(t)&&oa(t.children)})}function bv(e,t){t===void 0&&(t={}),t=k({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var n=new Ov(e,t).parse();if(n.err){var r=SyntaxError(D[n.err.kind]);throw r.location=n.err.location,r.originalMessage=n.err.message,r}return t!=null&&t.captureLocation||oa(n.val),n.val}var dt;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(dt||(dt={}));var en=function(e){Qe(t,e);function t(n,r,o){var i=e.call(this,n)||this;return i.code=r,i.originalMessage=o,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),pf=function(e){Qe(t,e);function t(n,r,o,i){return e.call(this,'Invalid values for "'.concat(n,'": "').concat(r,'". Options are "').concat(Object.keys(o).join('", "'),'"'),dt.INVALID_VALUE,i)||this}return t}(en),Mv=function(e){Qe(t,e);function t(n,r,o){return e.call(this,'Value for "'.concat(n,'" must be of type ').concat(r),dt.INVALID_VALUE,o)||this}return t}(en),Fv=function(e){Qe(t,e);function t(n,r){return e.call(this,'The intl string context variable "'.concat(n,'" was not provided to the string "').concat(r,'"'),dt.MISSING_VALUE,r)||this}return t}(en),ve;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(ve||(ve={}));function Hv(e){return e.length<2?e:e.reduce(function(t,n){var r=t[t.length-1];return!r||r.type!==ve.literal||n.type!==ve.literal?t.push(n):r.value+=n.value,t},[])}function Vh(e){return typeof e=="function"}function si(e,t,n,r,o,i,s){if(e.length===1&&sf(e[0]))return[{type:ve.literal,value:e[0].value}];for(var u=[],a=0,l=e;a<l.length;a++){var c=l[a];if(sf(c)){u.push({type:ve.literal,value:c.value});continue}if(uv(c)){typeof i=="number"&&u.push({type:ve.literal,value:n.getNumberFormat(t).format(i)});continue}var f=c.value;if(!(o&&f in o))throw new Fv(f,s);var d=o[f];if(sv(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),u.push({type:typeof d=="string"?ve.literal:ve.object,value:d});continue}if(Lh(c)){var m=typeof c.style=="string"?r.date[c.style]:Ju(c.style)?c.style.parsedOptions:void 0;u.push({type:ve.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(Ah(c)){var m=typeof c.style=="string"?r.time[c.style]:Ju(c.style)?c.style.parsedOptions:r.time.medium;u.push({type:ve.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(Oh(c)){var m=typeof c.style=="string"?r.number[c.style]:Fh(c.style)?c.style.parsedOptions:void 0;m&&m.scale&&(d=d*(m.scale||1)),u.push({type:ve.literal,value:n.getNumberFormat(t,m).format(d)});continue}if(Mh(c)){var y=c.children,v=c.value,S=o[v];if(!Vh(S))throw new Mv(v,"function",s);var h=si(y,t,n,r,o,i),p=S(h.map(function(w){return w.value}));Array.isArray(p)||(p=[p]),u.push.apply(u,p.map(function(w){return{type:typeof w=="string"?ve.literal:ve.object,value:w}}))}if(Dh(c)){var g=c.options[d]||c.options.other;if(!g)throw new pf(c.value,d,Object.keys(c.options),s);u.push.apply(u,si(g.value,t,n,r,o));continue}if(bh(c)){var g=c.options["=".concat(d)];if(!g){if(!Intl.PluralRules)throw new en(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,dt.MISSING_INTL_API,s);var E=n.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));g=c.options[E]||c.options.other}if(!g)throw new pf(c.value,d,Object.keys(c.options),s);u.push.apply(u,si(g.value,t,n,r,o,d-(c.offset||0)));continue}}return Hv(u)}function Bv(e,t){return t?k(k(k({},e||{}),t||{}),Object.keys(e).reduce(function(n,r){return n[r]=k(k({},e[r]),t[r]||{}),n},{})):e}function Uv(e,t){return t?Object.keys(e).reduce(function(n,r){return n[r]=Bv(e[r],t[r]),n},k({},e)):e}function Ys(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function $v(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:_e(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.NumberFormat).bind.apply(t,we([void 0],n,!1)))},{cache:Ys(e.number),strategy:Se.variadic}),getDateTimeFormat:_e(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.DateTimeFormat).bind.apply(t,we([void 0],n,!1)))},{cache:Ys(e.dateTime),strategy:Se.variadic}),getPluralRules:_e(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.PluralRules).bind.apply(t,we([void 0],n,!1)))},{cache:Ys(e.pluralRules),strategy:Se.variadic})}}var Wh=function(){function e(t,n,r,o){var i=this;if(n===void 0&&(n=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(a){var l=i.formatToParts(a);if(l.length===1)return l[0].value;var c=l.reduce(function(f,d){return!f.length||d.type!==ve.literal||typeof f[f.length-1]!="string"?f.push(d.value):f[f.length-1]+=d.value,f},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(a){return si(i.ast,i.locales,i.formatters,i.formats,a,void 0,i.message)},this.resolvedOptions=function(){var a;return{locale:((a=i.resolvedLocale)===null||a===void 0?void 0:a.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=n,this.resolvedLocale=e.resolveLocale(n),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=o||{};s.formatters;var u=es(s,["formatters"]);this.ast=e.__parse(t,k(k({},u),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Uv(e.formats,r),this.formatters=o&&o.formatters||$v(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var n=Intl.NumberFormat.supportedLocalesOf(t);return n.length>0?new Intl.Locale(n[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=bv,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),En;(function(e){e.FORMAT_ERROR="FORMAT_ERROR",e.UNSUPPORTED_FORMATTER="UNSUPPORTED_FORMATTER",e.INVALID_CONFIG="INVALID_CONFIG",e.MISSING_DATA="MISSING_DATA",e.MISSING_TRANSLATION="MISSING_TRANSLATION"})(En||(En={}));var fo=function(e){Qe(t,e);function t(n,r,o){var i=this,s=o?o instanceof Error?o:new Error(String(o)):void 0;return i=e.call(this,"[@formatjs/intl Error ".concat(n,"] ").concat(r,`
`).concat(s?`
`.concat(s.message,`
`).concat(s.stack):""))||this,i.code=n,typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(i,t),i}return t}(Error),jv=function(e){Qe(t,e);function t(n,r){return e.call(this,En.UNSUPPORTED_FORMATTER,n,r)||this}return t}(fo),zv=function(e){Qe(t,e);function t(n,r){return e.call(this,En.INVALID_CONFIG,n,r)||this}return t}(fo),hf=function(e){Qe(t,e);function t(n,r){return e.call(this,En.MISSING_DATA,n,r)||this}return t}(fo),Ke=function(e){Qe(t,e);function t(n,r,o){var i=e.call(this,En.FORMAT_ERROR,"".concat(n,`
Locale: `).concat(r,`
`),o)||this;return i.locale=r,i}return t}(fo),Qs=function(e){Qe(t,e);function t(n,r,o,i){var s=e.call(this,"".concat(n,`
MessageID: `).concat(o==null?void 0:o.id,`
Default Message: `).concat(o==null?void 0:o.defaultMessage,`
Description: `).concat(o==null?void 0:o.description,`
`),r,i)||this;return s.descriptor=o,s.locale=r,s}return t}(Ke),Gv=function(e){Qe(t,e);function t(n,r){var o=e.call(this,En.MISSING_TRANSLATION,'Missing message: "'.concat(n.id,'" for locale "').concat(r,'", using ').concat(n.defaultMessage?"default message (".concat(typeof n.defaultMessage=="string"?n.defaultMessage:n.defaultMessage.map(function(i){var s;return(s=i.value)!==null&&s!==void 0?s:JSON.stringify(i)}).join(),")"):"id"," as fallback."))||this;return o.descriptor=n,o}return t}(fo);function kn(e,t,n){return n===void 0&&(n={}),t.reduce(function(r,o){return o in e?r[o]=e[o]:o in n&&(r[o]=n[o]),r},{})}var Vv=function(e){},Wv=function(e){},Xh={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:Vv,onWarn:Wv};function Yh(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function rn(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function Xv(e){e===void 0&&(e=Yh());var t=Intl.RelativeTimeFormat,n=Intl.ListFormat,r=Intl.DisplayNames,o=_e(function(){for(var u,a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new((u=Intl.DateTimeFormat).bind.apply(u,we([void 0],a,!1)))},{cache:rn(e.dateTime),strategy:Se.variadic}),i=_e(function(){for(var u,a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new((u=Intl.NumberFormat).bind.apply(u,we([void 0],a,!1)))},{cache:rn(e.number),strategy:Se.variadic}),s=_e(function(){for(var u,a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new((u=Intl.PluralRules).bind.apply(u,we([void 0],a,!1)))},{cache:rn(e.pluralRules),strategy:Se.variadic});return{getDateTimeFormat:o,getNumberFormat:i,getMessageFormat:_e(function(u,a,l,c){return new Wh(u,a,l,k({formatters:{getNumberFormat:i,getDateTimeFormat:o,getPluralRules:s}},c||{}))},{cache:rn(e.message),strategy:Se.variadic}),getRelativeTimeFormat:_e(function(){for(var u=[],a=0;a<arguments.length;a++)u[a]=arguments[a];return new(t.bind.apply(t,we([void 0],u,!1)))},{cache:rn(e.relativeTime),strategy:Se.variadic}),getPluralRules:s,getListFormat:_e(function(){for(var u=[],a=0;a<arguments.length;a++)u[a]=arguments[a];return new(n.bind.apply(n,we([void 0],u,!1)))},{cache:rn(e.list),strategy:Se.variadic}),getDisplayNames:_e(function(){for(var u=[],a=0;a<arguments.length;a++)u[a]=arguments[a];return new(r.bind.apply(r,we([void 0],u,!1)))},{cache:rn(e.displayNames),strategy:Se.variadic})}}function Il(e,t,n,r){var o=e&&e[t],i;if(o&&(i=o[n]),i)return i;r(new jv("No ".concat(t," format named: ").concat(n)))}function Bo(e,t){return Object.keys(e).reduce(function(n,r){return n[r]=k({timeZone:t},e[r]),n},{})}function mf(e,t){var n=Object.keys(k(k({},e),t));return n.reduce(function(r,o){return r[o]=k(k({},e[o]||{}),t[o]||{}),r},{})}function gf(e,t){if(!t)return e;var n=Wh.formats;return k(k(k({},n),e),{date:mf(Bo(n.date,t),Bo(e.date||{},t)),time:mf(Bo(n.time,t),Bo(e.time||{},t))})}var ia=function(e,t,n,r,o){var i=e.locale,s=e.formats,u=e.messages,a=e.defaultLocale,l=e.defaultFormats,c=e.fallbackOnEmptyString,f=e.onError,d=e.timeZone,m=e.defaultRichTextElements;n===void 0&&(n={id:""});var y=n.id,v=n.defaultMessage;Rh(!!y,"[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");var S=String(y),h=u&&Object.prototype.hasOwnProperty.call(u,S)&&u[S];if(Array.isArray(h)&&h.length===1&&h[0].type===G.literal)return h[0].value;if(!r&&h&&typeof h=="string"&&!m)return h.replace(/'\{(.*?)\}'/gi,"{$1}");if(r=k(k({},m),r||{}),s=gf(s,d),l=gf(l,d),!h){if(c===!1&&h==="")return h;if((!v||i&&i.toLowerCase()!==a.toLowerCase())&&f(new Gv(n,i)),v)try{var p=t.getMessageFormat(v,a,l,o);return p.format(r)}catch(g){return f(new Qs('Error formatting default message for: "'.concat(S,'", rendering default message verbatim'),i,n,g)),typeof v=="string"?v:S}return S}try{var p=t.getMessageFormat(h,i,s,k({formatters:t},o||{}));return p.format(r)}catch(g){f(new Qs('Error formatting message: "'.concat(S,'", using ').concat(v?"default message":"id"," as fallback."),i,n,g))}if(v)try{var p=t.getMessageFormat(v,a,l,o);return p.format(r)}catch(g){f(new Qs('Error formatting the default message for: "'.concat(S,'", rendering message verbatim'),i,n,g))}return typeof h=="string"?h:typeof v=="string"?v:S},Qh=["formatMatcher","timeZone","hour12","weekday","era","year","month","day","hour","minute","second","timeZoneName","hourCycle","dateStyle","timeStyle","calendar","numberingSystem","fractionalSecondDigits"];function ts(e,t,n,r){var o=e.locale,i=e.formats,s=e.onError,u=e.timeZone;r===void 0&&(r={});var a=r.format,l=k(k({},u&&{timeZone:u}),a&&Il(i,t,a,s)),c=kn(r,Qh,l);return t==="time"&&!c.hour&&!c.minute&&!c.second&&!c.timeStyle&&!c.dateStyle&&(c=k(k({},c),{hour:"numeric",minute:"numeric"})),n(o,c)}function Yv(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return ts(e,"date",t,s).format(u)}catch(a){e.onError(new Ke("Error formatting date.",e.locale,a))}return String(u)}function Qv(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return ts(e,"time",t,s).format(u)}catch(a){e.onError(new Ke("Error formatting time.",e.locale,a))}return String(u)}function Kv(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=n[2],u=s===void 0?{}:s,a=e.timeZone,l=e.locale,c=e.onError,f=kn(u,Qh,a?{timeZone:a}:{});try{return t(l,f).formatRange(o,i)}catch(d){c(new Ke("Error formatting date time range.",e.locale,d))}return String(o)}function Zv(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return ts(e,"date",t,s).formatToParts(u)}catch(a){e.onError(new Ke("Error formatting date.",e.locale,a))}return[]}function qv(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return ts(e,"time",t,s).formatToParts(u)}catch(a){e.onError(new Ke("Error formatting time.",e.locale,a))}return[]}var Jv=["style","type","fallback","languageDisplay"];function eE(e,t,n,r){var o=e.locale,i=e.onError,s=Intl.DisplayNames;s||i(new en(`Intl.DisplayNames is not available in this environment.
Try polyfilling it using "@formatjs/intl-displaynames"
`,dt.MISSING_INTL_API));var u=kn(r,Jv);try{return t(o,u).of(n)}catch(a){i(new Ke("Error formatting display name.",o,a))}}var tE=["type","style"],yf=Date.now();function nE(e){return"".concat(yf,"_").concat(e,"_").concat(yf)}function rE(e,t,n,r){r===void 0&&(r={});var o=Kh(e,t,n,r).reduce(function(i,s){var u=s.value;return typeof u!="string"?i.push(u):typeof i[i.length-1]=="string"?i[i.length-1]+=u:i.push(u),i},[]);return o.length===1?o[0]:o.length===0?"":o}function Kh(e,t,n,r){var o=e.locale,i=e.onError;r===void 0&&(r={});var s=Intl.ListFormat;s||i(new en(`Intl.ListFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-listformat"
`,dt.MISSING_INTL_API));var u=kn(r,tE);try{var a={},l=n.map(function(c,f){if(typeof c=="object"){var d=nE(f);return a[d]=c,d}return String(c)});return t(o,u).formatToParts(l).map(function(c){return c.type==="literal"?c:k(k({},c),{value:a[c.value]||c.value})})}catch(c){i(new Ke("Error formatting list.",o,c))}return n}var oE=["type"];function iE(e,t,n,r){var o=e.locale,i=e.onError;r===void 0&&(r={}),Intl.PluralRules||i(new en(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,dt.MISSING_INTL_API));var s=kn(r,oE);try{return t(o,s).select(n)}catch(u){i(new Ke("Error formatting plural.",o,u))}return"other"}var sE=["numeric","style"];function uE(e,t,n){var r=e.locale,o=e.formats,i=e.onError;n===void 0&&(n={});var s=n.format,u=!!s&&Il(o,"relative",s,i)||{},a=kn(n,sE,u);return t(r,a)}function aE(e,t,n,r,o){o===void 0&&(o={}),r||(r="second");var i=Intl.RelativeTimeFormat;i||e.onError(new en(`Intl.RelativeTimeFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-relativetimeformat"
`,dt.MISSING_INTL_API));try{return uE(e,t,o).format(n,r)}catch(s){e.onError(new Ke("Error formatting relative time.",e.locale,s))}return String(n)}var lE=["style","currency","unit","unitDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","currencyDisplay","currencySign","notation","signDisplay","unit","unitDisplay","numberingSystem","trailingZeroDisplay","roundingPriority","roundingIncrement","roundingMode"];function Zh(e,t,n){var r=e.locale,o=e.formats,i=e.onError;n===void 0&&(n={});var s=n.format,u=s&&Il(o,"number",s,i)||{},a=kn(n,lE,u);return t(r,a)}function cE(e,t,n,r){r===void 0&&(r={});try{return Zh(e,t,r).format(n)}catch(o){e.onError(new Ke("Error formatting number.",e.locale,o))}return String(n)}function fE(e,t,n,r){r===void 0&&(r={});try{return Zh(e,t,r).formatToParts(n)}catch(o){e.onError(new Ke("Error formatting number.",e.locale,o))}return[]}function dE(e){var t=e?e[Object.keys(e)[0]]:void 0;return typeof t=="string"}function pE(e){e.onWarn&&e.defaultRichTextElements&&dE(e.messages||{})&&e.onWarn(`[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. 
Please consider using "@formatjs/cli" to pre-compile your messages for performance.
For more details see https://formatjs.io/docs/getting-started/message-distribution`)}function hE(e,t){var n=Xv(t),r=k(k({},Xh),e),o=r.locale,i=r.defaultLocale,s=r.onError;return o?!Intl.NumberFormat.supportedLocalesOf(o).length&&s?s(new hf('Missing locale data for locale: "'.concat(o,'" in Intl.NumberFormat. Using default locale: "').concat(i,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):!Intl.DateTimeFormat.supportedLocalesOf(o).length&&s&&s(new hf('Missing locale data for locale: "'.concat(o,'" in Intl.DateTimeFormat. Using default locale: "').concat(i,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):(s&&s(new zv('"locale" was not configured, using "'.concat(i,'" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details'))),r.locale=r.defaultLocale||"en"),pE(r),k(k({},r),{formatters:n,formatNumber:cE.bind(null,r,n.getNumberFormat),formatNumberToParts:fE.bind(null,r,n.getNumberFormat),formatRelativeTime:aE.bind(null,r,n.getRelativeTimeFormat),formatDate:Yv.bind(null,r,n.getDateTimeFormat),formatDateToParts:Zv.bind(null,r,n.getDateTimeFormat),formatTime:Qv.bind(null,r,n.getDateTimeFormat),formatDateTimeRange:Kv.bind(null,r,n.getDateTimeFormat),formatTimeToParts:qv.bind(null,r,n.getDateTimeFormat),formatPlural:iE.bind(null,r,n.getPluralRules),formatMessage:ia.bind(null,r,n),$t:ia.bind(null,r,n),formatList:rE.bind(null,r,n.getListFormat),formatListToParts:Kh.bind(null,r,n.getListFormat),formatDisplayName:eE.bind(null,r,n.getDisplayNames)})}function qh(e){Rh(e,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}var Jh=k(k({},Xh),{textComponent:me.Fragment});function mE(e){return function(t){return e(me.Children.toArray(t))}}function gE(e,t){if(e===t)return!0;if(!e||!t)return!1;var n=Object.keys(e),r=Object.keys(t),o=n.length;if(r.length!==o)return!1;for(var i=0;i<o;i++){var s=n[i];if(e[s]!==t[s]||!Object.prototype.hasOwnProperty.call(t,s))return!1}return!0}var em={exports:{}},B={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ue=typeof Symbol=="function"&&Symbol.for,kl=ue?Symbol.for("react.element"):60103,Nl=ue?Symbol.for("react.portal"):60106,ns=ue?Symbol.for("react.fragment"):60107,rs=ue?Symbol.for("react.strict_mode"):60108,os=ue?Symbol.for("react.profiler"):60114,is=ue?Symbol.for("react.provider"):60109,ss=ue?Symbol.for("react.context"):60110,Pl=ue?Symbol.for("react.async_mode"):60111,us=ue?Symbol.for("react.concurrent_mode"):60111,as=ue?Symbol.for("react.forward_ref"):60112,ls=ue?Symbol.for("react.suspense"):60113,yE=ue?Symbol.for("react.suspense_list"):60120,cs=ue?Symbol.for("react.memo"):60115,fs=ue?Symbol.for("react.lazy"):60116,vE=ue?Symbol.for("react.block"):60121,EE=ue?Symbol.for("react.fundamental"):60117,_E=ue?Symbol.for("react.responder"):60118,SE=ue?Symbol.for("react.scope"):60119;function Ue(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case kl:switch(e=e.type,e){case Pl:case us:case ns:case os:case rs:case ls:return e;default:switch(e=e&&e.$$typeof,e){case ss:case as:case fs:case cs:case is:return e;default:return t}}case Nl:return t}}}function tm(e){return Ue(e)===us}B.AsyncMode=Pl;B.ConcurrentMode=us;B.ContextConsumer=ss;B.ContextProvider=is;B.Element=kl;B.ForwardRef=as;B.Fragment=ns;B.Lazy=fs;B.Memo=cs;B.Portal=Nl;B.Profiler=os;B.StrictMode=rs;B.Suspense=ls;B.isAsyncMode=function(e){return tm(e)||Ue(e)===Pl};B.isConcurrentMode=tm;B.isContextConsumer=function(e){return Ue(e)===ss};B.isContextProvider=function(e){return Ue(e)===is};B.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===kl};B.isForwardRef=function(e){return Ue(e)===as};B.isFragment=function(e){return Ue(e)===ns};B.isLazy=function(e){return Ue(e)===fs};B.isMemo=function(e){return Ue(e)===cs};B.isPortal=function(e){return Ue(e)===Nl};B.isProfiler=function(e){return Ue(e)===os};B.isStrictMode=function(e){return Ue(e)===rs};B.isSuspense=function(e){return Ue(e)===ls};B.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ns||e===us||e===os||e===rs||e===ls||e===yE||typeof e=="object"&&e!==null&&(e.$$typeof===fs||e.$$typeof===cs||e.$$typeof===is||e.$$typeof===ss||e.$$typeof===as||e.$$typeof===EE||e.$$typeof===_E||e.$$typeof===SE||e.$$typeof===vE)};B.typeOf=Ue;em.exports=B;var wE=em.exports,nm=wE,xE={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},TE={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},rm={};rm[nm.ForwardRef]=xE;rm[nm.Memo]=TE;var Rl=typeof window<"u"&&!window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=me.createContext(null)):me.createContext(null);Rl.Consumer;var CE=Rl.Provider,IE=CE,kE=Rl;function om(){var e=me.useContext(kE);return qh(e),e}var sa;(function(e){e.formatDate="FormattedDate",e.formatTime="FormattedTime",e.formatNumber="FormattedNumber",e.formatList="FormattedList",e.formatDisplayName="FormattedDisplayName"})(sa||(sa={}));var ua;(function(e){e.formatDate="FormattedDateParts",e.formatTime="FormattedTimeParts",e.formatNumber="FormattedNumberParts",e.formatList="FormattedListParts"})(ua||(ua={}));function im(e){var t=function(n){var r=om(),o=n.value,i=n.children,s=es(n,["value","children"]),u=typeof o=="string"?new Date(o||0):o,a=e==="formatDate"?r.formatDateToParts(u,s):r.formatTimeToParts(u,s);return i(a)};return t.displayName=ua[e],t}function po(e){var t=function(n){var r=om(),o=n.value,i=n.children,s=es(n,["value","children"]),u=r[e](o,s);if(typeof i=="function")return i(u);var a=r.textComponent||me.Fragment;return me.createElement(a,null,u)};return t.displayName=sa[e],t}function sm(e){return e&&Object.keys(e).reduce(function(t,n){var r=e[n];return t[n]=Vh(r)?mE(r):r,t},{})}var vf=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];var s=sm(r),u=ia.apply(void 0,we([e,t,n,s],o,!1));return Array.isArray(u)?me.Children.toArray(u):u},Ef=function(e,t){var n=e.defaultRichTextElements,r=es(e,["defaultRichTextElements"]),o=sm(n),i=hE(k(k(k({},Jh),r),{defaultRichTextElements:o}),t),s={locale:i.locale,timeZone:i.timeZone,fallbackOnEmptyString:i.fallbackOnEmptyString,formats:i.formats,defaultLocale:i.defaultLocale,defaultFormats:i.defaultFormats,messages:i.messages,onError:i.onError,defaultRichTextElements:o};return k(k({},i),{formatMessage:vf.bind(null,s,i.formatters),$t:vf.bind(null,s,i.formatters)})};function Ks(e){return{locale:e.locale,timeZone:e.timeZone,fallbackOnEmptyString:e.fallbackOnEmptyString,formats:e.formats,textComponent:e.textComponent,messages:e.messages,defaultLocale:e.defaultLocale,defaultFormats:e.defaultFormats,onError:e.onError,onWarn:e.onWarn,wrapRichTextChunksInFragment:e.wrapRichTextChunksInFragment,defaultRichTextElements:e.defaultRichTextElements}}var NE=function(e){Qe(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.cache=Yh(),n.state={cache:n.cache,intl:Ef(Ks(n.props),n.cache),prevConfig:Ks(n.props)},n}return t.getDerivedStateFromProps=function(n,r){var o=r.prevConfig,i=r.cache,s=Ks(n);return gE(o,s)?null:{intl:Ef(s,i),prevConfig:s}},t.prototype.render=function(){return qh(this.state.intl),me.createElement(IE,{value:this.state.intl},this.props.children)},t.displayName="IntlProvider",t.defaultProps=Jh,t}(me.PureComponent);po("formatDate");po("formatTime");po("formatNumber");po("formatList");po("formatDisplayName");im("formatDate");im("formatTime");var aa=function(e,t){return aa=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])},aa(e,t)};function Ol(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");aa(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}function PE(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function u(c){try{l(r.next(c))}catch(f){s(f)}}function a(c){try{l(r.throw(c))}catch(f){s(f)}}function l(c){c.done?i(c.value):o(c.value).then(u,a)}l((r=r.apply(e,t||[])).next())})}function um(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,o,i,s=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return s.next=u(0),s.throw=u(1),s.return=u(2),typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function u(l){return function(c){return a([l,c])}}function a(l){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,l[0]&&(n=0)),n;)try{if(r=1,o&&(i=l[0]&2?o.return:l[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,l[1])).done)return i;switch(o=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return n.label++,{value:l[1],done:!1};case 5:n.label++,o=l[1],l=[0];continue;case 7:l=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){n=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){n.label=l[1];break}if(l[0]===6&&n.label<i[1]){n.label=i[1],i=l;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(l);break}i[2]&&n.ops.pop(),n.trys.pop();continue}l=t.call(e,n)}catch(c){l=[6,c],o=0}finally{r=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function oo(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function la(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=r.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(s)throw s.error}}return i}function ca(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}function qn(e){return this instanceof qn?(this.v=e,this):new qn(e)}function RE(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(m){return function(y){return Promise.resolve(y).then(m,f)}}function u(m,y){r[m]&&(o[m]=function(v){return new Promise(function(S,h){i.push([m,v,S,h])>1||a(m,v)})},y&&(o[m]=y(o[m])))}function a(m,y){try{l(r[m](y))}catch(v){d(i[0][3],v)}}function l(m){m.value instanceof qn?Promise.resolve(m.value.v).then(c,f):d(i[0][2],m)}function c(m){a("next",m)}function f(m){a("throw",m)}function d(m,y){m(y),i.shift(),i.length&&a(i[0][0],i[0][1])}}function OE(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof oo=="function"?oo(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(u,a){s=e[i](s),o(u,a,s.done,s.value)})}}function o(i,s,u,a){Promise.resolve(a).then(function(l){i({value:l,done:u})},s)}}function re(e){return typeof e=="function"}function LE(e){var t=function(r){Error.call(r),r.stack=new Error().stack},n=e(t);return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Zs=LE(function(e){return function(n){e(this),this.message=n?n.length+` errors occurred during unsubscription:
`+n.map(function(r,o){return o+1+") "+r.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=n}});function _f(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Ll=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,n,r,o,i;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var u=oo(s),a=u.next();!a.done;a=u.next()){var l=a.value;l.remove(this)}}catch(v){t={error:v}}finally{try{a&&!a.done&&(n=u.return)&&n.call(u)}finally{if(t)throw t.error}}else s.remove(this);var c=this.initialTeardown;if(re(c))try{c()}catch(v){i=v instanceof Zs?v.errors:[v]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var d=oo(f),m=d.next();!m.done;m=d.next()){var y=m.value;try{Sf(y)}catch(v){i=i??[],v instanceof Zs?i=ca(ca([],la(i)),la(v.errors)):i.push(v)}}}catch(v){r={error:v}}finally{try{m&&!m.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}}if(i)throw new Zs(i)}},e.prototype.add=function(t){var n;if(t&&t!==this)if(this.closed)Sf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}},e.prototype._hasParent=function(t){var n=this._parentage;return n===t||Array.isArray(n)&&n.includes(t)},e.prototype._addParent=function(t){var n=this._parentage;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t},e.prototype._removeParent=function(t){var n=this._parentage;n===t?this._parentage=null:Array.isArray(n)&&_f(n,t)},e.prototype.remove=function(t){var n=this._finalizers;n&&_f(n,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}();Ll.EMPTY;function am(e){return e instanceof Ll||e&&"closed"in e&&re(e.remove)&&re(e.add)&&re(e.unsubscribe)}function Sf(e){re(e)?e():e.unsubscribe()}var lm={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},AE={setTimeout:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,ca([e,t],la(n)))},clearTimeout:function(e){return clearTimeout(e)},delegate:void 0};function cm(e){AE.setTimeout(function(){throw e})}function Ai(){}function DE(e){e()}var Al=function(e){Ol(t,e);function t(n){var r=e.call(this)||this;return r.isStopped=!1,n?(r.destination=n,am(n)&&n.add(r)):r.destination=HE,r}return t.create=function(n,r,o){return new fa(n,r,o)},t.prototype.next=function(n){this.isStopped||this._next(n)},t.prototype.error=function(n){this.isStopped||(this.isStopped=!0,this._error(n))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(n){this.destination.next(n)},t.prototype._error=function(n){try{this.destination.error(n)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(Ll),bE=Function.prototype.bind;function qs(e,t){return bE.call(e,t)}var ME=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var n=this.partialObserver;if(n.next)try{n.next(t)}catch(r){Uo(r)}},e.prototype.error=function(t){var n=this.partialObserver;if(n.error)try{n.error(t)}catch(r){Uo(r)}else Uo(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(n){Uo(n)}},e}(),fa=function(e){Ol(t,e);function t(n,r,o){var i=e.call(this)||this,s;if(re(n)||!n)s={next:n??void 0,error:r??void 0,complete:o??void 0};else{var u;i&&lm.useDeprecatedNextContext?(u=Object.create(n),u.unsubscribe=function(){return i.unsubscribe()},s={next:n.next&&qs(n.next,u),error:n.error&&qs(n.error,u),complete:n.complete&&qs(n.complete,u)}):s=n}return i.destination=new ME(s),i}return t}(Al);function Uo(e){cm(e)}function FE(e){throw e}var HE={closed:!0,next:Ai,error:FE,complete:Ai},Dl=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function BE(e){return e}function UE(e){return e.length===0?BE:e.length===1?e[0]:function(n){return e.reduce(function(r,o){return o(r)},n)}}var $e=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(t,n,r){var o=this,i=jE(t)?t:new fa(t,n,r);return DE(function(){var s=o,u=s.operator,a=s.source;i.add(u?u.call(i,a):a?o._subscribe(i):o._trySubscribe(i))}),i},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(n){t.error(n)}},e.prototype.forEach=function(t,n){var r=this;return n=wf(n),new n(function(o,i){var s=new fa({next:function(u){try{t(u)}catch(a){i(a),s.unsubscribe()}},error:i,complete:o});r.subscribe(s)})},e.prototype._subscribe=function(t){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(t)},e.prototype[Dl]=function(){return this},e.prototype.pipe=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return UE(t)(this)},e.prototype.toPromise=function(t){var n=this;return t=wf(t),new t(function(r,o){var i;n.subscribe(function(s){return i=s},function(s){return o(s)},function(){return r(i)})})},e.create=function(t){return new e(t)},e}();function wf(e){var t;return(t=e??lm.Promise)!==null&&t!==void 0?t:Promise}function $E(e){return e&&re(e.next)&&re(e.error)&&re(e.complete)}function jE(e){return e&&e instanceof Al||$E(e)&&am(e)}function zE(e){return re(e==null?void 0:e.lift)}function Nt(e){return function(t){if(zE(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Yt(e,t,n,r,o){return new GE(e,t,n,r,o)}var GE=function(e){Ol(t,e);function t(n,r,o,i,s,u){var a=e.call(this,n)||this;return a.onFinalize=s,a.shouldUnsubscribe=u,a._next=r?function(l){try{r(l)}catch(c){n.error(c)}}:e.prototype._next,a._error=i?function(l){try{i(l)}catch(c){n.error(c)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=o?function(){try{o()}catch(l){n.error(l)}finally{this.unsubscribe()}}:e.prototype._complete,a}return t.prototype.unsubscribe=function(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&((n=this.onFinalize)===null||n===void 0||n.call(this))}},t}(Al);function VE(e){return e&&re(e.schedule)}function WE(e){return e[e.length-1]}function XE(e){return VE(WE(e))?e.pop():void 0}var fm=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function dm(e){return re(e==null?void 0:e.then)}function pm(e){return re(e[Dl])}function hm(e){return Symbol.asyncIterator&&re(e==null?void 0:e[Symbol.asyncIterator])}function mm(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function YE(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var gm=YE();function ym(e){return re(e==null?void 0:e[gm])}function vm(e){return RE(this,arguments,function(){var n,r,o,i;return um(this,function(s){switch(s.label){case 0:n=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,qn(n.read())];case 3:return r=s.sent(),o=r.value,i=r.done,i?[4,qn(void 0)]:[3,5];case 4:return[2,s.sent()];case 5:return[4,qn(o)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return n.releaseLock(),[7];case 10:return[2]}})})}function Em(e){return re(e==null?void 0:e.getReader)}function Nn(e){if(e instanceof $e)return e;if(e!=null){if(pm(e))return QE(e);if(fm(e))return KE(e);if(dm(e))return ZE(e);if(hm(e))return _m(e);if(ym(e))return qE(e);if(Em(e))return JE(e)}throw mm(e)}function QE(e){return new $e(function(t){var n=e[Dl]();if(re(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function KE(e){return new $e(function(t){for(var n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function ZE(e){return new $e(function(t){e.then(function(n){t.closed||(t.next(n),t.complete())},function(n){return t.error(n)}).then(null,cm)})}function qE(e){return new $e(function(t){var n,r;try{for(var o=oo(e),i=o.next();!i.done;i=o.next()){var s=i.value;if(t.next(s),t.closed)return}}catch(u){n={error:u}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}t.complete()})}function _m(e){return new $e(function(t){e_(e,t).catch(function(n){return t.error(n)})})}function JE(e){return _m(vm(e))}function e_(e,t){var n,r,o,i;return PE(this,void 0,void 0,function(){var s,u;return um(this,function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),n=OE(e),a.label=1;case 1:return[4,n.next()];case 2:if(r=a.sent(),!!r.done)return[3,4];if(s=r.value,t.next(s),t.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return u=a.sent(),o={error:u},[3,11];case 6:return a.trys.push([6,,9,10]),r&&!r.done&&(i=n.return)?[4,i.call(n)]:[3,8];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function dn(e,t,n,r,o){r===void 0&&(r=0),o===void 0&&(o=!1);var i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Sm(e,t){return t===void 0&&(t=0),Nt(function(n,r){n.subscribe(Yt(r,function(o){return dn(r,e,function(){return r.next(o)},t)},function(){return dn(r,e,function(){return r.complete()},t)},function(o){return dn(r,e,function(){return r.error(o)},t)}))})}function wm(e,t){return t===void 0&&(t=0),Nt(function(n,r){r.add(e.schedule(function(){return n.subscribe(r)},t))})}function t_(e,t){return Nn(e).pipe(wm(t),Sm(t))}function n_(e,t){return Nn(e).pipe(wm(t),Sm(t))}function r_(e,t){return new $e(function(n){var r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function o_(e,t){return new $e(function(n){var r;return dn(n,t,function(){r=e[gm](),dn(n,t,function(){var o,i,s;try{o=r.next(),i=o.value,s=o.done}catch(u){n.error(u);return}s?n.complete():n.next(i)},0,!0)}),function(){return re(r==null?void 0:r.return)&&r.return()}})}function xm(e,t){if(!e)throw new Error("Iterable cannot be null");return new $e(function(n){dn(n,t,function(){var r=e[Symbol.asyncIterator]();dn(n,t,function(){r.next().then(function(o){o.done?n.complete():n.next(o.value)})},0,!0)})})}function i_(e,t){return xm(vm(e),t)}function s_(e,t){if(e!=null){if(pm(e))return t_(e,t);if(fm(e))return r_(e,t);if(dm(e))return n_(e,t);if(hm(e))return xm(e,t);if(ym(e))return o_(e,t);if(Em(e))return i_(e,t)}throw mm(e)}function Tm(e,t){return t?s_(e,t):Nn(e)}function Js(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=XE(e);return Tm(e,n)}function Cm(e,t){return Nt(function(n,r){var o=0;n.subscribe(Yt(r,function(i){r.next(e.call(t,i,o++))}))})}function u_(e,t,n,r,o,i,s,u){var a=[],l=0,c=0,f=!1,d=function(){f&&!a.length&&!l&&t.complete()},m=function(v){return l<r?y(v):a.push(v)},y=function(v){l++;var S=!1;Nn(n(v,c++)).subscribe(Yt(t,function(h){t.next(h)},function(){S=!0},void 0,function(){if(S)try{l--;for(var h=function(){var p=a.shift();s||y(p)};a.length&&l<r;)h();d()}catch(p){t.error(p)}}))};return e.subscribe(Yt(t,m,function(){f=!0,d()})),function(){}}function Im(e,t,n){return n===void 0&&(n=1/0),re(t)?Im(function(r,o){return Cm(function(i,s){return t(r,i,o,s)})(Nn(e(r,o)))},n):(typeof t=="number"&&(n=t),Nt(function(r,o){return u_(r,o,e,n)}))}var km=new $e(Ai);function xf(e,t){return Nt(function(n,r){var o=0;n.subscribe(Yt(r,function(i){return e.call(t,i,o++)&&r.next(i)}))})}function Nm(e){return Nt(function(t,n){var r=null,o=!1,i;r=t.subscribe(Yt(n,void 0,void 0,function(s){i=Nn(e(s,Nm(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function a_(e,t,n,r,o){return function(i,s){var u=n,a=t,l=0;i.subscribe(Yt(s,function(c){var f=l++;a=u?e(a,c,f):(u=!0,c)},function(){u&&s.next(a),s.complete()}))}}function l_(e,t){return Nt(a_(e,t,arguments.length>=2,!1,!0))}var c_=function(e,t){return e.push(t),e};function f_(){return Nt(function(e,t){l_(c_,[])(e).subscribe(t)})}function bl(e){return Nt(function(t,n){Nn(e).subscribe(Yt(n,function(){return n.complete()},Ai)),!n.closed&&t.subscribe(n)})}var da={exports:{}},eu,Tf;function d_(){if(Tf)return eu;Tf=1;var e=1e3,t=e*60,n=t*60,r=n*24,o=r*7,i=r*365.25;eu=function(c,f){f=f||{};var d=typeof c;if(d==="string"&&c.length>0)return s(c);if(d==="number"&&isFinite(c))return f.long?a(c):u(c);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(c))};function s(c){if(c=String(c),!(c.length>100)){var f=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(f){var d=parseFloat(f[1]),m=(f[2]||"ms").toLowerCase();switch(m){case"years":case"year":case"yrs":case"yr":case"y":return d*i;case"weeks":case"week":case"w":return d*o;case"days":case"day":case"d":return d*r;case"hours":case"hour":case"hrs":case"hr":case"h":return d*n;case"minutes":case"minute":case"mins":case"min":case"m":return d*t;case"seconds":case"second":case"secs":case"sec":case"s":return d*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return d;default:return}}}}function u(c){var f=Math.abs(c);return f>=r?Math.round(c/r)+"d":f>=n?Math.round(c/n)+"h":f>=t?Math.round(c/t)+"m":f>=e?Math.round(c/e)+"s":c+"ms"}function a(c){var f=Math.abs(c);return f>=r?l(c,f,r,"day"):f>=n?l(c,f,n,"hour"):f>=t?l(c,f,t,"minute"):f>=e?l(c,f,e,"second"):c+" ms"}function l(c,f,d,m){var y=f>=d*1.5;return Math.round(c/d)+" "+m+(y?"s":"")}return eu}function p_(e){n.debug=n,n.default=n,n.coerce=a,n.disable=s,n.enable=o,n.enabled=u,n.humanize=d_(),n.destroy=l,Object.keys(e).forEach(c=>{n[c]=e[c]}),n.names=[],n.skips=[],n.formatters={};function t(c){let f=0;for(let d=0;d<c.length;d++)f=(f<<5)-f+c.charCodeAt(d),f|=0;return n.colors[Math.abs(f)%n.colors.length]}n.selectColor=t;function n(c){let f,d=null,m,y;function v(...S){if(!v.enabled)return;const h=v,p=Number(new Date),g=p-(f||p);h.diff=g,h.prev=f,h.curr=p,f=p,S[0]=n.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let E=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(x,I)=>{if(x==="%%")return"%";E++;const N=n.formatters[I];if(typeof N=="function"){const X=S[E];x=N.call(h,X),S.splice(E,1),E--}return x}),n.formatArgs.call(h,S),(h.log||n.log).apply(h,S)}return v.namespace=c,v.useColors=n.useColors(),v.color=n.selectColor(c),v.extend=r,v.destroy=n.destroy,Object.defineProperty(v,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==n.namespaces&&(m=n.namespaces,y=n.enabled(c)),y),set:S=>{d=S}}),typeof n.init=="function"&&n.init(v),v}function r(c,f){const d=n(this.namespace+(typeof f>"u"?":":f)+c);return d.log=this.log,d}function o(c){n.save(c),n.namespaces=c,n.names=[],n.skips=[];const f=(typeof c=="string"?c:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const d of f)d[0]==="-"?n.skips.push(d.slice(1)):n.names.push(d)}function i(c,f){let d=0,m=0,y=-1,v=0;for(;d<c.length;)if(m<f.length&&(f[m]===c[d]||f[m]==="*"))f[m]==="*"?(y=m,v=d,m++):(d++,m++);else if(y!==-1)m=y+1,v++,d=v;else return!1;for(;m<f.length&&f[m]==="*";)m++;return m===f.length}function s(){const c=[...n.names,...n.skips.map(f=>"-"+f)].join(",");return n.enable(""),c}function u(c){for(const f of n.skips)if(i(c,f))return!1;for(const f of n.names)if(i(c,f))return!0;return!1}function a(c){return c instanceof Error?c.stack||c.message:c}function l(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}var h_=p_;(function(e,t){var n={};t.formatArgs=o,t.save=i,t.load=s,t.useColors=r,t.storage=u(),t.destroy=(()=>{let l=!1;return()=>{l||(l=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function r(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let l;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(l=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(l[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function o(l){if(l[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+l[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const c="color: "+this.color;l.splice(1,0,c,"color: inherit");let f=0,d=0;l[0].replace(/%[a-zA-Z%]/g,m=>{m!=="%%"&&(f++,m==="%c"&&(d=f))}),l.splice(d,0,c)}t.log=console.debug||console.log||(()=>{});function i(l){try{l?t.storage.setItem("debug",l):t.storage.removeItem("debug")}catch{}}function s(){let l;try{l=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch{}return!l&&typeof process<"u"&&"env"in process&&(l=n.DEBUG),l}function u(){try{return localStorage}catch{}}e.exports=h_(t);const{formatters:a}=e.exports;a.j=function(l){try{return JSON.stringify(l)}catch(c){return"[UnexpectedJSONParseError]: "+c.message}}})(da,da.exports);var m_=da.exports;const Pm=pd(m_),Cf=Pm("claude:ipc");class g_{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(bl(this.onDestroy)).subscribe(t)}}function y_(e){return new $e(t=>e.subscribe(t))}function v_(e){return e==null?!1:typeof e=="object"&&"then"in e}function E_(e){return e!==null&&(typeof e=="object"||typeof e=="function")&&typeof e.subscribe=="function"}let tu={};function __(e,t,n,r){var s,u;const o=S_(e,t,n,r),i=Rm(t);return(u=(s=tu[e])==null?void 0:s.subscription)==null||u.unsubscribe(),tu[e]={subscription:o,metadata:i},o.add(()=>delete tu[e]),o}function S_(e,t,n,r){return y_(n).pipe(xf(o=>o.methodChain.split(".")[0]===e),xf(o=>r(o)?!0:(console.error(`Invalid message received: ${JSON.stringify(o)}`),!1)),Im(o=>{const i=o.methodChain.split(".").splice(1),s=i.pop(),u=i.reduce((f,d)=>f[d],t),a=o.customSendMethod??n.next.bind(n);let l;try{const f=u[s];Cf('Calling method "%s" with args %o',s,o.argList),l=f.call(u,...o.argList)}catch(f){return Cf(`Error in API call for message: %o
%o`,o,f),Js({sendMethod:a,result:{error:f,callId:o.callId}})}let c=Js(l);return v_(l)?c=Tm(l):E_(l)&&(c=l.pipe(f_())),c.pipe(Cm(f=>({sendMethod:a,result:{result:f,callId:o.callId}})),Nm(f=>Js({sendMethod:a,result:{result:null,callId:o.callId,error:f}})))}),bl(n.onDestroy)).subscribe({next:o=>o.sendMethod(o.result),error:o=>{console.error(`Error in API Handler - this should not happen! ${o}
${o.stack}`)}})}function Rm(e){return w_(e).reduce((t,n)=>(typeof e[n]=="function"&&(t[n]=!0),typeof e[n]=="object"&&e[n]!==null&&(t[n]=Rm(e[n])),t),{})}function w_(e){const t=Object.keys(e),n=Object.getOwnPropertyNames(e).filter(s=>!t.includes(s)),r=[];let o=Object.getPrototypeOf(e);for(;o&&o!==Object.prototype;)Object.getOwnPropertyNames(o).filter(s=>!["constructor"].includes(s)).forEach(s=>{!r.includes(s)&&!t.includes(s)&&!n.includes(s)&&r.push(s)}),o=Object.getPrototypeOf(o);const i=Object.getOwnPropertySymbols(e).map(s=>s.toString());return[...t,...n,...r,...i]}Pm("claude:ipc");class x_{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(bl(this.onDestroy)).subscribe(t)}}function T_(){return new x_({next:e=>window.rpcInternal.rpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new $e(e=>window.rpcInternal.rpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),km)}function C_(){return new g_({next:e=>window.rpcInternal.reverseRpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new $e(e=>window.rpcInternal.reverseRpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),km)}function I_(e,t){return __(e,t,window.replyPort,()=>!0)}window.sendPort=T_();window.replyPort=C_();var Om=(e=>(e.QuickWindow="QuickWindow",e.Find="Find",e.StartupSettings="StartupSettings",e.Filesystem="Filesystem",e.Intl="Intl",e.IntlSync="IntlSync",e.AboutWindow="AboutWindow",e.WindowControl="WindowControl",e))(Om||{});function k_(e){const[t,n]=me.useState(window.initialLocale),[r,o]=me.useState(window.initialMessages);return me.useEffect(()=>{const i=I_(Om.Intl,{localeChanged:(s,u)=>{n(s),o(u)}});return()=>i.unsubscribe()},[n,o]),iu.jsx(NE,{locale:t,messages:r,...e})}async function N_(e,t,n){const r=await t,o="default"in r?r.default:r,i=Ih(e),s=n??{};return i.render(iu.jsx(k_,{children:iu.jsx(o,{...s})})),()=>{i.unmount()}}window.attachReactToElement=N_;const Lm=Object.prototype.toString;function Ml(e){switch(Lm.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return _n(e,Error)}}function mr(e,t){return Lm.call(e)===`[object ${t}]`}function Am(e){return mr(e,"ErrorEvent")}function If(e){return mr(e,"DOMError")}function P_(e){return mr(e,"DOMException")}function St(e){return mr(e,"String")}function Fl(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function Hl(e){return e===null||Fl(e)||typeof e!="object"&&typeof e!="function"}function ar(e){return mr(e,"Object")}function ds(e){return typeof Event<"u"&&_n(e,Event)}function R_(e){return typeof Element<"u"&&_n(e,Element)}function O_(e){return mr(e,"RegExp")}function ps(e){return!!(e&&e.then&&typeof e.then=="function")}function L_(e){return ar(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function _n(e,t){try{return e instanceof t}catch{return!1}}function Dm(e){return!!(typeof e=="object"&&e!==null&&(e.__isVue||e._isVue))}function Jn(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function kf(e,t){if(!Array.isArray(e))return"";const n=[];for(let r=0;r<e.length;r++){const o=e[r];try{Dm(o)?n.push("[VueViewModel]"):n.push(String(o))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function A_(e,t,n=!1){return St(e)?O_(t)?t.test(e):St(t)?n?e===t:e.includes(t):!1:!1}function hs(e,t=[],n=!1){return t.some(r=>A_(e,r,n))}function D_(e,t,n=250,r,o,i,s){if(!i.exception||!i.exception.values||!s||!_n(s.originalException,Error))return;const u=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;u&&(i.exception.values=b_(pa(e,t,o,s.originalException,r,i.exception.values,u,0),n))}function pa(e,t,n,r,o,i,s,u){if(i.length>=n+1)return i;let a=[...i];if(_n(r[o],Error)){Nf(s,u);const l=e(t,r[o]),c=a.length;Pf(l,o,c,u),a=pa(e,t,n,r[o],o,[l,...a],l,c)}return Array.isArray(r.errors)&&r.errors.forEach((l,c)=>{if(_n(l,Error)){Nf(s,u);const f=e(t,l),d=a.length;Pf(f,`errors[${c}]`,d,u),a=pa(e,t,n,l,o,[f,...a],f,d)}}),a}function Nf(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function Pf(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function b_(e,t){return e.map(n=>(n.value&&(n.value=Jn(n.value,t)),n))}function bm(e){if(e!==void 0)return e>=400&&e<500?"warning":e>=500?"error":void 0}const pn="8.33.1",F=globalThis;function ms(e,t,n){const r=n||F,o=r.__SENTRY__=r.__SENTRY__||{},i=o[pn]=o[pn]||{};return i[e]||(i[e]=t())}const Bl=F,M_=80;function Mm(e,t={}){if(!e)return"<unknown>";try{let n=e;const r=5,o=[];let i=0,s=0;const u=" > ",a=u.length;let l;const c=Array.isArray(t)?t:t.keyAttrs,f=!Array.isArray(t)&&t.maxStringLength||M_;for(;n&&i++<r&&(l=F_(n,c),!(l==="html"||i>1&&s+o.length*a+l.length>=f));)o.push(l),s+=l.length,n=n.parentNode;return o.reverse().join(u)}catch{return"<unknown>"}}function F_(e,t){const n=e,r=[];if(!n||!n.tagName)return"";if(Bl.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const o=t&&t.length?t.filter(s=>n.getAttribute(s)).map(s=>[s,n.getAttribute(s)]):null;if(o&&o.length)o.forEach(s=>{r.push(`[${s[0]}="${s[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const s=n.className;if(s&&St(s)){const u=s.split(/\s+/);for(const a of u)r.push(`.${a}`)}}const i=["aria-label","type","name","title","alt"];for(const s of i){const u=n.getAttribute(s);u&&r.push(`[${s}="${u}"]`)}return r.join("")}function H_(){try{return Bl.document.location.href}catch{return""}}function B_(e){if(!Bl.HTMLElement)return null;let t=e;const n=5;for(let r=0;r<n;r++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}const ho=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,U_="Sentry Logger ",ha=["debug","info","warn","error","log","assert","trace"],Di={};function mo(e){if(!("console"in F))return e();const t=F.console,n={},r=Object.keys(Di);r.forEach(o=>{const i=Di[o];n[o]=t[o],t[o]=i});try{return e()}finally{r.forEach(o=>{t[o]=n[o]})}}function $_(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return ho?ha.forEach(n=>{t[n]=(...r)=>{e&&mo(()=>{F.console[n](`${U_}[${n}]:`,...r)})}}):ha.forEach(n=>{t[n]=()=>{}}),t}const P=ms("logger",$_),j_=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function z_(e){return e==="http"||e==="https"}function gs(e,t=!1){const{host:n,path:r,pass:o,port:i,projectId:s,protocol:u,publicKey:a}=e;return`${u}://${a}${t&&o?`:${o}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${s}`}function G_(e){const t=j_.exec(e);if(!t){mo(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});return}const[n,r,o="",i="",s="",u=""]=t.slice(1);let a="",l=u;const c=l.split("/");if(c.length>1&&(a=c.slice(0,-1).join("/"),l=c.pop()),l){const f=l.match(/^\d+/);f&&(l=f[0])}return Fm({host:i,pass:o,path:a,projectId:l,port:s,protocol:n,publicKey:r})}function Fm(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function V_(e){if(!ho)return!0;const{port:t,projectId:n,protocol:r}=e;return["protocol","publicKey","host","projectId"].find(s=>e[s]?!1:(P.error(`Invalid Sentry Dsn: ${s} missing`),!0))?!1:n.match(/^\d+$/)?z_(r)?t&&isNaN(parseInt(t,10))?(P.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):!0:(P.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(P.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function W_(e){const t=typeof e=="string"?G_(e):Fm(e);if(!(!t||!V_(t)))return t}class lt extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}function Ae(e,t,n){if(!(t in e))return;const r=e[t],o=n(r);typeof o=="function"&&Hm(o,r),e[t]=o}function Sn(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{ho&&P.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function Hm(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,Sn(e,"__sentry_original__",t)}catch{}}function Ul(e){return e.__sentry_original__}function X_(e){return Object.keys(e).map(t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`).join("&")}function Bm(e){if(Ml(e))return{message:e.message,name:e.name,stack:e.stack,...Of(e)};if(ds(e)){const t={type:e.type,target:Rf(e.target),currentTarget:Rf(e.currentTarget),...Of(e)};return typeof CustomEvent<"u"&&_n(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function Rf(e){try{return R_(e)?Mm(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function Of(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}else return{}}function Y_(e,t=40){const n=Object.keys(Bm(e));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return Jn(r,t);for(let o=n.length;o>0;o--){const i=n.slice(0,o).join(", ");if(!(i.length>t))return o===n.length?i:Jn(i,t)}return""}function De(e){return ma(e,new Map)}function ma(e,t){if(Q_(e)){const n=t.get(e);if(n!==void 0)return n;const r={};t.set(e,r);for(const o of Object.getOwnPropertyNames(e))typeof e[o]<"u"&&(r[o]=ma(e[o],t));return r}if(Array.isArray(e)){const n=t.get(e);if(n!==void 0)return n;const r=[];return t.set(e,r),e.forEach(o=>{r.push(ma(o,t))}),r}return e}function Q_(e){if(!ar(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||t==="Object"}catch{return!0}}const Um=50,Qt="?",Lf=/\(error: (.*)\)/,Af=/captureMessage|captureException/;function $m(...e){const t=e.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,o=0)=>{const i=[],s=n.split(`
`);for(let u=r;u<s.length;u++){const a=s[u];if(a.length>1024)continue;const l=Lf.test(a)?a.replace(Lf,"$1"):a;if(!l.match(/\S*Error: /)){for(const c of t){const f=c(l);if(f){i.push(f);break}}if(i.length>=Um+o)break}}return jm(i.slice(o))}}function K_(e){return Array.isArray(e)?$m(...e):e}function jm(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test($o(t).function||"")&&t.pop(),t.reverse(),Af.test($o(t).function||"")&&(t.pop(),Af.test($o(t).function||"")&&t.pop()),t.slice(0,Um).map(n=>({...n,filename:n.filename||$o(t).filename,function:n.function||Qt}))}function $o(e){return e[e.length-1]||{}}const nu="<anonymous>";function Kt(e){try{return!e||typeof e!="function"?nu:e.name||nu}catch{return nu}}function Df(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const ui={},bf={};function Pn(e,t){ui[e]=ui[e]||[],ui[e].push(t)}function Rn(e,t){bf[e]||(t(),bf[e]=!0)}function ot(e,t){const n=e&&ui[e];if(n)for(const r of n)try{r(t)}catch(o){ho&&P.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${Kt(r)}
Error:`,o)}}function Z_(e){const t="console";Pn(t,e),Rn(t,q_)}function q_(){"console"in F&&ha.forEach(function(e){e in F.console&&Ae(F.console,e,function(t){return Di[e]=t,function(...n){ot("console",{args:n,level:e});const o=Di[e];o&&o.apply(F.console,n)}})})}const ga=F;function zm(){if(!("fetch"in ga))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function ya(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function J_(){if(typeof EdgeRuntime=="string")return!0;if(!zm())return!1;if(ya(ga.fetch))return!0;let e=!1;const t=ga.document;if(t&&typeof t.createElement=="function")try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=ya(n.contentWindow.fetch)),t.head.removeChild(n)}catch(n){ho&&P.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return e}const Gm=1e3;function go(){return Date.now()/Gm}function eS(){const{performance:e}=F;if(!e||!e.now)return go;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/Gm}const wt=eS();(()=>{const{performance:e}=F;if(!e||!e.now)return;const t=3600*1e3,n=e.now(),r=Date.now(),o=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,i=o<t,s=e.timing&&e.timing.navigationStart,a=typeof s=="number"?Math.abs(s+n-r):t,l=a<t;return i||l?o<=a?e.timeOrigin:s:r})();function tS(e,t){const n="fetch";Pn(n,e),Rn(n,()=>nS(void 0,t))}function nS(e,t=!1){t&&!J_()||Ae(F,"fetch",function(n){return function(...r){const{method:o,url:i}=rS(r),s={args:r,fetchData:{method:o,url:i},startTimestamp:wt()*1e3};ot("fetch",{...s});const u=new Error().stack;return n.apply(F,r).then(async a=>(ot("fetch",{...s,endTimestamp:wt()*1e3,response:a}),a),a=>{throw ot("fetch",{...s,endTimestamp:wt()*1e3,error:a}),Ml(a)&&a.stack===void 0&&(a.stack=u,Sn(a,"framesToPop",1)),a})}})}function va(e,t){return!!e&&typeof e=="object"&&!!e[t]}function Mf(e){return typeof e=="string"?e:e?va(e,"url")?e.url:e.toString?e.toString():"":""}function rS(e){if(e.length===0)return{method:"GET",url:""};if(e.length===2){const[n,r]=e;return{url:Mf(n),method:va(r,"method")?String(r.method).toUpperCase():"GET"}}const t=e[0];return{url:Mf(t),method:va(t,"method")?String(t.method).toUpperCase():"GET"}}let jo=null;function oS(e){const t="error";Pn(t,e),Rn(t,iS)}function iS(){jo=F.onerror,F.onerror=function(e,t,n,r,o){return ot("error",{column:r,error:o,line:n,msg:e,url:t}),jo&&!jo.__SENTRY_LOADER__?jo.apply(this,arguments):!1},F.onerror.__SENTRY_INSTRUMENTED__=!0}let zo=null;function sS(e){const t="unhandledrejection";Pn(t,e),Rn(t,uS)}function uS(){zo=F.onunhandledrejection,F.onunhandledrejection=function(e){return ot("unhandledrejection",e),zo&&!zo.__SENTRY_LOADER__?zo.apply(this,arguments):!0},F.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function aS(){return"npm"}function lS(){const e=typeof WeakSet=="function",t=e?new WeakSet:[];function n(o){if(e)return t.has(o)?!0:(t.add(o),!1);for(let i=0;i<t.length;i++)if(t[i]===o)return!0;return t.push(o),!1}function r(o){if(e)t.delete(o);else for(let i=0;i<t.length;i++)if(t[i]===o){t.splice(i,1);break}}return[n,r]}function Me(){const e=F,t=e.crypto||e.msCrypto;let n=()=>Math.random()*16;try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(n=()=>{const r=new Uint8Array(1);return t.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function Vm(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function Lt(e){const{message:t,event_id:n}=e;if(t)return t;const r=Vm(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Ea(e,t,n){const r=e.exception=e.exception||{},o=r.values=r.values||[],i=o[0]=o[0]||{};i.value||(i.value=t||""),i.type||(i.type="Error")}function io(e,t){const n=Vm(e);if(!n)return;const r={type:"generic",handled:!0},o=n.mechanism;if(n.mechanism={...r,...o,...t},t&&"data"in t){const i={...o&&o.data,...t.data};n.mechanism.data=i}}function Ff(e){if(e&&e.__sentry_captured__)return!0;try{Sn(e,"__sentry_captured__",!0)}catch{}return!1}function Wm(e){return Array.isArray(e)?e:[e]}function yt(e,t=100,n=1/0){try{return _a("",e,t,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function Xm(e,t=3,n=100*1024){const r=yt(e,t);return pS(r)>n?Xm(e,t-1,n):r}function _a(e,t,n=1/0,r=1/0,o=lS()){const[i,s]=o;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const u=cS(e,t);if(!u.startsWith("[object "))return u;if(t.__sentry_skip_normalization__)return t;const a=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(a===0)return u.replace("object ","");if(i(t))return"[Circular ~]";const l=t;if(l&&typeof l.toJSON=="function")try{const m=l.toJSON();return _a("",m,a-1,r,o)}catch{}const c=Array.isArray(t)?[]:{};let f=0;const d=Bm(t);for(const m in d){if(!Object.prototype.hasOwnProperty.call(d,m))continue;if(f>=r){c[m]="[MaxProperties ~]";break}const y=d[m];c[m]=_a(m,y,a-1,r,o),f++}return s(t),c}function cS(e,t){try{if(e==="domain"&&t&&typeof t=="object"&&t._events)return"[Domain]";if(e==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&t===global)return"[Global]";if(typeof window<"u"&&t===window)return"[Window]";if(typeof document<"u"&&t===document)return"[Document]";if(Dm(t))return"[VueViewModel]";if(L_(t))return"[SyntheticEvent]";if(typeof t=="number"&&!Number.isFinite(t))return`[${t}]`;if(typeof t=="function")return`[Function: ${Kt(t)}]`;if(typeof t=="symbol")return`[${String(t)}]`;if(typeof t=="bigint")return`[BigInt: ${String(t)}]`;const n=fS(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function fS(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}function dS(e){return~-encodeURI(e).split(/%..|./).length}function pS(e){return dS(JSON.stringify(e))}var mt;(function(e){e[e.PENDING=0]="PENDING";const n=1;e[e.RESOLVED=n]="RESOLVED";const r=2;e[e.REJECTED=r]="REJECTED"})(mt||(mt={}));function wn(e){return new Ge(t=>{t(e)})}function bi(e){return new Ge((t,n)=>{n(e)})}class Ge{constructor(t){Ge.prototype.__init.call(this),Ge.prototype.__init2.call(this),Ge.prototype.__init3.call(this),Ge.prototype.__init4.call(this),this._state=mt.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(n){this._reject(n)}}then(t,n){return new Ge((r,o)=>{this._handlers.push([!1,i=>{if(!t)r(i);else try{r(t(i))}catch(s){o(s)}},i=>{if(!n)o(i);else try{r(n(i))}catch(s){o(s)}}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new Ge((n,r)=>{let o,i;return this.then(s=>{i=!1,o=s,t&&t()},s=>{i=!0,o=s,t&&t()}).then(()=>{if(i){r(o);return}n(o)})})}__init(){this._resolve=t=>{this._setResult(mt.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(mt.REJECTED,t)}}__init3(){this._setResult=(t,n)=>{if(this._state===mt.PENDING){if(ps(n)){n.then(this._resolve,this._reject);return}this._state=t,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===mt.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===mt.RESOLVED&&n[1](this._value),this._state===mt.REJECTED&&n[2](this._value),n[0]=!0)})}}}function hS(e){const t=[];function n(){return e===void 0||t.length<e}function r(s){return t.splice(t.indexOf(s),1)[0]||Promise.resolve(void 0)}function o(s){if(!n())return bi(new lt("Not adding Promise because buffer limit was reached."));const u=s();return t.indexOf(u)===-1&&t.push(u),u.then(()=>r(u)).then(null,()=>r(u).then(null,()=>{})),u}function i(s){return new Ge((u,a)=>{let l=t.length;if(!l)return u(!0);const c=setTimeout(()=>{s&&s>0&&u(!1)},s);t.forEach(f=>{wn(f).then(()=>{--l||(clearTimeout(c),u(!0))},a)})})}return{$:t,add:o,drain:i}}function ru(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}const mS=["fatal","error","warning","log","info","debug"];function gS(e){return e==="warn"?"warning":mS.includes(e)?e:"log"}function yS(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&e!==void 0&&!e.includes("node_modules/")}function vS(e){const t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{const o=r.match(n);if(o){let i,s,u,a,l;if(o[1]){u=o[1];let d=u.lastIndexOf(".");if(u[d-1]==="."&&d--,d>0){i=u.slice(0,d),s=u.slice(d+1);const m=i.indexOf(".Module");m>0&&(u=u.slice(m+1),i=i.slice(0,m))}a=void 0}s&&(a=i,l=s),s==="<anonymous>"&&(l=void 0,u=void 0),u===void 0&&(l=l||Qt,u=a?`${a}.${l}`:l);let c=o[2]&&o[2].startsWith("file://")?o[2].slice(7):o[2];const f=o[5]==="native";return c&&c.match(/\/[A-Z]:/)&&(c=c.slice(1)),!c&&o[5]&&!f&&(c=o[5]),{filename:c,module:void 0,function:u,lineno:Hf(o[3]),colno:Hf(o[4]),in_app:yS(c||"",f)}}if(r.match(t))return{filename:r}}}function ES(e){return[90,vS()]}function Hf(e){return parseInt(e||"",10)||void 0}const _S="sentry-",SS=/^sentry-/;function wS(e){const t=xS(e);if(!t)return;const n=Object.entries(t).reduce((r,[o,i])=>{if(o.match(SS)){const s=o.slice(_S.length);r[s]=i}return r},{});if(Object.keys(n).length>0)return n}function xS(e){if(!(!e||!St(e)&&!Array.isArray(e)))return Array.isArray(e)?e.reduce((t,n)=>{const r=Bf(n);return Object.entries(r).forEach(([o,i])=>{t[o]=i}),t},{}):Bf(e)}function Bf(e){return e.split(",").map(t=>t.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((t,[n,r])=>(n&&r&&(t[n]=r),t),{})}function yo(e,t=[]){return[e,t]}function TS(e,t){const[n,r]=e;return[n,[...r,t]]}function Uf(e,t){const n=e[1];for(const r of n){const o=r[0].type;if(t(r,o))return!0}return!1}function Sa(e){return F.__SENTRY__&&F.__SENTRY__.encodePolyfill?F.__SENTRY__.encodePolyfill(e):new TextEncoder().encode(e)}function CS(e){const[t,n]=e;let r=JSON.stringify(t);function o(i){typeof r=="string"?r=typeof i=="string"?r+i:[Sa(r),i]:r.push(typeof i=="string"?Sa(i):i)}for(const i of n){const[s,u]=i;if(o(`
${JSON.stringify(s)}
`),typeof u=="string"||u instanceof Uint8Array)o(u);else{let a;try{a=JSON.stringify(u)}catch{a=JSON.stringify(yt(u))}o(a)}}return typeof r=="string"?r:IS(r)}function IS(e){const t=e.reduce((o,i)=>o+i.length,0),n=new Uint8Array(t);let r=0;for(const o of e)n.set(o,r),r+=o.length;return n}function kS(e){const t=typeof e.data=="string"?Sa(e.data):e.data;return[De({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const NS={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function $f(e){return NS[e]}function Ym(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function PS(e,t,n,r){const o=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:gs(r)},...o&&{trace:De({...o})}}}function RS(e,t,n){const r=[{type:"client_report"},{timestamp:go(),discarded_events:e}];return yo(t?{dsn:t}:{},[r])}const OS=60*1e3;function LS(e,t=Date.now()){const n=parseInt(`${e}`,10);if(!isNaN(n))return n*1e3;const r=Date.parse(`${e}`);return isNaN(r)?OS:r-t}function AS(e,t){return e[t]||e.all||0}function DS(e,t,n=Date.now()){return AS(e,t)>n}function bS(e,{statusCode:t,headers:n},r=Date.now()){const o={...e},i=n&&n["x-sentry-rate-limits"],s=n&&n["retry-after"];if(i)for(const u of i.trim().split(",")){const[a,l,,,c]=u.split(":",5),f=parseInt(a,10),d=(isNaN(f)?60:f)*1e3;if(!l)o.all=r+d;else for(const m of l.split(";"))m==="metric_bucket"?(!c||c.split(";").includes("custom"))&&(o[m]=r+d):o[m]=r+d}else s?o.all=r+LS(s,r):t===429&&(o.all=r+60*1e3);return o}function jf(){return{traceId:Me(),spanId:Me().substring(16)}}const Go=F;function MS(){const e=Go.chrome,t=e&&e.app&&e.app.runtime,n="history"in Go&&!!Go.history.pushState&&!!Go.history.replaceState;return!t&&n}const U=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function ys(){return $l(F),F}function $l(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||pn,t[pn]=t[pn]||{}}function FS(e){const t=wt(),n={sid:Me(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>BS(n)};return e&&lr(n,e),n}function lr(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||wt(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:Me()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function HS(e,t){let n={};e.status==="ok"&&(n={status:"exited"}),lr(e,n)}function BS(e){return De({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}const wa="_sentrySpan";function zf(e,t){t?Sn(e,wa,t):delete e[wa]}function Gf(e){return e[wa]}const US=100;class jl{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=jf()}clone(){const t=new jl;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,zf(t,Gf(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&lr(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,[r,o]=n instanceof xn?[n.getScopeData(),n.getRequestSession()]:ar(n)?[t,t.requestSession]:[],{tags:i,extra:s,user:u,contexts:a,level:l,fingerprint:c=[],propagationContext:f}=r||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...s},this._contexts={...this._contexts,...a},u&&Object.keys(u).length&&(this._user=u),l&&(this._level=l),c.length&&(this._fingerprint=c),f&&(this._propagationContext=f),o&&(this._requestSession=o),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,zf(this,void 0),this._attachments=[],this._propagationContext=jf(),this._notifyScopeListeners(),this}addBreadcrumb(t,n){const r=typeof n=="number"?n:US;if(r<=0)return this;const o={timestamp:go(),...t},i=this._breadcrumbs;return i.push(o),this._breadcrumbs=i.length>r?i.slice(-r):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Gf(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,n){const r=n&&n.event_id?n.event_id:Me();if(!this._client)return P.warn("No client configured on scope - will not capture exception!"),r;const o=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:o,...n,event_id:r},this),r}captureMessage(t,n,r){const o=r&&r.event_id?r.event_id:Me();if(!this._client)return P.warn("No client configured on scope - will not capture message!"),o;const i=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:i,...r,event_id:o},this),o}captureEvent(t,n){const r=n&&n.event_id?n.event_id:Me();return this._client?(this._client.captureEvent(t,{...n,event_id:r},this),r):(P.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}const xn=jl;function $S(){return ms("defaultCurrentScope",()=>new xn)}function jS(){return ms("defaultIsolationScope",()=>new xn)}class zS{constructor(t,n){let r;t?r=t:r=new xn;let o;n?o=n:o=new xn,this._stack=[{scope:r}],this._isolationScope=o}withScope(t){const n=this._pushScope();let r;try{r=t(n)}catch(o){throw this._popScope(),o}return ps(r)?r.then(o=>(this._popScope(),o),o=>{throw this._popScope(),o}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function cr(){const e=ys(),t=$l(e);return t.stack=t.stack||new zS($S(),jS())}function GS(e){return cr().withScope(e)}function VS(e,t){const n=cr();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function Vf(e){return cr().withScope(()=>e(cr().getIsolationScope()))}function WS(){return{withIsolationScope:Vf,withScope:GS,withSetScope:VS,withSetIsolationScope:(e,t)=>Vf(t),getCurrentScope:()=>cr().getScope(),getIsolationScope:()=>cr().getIsolationScope()}}function zl(e){const t=$l(e);return t.acs?t.acs:WS()}function pt(){const e=ys();return zl(e).getCurrentScope()}function On(){const e=ys();return zl(e).getIsolationScope()}function XS(){return ms("globalScope",()=>new xn)}function YS(...e){const t=ys(),n=zl(t);if(e.length===2){const[r,o]=e;return r?n.withSetScope(r,o):n.withScope(o)}return n.withScope(e[0])}function ye(){return pt().getClient()}const QS="_sentryMetrics";function KS(e){const t=e[QS];if(!t)return;const n={};for(const[,[r,o]]of t)(n[r]||(n[r]=[])).push(De(o));return n}const ZS="sentry.source",qS="sentry.sample_rate",JS="sentry.op",e1="sentry.origin",t1=0,n1=1,r1=1;function o1(e){const{spanId:t,traceId:n}=e.spanContext(),{parent_span_id:r}=Mi(e);return De({parent_span_id:r,span_id:t,trace_id:n})}function Wf(e){return typeof e=="number"?Xf(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?Xf(e.getTime()):wt()}function Xf(e){return e>9999999999?e/1e3:e}function Mi(e){if(s1(e))return e.getSpanJSON();try{const{spanId:t,traceId:n}=e.spanContext();if(i1(e)){const{attributes:r,startTime:o,name:i,endTime:s,parentSpanId:u,status:a}=e;return De({span_id:t,trace_id:n,data:r,description:i,parent_span_id:u,start_timestamp:Wf(o),timestamp:Wf(s)||void 0,status:a1(a),op:r[JS],origin:r[e1],_metrics_summary:KS(e)})}return{span_id:t,trace_id:n}}catch{return{}}}function i1(e){const t=e;return!!t.attributes&&!!t.startTime&&!!t.name&&!!t.endTime&&!!t.status}function s1(e){return typeof e.getSpanJSON=="function"}function u1(e){const{traceFlags:t}=e.spanContext();return t===r1}function a1(e){if(!(!e||e.code===t1))return e.code===n1?"ok":e.message||"unknown_error"}const l1="_sentryRootSpan";function Qm(e){return e[l1]||e}function c1(e){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=ye(),n=t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const Gl="production",f1="_frozenDsc";function Km(e,t){const n=t.getOptions(),{publicKey:r}=t.getDsn()||{},o=De({environment:n.environment||Gl,release:n.release,public_key:r,trace_id:e});return t.emit("createDsc",o),o}function d1(e){const t=ye();if(!t)return{};const n=Km(Mi(e).trace_id||"",t),r=Qm(e),o=r[f1];if(o)return o;const i=r.spanContext().traceState,s=i&&i.get("sentry.dsc"),u=s&&wS(s);if(u)return u;const a=Mi(r),l=a.data||{},c=l[qS];c!=null&&(n.sample_rate=`${c}`);const f=l[ZS],d=a.description;return f!=="url"&&d&&(n.transaction=d),c1()&&(n.sampled=String(u1(r))),t.emit("createDsc",n,r),n}function p1(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;if(typeof t!="number"||isNaN(t)||t<0||t>1){U&&P.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`);return}return t}function h1(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function m1(e,t,n,r){const o=Ym(n),i={sent_at:new Date().toISOString(),...o&&{sdk:o},...!!r&&t&&{dsn:gs(t)}},s="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return yo(i,[s])}function g1(e,t,n,r){const o=Ym(n),i=e.type&&e.type!=="replay_event"?e.type:"event";h1(e,n&&n.sdk);const s=PS(e,o,r,t);return delete e.sdkProcessingMetadata,yo(s,[[{type:i},e]])}function xa(e,t,n,r=0){return new Ge((o,i)=>{const s=e[r];if(t===null||typeof s!="function")o(t);else{const u=s({...t},n);U&&s.id&&u===null&&P.log(`Event processor "${s.id}" dropped event`),ps(u)?u.then(a=>xa(e,a,n,r+1).then(o)).then(null,i):xa(e,u,n,r+1).then(o).then(null,i)}})}function y1(e,t){const{fingerprint:n,span:r,breadcrumbs:o,sdkProcessingMetadata:i}=t;v1(e,t),r&&S1(e,r),w1(e,n),E1(e,o),_1(e,i)}function Ta(e,t){const{extra:n,tags:r,user:o,contexts:i,level:s,sdkProcessingMetadata:u,breadcrumbs:a,fingerprint:l,eventProcessors:c,attachments:f,propagationContext:d,transactionName:m,span:y}=t;Ir(e,"extra",n),Ir(e,"tags",r),Ir(e,"user",o),Ir(e,"contexts",i),Ir(e,"sdkProcessingMetadata",u),s&&(e.level=s),m&&(e.transactionName=m),y&&(e.span=y),a.length&&(e.breadcrumbs=[...e.breadcrumbs,...a]),l.length&&(e.fingerprint=[...e.fingerprint,...l]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),f.length&&(e.attachments=[...e.attachments,...f]),e.propagationContext={...e.propagationContext,...d}}function Ir(e,t,n){if(n&&Object.keys(n).length){e[t]={...e[t]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[t][r]=n[r])}}function v1(e,t){const{extra:n,tags:r,user:o,contexts:i,level:s,transactionName:u}=t,a=De(n);a&&Object.keys(a).length&&(e.extra={...a,...e.extra});const l=De(r);l&&Object.keys(l).length&&(e.tags={...l,...e.tags});const c=De(o);c&&Object.keys(c).length&&(e.user={...c,...e.user});const f=De(i);f&&Object.keys(f).length&&(e.contexts={...f,...e.contexts}),s&&(e.level=s),u&&e.type!=="transaction"&&(e.transaction=u)}function E1(e,t){const n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}function _1(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}function S1(e,t){e.contexts={trace:o1(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:d1(t),...e.sdkProcessingMetadata};const n=Qm(t),r=Mi(n).description;r&&!e.transaction&&e.type==="transaction"&&(e.transaction=r)}function w1(e,t){e.fingerprint=e.fingerprint?Wm(e.fingerprint):[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}function x1(e,t,n,r,o,i){const{normalizeDepth:s=3,normalizeMaxBreadth:u=1e3}=e,a={...t,event_id:t.event_id||n.event_id||Me(),timestamp:t.timestamp||go()},l=n.integrations||e.integrations.map(S=>S.name);T1(a,e),k1(a,l),o&&o.emit("applyFrameMetadata",t),t.type===void 0&&C1(a,e.stackParser);const c=P1(r,n.captureContext);n.mechanism&&io(a,n.mechanism);const f=o?o.getEventProcessors():[],d=XS().getScopeData();if(i){const S=i.getScopeData();Ta(d,S)}if(c){const S=c.getScopeData();Ta(d,S)}const m=[...n.attachments||[],...d.attachments];m.length&&(n.attachments=m),y1(a,d);const y=[...f,...d.eventProcessors];return xa(y,a,n).then(S=>(S&&I1(S),typeof s=="number"&&s>0?N1(S,s,u):S))}function T1(e,t){const{environment:n,release:r,dist:o,maxValueLength:i=250}=t;"environment"in e||(e.environment="environment"in t?n:Gl),e.release===void 0&&r!==void 0&&(e.release=r),e.dist===void 0&&o!==void 0&&(e.dist=o),e.message&&(e.message=Jn(e.message,i));const s=e.exception&&e.exception.values&&e.exception.values[0];s&&s.value&&(s.value=Jn(s.value,i));const u=e.request;u&&u.url&&(u.url=Jn(u.url,i))}const Yf=new WeakMap;function C1(e,t){const n=F._sentryDebugIds;if(!n)return;let r;const o=Yf.get(t);o?r=o:(r=new Map,Yf.set(t,r));const i=Object.entries(n).reduce((s,[u,a])=>{let l;const c=r.get(u);c?l=c:(l=t(u),r.set(u,l));for(let f=l.length-1;f>=0;f--){const d=l[f];if(d.filename){s[d.filename]=a;break}}return s},{});try{e.exception.values.forEach(s=>{s.stacktrace.frames.forEach(u=>{u.filename&&(u.debug_id=i[u.filename])})})}catch{}}function I1(e){const t={};try{e.exception.values.forEach(r=>{r.stacktrace.frames.forEach(o=>{o.debug_id&&(o.abs_path?t[o.abs_path]=o.debug_id:o.filename&&(t[o.filename]=o.debug_id),delete o.debug_id)})})}catch{}if(Object.keys(t).length===0)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.entries(t).forEach(([r,o])=>{n.push({type:"sourcemap",code_file:r,debug_id:o})})}function k1(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}function N1(e,t,n){if(!e)return null;const r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(o=>({...o,...o.data&&{data:yt(o.data,t,n)}}))},...e.user&&{user:yt(e.user,t,n)},...e.contexts&&{contexts:yt(e.contexts,t,n)},...e.extra&&{extra:yt(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=yt(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(o=>({...o,...o.data&&{data:yt(o.data,t,n)}}))),r}function P1(e,t){if(!t)return e;const n=e?e.clone():new xn;return n.update(t),n}function R1(e,t){return pt().captureException(e,void 0)}function Zm(e,t){return pt().captureEvent(e,t)}function Qf(e){const t=ye(),n=On(),r=pt(),{release:o,environment:i=Gl}=t&&t.getOptions()||{},{userAgent:s}=F.navigator||{},u=FS({release:o,environment:i,user:r.getUser()||n.getUser(),...s&&{userAgent:s},...e}),a=n.getSession();return a&&a.status==="ok"&&lr(a,{status:"exited"}),qm(),n.setSession(u),r.setSession(u),u}function qm(){const e=On(),t=pt(),n=t.getSession()||e.getSession();n&&HS(n),Jm(),e.setSession(),t.setSession()}function Jm(){const e=On(),t=pt(),n=ye(),r=t.getSession()||e.getSession();r&&n&&n.captureSession(r)}function Kf(e=!1){if(e){qm();return}Jm()}const O1="7";function L1(e){const t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function A1(e){return`${L1(e)}${e.projectId}/envelope/`}function D1(e,t){return X_({sentry_key:e.publicKey,sentry_version:O1,...t&&{sentry_client:`${t.name}/${t.version}`}})}function b1(e,t,n){return t||`${A1(e)}?${D1(e,n)}`}const Zf=[];function M1(e){const t={};return e.forEach(n=>{const{name:r}=n,o=t[r];o&&!o.isDefaultInstance&&n.isDefaultInstance||(t[r]=n)}),Object.values(t)}function F1(e){const t=e.defaultIntegrations||[],n=e.integrations;t.forEach(s=>{s.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...t,...n]:typeof n=="function"?r=Wm(n(t)):r=t;const o=M1(r),i=o.findIndex(s=>s.name==="Debug");if(i>-1){const[s]=o.splice(i,1);o.push(s)}return o}function H1(e,t){const n={};return t.forEach(r=>{r&&e0(e,r,n)}),n}function qf(e,t){for(const n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function e0(e,t,n){if(n[t.name]){U&&P.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,Zf.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),Zf.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(o,i)=>r(o,i,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),o=Object.assign((i,s)=>r(i,s,e),{id:t.name});e.addEventProcessor(o)}U&&P.log(`Integration installed: ${t.name}`)}const Jf="Not capturing exception because it's already been captured.";class B1{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=W_(t.dsn):U&&P.warn("No DSN provided, client will not send events."),this._dsn){const n=b1(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const o=Me();if(Ff(t))return U&&P.log(Jf),o;const i={event_id:o,...n};return this._process(this.eventFromException(t,i).then(s=>this._captureEvent(s,i,r))),i.event_id}captureMessage(t,n,r,o){const i={event_id:Me(),...r},s=Fl(t)?t:String(t),u=Hl(t)?this.eventFromMessage(s,n,i):this.eventFromException(t,i);return this._process(u.then(a=>this._captureEvent(a,i,o))),i.event_id}captureEvent(t,n,r){const o=Me();if(n&&n.originalException&&Ff(n.originalException))return U&&P.log(Jf),o;const i={event_id:o,...n},u=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,i,u||r)),i.event_id}captureSession(t){typeof t.release!="string"?U&&P.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),lr(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(o=>r&&o))):wn(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];e0(this,t,this._integrations),n||qf(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=g1(t,this._dsn,this._options._metadata,this._options.tunnel);for(const i of n.attachments||[])r=TS(r,kS(i));const o=this.sendEnvelope(r);o&&o.then(i=>this.emit("afterSendEvent",t,i),null)}sendSession(t){const n=m1(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(t,n,r){if(this._options.sendClientReports){const o=typeof r=="number"?r:1,i=`${t}:${n}`;U&&P.log(`Recording outcome: "${i}"${o>1?` (${o} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+o}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const o=r.indexOf(n);o>-1&&r.splice(o,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(o=>o(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(U&&P.error("Error while sending event:",n),n)):(U&&P.error("Transport disabled"),wn({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=H1(this,t),qf(this,t)}_updateSessionFromEvent(t,n){let r=!1,o=!1;const i=n.exception&&n.exception.values;if(i){o=!0;for(const a of i){const l=a.mechanism;if(l&&l.handled===!1){r=!0;break}}}const s=t.status==="ok";(s&&t.errors===0||s&&r)&&(lr(t,{...r&&{status:"crashed"},errors:t.errors||Number(o||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new Ge(n=>{let r=0;const o=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=o,t&&r>=t&&(clearInterval(i),n(!1)))},o)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,o=On()){const i=this.getOptions(),s=Object.keys(this._integrations);return!n.integrations&&s.length>0&&(n.integrations=s),this.emit("preprocessEvent",t,n),t.type||o.setLastEventId(t.event_id||n.event_id),x1(i,t,n,r,this,o).then(u=>{if(u===null)return u;const a={...o.getPropagationContext(),...r?r.getPropagationContext():void 0};if(!(u.contexts&&u.contexts.trace)&&a){const{traceId:c,spanId:f,parentSpanId:d,dsc:m}=a;u.contexts={trace:De({trace_id:c,span_id:f,parent_span_id:d}),...u.contexts};const y=m||Km(c,this);u.sdkProcessingMetadata={dynamicSamplingContext:y,...u.sdkProcessingMetadata}}return u})}_captureEvent(t,n={},r){return this._processEvent(t,n,r).then(o=>o.event_id,o=>{if(U){const i=o;i.logLevel==="log"?P.log(i.message):P.warn(i)}})}_processEvent(t,n,r){const o=this.getOptions(),{sampleRate:i}=o,s=n0(t),u=t0(t),a=t.type||"error",l=`before send for type \`${a}\``,c=typeof i>"u"?void 0:p1(i);if(u&&typeof c=="number"&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",t),bi(new lt(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));const f=a==="replay_event"?"replay":a,m=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,n,r,m).then(y=>{if(y===null)throw this.recordDroppedEvent("event_processor",f,t),new lt("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return y;const S=$1(this,o,y,n);return U1(S,l)}).then(y=>{if(y===null){if(this.recordDroppedEvent("before_send",f,t),s){const p=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",p)}throw new lt(`${l} returned \`null\`, will not send event.`,"log")}const v=r&&r.getSession();if(!s&&v&&this._updateSessionFromEvent(v,y),s){const h=y.sdkProcessingMetadata&&y.sdkProcessingMetadata.spanCountBeforeProcessing||0,p=y.spans?y.spans.length:0,g=h-p;g>0&&this.recordDroppedEvent("before_send","span",g)}const S=y.transaction_info;if(s&&S&&y.transaction!==t.transaction){const h="custom";y.transaction_info={...S,source:h}}return this.sendEvent(y,n),y}).then(null,y=>{throw y instanceof lt?y:(this.captureException(y,{data:{__sentry__:!0},originalException:y}),new lt(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${y}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[o,i]=n.split(":");return{reason:o,category:i,quantity:r}})}_flushOutcomes(){U&&P.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0){U&&P.log("No outcomes to send");return}if(!this._dsn){U&&P.log("No dsn provided, will not send outcomes");return}U&&P.log("Sending outcomes:",t);const n=RS(t,this._options.tunnel&&gs(this._dsn));this.sendEnvelope(n)}}function U1(e,t){const n=`${t} must return \`null\` or a valid event.`;if(ps(e))return e.then(r=>{if(!ar(r)&&r!==null)throw new lt(n);return r},r=>{throw new lt(`${t} rejected with ${r}`)});if(!ar(e)&&e!==null)throw new lt(n);return e}function $1(e,t,n,r){const{beforeSend:o,beforeSendTransaction:i,beforeSendSpan:s}=t;if(t0(n)&&o)return o(n,r);if(n0(n)){if(n.spans&&s){const u=[];for(const a of n.spans){const l=s(a);l?u.push(l):e.recordDroppedEvent("before_send","span")}n.spans=u}if(i){if(n.spans){const u=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:u}}return i(n,r)}}return n}function t0(e){return e.type===void 0}function n0(e){return e.type==="transaction"}function j1(e,t){t.debug===!0&&(U?P.enable():mo(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),pt().update(t.initialScope);const r=new e(t);return z1(r),r.init(),r}function z1(e){pt().setClient(e)}const G1=64;function r0(e,t,n=hS(e.bufferSize||G1)){let r={};const o=s=>n.drain(s);function i(s){const u=[];if(Uf(s,(f,d)=>{const m=$f(d);if(DS(r,m)){const y=ed(f,d);e.recordDroppedEvent("ratelimit_backoff",m,y)}else u.push(f)}),u.length===0)return wn({});const a=yo(s[0],u),l=f=>{Uf(a,(d,m)=>{const y=ed(d,m);e.recordDroppedEvent(f,$f(m),y)})},c=()=>t({body:CS(a)}).then(f=>(f.statusCode!==void 0&&(f.statusCode<200||f.statusCode>=300)&&U&&P.warn(`Sentry responded with status code ${f.statusCode} to sent event.`),r=bS(r,f),f),f=>{throw l("network_error"),f});return n.add(c).then(f=>f,f=>{if(f instanceof lt)return U&&P.error("Skipped sending event because buffer is full."),l("queue_overflow"),wn({});throw f})}return{send:i,flush:o}}function ed(e,t){if(!(t!=="event"&&t!=="transaction"))return Array.isArray(e)?e[1]:void 0}function V1(e,t,n=[t],r="npm"){const o=e._metadata||{};o.sdk||(o.sdk={name:`sentry.javascript.${t}`,packages:n.map(i=>({name:`${r}:@sentry/${i}`,version:pn})),version:pn}),e._metadata=o}const W1=100;function Tn(e,t){const n=ye(),r=On();if(!n)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:i=W1}=n.getOptions();if(i<=0)return;const u={timestamp:go(),...e},a=o?mo(()=>o(u,t)):u;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,t),r.addBreadcrumb(a,i))}let td;const X1="FunctionToString",nd=new WeakMap,Y1=()=>({name:X1,setupOnce(){td=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=Ul(this),n=nd.has(ye())&&t!==void 0?t:this;return td.apply(n,e)}}catch{}},setup(e){nd.set(e,!0)}}),Q1=Y1,K1=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"],Z1="InboundFilters",q1=(e={})=>({name:Z1,processEvent(t,n,r){const o=r.getOptions(),i=ew(e,o);return tw(t,i)?null:t}}),J1=q1;function ew(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:K1],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function tw(e,t){return t.ignoreInternal&&uw(e)?(U&&P.warn(`Event dropped due to being internal Sentry Error.
Event: ${Lt(e)}`),!0):nw(e,t.ignoreErrors)?(U&&P.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Lt(e)}`),!0):lw(e)?(U&&P.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${Lt(e)}`),!0):rw(e,t.ignoreTransactions)?(U&&P.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Lt(e)}`),!0):ow(e,t.denyUrls)?(U&&P.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Lt(e)}.
Url: ${Fi(e)}`),!0):iw(e,t.allowUrls)?!1:(U&&P.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Lt(e)}.
Url: ${Fi(e)}`),!0)}function nw(e,t){return e.type||!t||!t.length?!1:sw(e).some(n=>hs(n,t))}function rw(e,t){if(e.type!=="transaction"||!t||!t.length)return!1;const n=e.transaction;return n?hs(n,t):!1}function ow(e,t){if(!t||!t.length)return!1;const n=Fi(e);return n?hs(n,t):!1}function iw(e,t){if(!t||!t.length)return!0;const n=Fi(e);return n?hs(n,t):!0}function sw(e){const t=[];e.message&&t.push(e.message);let n;try{n=e.exception.values[e.exception.values.length-1]}catch{}return n&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`)),t}function uw(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function aw(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Fi(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?aw(t):null}catch{return U&&P.error(`Cannot extract url for event ${Lt(e)}`),null}}function lw(e){return e.type||!e.exception||!e.exception.values||e.exception.values.length===0?!1:!e.message&&!e.exception.values.some(t=>t.stacktrace||t.type&&t.type!=="Error"||t.value)}const cw="Dedupe",fw=()=>{let e;return{name:cw,processEvent(t){if(t.type)return t;try{if(pw(t,e))return U&&P.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}},dw=fw;function pw(e,t){return t?!!(hw(e,t)||mw(e,t)):!1}function hw(e,t){const n=e.message,r=t.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!i0(e,t)||!o0(e,t))}function mw(e,t){const n=rd(t),r=rd(e);return!(!n||!r||n.type!==r.type||n.value!==r.value||!i0(e,t)||!o0(e,t))}function o0(e,t){let n=Df(e),r=Df(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let o=0;o<r.length;o++){const i=r[o],s=n[o];if(i.filename!==s.filename||i.lineno!==s.lineno||i.colno!==s.colno||i.function!==s.function)return!1}return!0}function i0(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function rd(e){return e.exception&&e.exception.values&&e.exception.values[0]}const $=F;let Ca=0;function s0(){return Ca>0}function gw(){Ca++,setTimeout(()=>{Ca--})}function fr(e,t={},n){if(typeof e!="function")return e;try{const o=e.__sentry_wrapped__;if(o)return typeof o=="function"?o:e;if(Ul(e))return e}catch{return e}const r=function(){const o=Array.prototype.slice.call(arguments);try{const i=o.map(s=>fr(s,t));return e.apply(this,i)}catch(i){throw gw(),YS(s=>{s.addEventProcessor(u=>(t.mechanism&&(Ea(u,void 0),io(u,t.mechanism)),u.extra={...u.extra,arguments:o},u)),R1(i)}),i}};try{for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o])}catch{}Hm(r,e),Sn(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return e.name}})}catch{}return r}const vo=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Vl(e,t){const n=Wl(e,t),r={type:t&&t.name,value:Sw(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function yw(e,t,n,r){const o=ye(),i=o&&o.getOptions().normalizeDepth,s=Iw(t),u={__serialized__:Xm(t,i)};if(s)return{exception:{values:[Vl(e,s)]},extra:u};const a={exception:{values:[{type:ds(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:Tw(t,{isUnhandledRejection:r})}]},extra:u};if(n){const l=Wl(e,n);l.length&&(a.exception.values[0].stacktrace={frames:l})}return a}function ou(e,t){return{exception:{values:[Vl(e,t)]}}}function Wl(e,t){const n=t.stacktrace||t.stack||"",r=Ew(t),o=_w(t);try{return e(n,r,o)}catch{}return[]}const vw=/Minified React error #\d+;/i;function Ew(e){return e&&vw.test(e.message)?1:0}function _w(e){return typeof e.framesToPop=="number"?e.framesToPop:0}function Sw(e){const t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function ww(e,t,n,r){const o=n&&n.syntheticException||void 0,i=Xl(e,t,o,r);return io(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),wn(i)}function xw(e,t,n="info",r,o){const i=r&&r.syntheticException||void 0,s=Ia(e,t,i,o);return s.level=n,r&&r.event_id&&(s.event_id=r.event_id),wn(s)}function Xl(e,t,n,r,o){let i;if(Am(t)&&t.error)return ou(e,t.error);if(If(t)||P_(t)){const s=t;if("stack"in t)i=ou(e,t);else{const u=s.name||(If(s)?"DOMError":"DOMException"),a=s.message?`${u}: ${s.message}`:u;i=Ia(e,a,n,r),Ea(i,a)}return"code"in s&&(i.tags={...i.tags,"DOMException.code":`${s.code}`}),i}return Ml(t)?ou(e,t):ar(t)||ds(t)?(i=yw(e,t,n,o),io(i,{synthetic:!0}),i):(i=Ia(e,t,n,r),Ea(i,`${t}`),io(i,{synthetic:!0}),i)}function Ia(e,t,n,r){const o={};if(r&&n){const i=Wl(e,n);i.length&&(o.exception={values:[{value:t,stacktrace:{frames:i}}]})}if(Fl(t)){const{__sentry_template_string__:i,__sentry_template_values__:s}=t;return o.logentry={message:i,params:s},o}return o.message=t,o}function Tw(e,{isUnhandledRejection:t}){const n=Y_(e),r=t?"promise rejection":"exception";return Am(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:ds(e)?`Event \`${Cw(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function Cw(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch{}}function Iw(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const n=e[t];if(n instanceof Error)return n}}function kw(e,{metadata:t,tunnel:n,dsn:r}){const o={event_id:e.event_id,sent_at:new Date().toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!r&&{dsn:gs(r)}},i=Nw(e);return yo(o,[i])}function Nw(e){return[{type:"user_report"},e]}class Pw extends B1{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t},r=$.SENTRY_SDK_SOURCE||aS();V1(n,"browser",["browser"],r),super(n),n.sendClientReports&&$.document&&$.document.addEventListener("visibilitychange",()=>{$.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,n){return ww(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return xw(this._options.stackParser,t,n,r,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled()){vo&&P.warn("SDK not enabled, will not capture user feedback.");return}const n=kw(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}_prepareEvent(t,n,r){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r)}}const Rw=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,he=F,Ow=1e3;let od,ka,Na;function Lw(e){const t="dom";Pn(t,e),Rn(t,Aw)}function Aw(){if(!he.document)return;const e=ot.bind(null,"dom"),t=id(e,!0);he.document.addEventListener("click",t,!1),he.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{const r=he[n]&&he[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(Ae(r,"addEventListener",function(o){return function(i,s,u){if(i==="click"||i=="keypress")try{const a=this,l=a.__sentry_instrumentation_handlers__=a.__sentry_instrumentation_handlers__||{},c=l[i]=l[i]||{refCount:0};if(!c.handler){const f=id(e);c.handler=f,o.call(this,i,f,u)}c.refCount++}catch{}return o.call(this,i,s,u)}}),Ae(r,"removeEventListener",function(o){return function(i,s,u){if(i==="click"||i=="keypress")try{const a=this,l=a.__sentry_instrumentation_handlers__||{},c=l[i];c&&(c.refCount--,c.refCount<=0&&(o.call(this,i,c.handler,u),c.handler=void 0,delete l[i]),Object.keys(l).length===0&&delete a.__sentry_instrumentation_handlers__)}catch{}return o.call(this,i,s,u)}}))})}function Dw(e){if(e.type!==ka)return!1;try{if(!e.target||e.target._sentryId!==Na)return!1}catch{}return!0}function bw(e,t){return e!=="keypress"?!1:!t||!t.tagName?!0:!(t.tagName==="INPUT"||t.tagName==="TEXTAREA"||t.isContentEditable)}function id(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=Mw(n);if(bw(n.type,r))return;Sn(n,"_sentryCaptured",!0),r&&!r._sentryId&&Sn(r,"_sentryId",Me());const o=n.type==="keypress"?"input":n.type;Dw(n)||(e({event:n,name:o,global:t}),ka=n.type,Na=r?r._sentryId:void 0),clearTimeout(od),od=he.setTimeout(()=>{Na=void 0,ka=void 0},Ow)}}function Mw(e){try{return e.target}catch{return null}}let Vo;function u0(e){const t="history";Pn(t,e),Rn(t,Fw)}function Fw(){if(!MS())return;const e=he.onpopstate;he.onpopstate=function(...n){const r=he.location.href,o=Vo;if(Vo=r,ot("history",{from:o,to:r}),e)try{return e.apply(this,n)}catch{}};function t(n){return function(...r){const o=r.length>2?r[2]:void 0;if(o){const i=Vo,s=String(o);Vo=s,ot("history",{from:i,to:s})}return n.apply(this,r)}}Ae(he.history,"pushState",t),Ae(he.history,"replaceState",t)}const ai={};function Hw(e){const t=ai[e];if(t)return t;let n=he[e];if(ya(n))return ai[e]=n.bind(he);const r=he.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o);const i=o.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(o)}catch(o){Rw&&P.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,o)}return n&&(ai[e]=n.bind(he))}function sd(e){ai[e]=void 0}const Or="__sentry_xhr_v3__";function Bw(e){const t="xhr";Pn(t,e),Rn(t,Uw)}function Uw(){if(!he.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const o=wt()*1e3,i=St(r[0])?r[0].toUpperCase():void 0,s=$w(r[1]);if(!i||!s)return t.apply(n,r);n[Or]={method:i,url:s,request_headers:{}},i==="POST"&&s.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const u=()=>{const a=n[Or];if(a&&n.readyState===4){try{a.status_code=n.status}catch{}const l={endTimestamp:wt()*1e3,startTimestamp:o,xhr:n};ot("xhr",l)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(a,l,c){return u(),a.apply(l,c)}}):n.addEventListener("readystatechange",u),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(a,l,c){const[f,d]=c,m=l[Or];return m&&St(f)&&St(d)&&(m.request_headers[f.toLowerCase()]=d),a.apply(l,c)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const o=n[Or];if(!o)return t.apply(n,r);r[0]!==void 0&&(o.body=r[0]);const i={startTimestamp:wt()*1e3,xhr:n};return ot("xhr",i),t.apply(n,r)}})}function $w(e){if(St(e))return e;try{return e.toString()}catch{}}function jw(e,t=Hw("fetch")){let n=0,r=0;function o(i){const s=i.body.length;n+=s,r++;const u={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return sd("fetch"),bi("No fetch implementation available");try{return t(e.url,u).then(a=>(n-=s,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return sd("fetch"),n-=s,r--,bi(a)}}return r0(e,o)}const zw=30,Gw=50;function Pa(e,t,n,r){const o={filename:e,function:t==="<anonymous>"?Qt:t,in_app:!0};return n!==void 0&&(o.lineno=n),r!==void 0&&(o.colno=r),o}const Vw=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Ww=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Xw=/\((\S*)(?::(\d+))(?::(\d+))\)/,Yw=e=>{const t=Vw.exec(e);if(t){const[,r,o,i]=t;return Pa(r,Qt,+o,+i)}const n=Ww.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const s=Xw.exec(n[2]);s&&(n[2]=s[1],n[3]=s[2],n[4]=s[3])}const[o,i]=l0(n[1]||Qt,n[2]);return Pa(i,o,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},a0=[zw,Yw],Qw=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Kw=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Zw=e=>{const t=Qw.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const i=Kw.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}let r=t[3],o=t[1]||Qt;return[o,r]=l0(o,r),Pa(r,o,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},qw=[Gw,Zw],Jw=[a0,qw],ex=$m(...Jw),l0=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:Qt,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},Wo=1024,tx="Breadcrumbs",nx=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:tx,setup(n){t.console&&Z_(sx(n)),t.dom&&Lw(ix(n,t.dom)),t.xhr&&Bw(ux(n)),t.fetch&&tS(ax(n)),t.history&&u0(lx(n)),t.sentry&&n.on("beforeSendEvent",ox(n))}}},rx=nx;function ox(e){return function(n){ye()===e&&Tn({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:Lt(n)},{event:n})}}function ix(e,t){return function(r){if(ye()!==e)return;let o,i,s=typeof t=="object"?t.serializeAttribute:void 0,u=typeof t=="object"&&typeof t.maxStringLength=="number"?t.maxStringLength:void 0;u&&u>Wo&&(vo&&P.warn(`\`dom.maxStringLength\` cannot exceed ${Wo}, but a value of ${u} was configured. Sentry will use ${Wo} instead.`),u=Wo),typeof s=="string"&&(s=[s]);try{const l=r.event,c=cx(l)?l.target:l;o=Mm(c,{keyAttrs:s,maxStringLength:u}),i=B_(c)}catch{o="<unknown>"}if(o.length===0)return;const a={category:`ui.${r.name}`,message:o};i&&(a.data={"ui.component_name":i}),Tn(a,{event:r.event,name:r.name,global:r.global})}}function sx(e){return function(n){if(ye()!==e)return;const r={category:"console",data:{arguments:n.args,logger:"console"},level:gS(n.level),message:kf(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)r.message=`Assertion failed: ${kf(n.args.slice(1)," ")||"console.assert"}`,r.data.arguments=n.args.slice(1);else return;Tn(r,{input:n.args,level:n.level})}}function ux(e){return function(n){if(ye()!==e)return;const{startTimestamp:r,endTimestamp:o}=n,i=n.xhr[Or];if(!r||!o||!i)return;const{method:s,url:u,status_code:a,body:l}=i,c={method:s,url:u,status_code:a},f={xhr:n.xhr,input:l,startTimestamp:r,endTimestamp:o},d=bm(a);Tn({category:"xhr",data:c,type:"http",level:d},f)}}function ax(e){return function(n){if(ye()!==e)return;const{startTimestamp:r,endTimestamp:o}=n;if(o&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.error){const i=n.fetchData,s={data:n.error,input:n.args,startTimestamp:r,endTimestamp:o};Tn({category:"fetch",data:i,level:"error",type:"http"},s)}else{const i=n.response,s={...n.fetchData,status_code:i&&i.status},u={input:n.args,response:i,startTimestamp:r,endTimestamp:o},a=bm(s.status_code);Tn({category:"fetch",data:s,type:"http",level:a},u)}}}function lx(e){return function(n){if(ye()!==e)return;let r=n.from,o=n.to;const i=ru($.location.href);let s=r?ru(r):void 0;const u=ru(o);(!s||!s.path)&&(s=i),i.protocol===u.protocol&&i.host===u.host&&(o=u.relative),i.protocol===s.protocol&&i.host===s.host&&(r=s.relative),Tn({category:"navigation",data:{from:r,to:o}})}}function cx(e){return!!e&&!!e.target}const fx=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],dx="BrowserApiErrors",px=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:dx,setupOnce(){t.setTimeout&&Ae($,"setTimeout",ud),t.setInterval&&Ae($,"setInterval",ud),t.requestAnimationFrame&&Ae($,"requestAnimationFrame",mx),t.XMLHttpRequest&&"XMLHttpRequest"in $&&Ae(XMLHttpRequest.prototype,"send",gx);const n=t.eventTarget;n&&(Array.isArray(n)?n:fx).forEach(yx)}}},hx=px;function ud(e){return function(...t){const n=t[0];return t[0]=fr(n,{mechanism:{data:{function:Kt(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function mx(e){return function(t){return e.apply(this,[fr(t,{mechanism:{data:{function:"requestAnimationFrame",handler:Kt(e)},handled:!1,type:"instrument"}})])}}function gx(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(o=>{o in n&&typeof n[o]=="function"&&Ae(n,o,function(i){const s={mechanism:{data:{function:o,handler:Kt(i)},handled:!1,type:"instrument"}},u=Ul(i);return u&&(s.mechanism.data.handler=Kt(u)),fr(i,s)})}),e.apply(this,t)}}function yx(e){const t=$,n=t[e]&&t[e].prototype;!n||!n.hasOwnProperty||!n.hasOwnProperty("addEventListener")||(Ae(n,"addEventListener",function(r){return function(o,i,s){try{typeof i.handleEvent=="function"&&(i.handleEvent=fr(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Kt(i),target:e},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[o,fr(i,{mechanism:{data:{function:"addEventListener",handler:Kt(i),target:e},handled:!1,type:"instrument"}}),s])}}),Ae(n,"removeEventListener",function(r){return function(o,i,s){const u=i;try{const a=u&&u.__sentry_wrapped__;a&&r.call(this,o,a,s)}catch{}return r.call(this,o,u,s)}}))}const vx="GlobalHandlers",Ex=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:vx,setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(Sx(n),ad("onerror")),t.onunhandledrejection&&(wx(n),ad("onunhandledrejection"))}}},_x=Ex;function Sx(e){oS(t=>{const{stackParser:n,attachStacktrace:r}=c0();if(ye()!==e||s0())return;const{msg:o,url:i,line:s,column:u,error:a}=t,l=Cx(Xl(n,a||o,void 0,r,!1),i,s,u);l.level="error",Zm(l,{originalException:a,mechanism:{handled:!1,type:"onerror"}})})}function wx(e){sS(t=>{const{stackParser:n,attachStacktrace:r}=c0();if(ye()!==e||s0())return;const o=xx(t),i=Hl(o)?Tx(o):Xl(n,o,void 0,r,!0);i.level="error",Zm(i,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function xx(e){if(Hl(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}function Tx(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function Cx(e,t,n,r){const o=e.exception=e.exception||{},i=o.values=o.values||[],s=i[0]=i[0]||{},u=s.stacktrace=s.stacktrace||{},a=u.frames=u.frames||[],l=isNaN(parseInt(r,10))?void 0:r,c=isNaN(parseInt(n,10))?void 0:n,f=St(t)&&t.length>0?t:H_();return a.length===0&&a.push({colno:l,filename:f,function:Qt,in_app:!0,lineno:c}),e}function ad(e){vo&&P.log(`Global Handler attached: ${e}`)}function c0(){const e=ye();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const Ix=()=>({name:"HttpContext",preprocessEvent(e){if(!$.navigator&&!$.location&&!$.document)return;const t=e.request&&e.request.url||$.location&&$.location.href,{referrer:n}=$.document||{},{userAgent:r}=$.navigator||{},o={...e.request&&e.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},i={...e.request,...t&&{url:t},headers:o};e.request=i}}),kx="cause",Nx=5,Px="LinkedErrors",Rx=(e={})=>{const t=e.limit||Nx,n=e.key||kx;return{name:Px,preprocessEvent(r,o,i){const s=i.getOptions();D_(Vl,s.stackParser,s.maxValueLength,n,t,r,o)}}},Ox=Rx;function f0(e){return[J1(),Q1(),hx(),rx(),_x(),Ox(),dw(),Ix()]}function Lx(e={}){const t={defaultIntegrations:f0(),release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:$.SENTRY_RELEASE&&$.SENTRY_RELEASE.id?$.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return e.defaultIntegrations==null&&delete e.defaultIntegrations,{...t,...e}}function Ax(){const e=typeof $.window<"u"&&$;if(!e)return!1;const t=e.chrome?"chrome":"browser",n=e[t],r=n&&n.runtime&&n.runtime.id,o=$.location&&$.location.href||"",i=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],s=!!r&&$===$.top&&i.some(a=>o.startsWith(`${a}//`)),u=typeof e.nw<"u";return!!r&&!s&&!u}function Dx(e={}){const t=Lx(e);if(Ax()){mo(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});return}vo&&(zm()||P.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...t,stackParser:K_(t.stackParser||ex),integrations:F1(t),transport:t.transport||jw},r=j1(Pw,n);return t.autoSessionTracking&&bx(),r}function bx(){if(typeof $.document>"u"){vo&&P.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}Qf({ignoreDuration:!0}),Kf(),u0(({from:e,to:t})=>{e!==void 0&&e!==t&&(Qf({ignoreDuration:!0}),Kf())})}function ld(){const e=On().getScopeData();return Ta(e,pt().getScopeData()),e.eventProcessors=[],e}function Mx(e){On().addScopeListener(t=>{const n=ld();e(n,t)}),pt().addScopeListener(t=>{const n=ld();e(n,t)})}var cd;(function(e){e[e.Classic=1]="Classic",e[e.Protocol=2]="Protocol",e[e.Both=3]="Both"})(cd||(cd={}));const Fx="sentry-ipc";var At;(function(e){e.RENDERER_START="sentry-electron.renderer-start",e.EVENT="sentry-electron.event",e.SCOPE="sentry-electron.scope",e.ENVELOPE="sentry-electron.envelope",e.STATUS="sentry-electron.status",e.ADD_METRIC="sentry-electron.add-metric"})(At||(At={}));const Hx="sentry-electron-renderer-id";function An(e){return`${Fx}://${e}/sentry_key`}function Bx(){if(window.__SENTRY_IPC__)return window.__SENTRY_IPC__;{P.log("IPC was not configured in preload script, falling back to custom protocol and fetch");const e=window.__SENTRY_RENDERER_ID__=Me(),t={[Hx]:e};return{sendRendererStart:()=>{fetch(An(At.RENDERER_START),{method:"POST",body:"",headers:t}).catch(()=>{console.error(`Sentry SDK failed to establish connection with the Electron main process.
  - Ensure you have initialized the SDK in the main process
  - If your renderers use custom sessions, be sure to set 'getSessions' in the main process options
  - If you are bundling your main process code and using Electron < v5, you'll need to manually configure a preload script`)})},sendScope:n=>{fetch(An(At.SCOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEvent:n=>{fetch(An(At.EVENT),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEnvelope:n=>{fetch(An(At.ENVELOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendStatus:n=>{fetch(An(At.STATUS),{method:"POST",body:JSON.stringify({status:n}),headers:t}).catch(()=>{})},sendAddMetric:n=>{fetch(An(At.ADD_METRIC),{method:"POST",body:JSON.stringify(n),headers:t}).catch(()=>{})}}}}let Xo;function Yl(){return Xo||(Xo=Bx(),Xo.sendRendererStart()),Xo}const Ux=()=>({name:"ScopeToMain",setup(){const e=Yl();Mx((t,n)=>{e.sendScope(JSON.stringify(yt(t,20,2e3))),n.clearBreadcrumbs(),n.clearAttachments()})}});function $x(e){const t=Yl();return r0(e,async n=>(t.sendEnvelope(n.body),{statusCode:200}))}function jx(e){const t={pollInterval:1e3,anrThreshold:5e3,captureStackTrace:!1,...e},n=Yl();document.addEventListener("visibilitychange",()=>{n.sendStatus({status:document.visibilityState,config:t})}),n.sendStatus({status:document.visibilityState,config:t}),setInterval(()=>{n.sendStatus({status:"alive",config:t})},t.pollInterval)}const zx=50,[,Gx]=a0,[,Vx]=ES(),Wx=(e,t=0)=>{const n=[];for(const r of e.split(`
`).slice(t)){const o=Gx(r),i=Vx(r);if(o&&(i==null?void 0:i.in_app)!==!1?n.push(o):i&&n.push(De(i)),n.length>=zx)break}return jm(n)};function Xx(e){return[...f0(),Ux()]}function Yx(e={},t=Dx){if(window!=null&&window.__SENTRY__RENDERER_INIT__){P.warn(`The browser SDK has already been initialized.
If init has been called in the preload and contextIsolation is disabled, is not required to call init in the renderer`);return}window.__SENTRY__RENDERER_INIT__=!0,e.autoSessionTracking===void 0&&(e.autoSessionTracking=!1),e.sendClientReports=!1,e.defaultIntegrations===void 0&&(e.defaultIntegrations=Xx()),e.stackParser===void 0&&(e.stackParser=Wx),e.dsn===void 0&&(e.dsn="https://<EMAIL>/12345"),e.transport===void 0&&(e.transport=$x),e.anrDetection&&jx(e.anrDetection===!0?{}:e.anrDetection),delete e.initialScope,t(e)}Yx();window.mainProcess.titleBarApi.onUpdateDarkMode(e=>{document.body.className=e=="dark"?"darkTheme":""});const Qx="modulepreload",Kx=function(e,t){return new URL(e,t).href},fd={},Zx=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const s=document.getElementsByTagName("link"),u=document.querySelector("meta[property=csp-nonce]"),a=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));o=Promise.allSettled(n.map(l=>{if(l=Kx(l,r),l in fd)return;fd[l]=!0;const c=l.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(!!r)for(let y=s.length-1;y>=0;y--){const v=s[y];if(v.href===l&&(!c||v.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${f}`))return;const m=document.createElement("link");if(m.rel=c?"stylesheet":Qx,c||(m.as="script"),m.crossOrigin="",m.href=l,a&&m.setAttribute("nonce",a),document.head.appendChild(m),c)return new Promise((y,v)=>{m.addEventListener("load",y),m.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${l}`)))})}))}function i(s){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=s,window.dispatchEvent(u),!u.defaultPrevented)throw s}return o.then(s=>{for(const u of s||[])u.status==="rejected"&&i(u.reason);return t().catch(i)})};attachReactToElement(document.querySelector("body"),Zx(()=>import("./MainWindowPage-C2Qk6EMl.js"),[],import.meta.url));export{qx as R,es as _,tT as a,Jx as i,iu as j,eT as m,me as r,gE as s,om as u};
