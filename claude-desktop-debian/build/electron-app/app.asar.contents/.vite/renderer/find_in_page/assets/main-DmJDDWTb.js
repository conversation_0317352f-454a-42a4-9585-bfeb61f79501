var pc=e=>{throw TypeError(e)};var Fs=(e,t,n)=>t.has(e)||pc("Cannot "+n);var je=(e,t,n)=>(Fs(e,t,"read from private field"),n?n.call(e):t.get(e)),un=(e,t,n)=>t.has(e)?pc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Ir=(e,t,n,r)=>(Fs(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Hs=(e,t,n)=>(Fs(e,t,"access private method"),n);(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="949881c4-5aec-4d60-aa64-9645e78ea4f6",e._sentryDebugIdIdentifier="sentry-dbid-949881c4-5aec-4d60-aa64-9645e78ea4f6")}catch{}})();var bg=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};bg.SENTRY_RELEASE={id:"27cc6f763724a1af75b35c386a6b8d014eedc334"};(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();const Dg=""+new URL("Copernicus-Book-Dqxs7atU.otf",import.meta.url).href,Mg=""+new URL("Copernicus-BookItalic-DE-FEDdC.otf",import.meta.url).href,Fg=""+new URL("Copernicus-Medium-BDFrxFZK.otf",import.meta.url).href,Hg=""+new URL("Copernicus-MediumItalic-DwCSBWW0.otf",import.meta.url).href,Bg=""+new URL("Copernicus-Semibold-CN_XmW6o.otf",import.meta.url).href,Ug=""+new URL("StyreneBLC-Medium-Cw-IvyMy.otf",import.meta.url).href,$g=""+new URL("StyreneBLC-MediumItalic-CKHvGCIz.otf",import.meta.url).href,jg=""+new URL("StyreneBLC-Regular-DLVQLT8g.otf",import.meta.url).href,zg=""+new URL("StyreneBLC-RegularItalic-hJCoPVD5.otf",import.meta.url).href,Gg=""+new URL("TiemposText-Medium-vqMEr0TH.otf",import.meta.url).href,Vg=""+new URL("TiemposText-MediumItalic-CIeY-CUo.otf",import.meta.url).href,Wg=""+new URL("TiemposText-Regular-CoJqehkj.otf",import.meta.url).href,Xg=""+new URL("TiemposText-RegularItalic-C4EVGPqi.otf",import.meta.url).href,Yg=`
@font-face {
    font-family: 'Copernicus Book';
    src: url('${Dg}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus Book';
    src: url('${Mg}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${Fg}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${Hg}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Copernicus';
    src: url('${Bg}') format('opentype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${Ug}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${$g}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${jg}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Styrene B LC';
    src: url('${zg}') format('opentype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Gg}') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Vg}') format('opentype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Wg}') format('opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Tiempos Text';
    src: url('${Xg}') format('opentype');
    font-weight: normal;
    font-style: italic;
}
`,Rd=document.createElement("style");Rd.textContent=Yg;document.head.appendChild(Rd);function Od(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ld={exports:{}},Ji={},Ad={exports:{}},L={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yo=Symbol.for("react.element"),Qg=Symbol.for("react.portal"),Kg=Symbol.for("react.fragment"),Zg=Symbol.for("react.strict_mode"),qg=Symbol.for("react.profiler"),Jg=Symbol.for("react.provider"),e0=Symbol.for("react.context"),t0=Symbol.for("react.forward_ref"),n0=Symbol.for("react.suspense"),r0=Symbol.for("react.memo"),o0=Symbol.for("react.lazy"),hc=Symbol.iterator;function i0(e){return e===null||typeof e!="object"?null:(e=hc&&e[hc]||e["@@iterator"],typeof e=="function"?e:null)}var bd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Dd=Object.assign,Md={};function Er(e,t,n){this.props=e,this.context=t,this.refs=Md,this.updater=n||bd}Er.prototype.isReactComponent={};Er.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Er.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Fd(){}Fd.prototype=Er.prototype;function Xa(e,t,n){this.props=e,this.context=t,this.refs=Md,this.updater=n||bd}var Ya=Xa.prototype=new Fd;Ya.constructor=Xa;Dd(Ya,Er.prototype);Ya.isPureReactComponent=!0;var mc=Array.isArray,Hd=Object.prototype.hasOwnProperty,Qa={current:null},Bd={key:!0,ref:!0,__self:!0,__source:!0};function Ud(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Hd.call(t,r)&&!Bd.hasOwnProperty(r)&&(o[r]=t[r]);var u=arguments.length-2;if(u===1)o.children=n;else if(1<u){for(var a=Array(u),l=0;l<u;l++)a[l]=arguments[l+2];o.children=a}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)o[r]===void 0&&(o[r]=u[r]);return{$$typeof:yo,type:e,key:i,ref:s,props:o,_owner:Qa.current}}function s0(e,t){return{$$typeof:yo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ka(e){return typeof e=="object"&&e!==null&&e.$$typeof===yo}function u0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var gc=/\/+/g;function Bs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?u0(""+e.key):t.toString(36)}function oi(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case yo:case Qg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Bs(s,0):r,mc(o)?(n="",e!=null&&(n=e.replace(gc,"$&/")+"/"),oi(o,t,n,"",function(l){return l})):o!=null&&(Ka(o)&&(o=s0(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(gc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",mc(e))for(var u=0;u<e.length;u++){i=e[u];var a=r+Bs(i,u);s+=oi(i,t,n,a,o)}else if(a=i0(e),typeof a=="function")for(e=a.call(e),u=0;!(i=e.next()).done;)i=i.value,a=r+Bs(i,u++),s+=oi(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Oo(e,t,n){if(e==null)return e;var r=[],o=0;return oi(e,r,"","",function(i){return t.call(n,i,o++)}),r}function a0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},ii={transition:null},l0={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:ii,ReactCurrentOwner:Qa};function $d(){throw Error("act(...) is not supported in production builds of React.")}L.Children={map:Oo,forEach:function(e,t,n){Oo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Oo(e,function(){t++}),t},toArray:function(e){return Oo(e,function(t){return t})||[]},only:function(e){if(!Ka(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};L.Component=Er;L.Fragment=Kg;L.Profiler=qg;L.PureComponent=Xa;L.StrictMode=Zg;L.Suspense=n0;L.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=l0;L.act=$d;L.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Dd({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Qa.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in t)Hd.call(t,a)&&!Bd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&u!==void 0?u[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){u=Array(a);for(var l=0;l<a;l++)u[l]=arguments[l+2];r.children=u}return{$$typeof:yo,type:e.type,key:o,ref:i,props:r,_owner:s}};L.createContext=function(e){return e={$$typeof:e0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Jg,_context:e},e.Consumer=e};L.createElement=Ud;L.createFactory=function(e){var t=Ud.bind(null,e);return t.type=e,t};L.createRef=function(){return{current:null}};L.forwardRef=function(e){return{$$typeof:t0,render:e}};L.isValidElement=Ka;L.lazy=function(e){return{$$typeof:o0,_payload:{_status:-1,_result:e},_init:a0}};L.memo=function(e,t){return{$$typeof:r0,type:e,compare:t===void 0?null:t}};L.startTransition=function(e){var t=ii.transition;ii.transition={};try{e()}finally{ii.transition=t}};L.unstable_act=$d;L.useCallback=function(e,t){return Te.current.useCallback(e,t)};L.useContext=function(e){return Te.current.useContext(e)};L.useDebugValue=function(){};L.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};L.useEffect=function(e,t){return Te.current.useEffect(e,t)};L.useId=function(){return Te.current.useId()};L.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};L.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};L.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};L.useMemo=function(e,t){return Te.current.useMemo(e,t)};L.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};L.useRef=function(e){return Te.current.useRef(e)};L.useState=function(e){return Te.current.useState(e)};L.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};L.useTransition=function(){return Te.current.useTransition()};L.version="18.3.1";Ad.exports=L;var me=Ad.exports;const bT=Od(me);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var c0=me,f0=Symbol.for("react.element"),d0=Symbol.for("react.fragment"),p0=Object.prototype.hasOwnProperty,h0=c0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,m0={key:!0,ref:!0,__self:!0,__source:!0};function jd(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)p0.call(t,r)&&!m0.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:f0,type:e,key:i,ref:s,props:o,_owner:h0.current}}Ji.Fragment=d0;Ji.jsx=jd;Ji.jsxs=jd;Ld.exports=Ji;var wu=Ld.exports,zd={exports:{}},Be={},Gd={exports:{}},Vd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,R){var O=C.length;C.push(R);e:for(;0<O;){var q=O-1>>>1,oe=C[q];if(0<o(oe,R))C[q]=R,C[O]=oe,O=q;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var R=C[0],O=C.pop();if(O!==R){C[0]=O;e:for(var q=0,oe=C.length,Po=oe>>>1;q<Po;){var on=2*(q+1)-1,Ms=C[on],sn=on+1,Ro=C[sn];if(0>o(Ms,O))sn<oe&&0>o(Ro,Ms)?(C[q]=Ro,C[sn]=O,q=sn):(C[q]=Ms,C[on]=O,q=on);else if(sn<oe&&0>o(Ro,O))C[q]=Ro,C[sn]=O,q=sn;else break e}}return R}function o(C,R){var O=C.sortIndex-R.sortIndex;return O!==0?O:C.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,u=s.now();e.unstable_now=function(){return s.now()-u}}var a=[],l=[],c=1,f=null,d=3,m=!1,y=!1,v=!1,S=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(C){for(var R=n(l);R!==null;){if(R.callback===null)r(l);else if(R.startTime<=C)r(l),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(l)}}function E(C){if(v=!1,g(C),!y)if(n(a)!==null)y=!0,bs(w);else{var R=n(l);R!==null&&Ds(E,R.startTime-C)}}function w(C,R){y=!1,v&&(v=!1,h(N),N=-1),m=!0;var O=d;try{for(g(R),f=n(a);f!==null&&(!(f.expirationTime>R)||C&&!qe());){var q=f.callback;if(typeof q=="function"){f.callback=null,d=f.priorityLevel;var oe=q(f.expirationTime<=R);R=e.unstable_now(),typeof oe=="function"?f.callback=oe:f===n(a)&&r(a),g(R)}else r(a);f=n(a)}if(f!==null)var Po=!0;else{var on=n(l);on!==null&&Ds(E,on.startTime-R),Po=!1}return Po}finally{f=null,d=O,m=!1}}var x=!1,I=null,N=-1,X=5,A=-1;function qe(){return!(e.unstable_now()-A<X)}function Tr(){if(I!==null){var C=e.unstable_now();A=C;var R=!0;try{R=I(!0,C)}finally{R?Cr():(x=!1,I=null)}}else x=!1}var Cr;if(typeof p=="function")Cr=function(){p(Tr)};else if(typeof MessageChannel<"u"){var dc=new MessageChannel,Ag=dc.port2;dc.port1.onmessage=Tr,Cr=function(){Ag.postMessage(null)}}else Cr=function(){S(Tr,0)};function bs(C){I=C,x||(x=!0,Cr())}function Ds(C,R){N=S(function(){C(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,bs(w))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):X=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(C){switch(d){case 1:case 2:case 3:var R=3;break;default:R=d}var O=d;d=R;try{return C()}finally{d=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,R){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var O=d;d=C;try{return R()}finally{d=O}},e.unstable_scheduleCallback=function(C,R,O){var q=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?q+O:q):O=q,C){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=**********;break;case 4:oe=1e4;break;default:oe=5e3}return oe=O+oe,C={id:c++,callback:R,priorityLevel:C,startTime:O,expirationTime:oe,sortIndex:-1},O>q?(C.sortIndex=O,t(l,C),n(a)===null&&C===n(l)&&(v?(h(N),N=-1):v=!0,Ds(E,O-q))):(C.sortIndex=oe,t(a,C),y||m||(y=!0,bs(w))),C},e.unstable_shouldYield=qe,e.unstable_wrapCallback=function(C){var R=d;return function(){var O=d;d=R;try{return C.apply(this,arguments)}finally{d=O}}}})(Vd);Gd.exports=Vd;var g0=Gd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var y0=me,He=g0;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Wd=new Set,Kr={};function Pn(e,t){sr(e,t),sr(e+"Capture",t)}function sr(e,t){for(Kr[e]=t,e=0;e<t.length;e++)Wd.add(t[e])}var Ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xu=Object.prototype.hasOwnProperty,v0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,yc={},vc={};function E0(e){return xu.call(vc,e)?!0:xu.call(yc,e)?!1:v0.test(e)?vc[e]=!0:(yc[e]=!0,!1)}function _0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function S0(e,t,n,r){if(t===null||typeof t>"u"||_0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ce(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new Ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new Ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new Ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new Ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new Ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new Ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new Ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new Ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new Ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var Za=/[\-:]([a-z])/g;function qa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Za,qa);ce[t]=new Ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Za,qa);ce[t]=new Ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Za,qa);ce[t]=new Ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new Ce(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new Ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new Ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ja(e,t,n,r){var o=ce.hasOwnProperty(t)?ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(S0(t,n,o,r)&&(n=null),r||o===null?E0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Rt=y0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Lo=Symbol.for("react.element"),Fn=Symbol.for("react.portal"),Hn=Symbol.for("react.fragment"),el=Symbol.for("react.strict_mode"),Tu=Symbol.for("react.profiler"),Xd=Symbol.for("react.provider"),Yd=Symbol.for("react.context"),tl=Symbol.for("react.forward_ref"),Cu=Symbol.for("react.suspense"),Iu=Symbol.for("react.suspense_list"),nl=Symbol.for("react.memo"),Lt=Symbol.for("react.lazy"),Qd=Symbol.for("react.offscreen"),Ec=Symbol.iterator;function kr(e){return e===null||typeof e!="object"?null:(e=Ec&&e[Ec]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Us;function Mr(e){if(Us===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Us=t&&t[1]||""}return`
`+Us+e}var $s=!1;function js(e,t){if(!e||$s)return"";$s=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(l){var r=l}Reflect.construct(e,[],t)}else{try{t.call()}catch(l){r=l}e.call(t.prototype)}else{try{throw Error()}catch(l){r=l}e()}}catch(l){if(l&&r&&typeof l.stack=="string"){for(var o=l.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,u=i.length-1;1<=s&&0<=u&&o[s]!==i[u];)u--;for(;1<=s&&0<=u;s--,u--)if(o[s]!==i[u]){if(s!==1||u!==1)do if(s--,u--,0>u||o[s]!==i[u]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=u);break}}}finally{$s=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Mr(e):""}function w0(e){switch(e.tag){case 5:return Mr(e.type);case 16:return Mr("Lazy");case 13:return Mr("Suspense");case 19:return Mr("SuspenseList");case 0:case 2:case 15:return e=js(e.type,!1),e;case 11:return e=js(e.type.render,!1),e;case 1:return e=js(e.type,!0),e;default:return""}}function ku(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Hn:return"Fragment";case Fn:return"Portal";case Tu:return"Profiler";case el:return"StrictMode";case Cu:return"Suspense";case Iu:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Yd:return(e.displayName||"Context")+".Consumer";case Xd:return(e._context.displayName||"Context")+".Provider";case tl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case nl:return t=e.displayName||null,t!==null?t:ku(e.type)||"Memo";case Lt:t=e._payload,e=e._init;try{return ku(e(t))}catch{}}return null}function x0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ku(t);case 8:return t===el?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Kd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function T0(e){var t=Kd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ao(e){e._valueTracker||(e._valueTracker=T0(e))}function Zd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Kd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function _i(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Nu(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function _c(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qd(e,t){t=t.checked,t!=null&&Ja(e,"checked",t,!1)}function Pu(e,t){qd(e,t);var n=Qt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ru(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ru(e,t.type,Qt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Sc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ru(e,t,n){(t!=="number"||_i(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Fr=Array.isArray;function Qn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ou(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function wc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(Fr(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qt(n)}}function Jd(e,t){var n=Qt(t.value),r=Qt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function xc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ep(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Lu(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ep(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var bo,tp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(bo=bo||document.createElement("div"),bo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=bo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Zr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var $r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},C0=["Webkit","ms","Moz","O"];Object.keys($r).forEach(function(e){C0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),$r[t]=$r[e]})});function np(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||$r.hasOwnProperty(e)&&$r[e]?(""+t).trim():t+"px"}function rp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=np(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var I0=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Au(e,t){if(t){if(I0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function bu(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Du=null;function rl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Mu=null,Kn=null,Zn=null;function Tc(e){if(e=_o(e)){if(typeof Mu!="function")throw Error(_(280));var t=e.stateNode;t&&(t=os(t),Mu(e.stateNode,e.type,t))}}function op(e){Kn?Zn?Zn.push(e):Zn=[e]:Kn=e}function ip(){if(Kn){var e=Kn,t=Zn;if(Zn=Kn=null,Tc(e),t)for(e=0;e<t.length;e++)Tc(t[e])}}function sp(e,t){return e(t)}function up(){}var zs=!1;function ap(e,t,n){if(zs)return e(t,n);zs=!0;try{return sp(e,t,n)}finally{zs=!1,(Kn!==null||Zn!==null)&&(up(),ip())}}function qr(e,t){var n=e.stateNode;if(n===null)return null;var r=os(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var Fu=!1;if(Ct)try{var Nr={};Object.defineProperty(Nr,"passive",{get:function(){Fu=!0}}),window.addEventListener("test",Nr,Nr),window.removeEventListener("test",Nr,Nr)}catch{Fu=!1}function k0(e,t,n,r,o,i,s,u,a){var l=Array.prototype.slice.call(arguments,3);try{t.apply(n,l)}catch(c){this.onError(c)}}var jr=!1,Si=null,wi=!1,Hu=null,N0={onError:function(e){jr=!0,Si=e}};function P0(e,t,n,r,o,i,s,u,a){jr=!1,Si=null,k0.apply(N0,arguments)}function R0(e,t,n,r,o,i,s,u,a){if(P0.apply(this,arguments),jr){if(jr){var l=Si;jr=!1,Si=null}else throw Error(_(198));wi||(wi=!0,Hu=l)}}function Rn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function lp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Cc(e){if(Rn(e)!==e)throw Error(_(188))}function O0(e){var t=e.alternate;if(!t){if(t=Rn(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Cc(o),e;if(i===r)return Cc(o),t;i=i.sibling}throw Error(_(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,u=o.child;u;){if(u===n){s=!0,n=o,r=i;break}if(u===r){s=!0,r=o,n=i;break}u=u.sibling}if(!s){for(u=i.child;u;){if(u===n){s=!0,n=i,r=o;break}if(u===r){s=!0,r=i,n=o;break}u=u.sibling}if(!s)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function cp(e){return e=O0(e),e!==null?fp(e):null}function fp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=fp(e);if(t!==null)return t;e=e.sibling}return null}var dp=He.unstable_scheduleCallback,Ic=He.unstable_cancelCallback,L0=He.unstable_shouldYield,A0=He.unstable_requestPaint,J=He.unstable_now,b0=He.unstable_getCurrentPriorityLevel,ol=He.unstable_ImmediatePriority,pp=He.unstable_UserBlockingPriority,xi=He.unstable_NormalPriority,D0=He.unstable_LowPriority,hp=He.unstable_IdlePriority,es=null,ft=null;function M0(e){if(ft&&typeof ft.onCommitFiberRoot=="function")try{ft.onCommitFiberRoot(es,e,void 0,(e.current.flags&128)===128)}catch{}}var rt=Math.clz32?Math.clz32:B0,F0=Math.log,H0=Math.LN2;function B0(e){return e>>>=0,e===0?32:31-(F0(e)/H0|0)|0}var Do=64,Mo=4194304;function Hr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ti(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var u=s&~o;u!==0?r=Hr(u):(i&=s,i!==0&&(r=Hr(i)))}else s=n&~o,s!==0?r=Hr(s):i!==0&&(r=Hr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-rt(t),o=1<<n,r|=e[n],t&=~o;return r}function U0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-rt(i),u=1<<s,a=o[s];a===-1?(!(u&n)||u&r)&&(o[s]=U0(u,t)):a<=t&&(e.expiredLanes|=u),i&=~u}}function Bu(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function mp(){var e=Do;return Do<<=1,!(Do&4194240)&&(Do=64),e}function Gs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-rt(t),e[t]=n}function j0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-rt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function il(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-rt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var H=0;function gp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var yp,sl,vp,Ep,_p,Uu=!1,Fo=[],Ut=null,$t=null,jt=null,Jr=new Map,eo=new Map,Mt=[],z0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function kc(e,t){switch(e){case"focusin":case"focusout":Ut=null;break;case"dragenter":case"dragleave":$t=null;break;case"mouseover":case"mouseout":jt=null;break;case"pointerover":case"pointerout":Jr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":eo.delete(t.pointerId)}}function Pr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=_o(t),t!==null&&sl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function G0(e,t,n,r,o){switch(t){case"focusin":return Ut=Pr(Ut,e,t,n,r,o),!0;case"dragenter":return $t=Pr($t,e,t,n,r,o),!0;case"mouseover":return jt=Pr(jt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Jr.set(i,Pr(Jr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,eo.set(i,Pr(eo.get(i)||null,e,t,n,r,o)),!0}return!1}function Sp(e){var t=fn(e.target);if(t!==null){var n=Rn(t);if(n!==null){if(t=n.tag,t===13){if(t=lp(n),t!==null){e.blockedOn=t,_p(e.priority,function(){vp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function si(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=$u(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Du=r,n.target.dispatchEvent(r),Du=null}else return t=_o(n),t!==null&&sl(t),e.blockedOn=n,!1;t.shift()}return!0}function Nc(e,t,n){si(e)&&n.delete(t)}function V0(){Uu=!1,Ut!==null&&si(Ut)&&(Ut=null),$t!==null&&si($t)&&($t=null),jt!==null&&si(jt)&&(jt=null),Jr.forEach(Nc),eo.forEach(Nc)}function Rr(e,t){e.blockedOn===t&&(e.blockedOn=null,Uu||(Uu=!0,He.unstable_scheduleCallback(He.unstable_NormalPriority,V0)))}function to(e){function t(o){return Rr(o,e)}if(0<Fo.length){Rr(Fo[0],e);for(var n=1;n<Fo.length;n++){var r=Fo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ut!==null&&Rr(Ut,e),$t!==null&&Rr($t,e),jt!==null&&Rr(jt,e),Jr.forEach(t),eo.forEach(t),n=0;n<Mt.length;n++)r=Mt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Mt.length&&(n=Mt[0],n.blockedOn===null);)Sp(n),n.blockedOn===null&&Mt.shift()}var qn=Rt.ReactCurrentBatchConfig,Ci=!0;function W0(e,t,n,r){var o=H,i=qn.transition;qn.transition=null;try{H=1,ul(e,t,n,r)}finally{H=o,qn.transition=i}}function X0(e,t,n,r){var o=H,i=qn.transition;qn.transition=null;try{H=4,ul(e,t,n,r)}finally{H=o,qn.transition=i}}function ul(e,t,n,r){if(Ci){var o=$u(e,t,n,r);if(o===null)eu(e,t,r,Ii,n),kc(e,r);else if(G0(o,e,t,n,r))r.stopPropagation();else if(kc(e,r),t&4&&-1<z0.indexOf(e)){for(;o!==null;){var i=_o(o);if(i!==null&&yp(i),i=$u(e,t,n,r),i===null&&eu(e,t,r,Ii,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else eu(e,t,r,null,n)}}var Ii=null;function $u(e,t,n,r){if(Ii=null,e=rl(r),e=fn(e),e!==null)if(t=Rn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=lp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ii=e,null}function wp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(b0()){case ol:return 1;case pp:return 4;case xi:case D0:return 16;case hp:return 536870912;default:return 16}default:return 16}}var Ht=null,al=null,ui=null;function xp(){if(ui)return ui;var e,t=al,n=t.length,r,o="value"in Ht?Ht.value:Ht.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return ui=o.slice(e,1<r?1-r:void 0)}function ai(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ho(){return!0}function Pc(){return!1}function Ue(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(i):i[u]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ho:Pc,this.isPropagationStopped=Pc,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ho)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ho)},persist:function(){},isPersistent:Ho}),t}var _r={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ll=Ue(_r),Eo=K({},_r,{view:0,detail:0}),Y0=Ue(Eo),Vs,Ws,Or,ts=K({},Eo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Or&&(Or&&e.type==="mousemove"?(Vs=e.screenX-Or.screenX,Ws=e.screenY-Or.screenY):Ws=Vs=0,Or=e),Vs)},movementY:function(e){return"movementY"in e?e.movementY:Ws}}),Rc=Ue(ts),Q0=K({},ts,{dataTransfer:0}),K0=Ue(Q0),Z0=K({},Eo,{relatedTarget:0}),Xs=Ue(Z0),q0=K({},_r,{animationName:0,elapsedTime:0,pseudoElement:0}),J0=Ue(q0),ey=K({},_r,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ty=Ue(ey),ny=K({},_r,{data:0}),Oc=Ue(ny),ry={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},oy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},iy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=iy[e])?!!t[e]:!1}function cl(){return sy}var uy=K({},Eo,{key:function(e){if(e.key){var t=ry[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ai(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?oy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cl,charCode:function(e){return e.type==="keypress"?ai(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ai(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ay=Ue(uy),ly=K({},ts,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Lc=Ue(ly),cy=K({},Eo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cl}),fy=Ue(cy),dy=K({},_r,{propertyName:0,elapsedTime:0,pseudoElement:0}),py=Ue(dy),hy=K({},ts,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),my=Ue(hy),gy=[9,13,27,32],fl=Ct&&"CompositionEvent"in window,zr=null;Ct&&"documentMode"in document&&(zr=document.documentMode);var yy=Ct&&"TextEvent"in window&&!zr,Tp=Ct&&(!fl||zr&&8<zr&&11>=zr),Ac=" ",bc=!1;function Cp(e,t){switch(e){case"keyup":return gy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ip(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Bn=!1;function vy(e,t){switch(e){case"compositionend":return Ip(t);case"keypress":return t.which!==32?null:(bc=!0,Ac);case"textInput":return e=t.data,e===Ac&&bc?null:e;default:return null}}function Ey(e,t){if(Bn)return e==="compositionend"||!fl&&Cp(e,t)?(e=xp(),ui=al=Ht=null,Bn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Tp&&t.locale!=="ko"?null:t.data;default:return null}}var _y={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Dc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!_y[e.type]:t==="textarea"}function kp(e,t,n,r){op(r),t=ki(t,"onChange"),0<t.length&&(n=new ll("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gr=null,no=null;function Sy(e){Hp(e,0)}function ns(e){var t=jn(e);if(Zd(t))return e}function wy(e,t){if(e==="change")return t}var Np=!1;if(Ct){var Ys;if(Ct){var Qs="oninput"in document;if(!Qs){var Mc=document.createElement("div");Mc.setAttribute("oninput","return;"),Qs=typeof Mc.oninput=="function"}Ys=Qs}else Ys=!1;Np=Ys&&(!document.documentMode||9<document.documentMode)}function Fc(){Gr&&(Gr.detachEvent("onpropertychange",Pp),no=Gr=null)}function Pp(e){if(e.propertyName==="value"&&ns(no)){var t=[];kp(t,no,e,rl(e)),ap(Sy,t)}}function xy(e,t,n){e==="focusin"?(Fc(),Gr=t,no=n,Gr.attachEvent("onpropertychange",Pp)):e==="focusout"&&Fc()}function Ty(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ns(no)}function Cy(e,t){if(e==="click")return ns(t)}function Iy(e,t){if(e==="input"||e==="change")return ns(t)}function ky(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var st=typeof Object.is=="function"?Object.is:ky;function ro(e,t){if(st(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!xu.call(t,o)||!st(e[o],t[o]))return!1}return!0}function Hc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bc(e,t){var n=Hc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hc(n)}}function Rp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Rp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Op(){for(var e=window,t=_i();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=_i(e.document)}return t}function dl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ny(e){var t=Op(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Rp(n.ownerDocument.documentElement,n)){if(r!==null&&dl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Bc(n,i);var s=Bc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Py=Ct&&"documentMode"in document&&11>=document.documentMode,Un=null,ju=null,Vr=null,zu=!1;function Uc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zu||Un==null||Un!==_i(r)||(r=Un,"selectionStart"in r&&dl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Vr&&ro(Vr,r)||(Vr=r,r=ki(ju,"onSelect"),0<r.length&&(t=new ll("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Un)))}function Bo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $n={animationend:Bo("Animation","AnimationEnd"),animationiteration:Bo("Animation","AnimationIteration"),animationstart:Bo("Animation","AnimationStart"),transitionend:Bo("Transition","TransitionEnd")},Ks={},Lp={};Ct&&(Lp=document.createElement("div").style,"AnimationEvent"in window||(delete $n.animationend.animation,delete $n.animationiteration.animation,delete $n.animationstart.animation),"TransitionEvent"in window||delete $n.transitionend.transition);function rs(e){if(Ks[e])return Ks[e];if(!$n[e])return e;var t=$n[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Lp)return Ks[e]=t[n];return e}var Ap=rs("animationend"),bp=rs("animationiteration"),Dp=rs("animationstart"),Mp=rs("transitionend"),Fp=new Map,$c="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Jt(e,t){Fp.set(e,t),Pn(t,[e])}for(var Zs=0;Zs<$c.length;Zs++){var qs=$c[Zs],Ry=qs.toLowerCase(),Oy=qs[0].toUpperCase()+qs.slice(1);Jt(Ry,"on"+Oy)}Jt(Ap,"onAnimationEnd");Jt(bp,"onAnimationIteration");Jt(Dp,"onAnimationStart");Jt("dblclick","onDoubleClick");Jt("focusin","onFocus");Jt("focusout","onBlur");Jt(Mp,"onTransitionEnd");sr("onMouseEnter",["mouseout","mouseover"]);sr("onMouseLeave",["mouseout","mouseover"]);sr("onPointerEnter",["pointerout","pointerover"]);sr("onPointerLeave",["pointerout","pointerover"]);Pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Pn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Br="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ly=new Set("cancel close invalid load scroll toggle".split(" ").concat(Br));function jc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,R0(r,t,void 0,e),e.currentTarget=null}function Hp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var u=r[s],a=u.instance,l=u.currentTarget;if(u=u.listener,a!==i&&o.isPropagationStopped())break e;jc(o,u,l),i=a}else for(s=0;s<r.length;s++){if(u=r[s],a=u.instance,l=u.currentTarget,u=u.listener,a!==i&&o.isPropagationStopped())break e;jc(o,u,l),i=a}}}if(wi)throw e=Hu,wi=!1,Hu=null,e}function z(e,t){var n=t[Yu];n===void 0&&(n=t[Yu]=new Set);var r=e+"__bubble";n.has(r)||(Bp(t,e,2,!1),n.add(r))}function Js(e,t,n){var r=0;t&&(r|=4),Bp(n,e,r,t)}var Uo="_reactListening"+Math.random().toString(36).slice(2);function oo(e){if(!e[Uo]){e[Uo]=!0,Wd.forEach(function(n){n!=="selectionchange"&&(Ly.has(n)||Js(n,!1,e),Js(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Uo]||(t[Uo]=!0,Js("selectionchange",!1,t))}}function Bp(e,t,n,r){switch(wp(t)){case 1:var o=W0;break;case 4:o=X0;break;default:o=ul}n=o.bind(null,t,n,e),o=void 0,!Fu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function eu(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var u=r.stateNode.containerInfo;if(u===o||u.nodeType===8&&u.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;u!==null;){if(s=fn(u),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}u=u.parentNode}}r=r.return}ap(function(){var l=i,c=rl(n),f=[];e:{var d=Fp.get(e);if(d!==void 0){var m=ll,y=e;switch(e){case"keypress":if(ai(n)===0)break e;case"keydown":case"keyup":m=ay;break;case"focusin":y="focus",m=Xs;break;case"focusout":y="blur",m=Xs;break;case"beforeblur":case"afterblur":m=Xs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Rc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=K0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=fy;break;case Ap:case bp:case Dp:m=J0;break;case Mp:m=py;break;case"scroll":m=Y0;break;case"wheel":m=my;break;case"copy":case"cut":case"paste":m=ty;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Lc}var v=(t&4)!==0,S=!v&&e==="scroll",h=v?d!==null?d+"Capture":null:d;v=[];for(var p=l,g;p!==null;){g=p;var E=g.stateNode;if(g.tag===5&&E!==null&&(g=E,h!==null&&(E=qr(p,h),E!=null&&v.push(io(p,E,g)))),S)break;p=p.return}0<v.length&&(d=new m(d,y,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",d&&n!==Du&&(y=n.relatedTarget||n.fromElement)&&(fn(y)||y[It]))break e;if((m||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=l,y=y?fn(y):null,y!==null&&(S=Rn(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=l),m!==y)){if(v=Rc,E="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=Lc,E="onPointerLeave",h="onPointerEnter",p="pointer"),S=m==null?d:jn(m),g=y==null?d:jn(y),d=new v(E,p+"leave",m,n,c),d.target=S,d.relatedTarget=g,E=null,fn(c)===l&&(v=new v(h,p+"enter",y,n,c),v.target=g,v.relatedTarget=S,E=v),S=E,m&&y)t:{for(v=m,h=y,p=0,g=v;g;g=Dn(g))p++;for(g=0,E=h;E;E=Dn(E))g++;for(;0<p-g;)v=Dn(v),p--;for(;0<g-p;)h=Dn(h),g--;for(;p--;){if(v===h||h!==null&&v===h.alternate)break t;v=Dn(v),h=Dn(h)}v=null}else v=null;m!==null&&zc(f,d,m,v,!1),y!==null&&S!==null&&zc(f,S,y,v,!0)}}e:{if(d=l?jn(l):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var w=wy;else if(Dc(d))if(Np)w=Iy;else{w=Ty;var x=xy}else(m=d.nodeName)&&m.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(w=Cy);if(w&&(w=w(e,l))){kp(f,w,n,c);break e}x&&x(e,d,l),e==="focusout"&&(x=d._wrapperState)&&x.controlled&&d.type==="number"&&Ru(d,"number",d.value)}switch(x=l?jn(l):window,e){case"focusin":(Dc(x)||x.contentEditable==="true")&&(Un=x,ju=l,Vr=null);break;case"focusout":Vr=ju=Un=null;break;case"mousedown":zu=!0;break;case"contextmenu":case"mouseup":case"dragend":zu=!1,Uc(f,n,c);break;case"selectionchange":if(Py)break;case"keydown":case"keyup":Uc(f,n,c)}var I;if(fl)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else Bn?Cp(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(Tp&&n.locale!=="ko"&&(Bn||N!=="onCompositionStart"?N==="onCompositionEnd"&&Bn&&(I=xp()):(Ht=c,al="value"in Ht?Ht.value:Ht.textContent,Bn=!0)),x=ki(l,N),0<x.length&&(N=new Oc(N,e,null,n,c),f.push({event:N,listeners:x}),I?N.data=I:(I=Ip(n),I!==null&&(N.data=I)))),(I=yy?vy(e,n):Ey(e,n))&&(l=ki(l,"onBeforeInput"),0<l.length&&(c=new Oc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:l}),c.data=I))}Hp(f,t)})}function io(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ki(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=qr(e,n),i!=null&&r.unshift(io(e,i,o)),i=qr(e,t),i!=null&&r.push(io(e,i,o))),e=e.return}return r}function Dn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function zc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var u=n,a=u.alternate,l=u.stateNode;if(a!==null&&a===r)break;u.tag===5&&l!==null&&(u=l,o?(a=qr(n,i),a!=null&&s.unshift(io(n,a,u))):o||(a=qr(n,i),a!=null&&s.push(io(n,a,u)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Ay=/\r\n?/g,by=/\u0000|\uFFFD/g;function Gc(e){return(typeof e=="string"?e:""+e).replace(Ay,`
`).replace(by,"")}function $o(e,t,n){if(t=Gc(t),Gc(e)!==t&&n)throw Error(_(425))}function Ni(){}var Gu=null,Vu=null;function Wu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Xu=typeof setTimeout=="function"?setTimeout:void 0,Dy=typeof clearTimeout=="function"?clearTimeout:void 0,Vc=typeof Promise=="function"?Promise:void 0,My=typeof queueMicrotask=="function"?queueMicrotask:typeof Vc<"u"?function(e){return Vc.resolve(null).then(e).catch(Fy)}:Xu;function Fy(e){setTimeout(function(){throw e})}function tu(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),to(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);to(t)}function zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Wc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Sr=Math.random().toString(36).slice(2),lt="__reactFiber$"+Sr,so="__reactProps$"+Sr,It="__reactContainer$"+Sr,Yu="__reactEvents$"+Sr,Hy="__reactListeners$"+Sr,By="__reactHandles$"+Sr;function fn(e){var t=e[lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[It]||n[lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Wc(e);e!==null;){if(n=e[lt])return n;e=Wc(e)}return t}e=n,n=e.parentNode}return null}function _o(e){return e=e[lt]||e[It],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function jn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function os(e){return e[so]||null}var Qu=[],zn=-1;function en(e){return{current:e}}function V(e){0>zn||(e.current=Qu[zn],Qu[zn]=null,zn--)}function j(e,t){zn++,Qu[zn]=e.current,e.current=t}var Kt={},ge=en(Kt),Pe=en(!1),vn=Kt;function ur(e,t){var n=e.type.contextTypes;if(!n)return Kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Re(e){return e=e.childContextTypes,e!=null}function Pi(){V(Pe),V(ge)}function Xc(e,t,n){if(ge.current!==Kt)throw Error(_(168));j(ge,t),j(Pe,n)}function Up(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(_(108,x0(e)||"Unknown",o));return K({},n,r)}function Ri(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kt,vn=ge.current,j(ge,e),j(Pe,Pe.current),!0}function Yc(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=Up(e,t,vn),r.__reactInternalMemoizedMergedChildContext=e,V(Pe),V(ge),j(ge,e)):V(Pe),j(Pe,n)}var vt=null,is=!1,nu=!1;function $p(e){vt===null?vt=[e]:vt.push(e)}function Uy(e){is=!0,$p(e)}function tn(){if(!nu&&vt!==null){nu=!0;var e=0,t=H;try{var n=vt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}vt=null,is=!1}catch(o){throw vt!==null&&(vt=vt.slice(e+1)),dp(ol,tn),o}finally{H=t,nu=!1}}return null}var Gn=[],Vn=0,Oi=null,Li=0,ze=[],Ge=0,En=null,_t=1,St="";function ln(e,t){Gn[Vn++]=Li,Gn[Vn++]=Oi,Oi=e,Li=t}function jp(e,t,n){ze[Ge++]=_t,ze[Ge++]=St,ze[Ge++]=En,En=e;var r=_t;e=St;var o=32-rt(r)-1;r&=~(1<<o),n+=1;var i=32-rt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,_t=1<<32-rt(t)+o|n<<o|r,St=i+e}else _t=1<<i|n<<o|r,St=e}function pl(e){e.return!==null&&(ln(e,1),jp(e,1,0))}function hl(e){for(;e===Oi;)Oi=Gn[--Vn],Gn[Vn]=null,Li=Gn[--Vn],Gn[Vn]=null;for(;e===En;)En=ze[--Ge],ze[Ge]=null,St=ze[--Ge],ze[Ge]=null,_t=ze[--Ge],ze[Ge]=null}var Me=null,Ae=null,W=!1,nt=null;function zp(e,t){var n=We(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Qc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Me=e,Ae=zt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Me=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=En!==null?{id:_t,overflow:St}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=We(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Me=e,Ae=null,!0):!1;default:return!1}}function Ku(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Zu(e){if(W){var t=Ae;if(t){var n=t;if(!Qc(e,t)){if(Ku(e))throw Error(_(418));t=zt(n.nextSibling);var r=Me;t&&Qc(e,t)?zp(r,n):(e.flags=e.flags&-4097|2,W=!1,Me=e)}}else{if(Ku(e))throw Error(_(418));e.flags=e.flags&-4097|2,W=!1,Me=e}}}function Kc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Me=e}function jo(e){if(e!==Me)return!1;if(!W)return Kc(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Wu(e.type,e.memoizedProps)),t&&(t=Ae)){if(Ku(e))throw Gp(),Error(_(418));for(;t;)zp(e,t),t=zt(t.nextSibling)}if(Kc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Me?zt(e.stateNode.nextSibling):null;return!0}function Gp(){for(var e=Ae;e;)e=zt(e.nextSibling)}function ar(){Ae=Me=null,W=!1}function ml(e){nt===null?nt=[e]:nt.push(e)}var $y=Rt.ReactCurrentBatchConfig;function Lr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var u=o.refs;s===null?delete u[i]:u[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function zo(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Zc(e){var t=e._init;return t(e._payload)}function Vp(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=Xt(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function u(h,p,g,E){return p===null||p.tag!==6?(p=lu(g,h.mode,E),p.return=h,p):(p=o(p,g),p.return=h,p)}function a(h,p,g,E){var w=g.type;return w===Hn?c(h,p,g.props.children,E,g.key):p!==null&&(p.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Lt&&Zc(w)===p.type)?(E=o(p,g.props),E.ref=Lr(h,p,g),E.return=h,E):(E=mi(g.type,g.key,g.props,null,h.mode,E),E.ref=Lr(h,p,g),E.return=h,E)}function l(h,p,g,E){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=cu(g,h.mode,E),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function c(h,p,g,E,w){return p===null||p.tag!==7?(p=gn(g,h.mode,E,w),p.return=h,p):(p=o(p,g),p.return=h,p)}function f(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=lu(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Lo:return g=mi(p.type,p.key,p.props,null,h.mode,g),g.ref=Lr(h,null,p),g.return=h,g;case Fn:return p=cu(p,h.mode,g),p.return=h,p;case Lt:var E=p._init;return f(h,E(p._payload),g)}if(Fr(p)||kr(p))return p=gn(p,h.mode,g,null),p.return=h,p;zo(h,p)}return null}function d(h,p,g,E){var w=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return w!==null?null:u(h,p,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Lo:return g.key===w?a(h,p,g,E):null;case Fn:return g.key===w?l(h,p,g,E):null;case Lt:return w=g._init,d(h,p,w(g._payload),E)}if(Fr(g)||kr(g))return w!==null?null:c(h,p,g,E,null);zo(h,g)}return null}function m(h,p,g,E,w){if(typeof E=="string"&&E!==""||typeof E=="number")return h=h.get(g)||null,u(p,h,""+E,w);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Lo:return h=h.get(E.key===null?g:E.key)||null,a(p,h,E,w);case Fn:return h=h.get(E.key===null?g:E.key)||null,l(p,h,E,w);case Lt:var x=E._init;return m(h,p,g,x(E._payload),w)}if(Fr(E)||kr(E))return h=h.get(g)||null,c(p,h,E,w,null);zo(p,E)}return null}function y(h,p,g,E){for(var w=null,x=null,I=p,N=p=0,X=null;I!==null&&N<g.length;N++){I.index>N?(X=I,I=null):X=I.sibling;var A=d(h,I,g[N],E);if(A===null){I===null&&(I=X);break}e&&I&&A.alternate===null&&t(h,I),p=i(A,p,N),x===null?w=A:x.sibling=A,x=A,I=X}if(N===g.length)return n(h,I),W&&ln(h,N),w;if(I===null){for(;N<g.length;N++)I=f(h,g[N],E),I!==null&&(p=i(I,p,N),x===null?w=I:x.sibling=I,x=I);return W&&ln(h,N),w}for(I=r(h,I);N<g.length;N++)X=m(I,h,N,g[N],E),X!==null&&(e&&X.alternate!==null&&I.delete(X.key===null?N:X.key),p=i(X,p,N),x===null?w=X:x.sibling=X,x=X);return e&&I.forEach(function(qe){return t(h,qe)}),W&&ln(h,N),w}function v(h,p,g,E){var w=kr(g);if(typeof w!="function")throw Error(_(150));if(g=w.call(g),g==null)throw Error(_(151));for(var x=w=null,I=p,N=p=0,X=null,A=g.next();I!==null&&!A.done;N++,A=g.next()){I.index>N?(X=I,I=null):X=I.sibling;var qe=d(h,I,A.value,E);if(qe===null){I===null&&(I=X);break}e&&I&&qe.alternate===null&&t(h,I),p=i(qe,p,N),x===null?w=qe:x.sibling=qe,x=qe,I=X}if(A.done)return n(h,I),W&&ln(h,N),w;if(I===null){for(;!A.done;N++,A=g.next())A=f(h,A.value,E),A!==null&&(p=i(A,p,N),x===null?w=A:x.sibling=A,x=A);return W&&ln(h,N),w}for(I=r(h,I);!A.done;N++,A=g.next())A=m(I,h,N,A.value,E),A!==null&&(e&&A.alternate!==null&&I.delete(A.key===null?N:A.key),p=i(A,p,N),x===null?w=A:x.sibling=A,x=A);return e&&I.forEach(function(Tr){return t(h,Tr)}),W&&ln(h,N),w}function S(h,p,g,E){if(typeof g=="object"&&g!==null&&g.type===Hn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Lo:e:{for(var w=g.key,x=p;x!==null;){if(x.key===w){if(w=g.type,w===Hn){if(x.tag===7){n(h,x.sibling),p=o(x,g.props.children),p.return=h,h=p;break e}}else if(x.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Lt&&Zc(w)===x.type){n(h,x.sibling),p=o(x,g.props),p.ref=Lr(h,x,g),p.return=h,h=p;break e}n(h,x);break}else t(h,x);x=x.sibling}g.type===Hn?(p=gn(g.props.children,h.mode,E,g.key),p.return=h,h=p):(E=mi(g.type,g.key,g.props,null,h.mode,E),E.ref=Lr(h,p,g),E.return=h,h=E)}return s(h);case Fn:e:{for(x=g.key;p!==null;){if(p.key===x)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=cu(g,h.mode,E),p.return=h,h=p}return s(h);case Lt:return x=g._init,S(h,p,x(g._payload),E)}if(Fr(g))return y(h,p,g,E);if(kr(g))return v(h,p,g,E);zo(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=lu(g,h.mode,E),p.return=h,h=p),s(h)):n(h,p)}return S}var lr=Vp(!0),Wp=Vp(!1),Ai=en(null),bi=null,Wn=null,gl=null;function yl(){gl=Wn=bi=null}function vl(e){var t=Ai.current;V(Ai),e._currentValue=t}function qu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Jn(e,t){bi=e,gl=Wn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ne=!0),e.firstContext=null)}function Ye(e){var t=e._currentValue;if(gl!==e)if(e={context:e,memoizedValue:t,next:null},Wn===null){if(bi===null)throw Error(_(308));Wn=e,bi.dependencies={lanes:0,firstContext:e}}else Wn=Wn.next=e;return t}var dn=null;function El(e){dn===null?dn=[e]:dn.push(e)}function Xp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,El(t)):(n.next=o.next,o.next=n),t.interleaved=n,kt(e,r)}function kt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var At=!1;function _l(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Yp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function wt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Gt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,M&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,kt(e,n)}return o=r.interleaved,o===null?(t.next=t,El(r)):(t.next=o.next,o.next=t),r.interleaved=t,kt(e,n)}function li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,il(e,n)}}function qc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Di(e,t,n,r){var o=e.updateQueue;At=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,u=o.shared.pending;if(u!==null){o.shared.pending=null;var a=u,l=a.next;a.next=null,s===null?i=l:s.next=l,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,u=c.lastBaseUpdate,u!==s&&(u===null?c.firstBaseUpdate=l:u.next=l,c.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;s=0,c=l=a=null,u=i;do{var d=u.lane,m=u.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:m,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var y=e,v=u;switch(d=t,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(m,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(m,f,d):y,d==null)break e;f=K({},f,d);break e;case 2:At=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[u]:d.push(u))}else m={eventTime:m,lane:d,tag:u.tag,payload:u.payload,callback:u.callback,next:null},c===null?(l=c=m,a=f):c=c.next=m,s|=d;if(u=u.next,u===null){if(u=o.shared.pending,u===null)break;d=u,u=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(c===null&&(a=f),o.baseState=a,o.firstBaseUpdate=l,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Sn|=s,e.lanes=s,e.memoizedState=f}}function Jc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(_(191,o));o.call(r)}}}var So={},dt=en(So),uo=en(So),ao=en(So);function pn(e){if(e===So)throw Error(_(174));return e}function Sl(e,t){switch(j(ao,t),j(uo,e),j(dt,So),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Lu(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Lu(t,e)}V(dt),j(dt,t)}function cr(){V(dt),V(uo),V(ao)}function Qp(e){pn(ao.current);var t=pn(dt.current),n=Lu(t,e.type);t!==n&&(j(uo,e),j(dt,n))}function wl(e){uo.current===e&&(V(dt),V(uo))}var Y=en(0);function Mi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ru=[];function xl(){for(var e=0;e<ru.length;e++)ru[e]._workInProgressVersionPrimary=null;ru.length=0}var ci=Rt.ReactCurrentDispatcher,ou=Rt.ReactCurrentBatchConfig,_n=0,Q=null,ne=null,ie=null,Fi=!1,Wr=!1,lo=0,jy=0;function fe(){throw Error(_(321))}function Tl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!st(e[n],t[n]))return!1;return!0}function Cl(e,t,n,r,o,i){if(_n=i,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ci.current=e===null||e.memoizedState===null?Wy:Xy,e=n(r,o),Wr){i=0;do{if(Wr=!1,lo=0,25<=i)throw Error(_(301));i+=1,ie=ne=null,t.updateQueue=null,ci.current=Yy,e=n(r,o)}while(Wr)}if(ci.current=Hi,t=ne!==null&&ne.next!==null,_n=0,ie=ne=Q=null,Fi=!1,t)throw Error(_(300));return e}function Il(){var e=lo!==0;return lo=0,e}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?Q.memoizedState=ie=e:ie=ie.next=e,ie}function Qe(){if(ne===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=ie===null?Q.memoizedState:ie.next;if(t!==null)ie=t,ne=e;else{if(e===null)throw Error(_(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},ie===null?Q.memoizedState=ie=e:ie=ie.next=e}return ie}function co(e,t){return typeof t=="function"?t(e):t}function iu(e){var t=Qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=ne,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var u=s=null,a=null,l=i;do{var c=l.lane;if((_n&c)===c)a!==null&&(a=a.next={lane:0,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null}),r=l.hasEagerState?l.eagerState:e(r,l.action);else{var f={lane:c,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null};a===null?(u=a=f,s=r):a=a.next=f,Q.lanes|=c,Sn|=c}l=l.next}while(l!==null&&l!==i);a===null?s=r:a.next=u,st(r,t.memoizedState)||(Ne=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Q.lanes|=i,Sn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function su(e){var t=Qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);st(i,t.memoizedState)||(Ne=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Kp(){}function Zp(e,t){var n=Q,r=Qe(),o=t(),i=!st(r.memoizedState,o);if(i&&(r.memoizedState=o,Ne=!0),r=r.queue,kl(eh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ie!==null&&ie.memoizedState.tag&1){if(n.flags|=2048,fo(9,Jp.bind(null,n,r,o,t),void 0,null),se===null)throw Error(_(349));_n&30||qp(n,t,o)}return o}function qp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Jp(e,t,n,r){t.value=n,t.getSnapshot=r,th(t)&&nh(e)}function eh(e,t,n){return n(function(){th(t)&&nh(e)})}function th(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!st(e,n)}catch{return!0}}function nh(e){var t=kt(e,1);t!==null&&ot(t,e,1,-1)}function ef(e){var t=at();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:co,lastRenderedState:e},t.queue=e,e=e.dispatch=Vy.bind(null,Q,e),[t.memoizedState,e]}function fo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function rh(){return Qe().memoizedState}function fi(e,t,n,r){var o=at();Q.flags|=e,o.memoizedState=fo(1|t,n,void 0,r===void 0?null:r)}function ss(e,t,n,r){var o=Qe();r=r===void 0?null:r;var i=void 0;if(ne!==null){var s=ne.memoizedState;if(i=s.destroy,r!==null&&Tl(r,s.deps)){o.memoizedState=fo(t,n,i,r);return}}Q.flags|=e,o.memoizedState=fo(1|t,n,i,r)}function tf(e,t){return fi(8390656,8,e,t)}function kl(e,t){return ss(2048,8,e,t)}function oh(e,t){return ss(4,2,e,t)}function ih(e,t){return ss(4,4,e,t)}function sh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function uh(e,t,n){return n=n!=null?n.concat([e]):null,ss(4,4,sh.bind(null,t,e),n)}function Nl(){}function ah(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Tl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function lh(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Tl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ch(e,t,n){return _n&21?(st(n,t)||(n=mp(),Q.lanes|=n,Sn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ne=!0),e.memoizedState=n)}function zy(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=ou.transition;ou.transition={};try{e(!1),t()}finally{H=n,ou.transition=r}}function fh(){return Qe().memoizedState}function Gy(e,t,n){var r=Wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},dh(e))ph(t,n);else if(n=Xp(e,t,n,r),n!==null){var o=xe();ot(n,e,r,o),hh(n,t,r)}}function Vy(e,t,n){var r=Wt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(dh(e))ph(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,u=i(s,n);if(o.hasEagerState=!0,o.eagerState=u,st(u,s)){var a=t.interleaved;a===null?(o.next=o,El(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Xp(e,t,o,r),n!==null&&(o=xe(),ot(n,e,r,o),hh(n,t,r))}}function dh(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function ph(e,t){Wr=Fi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function hh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,il(e,n)}}var Hi={readContext:Ye,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},Wy={readContext:Ye,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:Ye,useEffect:tf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,fi(4194308,4,sh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fi(4194308,4,e,t)},useInsertionEffect:function(e,t){return fi(4,2,e,t)},useMemo:function(e,t){var n=at();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=at();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Gy.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:ef,useDebugValue:Nl,useDeferredValue:function(e){return at().memoizedState=e},useTransition:function(){var e=ef(!1),t=e[0];return e=zy.bind(null,e[1]),at().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,o=at();if(W){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),se===null)throw Error(_(349));_n&30||qp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,tf(eh.bind(null,r,i,e),[e]),r.flags|=2048,fo(9,Jp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=at(),t=se.identifierPrefix;if(W){var n=St,r=_t;n=(r&~(1<<32-rt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=lo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=jy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Xy={readContext:Ye,useCallback:ah,useContext:Ye,useEffect:kl,useImperativeHandle:uh,useInsertionEffect:oh,useLayoutEffect:ih,useMemo:lh,useReducer:iu,useRef:rh,useState:function(){return iu(co)},useDebugValue:Nl,useDeferredValue:function(e){var t=Qe();return ch(t,ne.memoizedState,e)},useTransition:function(){var e=iu(co)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:Kp,useSyncExternalStore:Zp,useId:fh,unstable_isNewReconciler:!1},Yy={readContext:Ye,useCallback:ah,useContext:Ye,useEffect:kl,useImperativeHandle:uh,useInsertionEffect:oh,useLayoutEffect:ih,useMemo:lh,useReducer:su,useRef:rh,useState:function(){return su(co)},useDebugValue:Nl,useDeferredValue:function(e){var t=Qe();return ne===null?t.memoizedState=e:ch(t,ne.memoizedState,e)},useTransition:function(){var e=su(co)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:Kp,useSyncExternalStore:Zp,useId:fh,unstable_isNewReconciler:!1};function et(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ju(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var us={isMounted:function(e){return(e=e._reactInternals)?Rn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),o=Wt(e),i=wt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Gt(e,i,o),t!==null&&(ot(t,e,o,r),li(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),o=Wt(e),i=wt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Gt(e,i,o),t!==null&&(ot(t,e,o,r),li(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Wt(e),o=wt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Gt(e,o,r),t!==null&&(ot(t,e,r,n),li(t,e,r))}};function nf(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!ro(n,r)||!ro(o,i):!0}function mh(e,t,n){var r=!1,o=Kt,i=t.contextType;return typeof i=="object"&&i!==null?i=Ye(i):(o=Re(t)?vn:ge.current,r=t.contextTypes,i=(r=r!=null)?ur(e,o):Kt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=us,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function rf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&us.enqueueReplaceState(t,t.state,null)}function ea(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},_l(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Ye(i):(i=Re(t)?vn:ge.current,o.context=ur(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ju(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&us.enqueueReplaceState(o,o.state,null),Di(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function fr(e,t){try{var n="",r=t;do n+=w0(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function uu(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ta(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Qy=typeof WeakMap=="function"?WeakMap:Map;function gh(e,t,n){n=wt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ui||(Ui=!0,fa=r),ta(e,t)},n}function yh(e,t,n){n=wt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ta(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ta(e,t),typeof r!="function"&&(Vt===null?Vt=new Set([this]):Vt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function of(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Qy;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=lv.bind(null,e,t,n),t.then(e,e))}function sf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function uf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=wt(-1,1),t.tag=2,Gt(n,t,1))),n.lanes|=1),e)}var Ky=Rt.ReactCurrentOwner,Ne=!1;function Ee(e,t,n,r){t.child=e===null?Wp(t,null,n,r):lr(t,e.child,n,r)}function af(e,t,n,r,o){n=n.render;var i=t.ref;return Jn(t,o),r=Cl(e,t,n,r,i,o),n=Il(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Nt(e,t,o)):(W&&n&&pl(t),t.flags|=1,Ee(e,t,r,o),t.child)}function lf(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ml(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,vh(e,t,i,r,o)):(e=mi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:ro,n(s,r)&&e.ref===t.ref)return Nt(e,t,o)}return t.flags|=1,e=Xt(i,r),e.ref=t.ref,e.return=t,t.child=e}function vh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ro(i,r)&&e.ref===t.ref)if(Ne=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ne=!0);else return t.lanes=e.lanes,Nt(e,t,o)}return na(e,t,n,r,o)}function Eh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},j(Yn,Le),Le|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,j(Yn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,j(Yn,Le),Le|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,j(Yn,Le),Le|=r;return Ee(e,t,o,n),t.child}function _h(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function na(e,t,n,r,o){var i=Re(n)?vn:ge.current;return i=ur(t,i),Jn(t,o),n=Cl(e,t,n,r,i,o),r=Il(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Nt(e,t,o)):(W&&r&&pl(t),t.flags|=1,Ee(e,t,n,o),t.child)}function cf(e,t,n,r,o){if(Re(n)){var i=!0;Ri(t)}else i=!1;if(Jn(t,o),t.stateNode===null)di(e,t),mh(t,n,r),ea(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,u=t.memoizedProps;s.props=u;var a=s.context,l=n.contextType;typeof l=="object"&&l!==null?l=Ye(l):(l=Re(n)?vn:ge.current,l=ur(t,l));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u!==r||a!==l)&&rf(t,s,r,l),At=!1;var d=t.memoizedState;s.state=d,Di(t,r,s,o),a=t.memoizedState,u!==r||d!==a||Pe.current||At?(typeof c=="function"&&(Ju(t,n,c,r),a=t.memoizedState),(u=At||nf(t,n,u,r,d,a,l))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=l,r=u):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Yp(e,t),u=t.memoizedProps,l=t.type===t.elementType?u:et(t.type,u),s.props=l,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ye(a):(a=Re(n)?vn:ge.current,a=ur(t,a));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u!==f||d!==a)&&rf(t,s,r,a),At=!1,d=t.memoizedState,s.state=d,Di(t,r,s,o);var y=t.memoizedState;u!==f||d!==y||Pe.current||At?(typeof m=="function"&&(Ju(t,n,m,r),y=t.memoizedState),(l=At||nf(t,n,l,r,d,y,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=l):(typeof s.componentDidUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return ra(e,t,n,r,i,o)}function ra(e,t,n,r,o,i){_h(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Yc(t,n,!1),Nt(e,t,i);r=t.stateNode,Ky.current=t;var u=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=lr(t,e.child,null,i),t.child=lr(t,null,u,i)):Ee(e,t,u,i),t.memoizedState=r.state,o&&Yc(t,n,!0),t.child}function Sh(e){var t=e.stateNode;t.pendingContext?Xc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Xc(e,t.context,!1),Sl(e,t.containerInfo)}function ff(e,t,n,r,o){return ar(),ml(o),t.flags|=256,Ee(e,t,n,r),t.child}var oa={dehydrated:null,treeContext:null,retryLane:0};function ia(e){return{baseLanes:e,cachePool:null,transitions:null}}function wh(e,t,n){var r=t.pendingProps,o=Y.current,i=!1,s=(t.flags&128)!==0,u;if((u=s)||(u=e!==null&&e.memoizedState===null?!1:(o&2)!==0),u?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),j(Y,o&1),e===null)return Zu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=cs(s,r,0,null),e=gn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ia(n),t.memoizedState=oa,e):Pl(t,s));if(o=e.memoizedState,o!==null&&(u=o.dehydrated,u!==null))return Zy(e,t,s,r,u,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,u=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Xt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),u!==null?i=Xt(u,i):(i=gn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?ia(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=oa,r}return i=e.child,e=i.sibling,r=Xt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Pl(e,t){return t=cs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Go(e,t,n,r){return r!==null&&ml(r),lr(t,e.child,null,n),e=Pl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Zy(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=uu(Error(_(422))),Go(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=cs({mode:"visible",children:r.children},o,0,null),i=gn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&lr(t,e.child,null,s),t.child.memoizedState=ia(s),t.memoizedState=oa,i);if(!(t.mode&1))return Go(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var u=r.dgst;return r=u,i=Error(_(419)),r=uu(i,r,void 0),Go(e,t,s,r)}if(u=(s&e.childLanes)!==0,Ne||u){if(r=se,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,kt(e,o),ot(r,e,o,-1))}return Dl(),r=uu(Error(_(421))),Go(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=cv.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ae=zt(o.nextSibling),Me=t,W=!0,nt=null,e!==null&&(ze[Ge++]=_t,ze[Ge++]=St,ze[Ge++]=En,_t=e.id,St=e.overflow,En=t),t=Pl(t,r.children),t.flags|=4096,t)}function df(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),qu(e.return,t,n)}function au(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function xh(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ee(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&df(e,n,t);else if(e.tag===19)df(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(j(Y,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Mi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),au(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Mi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}au(t,!0,n,null,i);break;case"together":au(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function di(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Nt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Sn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Xt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Xt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qy(e,t,n){switch(t.tag){case 3:Sh(t),ar();break;case 5:Qp(t);break;case 1:Re(t.type)&&Ri(t);break;case 4:Sl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;j(Ai,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(j(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?wh(e,t,n):(j(Y,Y.current&1),e=Nt(e,t,n),e!==null?e.sibling:null);j(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return xh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),j(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,Eh(e,t,n)}return Nt(e,t,n)}var Th,sa,Ch,Ih;Th=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};sa=function(){};Ch=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,pn(dt.current);var i=null;switch(n){case"input":o=Nu(e,o),r=Nu(e,r),i=[];break;case"select":o=K({},o,{value:void 0}),r=K({},r,{value:void 0}),i=[];break;case"textarea":o=Ou(e,o),r=Ou(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ni)}Au(n,r);var s;n=null;for(l in o)if(!r.hasOwnProperty(l)&&o.hasOwnProperty(l)&&o[l]!=null)if(l==="style"){var u=o[l];for(s in u)u.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else l!=="dangerouslySetInnerHTML"&&l!=="children"&&l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Kr.hasOwnProperty(l)?i||(i=[]):(i=i||[]).push(l,null));for(l in r){var a=r[l];if(u=o!=null?o[l]:void 0,r.hasOwnProperty(l)&&a!==u&&(a!=null||u!=null))if(l==="style")if(u){for(s in u)!u.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&u[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(l,n)),n=a;else l==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,u=u?u.__html:void 0,a!=null&&u!==a&&(i=i||[]).push(l,a)):l==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(l,""+a):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&(Kr.hasOwnProperty(l)?(a!=null&&l==="onScroll"&&z("scroll",e),i||u===a||(i=[])):(i=i||[]).push(l,a))}n&&(i=i||[]).push("style",n);var l=i;(t.updateQueue=l)&&(t.flags|=4)}};Ih=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ar(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Jy(e,t,n){var r=t.pendingProps;switch(hl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Re(t.type)&&Pi(),de(t),null;case 3:return r=t.stateNode,cr(),V(Pe),V(ge),xl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(jo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,nt!==null&&(ha(nt),nt=null))),sa(e,t),de(t),null;case 5:wl(t);var o=pn(ao.current);if(n=t.type,e!==null&&t.stateNode!=null)Ch(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return de(t),null}if(e=pn(dt.current),jo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[lt]=t,r[so]=i,e=(t.mode&1)!==0,n){case"dialog":z("cancel",r),z("close",r);break;case"iframe":case"object":case"embed":z("load",r);break;case"video":case"audio":for(o=0;o<Br.length;o++)z(Br[o],r);break;case"source":z("error",r);break;case"img":case"image":case"link":z("error",r),z("load",r);break;case"details":z("toggle",r);break;case"input":_c(r,i),z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},z("invalid",r);break;case"textarea":wc(r,i),z("invalid",r)}Au(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var u=i[s];s==="children"?typeof u=="string"?r.textContent!==u&&(i.suppressHydrationWarning!==!0&&$o(r.textContent,u,e),o=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(i.suppressHydrationWarning!==!0&&$o(r.textContent,u,e),o=["children",""+u]):Kr.hasOwnProperty(s)&&u!=null&&s==="onScroll"&&z("scroll",r)}switch(n){case"input":Ao(r),Sc(r,i,!0);break;case"textarea":Ao(r),xc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ni)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ep(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[lt]=t,e[so]=r,Th(e,t,!1,!1),t.stateNode=e;e:{switch(s=bu(n,r),n){case"dialog":z("cancel",e),z("close",e),o=r;break;case"iframe":case"object":case"embed":z("load",e),o=r;break;case"video":case"audio":for(o=0;o<Br.length;o++)z(Br[o],e);o=r;break;case"source":z("error",e),o=r;break;case"img":case"image":case"link":z("error",e),z("load",e),o=r;break;case"details":z("toggle",e),o=r;break;case"input":_c(e,r),o=Nu(e,r),z("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=K({},r,{value:void 0}),z("invalid",e);break;case"textarea":wc(e,r),o=Ou(e,r),z("invalid",e);break;default:o=r}Au(n,o),u=o;for(i in u)if(u.hasOwnProperty(i)){var a=u[i];i==="style"?rp(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&tp(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Zr(e,a):typeof a=="number"&&Zr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Kr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&z("scroll",e):a!=null&&Ja(e,i,a,s))}switch(n){case"input":Ao(e),Sc(e,r,!1);break;case"textarea":Ao(e),xc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Qn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Qn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ni)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Ih(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=pn(ao.current),pn(dt.current),jo(t)){if(r=t.stateNode,n=t.memoizedProps,r[lt]=t,(i=r.nodeValue!==n)&&(e=Me,e!==null))switch(e.tag){case 3:$o(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&$o(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[lt]=t,t.stateNode=r}return de(t),null;case 13:if(V(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ae!==null&&t.mode&1&&!(t.flags&128))Gp(),ar(),t.flags|=98560,i=!1;else if(i=jo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(_(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(_(317));i[lt]=t}else ar(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),i=!1}else nt!==null&&(ha(nt),nt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?re===0&&(re=3):Dl())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return cr(),sa(e,t),e===null&&oo(t.stateNode.containerInfo),de(t),null;case 10:return vl(t.type._context),de(t),null;case 17:return Re(t.type)&&Pi(),de(t),null;case 19:if(V(Y),i=t.memoizedState,i===null)return de(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Ar(i,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Mi(e),s!==null){for(t.flags|=128,Ar(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return j(Y,Y.current&1|2),t.child}e=e.sibling}i.tail!==null&&J()>dr&&(t.flags|=128,r=!0,Ar(i,!1),t.lanes=4194304)}else{if(!r)if(e=Mi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ar(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!W)return de(t),null}else 2*J()-i.renderingStartTime>dr&&n!==1073741824&&(t.flags|=128,r=!0,Ar(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=J(),t.sibling=null,n=Y.current,j(Y,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return bl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function ev(e,t){switch(hl(t),t.tag){case 1:return Re(t.type)&&Pi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return cr(),V(Pe),V(ge),xl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return wl(t),null;case 13:if(V(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));ar()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(Y),null;case 4:return cr(),null;case 10:return vl(t.type._context),null;case 22:case 23:return bl(),null;case 24:return null;default:return null}}var Vo=!1,pe=!1,tv=typeof WeakSet=="function"?WeakSet:Set,T=null;function Xn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function ua(e,t,n){try{n()}catch(r){Z(e,t,r)}}var pf=!1;function nv(e,t){if(Gu=Ci,e=Op(),dl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,u=-1,a=-1,l=0,c=0,f=e,d=null;t:for(;;){for(var m;f!==n||o!==0&&f.nodeType!==3||(u=s+o),f!==i||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===e)break t;if(d===n&&++l===o&&(u=s),d===i&&++c===r&&(a=s),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=u===-1||a===-1?null:{start:u,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Vu={focusedElem:e,selectionRange:n},Ci=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:et(t.type,v),S);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(E){Z(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return y=pf,pf=!1,y}function Xr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&ua(t,n,i)}o=o.next}while(o!==r)}}function as(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function aa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function kh(e){var t=e.alternate;t!==null&&(e.alternate=null,kh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[lt],delete t[so],delete t[Yu],delete t[Hy],delete t[By])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Nh(e){return e.tag===5||e.tag===3||e.tag===4}function hf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Nh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function la(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ni));else if(r!==4&&(e=e.child,e!==null))for(la(e,t,n),e=e.sibling;e!==null;)la(e,t,n),e=e.sibling}function ca(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ca(e,t,n),e=e.sibling;e!==null;)ca(e,t,n),e=e.sibling}var ae=null,tt=!1;function Ot(e,t,n){for(n=n.child;n!==null;)Ph(e,t,n),n=n.sibling}function Ph(e,t,n){if(ft&&typeof ft.onCommitFiberUnmount=="function")try{ft.onCommitFiberUnmount(es,n)}catch{}switch(n.tag){case 5:pe||Xn(n,t);case 6:var r=ae,o=tt;ae=null,Ot(e,t,n),ae=r,tt=o,ae!==null&&(tt?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(tt?(e=ae,n=n.stateNode,e.nodeType===8?tu(e.parentNode,n):e.nodeType===1&&tu(e,n),to(e)):tu(ae,n.stateNode));break;case 4:r=ae,o=tt,ae=n.stateNode.containerInfo,tt=!0,Ot(e,t,n),ae=r,tt=o;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&ua(n,t,s),o=o.next}while(o!==r)}Ot(e,t,n);break;case 1:if(!pe&&(Xn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){Z(n,t,u)}Ot(e,t,n);break;case 21:Ot(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,Ot(e,t,n),pe=r):Ot(e,t,n);break;default:Ot(e,t,n)}}function mf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new tv),t.forEach(function(r){var o=fv.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Je(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,u=s;e:for(;u!==null;){switch(u.tag){case 5:ae=u.stateNode,tt=!1;break e;case 3:ae=u.stateNode.containerInfo,tt=!0;break e;case 4:ae=u.stateNode.containerInfo,tt=!0;break e}u=u.return}if(ae===null)throw Error(_(160));Ph(i,s,o),ae=null,tt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(l){Z(o,t,l)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Rh(t,e),t=t.sibling}function Rh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Je(t,e),ut(e),r&4){try{Xr(3,e,e.return),as(3,e)}catch(v){Z(e,e.return,v)}try{Xr(5,e,e.return)}catch(v){Z(e,e.return,v)}}break;case 1:Je(t,e),ut(e),r&512&&n!==null&&Xn(n,n.return);break;case 5:if(Je(t,e),ut(e),r&512&&n!==null&&Xn(n,n.return),e.flags&32){var o=e.stateNode;try{Zr(o,"")}catch(v){Z(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,u=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{u==="input"&&i.type==="radio"&&i.name!=null&&qd(o,i),bu(u,s);var l=bu(u,i);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?rp(o,f):c==="dangerouslySetInnerHTML"?tp(o,f):c==="children"?Zr(o,f):Ja(o,c,f,l)}switch(u){case"input":Pu(o,i);break;case"textarea":Jd(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?Qn(o,!!i.multiple,m,!1):d!==!!i.multiple&&(i.defaultValue!=null?Qn(o,!!i.multiple,i.defaultValue,!0):Qn(o,!!i.multiple,i.multiple?[]:"",!1))}o[so]=i}catch(v){Z(e,e.return,v)}}break;case 6:if(Je(t,e),ut(e),r&4){if(e.stateNode===null)throw Error(_(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){Z(e,e.return,v)}}break;case 3:if(Je(t,e),ut(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{to(t.containerInfo)}catch(v){Z(e,e.return,v)}break;case 4:Je(t,e),ut(e);break;case 13:Je(t,e),ut(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Ll=J())),r&4&&mf(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(l=pe)||c,Je(t,e),pe=l):Je(t,e),ut(e),r&8192){if(l=e.memoizedState!==null,(e.stateNode.isHidden=l)&&!c&&e.mode&1)for(T=e,c=e.child;c!==null;){for(f=T=c;T!==null;){switch(d=T,m=d.child,d.tag){case 0:case 11:case 14:case 15:Xr(4,d,d.return);break;case 1:Xn(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){Z(r,n,v)}}break;case 5:Xn(d,d.return);break;case 22:if(d.memoizedState!==null){yf(f);continue}}m!==null?(m.return=d,T=m):yf(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,l?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(u=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,u.style.display=np("display",s))}catch(v){Z(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=l?"":f.memoizedProps}catch(v){Z(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Je(t,e),ut(e),r&4&&mf(e);break;case 21:break;default:Je(t,e),ut(e)}}function ut(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Nh(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Zr(o,""),r.flags&=-33);var i=hf(e);ca(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,u=hf(e);la(e,u,s);break;default:throw Error(_(161))}}catch(a){Z(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function rv(e,t,n){T=e,Oh(e)}function Oh(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var o=T,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Vo;if(!s){var u=o.alternate,a=u!==null&&u.memoizedState!==null||pe;u=Vo;var l=pe;if(Vo=s,(pe=a)&&!l)for(T=o;T!==null;)s=T,a=s.child,s.tag===22&&s.memoizedState!==null?vf(o):a!==null?(a.return=s,T=a):vf(o);for(;i!==null;)T=i,Oh(i),i=i.sibling;T=o,Vo=u,pe=l}gf(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,T=i):gf(e)}}function gf(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||as(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:et(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Jc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Jc(t,s,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var l=t.alternate;if(l!==null){var c=l.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&to(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}pe||t.flags&512&&aa(t)}catch(d){Z(t,t.return,d)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function yf(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function vf(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{as(4,t)}catch(a){Z(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){Z(t,o,a)}}var i=t.return;try{aa(t)}catch(a){Z(t,i,a)}break;case 5:var s=t.return;try{aa(t)}catch(a){Z(t,s,a)}}}catch(a){Z(t,t.return,a)}if(t===e){T=null;break}var u=t.sibling;if(u!==null){u.return=t.return,T=u;break}T=t.return}}var ov=Math.ceil,Bi=Rt.ReactCurrentDispatcher,Rl=Rt.ReactCurrentOwner,Xe=Rt.ReactCurrentBatchConfig,M=0,se=null,ee=null,le=0,Le=0,Yn=en(0),re=0,po=null,Sn=0,ls=0,Ol=0,Yr=null,ke=null,Ll=0,dr=1/0,gt=null,Ui=!1,fa=null,Vt=null,Wo=!1,Bt=null,$i=0,Qr=0,da=null,pi=-1,hi=0;function xe(){return M&6?J():pi!==-1?pi:pi=J()}function Wt(e){return e.mode&1?M&2&&le!==0?le&-le:$y.transition!==null?(hi===0&&(hi=mp()),hi):(e=H,e!==0||(e=window.event,e=e===void 0?16:wp(e.type)),e):1}function ot(e,t,n,r){if(50<Qr)throw Qr=0,da=null,Error(_(185));vo(e,n,r),(!(M&2)||e!==se)&&(e===se&&(!(M&2)&&(ls|=n),re===4&&Ft(e,le)),Oe(e,r),n===1&&M===0&&!(t.mode&1)&&(dr=J()+500,is&&tn()))}function Oe(e,t){var n=e.callbackNode;$0(e,t);var r=Ti(e,e===se?le:0);if(r===0)n!==null&&Ic(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ic(n),t===1)e.tag===0?Uy(Ef.bind(null,e)):$p(Ef.bind(null,e)),My(function(){!(M&6)&&tn()}),n=null;else{switch(gp(r)){case 1:n=ol;break;case 4:n=pp;break;case 16:n=xi;break;case 536870912:n=hp;break;default:n=xi}n=Bh(n,Lh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Lh(e,t){if(pi=-1,hi=0,M&6)throw Error(_(327));var n=e.callbackNode;if(er()&&e.callbackNode!==n)return null;var r=Ti(e,e===se?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ji(e,r);else{t=r;var o=M;M|=2;var i=bh();(se!==e||le!==t)&&(gt=null,dr=J()+500,mn(e,t));do try{uv();break}catch(u){Ah(e,u)}while(!0);yl(),Bi.current=i,M=o,ee!==null?t=0:(se=null,le=0,t=re)}if(t!==0){if(t===2&&(o=Bu(e),o!==0&&(r=o,t=pa(e,o))),t===1)throw n=po,mn(e,0),Ft(e,r),Oe(e,J()),n;if(t===6)Ft(e,r);else{if(o=e.current.alternate,!(r&30)&&!iv(o)&&(t=ji(e,r),t===2&&(i=Bu(e),i!==0&&(r=i,t=pa(e,i))),t===1))throw n=po,mn(e,0),Ft(e,r),Oe(e,J()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:cn(e,ke,gt);break;case 3:if(Ft(e,r),(r&130023424)===r&&(t=Ll+500-J(),10<t)){if(Ti(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Xu(cn.bind(null,e,ke,gt),t);break}cn(e,ke,gt);break;case 4:if(Ft(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-rt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ov(r/1960))-r,10<r){e.timeoutHandle=Xu(cn.bind(null,e,ke,gt),r);break}cn(e,ke,gt);break;case 5:cn(e,ke,gt);break;default:throw Error(_(329))}}}return Oe(e,J()),e.callbackNode===n?Lh.bind(null,e):null}function pa(e,t){var n=Yr;return e.current.memoizedState.isDehydrated&&(mn(e,t).flags|=256),e=ji(e,t),e!==2&&(t=ke,ke=n,t!==null&&ha(t)),e}function ha(e){ke===null?ke=e:ke.push.apply(ke,e)}function iv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!st(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ft(e,t){for(t&=~Ol,t&=~ls,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-rt(t),r=1<<n;e[n]=-1,t&=~r}}function Ef(e){if(M&6)throw Error(_(327));er();var t=Ti(e,0);if(!(t&1))return Oe(e,J()),null;var n=ji(e,t);if(e.tag!==0&&n===2){var r=Bu(e);r!==0&&(t=r,n=pa(e,r))}if(n===1)throw n=po,mn(e,0),Ft(e,t),Oe(e,J()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,cn(e,ke,gt),Oe(e,J()),null}function Al(e,t){var n=M;M|=1;try{return e(t)}finally{M=n,M===0&&(dr=J()+500,is&&tn())}}function wn(e){Bt!==null&&Bt.tag===0&&!(M&6)&&er();var t=M;M|=1;var n=Xe.transition,r=H;try{if(Xe.transition=null,H=1,e)return e()}finally{H=r,Xe.transition=n,M=t,!(M&6)&&tn()}}function bl(){Le=Yn.current,V(Yn)}function mn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Dy(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(hl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Pi();break;case 3:cr(),V(Pe),V(ge),xl();break;case 5:wl(r);break;case 4:cr();break;case 13:V(Y);break;case 19:V(Y);break;case 10:vl(r.type._context);break;case 22:case 23:bl()}n=n.return}if(se=e,ee=e=Xt(e.current,null),le=Le=t,re=0,po=null,Ol=ls=Sn=0,ke=Yr=null,dn!==null){for(t=0;t<dn.length;t++)if(n=dn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}dn=null}return e}function Ah(e,t){do{var n=ee;try{if(yl(),ci.current=Hi,Fi){for(var r=Q.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Fi=!1}if(_n=0,ie=ne=Q=null,Wr=!1,lo=0,Rl.current=null,n===null||n.return===null){re=1,po=t,ee=null;break}e:{var i=e,s=n.return,u=n,a=t;if(t=le,u.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var l=a,c=u,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=sf(s);if(m!==null){m.flags&=-257,uf(m,s,u,i,t),m.mode&1&&of(i,l,t),t=m,a=l;var y=t.updateQueue;if(y===null){var v=new Set;v.add(a),t.updateQueue=v}else y.add(a);break e}else{if(!(t&1)){of(i,l,t),Dl();break e}a=Error(_(426))}}else if(W&&u.mode&1){var S=sf(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),uf(S,s,u,i,t),ml(fr(a,u));break e}}i=a=fr(a,u),re!==4&&(re=2),Yr===null?Yr=[i]:Yr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=gh(i,a,t);qc(i,h);break e;case 1:u=a;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Vt===null||!Vt.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=yh(i,u,t);qc(i,E);break e}}i=i.return}while(i!==null)}Mh(n)}catch(w){t=w,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function bh(){var e=Bi.current;return Bi.current=Hi,e===null?Hi:e}function Dl(){(re===0||re===3||re===2)&&(re=4),se===null||!(Sn&268435455)&&!(ls&268435455)||Ft(se,le)}function ji(e,t){var n=M;M|=2;var r=bh();(se!==e||le!==t)&&(gt=null,mn(e,t));do try{sv();break}catch(o){Ah(e,o)}while(!0);if(yl(),M=n,Bi.current=r,ee!==null)throw Error(_(261));return se=null,le=0,re}function sv(){for(;ee!==null;)Dh(ee)}function uv(){for(;ee!==null&&!L0();)Dh(ee)}function Dh(e){var t=Hh(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?Mh(e):ee=t,Rl.current=null}function Mh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ev(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,ee=null;return}}else if(n=Jy(n,t,Le),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);re===0&&(re=5)}function cn(e,t,n){var r=H,o=Xe.transition;try{Xe.transition=null,H=1,av(e,t,n,r)}finally{Xe.transition=o,H=r}return null}function av(e,t,n,r){do er();while(Bt!==null);if(M&6)throw Error(_(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(j0(e,i),e===se&&(ee=se=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Wo||(Wo=!0,Bh(xi,function(){return er(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Xe.transition,Xe.transition=null;var s=H;H=1;var u=M;M|=4,Rl.current=null,nv(e,n),Rh(n,e),Ny(Vu),Ci=!!Gu,Vu=Gu=null,e.current=n,rv(n),A0(),M=u,H=s,Xe.transition=i}else e.current=n;if(Wo&&(Wo=!1,Bt=e,$i=o),i=e.pendingLanes,i===0&&(Vt=null),M0(n.stateNode),Oe(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ui)throw Ui=!1,e=fa,fa=null,e;return $i&1&&e.tag!==0&&er(),i=e.pendingLanes,i&1?e===da?Qr++:(Qr=0,da=e):Qr=0,tn(),null}function er(){if(Bt!==null){var e=gp($i),t=Xe.transition,n=H;try{if(Xe.transition=null,H=16>e?16:e,Bt===null)var r=!1;else{if(e=Bt,Bt=null,$i=0,M&6)throw Error(_(331));var o=M;for(M|=4,T=e.current;T!==null;){var i=T,s=i.child;if(T.flags&16){var u=i.deletions;if(u!==null){for(var a=0;a<u.length;a++){var l=u[a];for(T=l;T!==null;){var c=T;switch(c.tag){case 0:case 11:case 15:Xr(8,c,i)}var f=c.child;if(f!==null)f.return=c,T=f;else for(;T!==null;){c=T;var d=c.sibling,m=c.return;if(kh(c),c===l){T=null;break}if(d!==null){d.return=m,T=d;break}T=m}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}T=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,T=s;else e:for(;T!==null;){if(i=T,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Xr(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,T=h;break e}T=i.return}}var p=e.current;for(T=p;T!==null;){s=T;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,T=g;else e:for(s=p;T!==null;){if(u=T,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:as(9,u)}}catch(w){Z(u,u.return,w)}if(u===s){T=null;break e}var E=u.sibling;if(E!==null){E.return=u.return,T=E;break e}T=u.return}}if(M=o,tn(),ft&&typeof ft.onPostCommitFiberRoot=="function")try{ft.onPostCommitFiberRoot(es,e)}catch{}r=!0}return r}finally{H=n,Xe.transition=t}}return!1}function _f(e,t,n){t=fr(n,t),t=gh(e,t,1),e=Gt(e,t,1),t=xe(),e!==null&&(vo(e,1,t),Oe(e,t))}function Z(e,t,n){if(e.tag===3)_f(e,e,n);else for(;t!==null;){if(t.tag===3){_f(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Vt===null||!Vt.has(r))){e=fr(n,e),e=yh(t,e,1),t=Gt(t,e,1),e=xe(),t!==null&&(vo(t,1,e),Oe(t,e));break}}t=t.return}}function lv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(le&n)===n&&(re===4||re===3&&(le&130023424)===le&&500>J()-Ll?mn(e,0):Ol|=n),Oe(e,t)}function Fh(e,t){t===0&&(e.mode&1?(t=Mo,Mo<<=1,!(Mo&130023424)&&(Mo=4194304)):t=1);var n=xe();e=kt(e,t),e!==null&&(vo(e,t,n),Oe(e,n))}function cv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Fh(e,n)}function fv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),Fh(e,n)}var Hh;Hh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)Ne=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ne=!1,qy(e,t,n);Ne=!!(e.flags&131072)}else Ne=!1,W&&t.flags&1048576&&jp(t,Li,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;di(e,t),e=t.pendingProps;var o=ur(t,ge.current);Jn(t,n),o=Cl(null,t,r,e,o,n);var i=Il();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Re(r)?(i=!0,Ri(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,_l(t),o.updater=us,t.stateNode=o,o._reactInternals=t,ea(t,r,e,n),t=ra(null,t,r,!0,i,n)):(t.tag=0,W&&i&&pl(t),Ee(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(di(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=pv(r),e=et(r,e),o){case 0:t=na(null,t,r,e,n);break e;case 1:t=cf(null,t,r,e,n);break e;case 11:t=af(null,t,r,e,n);break e;case 14:t=lf(null,t,r,et(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),na(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),cf(e,t,r,o,n);case 3:e:{if(Sh(t),e===null)throw Error(_(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Yp(e,t),Di(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=fr(Error(_(423)),t),t=ff(e,t,r,n,o);break e}else if(r!==o){o=fr(Error(_(424)),t),t=ff(e,t,r,n,o);break e}else for(Ae=zt(t.stateNode.containerInfo.firstChild),Me=t,W=!0,nt=null,n=Wp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ar(),r===o){t=Nt(e,t,n);break e}Ee(e,t,r,n)}t=t.child}return t;case 5:return Qp(t),e===null&&Zu(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Wu(r,o)?s=null:i!==null&&Wu(r,i)&&(t.flags|=32),_h(e,t),Ee(e,t,s,n),t.child;case 6:return e===null&&Zu(t),null;case 13:return wh(e,t,n);case 4:return Sl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=lr(t,null,r,n):Ee(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),af(e,t,r,o,n);case 7:return Ee(e,t,t.pendingProps,n),t.child;case 8:return Ee(e,t,t.pendingProps.children,n),t.child;case 12:return Ee(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,j(Ai,r._currentValue),r._currentValue=s,i!==null)if(st(i.value,s)){if(i.children===o.children&&!Pe.current){t=Nt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var u=i.dependencies;if(u!==null){s=i.child;for(var a=u.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=wt(-1,n&-n),a.tag=2;var l=i.updateQueue;if(l!==null){l=l.shared;var c=l.pending;c===null?a.next=a:(a.next=c.next,c.next=a),l.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),qu(i.return,n,t),u.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(_(341));s.lanes|=n,u=s.alternate,u!==null&&(u.lanes|=n),qu(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Ee(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Jn(t,n),o=Ye(o),r=r(o),t.flags|=1,Ee(e,t,r,n),t.child;case 14:return r=t.type,o=et(r,t.pendingProps),o=et(r.type,o),lf(e,t,r,o,n);case 15:return vh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),di(e,t),t.tag=1,Re(r)?(e=!0,Ri(t)):e=!1,Jn(t,n),mh(t,r,o),ea(t,r,o,n),ra(null,t,r,!0,e,n);case 19:return xh(e,t,n);case 22:return Eh(e,t,n)}throw Error(_(156,t.tag))};function Bh(e,t){return dp(e,t)}function dv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function We(e,t,n,r){return new dv(e,t,n,r)}function Ml(e){return e=e.prototype,!(!e||!e.isReactComponent)}function pv(e){if(typeof e=="function")return Ml(e)?1:0;if(e!=null){if(e=e.$$typeof,e===tl)return 11;if(e===nl)return 14}return 2}function Xt(e,t){var n=e.alternate;return n===null?(n=We(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function mi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Ml(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Hn:return gn(n.children,o,i,t);case el:s=8,o|=8;break;case Tu:return e=We(12,n,t,o|2),e.elementType=Tu,e.lanes=i,e;case Cu:return e=We(13,n,t,o),e.elementType=Cu,e.lanes=i,e;case Iu:return e=We(19,n,t,o),e.elementType=Iu,e.lanes=i,e;case Qd:return cs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Xd:s=10;break e;case Yd:s=9;break e;case tl:s=11;break e;case nl:s=14;break e;case Lt:s=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=We(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function gn(e,t,n,r){return e=We(7,e,r,t),e.lanes=n,e}function cs(e,t,n,r){return e=We(22,e,r,t),e.elementType=Qd,e.lanes=n,e.stateNode={isHidden:!1},e}function lu(e,t,n){return e=We(6,e,null,t),e.lanes=n,e}function cu(e,t,n){return t=We(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function hv(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Gs(0),this.expirationTimes=Gs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Gs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Fl(e,t,n,r,o,i,s,u,a){return e=new hv(e,t,n,u,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=We(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},_l(i),e}function mv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Fn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Uh(e){if(!e)return Kt;e=e._reactInternals;e:{if(Rn(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Re(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(Re(n))return Up(e,n,t)}return t}function $h(e,t,n,r,o,i,s,u,a){return e=Fl(n,r,!0,e,o,i,s,u,a),e.context=Uh(null),n=e.current,r=xe(),o=Wt(n),i=wt(r,o),i.callback=t??null,Gt(n,i,o),e.current.lanes=o,vo(e,o,r),Oe(e,r),e}function fs(e,t,n,r){var o=t.current,i=xe(),s=Wt(o);return n=Uh(n),t.context===null?t.context=n:t.pendingContext=n,t=wt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Gt(o,t,s),e!==null&&(ot(e,o,s,i),li(e,o,s)),s}function zi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Sf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Hl(e,t){Sf(e,t),(e=e.alternate)&&Sf(e,t)}function gv(){return null}var jh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Bl(e){this._internalRoot=e}ds.prototype.render=Bl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));fs(e,t,null,null)};ds.prototype.unmount=Bl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wn(function(){fs(null,e,null,null)}),t[It]=null}};function ds(e){this._internalRoot=e}ds.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ep();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Mt.length&&t!==0&&t<Mt[n].priority;n++);Mt.splice(n,0,e),n===0&&Sp(e)}};function Ul(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ps(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function wf(){}function yv(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var l=zi(s);i.call(l)}}var s=$h(t,r,e,0,null,!1,!1,"",wf);return e._reactRootContainer=s,e[It]=s.current,oo(e.nodeType===8?e.parentNode:e),wn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var u=r;r=function(){var l=zi(a);u.call(l)}}var a=Fl(e,0,!1,null,null,!1,!1,"",wf);return e._reactRootContainer=a,e[It]=a.current,oo(e.nodeType===8?e.parentNode:e),wn(function(){fs(t,a,n,r)}),a}function hs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var u=o;o=function(){var a=zi(s);u.call(a)}}fs(t,s,e,o)}else s=yv(n,t,e,o,r);return zi(s)}yp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Hr(t.pendingLanes);n!==0&&(il(t,n|1),Oe(t,J()),!(M&6)&&(dr=J()+500,tn()))}break;case 13:wn(function(){var r=kt(e,1);if(r!==null){var o=xe();ot(r,e,1,o)}}),Hl(e,1)}};sl=function(e){if(e.tag===13){var t=kt(e,134217728);if(t!==null){var n=xe();ot(t,e,134217728,n)}Hl(e,134217728)}};vp=function(e){if(e.tag===13){var t=Wt(e),n=kt(e,t);if(n!==null){var r=xe();ot(n,e,t,r)}Hl(e,t)}};Ep=function(){return H};_p=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};Mu=function(e,t,n){switch(t){case"input":if(Pu(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=os(r);if(!o)throw Error(_(90));Zd(r),Pu(r,o)}}}break;case"textarea":Jd(e,n);break;case"select":t=n.value,t!=null&&Qn(e,!!n.multiple,t,!1)}};sp=Al;up=wn;var vv={usingClientEntryPoint:!1,Events:[_o,jn,os,op,ip,Al]},br={findFiberByHostInstance:fn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ev={bundleType:br.bundleType,version:br.version,rendererPackageName:br.rendererPackageName,rendererConfig:br.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Rt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=cp(e),e===null?null:e.stateNode},findFiberByHostInstance:br.findFiberByHostInstance||gv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xo.isDisabled&&Xo.supportsFiber)try{es=Xo.inject(Ev),ft=Xo}catch{}}Be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vv;Be.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ul(t))throw Error(_(200));return mv(e,t,null,n)};Be.createRoot=function(e,t){if(!Ul(e))throw Error(_(299));var n=!1,r="",o=jh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Fl(e,1,!1,null,null,n,!1,r,o),e[It]=t.current,oo(e.nodeType===8?e.parentNode:e),new Bl(t)};Be.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=cp(t),e=e===null?null:e.stateNode,e};Be.flushSync=function(e){return wn(e)};Be.hydrate=function(e,t,n){if(!ps(t))throw Error(_(200));return hs(null,e,t,!0,n)};Be.hydrateRoot=function(e,t,n){if(!Ul(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=jh;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=$h(t,null,e,1,n??null,o,!1,i,s),e[It]=t.current,oo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ds(t)};Be.render=function(e,t,n){if(!ps(t))throw Error(_(200));return hs(null,e,t,!1,n)};Be.unmountComponentAtNode=function(e){if(!ps(e))throw Error(_(40));return e._reactRootContainer?(wn(function(){hs(null,null,e,!1,function(){e._reactRootContainer=null,e[It]=null})}),!0):!1};Be.unstable_batchedUpdates=Al;Be.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ps(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return hs(e,t,n,!1,r)};Be.version="18.3.1-next-f1338f8080-20240426";function zh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(zh)}catch(e){console.error(e)}}zh(),zd.exports=Be;var _v=zd.exports,Gh,xf=_v;Gh=xf.createRoot,xf.hydrateRoot;process.platform;process.platform;process.platform;process.type;process.type;var ma=function(e,t){return ma=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])},ma(e,t)};function Ke(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");ma(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}var k=function(){return k=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},k.apply(this,arguments)};function ms(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function we(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}function _e(e,t){var n=t&&t.cache?t.cache:Iv,r=t&&t.serializer?t.serializer:Cv,o=t&&t.strategy?t.strategy:wv;return o(e,{cache:n,serializer:r})}function Sv(e){return e==null||typeof e=="number"||typeof e=="boolean"}function Vh(e,t,n,r){var o=Sv(r)?r:n(r),i=t.get(o);return typeof i>"u"&&(i=e.call(this,r),t.set(o,i)),i}function Wh(e,t,n){var r=Array.prototype.slice.call(arguments,3),o=n(r),i=t.get(o);return typeof i>"u"&&(i=e.apply(this,r),t.set(o,i)),i}function $l(e,t,n,r,o){return n.bind(t,e,r,o)}function wv(e,t){var n=e.length===1?Vh:Wh;return $l(e,this,n,t.cache.create(),t.serializer)}function xv(e,t){return $l(e,this,Wh,t.cache.create(),t.serializer)}function Tv(e,t){return $l(e,this,Vh,t.cache.create(),t.serializer)}var Cv=function(){return JSON.stringify(arguments)};function jl(){this.cache=Object.create(null)}jl.prototype.get=function(e){return this.cache[e]};jl.prototype.set=function(e,t){this.cache[e]=t};var Iv={create:function(){return new jl}},Se={variadic:xv,monadic:Tv};function Xh(e,t,n){if(n===void 0&&(n=Error),!e)throw new n(t)}_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.Locale).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});_e(function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.ListFormat).bind.apply(e,we([void 0],t,!1)))},{strategy:Se.variadic});var b;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(b||(b={}));var G;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(G||(G={}));var pr;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(pr||(pr={}));function Tf(e){return e.type===G.literal}function kv(e){return e.type===G.argument}function Yh(e){return e.type===G.number}function Qh(e){return e.type===G.date}function Kh(e){return e.type===G.time}function Zh(e){return e.type===G.select}function qh(e){return e.type===G.plural}function Nv(e){return e.type===G.pound}function Jh(e){return e.type===G.tag}function em(e){return!!(e&&typeof e=="object"&&e.type===pr.number)}function ga(e){return!!(e&&typeof e=="object"&&e.type===pr.dateTime)}var tm=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,Pv=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function Rv(e){var t={};return e.replace(Pv,function(n){var r=n.length;switch(n[0]){case"G":t.era=r===4?"long":r===5?"narrow":"short";break;case"y":t.year=r===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=r===4?"long":r===5?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var Ov=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Lv(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(Ov).filter(function(d){return d.length>0}),n=[],r=0,o=t;r<o.length;r++){var i=o[r],s=i.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var u=s[0],a=s.slice(1),l=0,c=a;l<c.length;l++){var f=c[l];if(f.length===0)throw new Error("Invalid number skeleton")}n.push({stem:u,options:a})}return n}function Av(e){return e.replace(/^(.*?)-/,"")}var Cf=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,nm=/^(@+)?(\+|#+)?[rs]?$/g,bv=/(\*)(0+)|(#+)(0+)|(0+)/g,rm=/^(0+)$/;function If(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(nm,function(n,r,o){return typeof o!="string"?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):o==="+"?t.minimumSignificantDigits=r.length:r[0]==="#"?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+(typeof o=="string"?o.length:0)),""}),t}function om(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Dv(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var n=e.slice(0,2);if(n==="+!"?(t.signDisplay="always",e=e.slice(2)):n==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!rm.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function kf(e){var t={},n=om(e);return n||t}function Mv(e){for(var t={},n=0,r=e;n<r.length;n++){var o=r[n];switch(o.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=o.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=Av(o.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=k(k(k({},t),{notation:"scientific"}),o.options.reduce(function(a,l){return k(k({},a),kf(l))},{}));continue;case"engineering":t=k(k(k({},t),{notation:"engineering"}),o.options.reduce(function(a,l){return k(k({},a),kf(l))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(o.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(o.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");o.options[0].replace(bv,function(a,l,c,f,d,m){if(l)t.minimumIntegerDigits=c.length;else{if(f&&d)throw new Error("We currently do not support maximum integer digits");if(m)throw new Error("We currently do not support exact integer digits")}return""});continue}if(rm.test(o.stem)){t.minimumIntegerDigits=o.stem.length;continue}if(Cf.test(o.stem)){if(o.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");o.stem.replace(Cf,function(a,l,c,f,d,m){return c==="*"?t.minimumFractionDigits=l.length:f&&f[0]==="#"?t.maximumFractionDigits=f.length:d&&m?(t.minimumFractionDigits=d.length,t.maximumFractionDigits=d.length+m.length):(t.minimumFractionDigits=l.length,t.maximumFractionDigits=l.length),""});var i=o.options[0];i==="w"?t=k(k({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=k(k({},t),If(i)));continue}if(nm.test(o.stem)){t=k(k({},t),If(o.stem));continue}var s=om(o.stem);s&&(t=k(k({},t),s));var u=Dv(o.stem);u&&(t=k(k({},t),u))}return t}var Yo={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Fv(e,t){for(var n="",r=0;r<e.length;r++){var o=e.charAt(r);if(o==="j"){for(var i=0;r+1<e.length&&e.charAt(r+1)===o;)i++,r++;var s=1+(i&1),u=i<2?1:3+(i>>1),a="a",l=Hv(t);for((l=="H"||l=="k")&&(u=0);u-- >0;)n+=a;for(;s-- >0;)n=l+n}else o==="J"?n+="H":n+=o}return n}function Hv(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n=e.language,r;n!=="root"&&(r=e.maximize().region);var o=Yo[r||""]||Yo[n||""]||Yo["".concat(n,"-001")]||Yo["001"];return o[0]}var fu,Bv=new RegExp("^".concat(tm.source,"*")),Uv=new RegExp("".concat(tm.source,"*$"));function D(e,t){return{start:e,end:t}}var $v=!!String.prototype.startsWith&&"_a".startsWith("a",1),jv=!!String.fromCodePoint,zv=!!Object.fromEntries,Gv=!!String.prototype.codePointAt,Vv=!!String.prototype.trimStart,Wv=!!String.prototype.trimEnd,Xv=!!Number.isSafeInteger,Yv=Xv?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},ya=!0;try{var Qv=sm("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");ya=((fu=Qv.exec("a"))===null||fu===void 0?void 0:fu[0])==="a"}catch{ya=!1}var Nf=$v?function(t,n,r){return t.startsWith(n,r)}:function(t,n,r){return t.slice(r,r+n.length)===n},va=jv?String.fromCodePoint:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var r="",o=t.length,i=0,s;o>i;){if(s=t[i++],s>1114111)throw RangeError(s+" is not a valid code point");r+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return r},Pf=zv?Object.fromEntries:function(t){for(var n={},r=0,o=t;r<o.length;r++){var i=o[r],s=i[0],u=i[1];n[s]=u}return n},im=Gv?function(t,n){return t.codePointAt(n)}:function(t,n){var r=t.length;if(!(n<0||n>=r)){var o=t.charCodeAt(n),i;return o<55296||o>56319||n+1===r||(i=t.charCodeAt(n+1))<56320||i>57343?o:(o-55296<<10)+(i-56320)+65536}},Kv=Vv?function(t){return t.trimStart()}:function(t){return t.replace(Bv,"")},Zv=Wv?function(t){return t.trimEnd()}:function(t){return t.replace(Uv,"")};function sm(e,t){return new RegExp(e,t)}var Ea;if(ya){var Rf=sm("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Ea=function(t,n){var r;Rf.lastIndex=n;var o=Rf.exec(t);return(r=o[1])!==null&&r!==void 0?r:""}}else Ea=function(t,n){for(var r=[];;){var o=im(t,n);if(o===void 0||um(o)||tE(o))break;r.push(o),n+=o>=65536?2:1}return va.apply(void 0,r)};var qv=function(){function e(t,n){n===void 0&&(n={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!n.ignoreTag,this.locale=n.locale,this.requiresOtherClause=!!n.requiresOtherClause,this.shouldParseSkeletons=!!n.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,n,r){for(var o=[];!this.isEOF();){var i=this.char();if(i===123){var s=this.parseArgument(t,r);if(s.err)return s;o.push(s.val)}else{if(i===125&&t>0)break;if(i===35&&(n==="plural"||n==="selectordinal")){var u=this.clonePosition();this.bump(),o.push({type:G.pound,location:D(u,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(r)break;return this.error(b.UNMATCHED_CLOSING_TAG,D(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&_a(this.peek()||0)){var s=this.parseTag(t,n);if(s.err)return s;o.push(s.val)}else{var s=this.parseLiteral(t,n);if(s.err)return s;o.push(s.val)}}}return{val:o,err:null}},e.prototype.parseTag=function(t,n){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:G.literal,value:"<".concat(o,"/>"),location:D(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,n,!0);if(i.err)return i;var s=i.val,u=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!_a(this.char()))return this.error(b.INVALID_TAG,D(u,this.clonePosition()));var a=this.clonePosition(),l=this.parseTagName();return o!==l?this.error(b.UNMATCHED_CLOSING_TAG,D(a,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:G.tag,value:o,children:s,location:D(r,this.clonePosition())},err:null}:this.error(b.INVALID_TAG,D(u,this.clonePosition())))}else return this.error(b.UNCLOSED_TAG,D(r,this.clonePosition()))}else return this.error(b.INVALID_TAG,D(r,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&eE(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,n){for(var r=this.clonePosition(),o="";;){var i=this.tryParseQuote(n);if(i){o+=i;continue}var s=this.tryParseUnquoted(t,n);if(s){o+=s;continue}var u=this.tryParseLeftAngleBracket();if(u){o+=u;continue}break}var a=D(r,this.clonePosition());return{val:{type:G.literal,value:o,location:a},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Jv(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var n=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(r===39)if(this.peek()===39)n.push(39),this.bump();else{this.bump();break}else n.push(r);this.bump()}return va.apply(void 0,n)},e.prototype.tryParseUnquoted=function(t,n){if(this.isEOF())return null;var r=this.char();return r===60||r===123||r===35&&(n==="plural"||n==="selectordinal")||r===125&&t>0?null:(this.bump(),va(r))},e.prototype.parseArgument=function(t,n){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(b.EMPTY_ARGUMENT,D(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(b.MALFORMED_ARGUMENT,D(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:G.argument,value:o,location:D(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition())):this.parseArgumentOptions(t,n,o,r);default:return this.error(b.MALFORMED_ARGUMENT,D(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),n=this.offset(),r=Ea(this.message,n),o=n+r.length;this.bumpTo(o);var i=this.clonePosition(),s=D(t,i);return{value:r,location:s}},e.prototype.parseArgumentOptions=function(t,n,r,o){var i,s=this.clonePosition(),u=this.parseIdentifierIfPossible().value,a=this.clonePosition();switch(u){case"":return this.error(b.EXPECT_ARGUMENT_TYPE,D(s,a));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),f=this.parseSimpleArgStyleIfPossible();if(f.err)return f;var d=Zv(f.val);if(d.length===0)return this.error(b.EXPECT_ARGUMENT_STYLE,D(this.clonePosition(),this.clonePosition()));var m=D(c,this.clonePosition());l={style:d,styleLocation:m}}var y=this.tryParseArgumentClose(o);if(y.err)return y;var v=D(o,this.clonePosition());if(l&&Nf(l==null?void 0:l.style,"::",0)){var S=Kv(l.style.slice(2));if(u==="number"){var f=this.parseNumberSkeletonFromString(S,l.styleLocation);return f.err?f:{val:{type:G.number,value:r,location:v,style:f.val},err:null}}else{if(S.length===0)return this.error(b.EXPECT_DATE_TIME_SKELETON,v);var h=S;this.locale&&(h=Fv(S,this.locale));var d={type:pr.dateTime,pattern:h,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?Rv(h):{}},p=u==="date"?G.date:G.time;return{val:{type:p,value:r,location:v,style:d},err:null}}}return{val:{type:u==="number"?G.number:u==="date"?G.date:G.time,value:r,location:v,style:(i=l==null?void 0:l.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var g=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(b.EXPECT_SELECT_ARGUMENT_OPTIONS,D(g,k({},g)));this.bumpSpace();var E=this.parseIdentifierIfPossible(),w=0;if(u!=="select"&&E.value==="offset"){if(!this.bumpIf(":"))return this.error(b.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,D(this.clonePosition(),this.clonePosition()));this.bumpSpace();var f=this.tryParseDecimalInteger(b.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,b.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(f.err)return f;this.bumpSpace(),E=this.parseIdentifierIfPossible(),w=f.val}var x=this.tryParsePluralOrSelectOptions(t,u,n,E);if(x.err)return x;var y=this.tryParseArgumentClose(o);if(y.err)return y;var I=D(o,this.clonePosition());return u==="select"?{val:{type:G.select,value:r,options:Pf(x.val),location:I},err:null}:{val:{type:G.plural,value:r,options:Pf(x.val),offset:w,pluralType:u==="plural"?"cardinal":"ordinal",location:I},err:null}}default:return this.error(b.INVALID_ARGUMENT_TYPE,D(s,a))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(b.EXPECT_ARGUMENT_CLOSING_BRACE,D(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,n=this.clonePosition();!this.isEOF();){var r=this.char();switch(r){case 39:{this.bump();var o=this.clonePosition();if(!this.bumpUntil("'"))return this.error(b.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,D(o,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(n.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(n.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,n){var r=[];try{r=Lv(t)}catch{return this.error(b.INVALID_NUMBER_SKELETON,n)}return{val:{type:pr.number,tokens:r,location:n,parsedOptions:this.shouldParseSkeletons?Mv(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,n,r,o){for(var i,s=!1,u=[],a=new Set,l=o.value,c=o.location;;){if(l.length===0){var f=this.clonePosition();if(n!=="select"&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(b.EXPECT_PLURAL_ARGUMENT_SELECTOR,b.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=D(f,this.clonePosition()),l=this.message.slice(f.offset,this.offset())}else break}if(a.has(l))return this.error(n==="select"?b.DUPLICATE_SELECT_ARGUMENT_SELECTOR:b.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);l==="other"&&(s=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error(n==="select"?b.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:b.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,D(this.clonePosition(),this.clonePosition()));var y=this.parseMessage(t+1,n,r);if(y.err)return y;var v=this.tryParseArgumentClose(m);if(v.err)return v;u.push([l,{value:y.val,location:D(m,this.clonePosition())}]),a.add(l),this.bumpSpace(),i=this.parseIdentifierIfPossible(),l=i.value,c=i.location}return u.length===0?this.error(n==="select"?b.EXPECT_SELECT_ARGUMENT_SELECTOR:b.EXPECT_PLURAL_ARGUMENT_SELECTOR,D(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(b.MISSING_OTHER_CLAUSE,D(this.clonePosition(),this.clonePosition())):{val:u,err:null}},e.prototype.tryParseDecimalInteger=function(t,n){var r=1,o=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,s=0;!this.isEOF();){var u=this.char();if(u>=48&&u<=57)i=!0,s=s*10+(u-48),this.bump();else break}var a=D(o,this.clonePosition());return i?(s*=r,Yv(s)?{val:s,err:null}:this.error(n,a)):this.error(t,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var n=im(this.message,t);if(n===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return n},e.prototype.error=function(t,n){return{val:null,err:{kind:t,message:this.message,location:n}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(Nf(this.message,t,this.offset())){for(var n=0;n<t.length;n++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var n=this.offset(),r=this.message.indexOf(t,n);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var n=this.offset();if(n===t)break;if(n>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&um(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),n=this.offset(),r=this.message.charCodeAt(n+(t>=65536?2:1));return r??null},e}();function _a(e){return e>=97&&e<=122||e>=65&&e<=90}function Jv(e){return _a(e)||e===47}function eE(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function um(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function tE(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function Sa(e){e.forEach(function(t){if(delete t.location,Zh(t)||qh(t))for(var n in t.options)delete t.options[n].location,Sa(t.options[n].value);else Yh(t)&&em(t.style)||(Qh(t)||Kh(t))&&ga(t.style)?delete t.style.location:Jh(t)&&Sa(t.children)})}function nE(e,t){t===void 0&&(t={}),t=k({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var n=new qv(e,t).parse();if(n.err){var r=SyntaxError(b[n.err.kind]);throw r.location=n.err.location,r.originalMessage=n.err.message,r}return t!=null&&t.captureLocation||Sa(n.val),n.val}var pt;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(pt||(pt={}));var nn=function(e){Ke(t,e);function t(n,r,o){var i=e.call(this,n)||this;return i.code=r,i.originalMessage=o,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),Of=function(e){Ke(t,e);function t(n,r,o,i){return e.call(this,'Invalid values for "'.concat(n,'": "').concat(r,'". Options are "').concat(Object.keys(o).join('", "'),'"'),pt.INVALID_VALUE,i)||this}return t}(nn),rE=function(e){Ke(t,e);function t(n,r,o){return e.call(this,'Value for "'.concat(n,'" must be of type ').concat(r),pt.INVALID_VALUE,o)||this}return t}(nn),oE=function(e){Ke(t,e);function t(n,r){return e.call(this,'The intl string context variable "'.concat(n,'" was not provided to the string "').concat(r,'"'),pt.MISSING_VALUE,r)||this}return t}(nn),ve;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(ve||(ve={}));function iE(e){return e.length<2?e:e.reduce(function(t,n){var r=t[t.length-1];return!r||r.type!==ve.literal||n.type!==ve.literal?t.push(n):r.value+=n.value,t},[])}function am(e){return typeof e=="function"}function gi(e,t,n,r,o,i,s){if(e.length===1&&Tf(e[0]))return[{type:ve.literal,value:e[0].value}];for(var u=[],a=0,l=e;a<l.length;a++){var c=l[a];if(Tf(c)){u.push({type:ve.literal,value:c.value});continue}if(Nv(c)){typeof i=="number"&&u.push({type:ve.literal,value:n.getNumberFormat(t).format(i)});continue}var f=c.value;if(!(o&&f in o))throw new oE(f,s);var d=o[f];if(kv(c)){(!d||typeof d=="string"||typeof d=="number")&&(d=typeof d=="string"||typeof d=="number"?String(d):""),u.push({type:typeof d=="string"?ve.literal:ve.object,value:d});continue}if(Qh(c)){var m=typeof c.style=="string"?r.date[c.style]:ga(c.style)?c.style.parsedOptions:void 0;u.push({type:ve.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(Kh(c)){var m=typeof c.style=="string"?r.time[c.style]:ga(c.style)?c.style.parsedOptions:r.time.medium;u.push({type:ve.literal,value:n.getDateTimeFormat(t,m).format(d)});continue}if(Yh(c)){var m=typeof c.style=="string"?r.number[c.style]:em(c.style)?c.style.parsedOptions:void 0;m&&m.scale&&(d=d*(m.scale||1)),u.push({type:ve.literal,value:n.getNumberFormat(t,m).format(d)});continue}if(Jh(c)){var y=c.children,v=c.value,S=o[v];if(!am(S))throw new rE(v,"function",s);var h=gi(y,t,n,r,o,i),p=S(h.map(function(w){return w.value}));Array.isArray(p)||(p=[p]),u.push.apply(u,p.map(function(w){return{type:typeof w=="string"?ve.literal:ve.object,value:w}}))}if(Zh(c)){var g=c.options[d]||c.options.other;if(!g)throw new Of(c.value,d,Object.keys(c.options),s);u.push.apply(u,gi(g.value,t,n,r,o));continue}if(qh(c)){var g=c.options["=".concat(d)];if(!g){if(!Intl.PluralRules)throw new nn(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,pt.MISSING_INTL_API,s);var E=n.getPluralRules(t,{type:c.pluralType}).select(d-(c.offset||0));g=c.options[E]||c.options.other}if(!g)throw new Of(c.value,d,Object.keys(c.options),s);u.push.apply(u,gi(g.value,t,n,r,o,d-(c.offset||0)));continue}}return iE(u)}function sE(e,t){return t?k(k(k({},e||{}),t||{}),Object.keys(e).reduce(function(n,r){return n[r]=k(k({},e[r]),t[r]||{}),n},{})):e}function uE(e,t){return t?Object.keys(e).reduce(function(n,r){return n[r]=sE(e[r],t[r]),n},k({},e)):e}function du(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function aE(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:_e(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.NumberFormat).bind.apply(t,we([void 0],n,!1)))},{cache:du(e.number),strategy:Se.variadic}),getDateTimeFormat:_e(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.DateTimeFormat).bind.apply(t,we([void 0],n,!1)))},{cache:du(e.dateTime),strategy:Se.variadic}),getPluralRules:_e(function(){for(var t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new((t=Intl.PluralRules).bind.apply(t,we([void 0],n,!1)))},{cache:du(e.pluralRules),strategy:Se.variadic})}}var lm=function(){function e(t,n,r,o){var i=this;if(n===void 0&&(n=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(a){var l=i.formatToParts(a);if(l.length===1)return l[0].value;var c=l.reduce(function(f,d){return!f.length||d.type!==ve.literal||typeof f[f.length-1]!="string"?f.push(d.value):f[f.length-1]+=d.value,f},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(a){return gi(i.ast,i.locales,i.formatters,i.formats,a,void 0,i.message)},this.resolvedOptions=function(){var a;return{locale:((a=i.resolvedLocale)===null||a===void 0?void 0:a.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=n,this.resolvedLocale=e.resolveLocale(n),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=o||{};s.formatters;var u=ms(s,["formatters"]);this.ast=e.__parse(t,k(k({},u),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=uE(e.formats,r),this.formatters=o&&o.formatters||aE(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var n=Intl.NumberFormat.supportedLocalesOf(t);return n.length>0?new Intl.Locale(n[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=nE,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),xn;(function(e){e.FORMAT_ERROR="FORMAT_ERROR",e.UNSUPPORTED_FORMATTER="UNSUPPORTED_FORMATTER",e.INVALID_CONFIG="INVALID_CONFIG",e.MISSING_DATA="MISSING_DATA",e.MISSING_TRANSLATION="MISSING_TRANSLATION"})(xn||(xn={}));var wo=function(e){Ke(t,e);function t(n,r,o){var i=this,s=o?o instanceof Error?o:new Error(String(o)):void 0;return i=e.call(this,"[@formatjs/intl Error ".concat(n,"] ").concat(r,`
`).concat(s?`
`.concat(s.message,`
`).concat(s.stack):""))||this,i.code=n,typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(i,t),i}return t}(Error),lE=function(e){Ke(t,e);function t(n,r){return e.call(this,xn.UNSUPPORTED_FORMATTER,n,r)||this}return t}(wo),cE=function(e){Ke(t,e);function t(n,r){return e.call(this,xn.INVALID_CONFIG,n,r)||this}return t}(wo),Lf=function(e){Ke(t,e);function t(n,r){return e.call(this,xn.MISSING_DATA,n,r)||this}return t}(wo),Ze=function(e){Ke(t,e);function t(n,r,o){var i=e.call(this,xn.FORMAT_ERROR,"".concat(n,`
Locale: `).concat(r,`
`),o)||this;return i.locale=r,i}return t}(wo),pu=function(e){Ke(t,e);function t(n,r,o,i){var s=e.call(this,"".concat(n,`
MessageID: `).concat(o==null?void 0:o.id,`
Default Message: `).concat(o==null?void 0:o.defaultMessage,`
Description: `).concat(o==null?void 0:o.description,`
`),r,i)||this;return s.descriptor=o,s.locale=r,s}return t}(Ze),fE=function(e){Ke(t,e);function t(n,r){var o=e.call(this,xn.MISSING_TRANSLATION,'Missing message: "'.concat(n.id,'" for locale "').concat(r,'", using ').concat(n.defaultMessage?"default message (".concat(typeof n.defaultMessage=="string"?n.defaultMessage:n.defaultMessage.map(function(i){var s;return(s=i.value)!==null&&s!==void 0?s:JSON.stringify(i)}).join(),")"):"id"," as fallback."))||this;return o.descriptor=n,o}return t}(wo);function On(e,t,n){return n===void 0&&(n={}),t.reduce(function(r,o){return o in e?r[o]=e[o]:o in n&&(r[o]=n[o]),r},{})}var dE=function(e){},pE=function(e){},cm={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:dE,onWarn:pE};function fm(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function an(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function hE(e){e===void 0&&(e=fm());var t=Intl.RelativeTimeFormat,n=Intl.ListFormat,r=Intl.DisplayNames,o=_e(function(){for(var u,a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new((u=Intl.DateTimeFormat).bind.apply(u,we([void 0],a,!1)))},{cache:an(e.dateTime),strategy:Se.variadic}),i=_e(function(){for(var u,a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new((u=Intl.NumberFormat).bind.apply(u,we([void 0],a,!1)))},{cache:an(e.number),strategy:Se.variadic}),s=_e(function(){for(var u,a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return new((u=Intl.PluralRules).bind.apply(u,we([void 0],a,!1)))},{cache:an(e.pluralRules),strategy:Se.variadic});return{getDateTimeFormat:o,getNumberFormat:i,getMessageFormat:_e(function(u,a,l,c){return new lm(u,a,l,k({formatters:{getNumberFormat:i,getDateTimeFormat:o,getPluralRules:s}},c||{}))},{cache:an(e.message),strategy:Se.variadic}),getRelativeTimeFormat:_e(function(){for(var u=[],a=0;a<arguments.length;a++)u[a]=arguments[a];return new(t.bind.apply(t,we([void 0],u,!1)))},{cache:an(e.relativeTime),strategy:Se.variadic}),getPluralRules:s,getListFormat:_e(function(){for(var u=[],a=0;a<arguments.length;a++)u[a]=arguments[a];return new(n.bind.apply(n,we([void 0],u,!1)))},{cache:an(e.list),strategy:Se.variadic}),getDisplayNames:_e(function(){for(var u=[],a=0;a<arguments.length;a++)u[a]=arguments[a];return new(r.bind.apply(r,we([void 0],u,!1)))},{cache:an(e.displayNames),strategy:Se.variadic})}}function zl(e,t,n,r){var o=e&&e[t],i;if(o&&(i=o[n]),i)return i;r(new lE("No ".concat(t," format named: ").concat(n)))}function Qo(e,t){return Object.keys(e).reduce(function(n,r){return n[r]=k({timeZone:t},e[r]),n},{})}function Af(e,t){var n=Object.keys(k(k({},e),t));return n.reduce(function(r,o){return r[o]=k(k({},e[o]||{}),t[o]||{}),r},{})}function bf(e,t){if(!t)return e;var n=lm.formats;return k(k(k({},n),e),{date:Af(Qo(n.date,t),Qo(e.date||{},t)),time:Af(Qo(n.time,t),Qo(e.time||{},t))})}var wa=function(e,t,n,r,o){var i=e.locale,s=e.formats,u=e.messages,a=e.defaultLocale,l=e.defaultFormats,c=e.fallbackOnEmptyString,f=e.onError,d=e.timeZone,m=e.defaultRichTextElements;n===void 0&&(n={id:""});var y=n.id,v=n.defaultMessage;Xh(!!y,"[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");var S=String(y),h=u&&Object.prototype.hasOwnProperty.call(u,S)&&u[S];if(Array.isArray(h)&&h.length===1&&h[0].type===G.literal)return h[0].value;if(!r&&h&&typeof h=="string"&&!m)return h.replace(/'\{(.*?)\}'/gi,"{$1}");if(r=k(k({},m),r||{}),s=bf(s,d),l=bf(l,d),!h){if(c===!1&&h==="")return h;if((!v||i&&i.toLowerCase()!==a.toLowerCase())&&f(new fE(n,i)),v)try{var p=t.getMessageFormat(v,a,l,o);return p.format(r)}catch(g){return f(new pu('Error formatting default message for: "'.concat(S,'", rendering default message verbatim'),i,n,g)),typeof v=="string"?v:S}return S}try{var p=t.getMessageFormat(h,i,s,k({formatters:t},o||{}));return p.format(r)}catch(g){f(new pu('Error formatting message: "'.concat(S,'", using ').concat(v?"default message":"id"," as fallback."),i,n,g))}if(v)try{var p=t.getMessageFormat(v,a,l,o);return p.format(r)}catch(g){f(new pu('Error formatting the default message for: "'.concat(S,'", rendering message verbatim'),i,n,g))}return typeof h=="string"?h:typeof v=="string"?v:S},dm=["formatMatcher","timeZone","hour12","weekday","era","year","month","day","hour","minute","second","timeZoneName","hourCycle","dateStyle","timeStyle","calendar","numberingSystem","fractionalSecondDigits"];function gs(e,t,n,r){var o=e.locale,i=e.formats,s=e.onError,u=e.timeZone;r===void 0&&(r={});var a=r.format,l=k(k({},u&&{timeZone:u}),a&&zl(i,t,a,s)),c=On(r,dm,l);return t==="time"&&!c.hour&&!c.minute&&!c.second&&!c.timeStyle&&!c.dateStyle&&(c=k(k({},c),{hour:"numeric",minute:"numeric"})),n(o,c)}function mE(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return gs(e,"date",t,s).format(u)}catch(a){e.onError(new Ze("Error formatting date.",e.locale,a))}return String(u)}function gE(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return gs(e,"time",t,s).format(u)}catch(a){e.onError(new Ze("Error formatting time.",e.locale,a))}return String(u)}function yE(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=n[2],u=s===void 0?{}:s,a=e.timeZone,l=e.locale,c=e.onError,f=On(u,dm,a?{timeZone:a}:{});try{return t(l,f).formatRange(o,i)}catch(d){c(new Ze("Error formatting date time range.",e.locale,d))}return String(o)}function vE(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return gs(e,"date",t,s).formatToParts(u)}catch(a){e.onError(new Ze("Error formatting date.",e.locale,a))}return[]}function EE(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o=n[0],i=n[1],s=i===void 0?{}:i,u=typeof o=="string"?new Date(o||0):o;try{return gs(e,"time",t,s).formatToParts(u)}catch(a){e.onError(new Ze("Error formatting time.",e.locale,a))}return[]}var _E=["style","type","fallback","languageDisplay"];function SE(e,t,n,r){var o=e.locale,i=e.onError,s=Intl.DisplayNames;s||i(new nn(`Intl.DisplayNames is not available in this environment.
Try polyfilling it using "@formatjs/intl-displaynames"
`,pt.MISSING_INTL_API));var u=On(r,_E);try{return t(o,u).of(n)}catch(a){i(new Ze("Error formatting display name.",o,a))}}var wE=["type","style"],Df=Date.now();function xE(e){return"".concat(Df,"_").concat(e,"_").concat(Df)}function TE(e,t,n,r){r===void 0&&(r={});var o=pm(e,t,n,r).reduce(function(i,s){var u=s.value;return typeof u!="string"?i.push(u):typeof i[i.length-1]=="string"?i[i.length-1]+=u:i.push(u),i},[]);return o.length===1?o[0]:o.length===0?"":o}function pm(e,t,n,r){var o=e.locale,i=e.onError;r===void 0&&(r={});var s=Intl.ListFormat;s||i(new nn(`Intl.ListFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-listformat"
`,pt.MISSING_INTL_API));var u=On(r,wE);try{var a={},l=n.map(function(c,f){if(typeof c=="object"){var d=xE(f);return a[d]=c,d}return String(c)});return t(o,u).formatToParts(l).map(function(c){return c.type==="literal"?c:k(k({},c),{value:a[c.value]||c.value})})}catch(c){i(new Ze("Error formatting list.",o,c))}return n}var CE=["type"];function IE(e,t,n,r){var o=e.locale,i=e.onError;r===void 0&&(r={}),Intl.PluralRules||i(new nn(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,pt.MISSING_INTL_API));var s=On(r,CE);try{return t(o,s).select(n)}catch(u){i(new Ze("Error formatting plural.",o,u))}return"other"}var kE=["numeric","style"];function NE(e,t,n){var r=e.locale,o=e.formats,i=e.onError;n===void 0&&(n={});var s=n.format,u=!!s&&zl(o,"relative",s,i)||{},a=On(n,kE,u);return t(r,a)}function PE(e,t,n,r,o){o===void 0&&(o={}),r||(r="second");var i=Intl.RelativeTimeFormat;i||e.onError(new nn(`Intl.RelativeTimeFormat is not available in this environment.
Try polyfilling it using "@formatjs/intl-relativetimeformat"
`,pt.MISSING_INTL_API));try{return NE(e,t,o).format(n,r)}catch(s){e.onError(new Ze("Error formatting relative time.",e.locale,s))}return String(n)}var RE=["style","currency","unit","unitDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","currencyDisplay","currencySign","notation","signDisplay","unit","unitDisplay","numberingSystem","trailingZeroDisplay","roundingPriority","roundingIncrement","roundingMode"];function hm(e,t,n){var r=e.locale,o=e.formats,i=e.onError;n===void 0&&(n={});var s=n.format,u=s&&zl(o,"number",s,i)||{},a=On(n,RE,u);return t(r,a)}function OE(e,t,n,r){r===void 0&&(r={});try{return hm(e,t,r).format(n)}catch(o){e.onError(new Ze("Error formatting number.",e.locale,o))}return String(n)}function LE(e,t,n,r){r===void 0&&(r={});try{return hm(e,t,r).formatToParts(n)}catch(o){e.onError(new Ze("Error formatting number.",e.locale,o))}return[]}function AE(e){var t=e?e[Object.keys(e)[0]]:void 0;return typeof t=="string"}function bE(e){e.onWarn&&e.defaultRichTextElements&&AE(e.messages||{})&&e.onWarn(`[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. 
Please consider using "@formatjs/cli" to pre-compile your messages for performance.
For more details see https://formatjs.io/docs/getting-started/message-distribution`)}function DE(e,t){var n=hE(t),r=k(k({},cm),e),o=r.locale,i=r.defaultLocale,s=r.onError;return o?!Intl.NumberFormat.supportedLocalesOf(o).length&&s?s(new Lf('Missing locale data for locale: "'.concat(o,'" in Intl.NumberFormat. Using default locale: "').concat(i,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):!Intl.DateTimeFormat.supportedLocalesOf(o).length&&s&&s(new Lf('Missing locale data for locale: "'.concat(o,'" in Intl.DateTimeFormat. Using default locale: "').concat(i,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):(s&&s(new cE('"locale" was not configured, using "'.concat(i,'" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details'))),r.locale=r.defaultLocale||"en"),bE(r),k(k({},r),{formatters:n,formatNumber:OE.bind(null,r,n.getNumberFormat),formatNumberToParts:LE.bind(null,r,n.getNumberFormat),formatRelativeTime:PE.bind(null,r,n.getRelativeTimeFormat),formatDate:mE.bind(null,r,n.getDateTimeFormat),formatDateToParts:vE.bind(null,r,n.getDateTimeFormat),formatTime:gE.bind(null,r,n.getDateTimeFormat),formatDateTimeRange:yE.bind(null,r,n.getDateTimeFormat),formatTimeToParts:EE.bind(null,r,n.getDateTimeFormat),formatPlural:IE.bind(null,r,n.getPluralRules),formatMessage:wa.bind(null,r,n),$t:wa.bind(null,r,n),formatList:TE.bind(null,r,n.getListFormat),formatListToParts:pm.bind(null,r,n.getListFormat),formatDisplayName:SE.bind(null,r,n.getDisplayNames)})}function mm(e){Xh(e,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}var gm=k(k({},cm),{textComponent:me.Fragment});function ME(e){return function(t){return e(me.Children.toArray(t))}}function FE(e,t){if(e===t)return!0;if(!e||!t)return!1;var n=Object.keys(e),r=Object.keys(t),o=n.length;if(r.length!==o)return!1;for(var i=0;i<o;i++){var s=n[i];if(e[s]!==t[s]||!Object.prototype.hasOwnProperty.call(t,s))return!1}return!0}var ym={exports:{}},B={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ue=typeof Symbol=="function"&&Symbol.for,Gl=ue?Symbol.for("react.element"):60103,Vl=ue?Symbol.for("react.portal"):60106,ys=ue?Symbol.for("react.fragment"):60107,vs=ue?Symbol.for("react.strict_mode"):60108,Es=ue?Symbol.for("react.profiler"):60114,_s=ue?Symbol.for("react.provider"):60109,Ss=ue?Symbol.for("react.context"):60110,Wl=ue?Symbol.for("react.async_mode"):60111,ws=ue?Symbol.for("react.concurrent_mode"):60111,xs=ue?Symbol.for("react.forward_ref"):60112,Ts=ue?Symbol.for("react.suspense"):60113,HE=ue?Symbol.for("react.suspense_list"):60120,Cs=ue?Symbol.for("react.memo"):60115,Is=ue?Symbol.for("react.lazy"):60116,BE=ue?Symbol.for("react.block"):60121,UE=ue?Symbol.for("react.fundamental"):60117,$E=ue?Symbol.for("react.responder"):60118,jE=ue?Symbol.for("react.scope"):60119;function $e(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Gl:switch(e=e.type,e){case Wl:case ws:case ys:case Es:case vs:case Ts:return e;default:switch(e=e&&e.$$typeof,e){case Ss:case xs:case Is:case Cs:case _s:return e;default:return t}}case Vl:return t}}}function vm(e){return $e(e)===ws}B.AsyncMode=Wl;B.ConcurrentMode=ws;B.ContextConsumer=Ss;B.ContextProvider=_s;B.Element=Gl;B.ForwardRef=xs;B.Fragment=ys;B.Lazy=Is;B.Memo=Cs;B.Portal=Vl;B.Profiler=Es;B.StrictMode=vs;B.Suspense=Ts;B.isAsyncMode=function(e){return vm(e)||$e(e)===Wl};B.isConcurrentMode=vm;B.isContextConsumer=function(e){return $e(e)===Ss};B.isContextProvider=function(e){return $e(e)===_s};B.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Gl};B.isForwardRef=function(e){return $e(e)===xs};B.isFragment=function(e){return $e(e)===ys};B.isLazy=function(e){return $e(e)===Is};B.isMemo=function(e){return $e(e)===Cs};B.isPortal=function(e){return $e(e)===Vl};B.isProfiler=function(e){return $e(e)===Es};B.isStrictMode=function(e){return $e(e)===vs};B.isSuspense=function(e){return $e(e)===Ts};B.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ys||e===ws||e===Es||e===vs||e===Ts||e===HE||typeof e=="object"&&e!==null&&(e.$$typeof===Is||e.$$typeof===Cs||e.$$typeof===_s||e.$$typeof===Ss||e.$$typeof===xs||e.$$typeof===UE||e.$$typeof===$E||e.$$typeof===jE||e.$$typeof===BE)};B.typeOf=$e;ym.exports=B;var zE=ym.exports,Em=zE,GE={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},VE={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},_m={};_m[Em.ForwardRef]=GE;_m[Em.Memo]=VE;var Xl=typeof window<"u"&&!window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=me.createContext(null)):me.createContext(null);Xl.Consumer;var WE=Xl.Provider,XE=WE,YE=Xl;function Sm(){var e=me.useContext(YE);return mm(e),e}var xa;(function(e){e.formatDate="FormattedDate",e.formatTime="FormattedTime",e.formatNumber="FormattedNumber",e.formatList="FormattedList",e.formatDisplayName="FormattedDisplayName"})(xa||(xa={}));var Ta;(function(e){e.formatDate="FormattedDateParts",e.formatTime="FormattedTimeParts",e.formatNumber="FormattedNumberParts",e.formatList="FormattedListParts"})(Ta||(Ta={}));function wm(e){var t=function(n){var r=Sm(),o=n.value,i=n.children,s=ms(n,["value","children"]),u=typeof o=="string"?new Date(o||0):o,a=e==="formatDate"?r.formatDateToParts(u,s):r.formatTimeToParts(u,s);return i(a)};return t.displayName=Ta[e],t}function xo(e){var t=function(n){var r=Sm(),o=n.value,i=n.children,s=ms(n,["value","children"]),u=r[e](o,s);if(typeof i=="function")return i(u);var a=r.textComponent||me.Fragment;return me.createElement(a,null,u)};return t.displayName=xa[e],t}function xm(e){return e&&Object.keys(e).reduce(function(t,n){var r=e[n];return t[n]=am(r)?ME(r):r,t},{})}var Mf=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];var s=xm(r),u=wa.apply(void 0,we([e,t,n,s],o,!1));return Array.isArray(u)?me.Children.toArray(u):u},Ff=function(e,t){var n=e.defaultRichTextElements,r=ms(e,["defaultRichTextElements"]),o=xm(n),i=DE(k(k(k({},gm),r),{defaultRichTextElements:o}),t),s={locale:i.locale,timeZone:i.timeZone,fallbackOnEmptyString:i.fallbackOnEmptyString,formats:i.formats,defaultLocale:i.defaultLocale,defaultFormats:i.defaultFormats,messages:i.messages,onError:i.onError,defaultRichTextElements:o};return k(k({},i),{formatMessage:Mf.bind(null,s,i.formatters),$t:Mf.bind(null,s,i.formatters)})};function hu(e){return{locale:e.locale,timeZone:e.timeZone,fallbackOnEmptyString:e.fallbackOnEmptyString,formats:e.formats,textComponent:e.textComponent,messages:e.messages,defaultLocale:e.defaultLocale,defaultFormats:e.defaultFormats,onError:e.onError,onWarn:e.onWarn,wrapRichTextChunksInFragment:e.wrapRichTextChunksInFragment,defaultRichTextElements:e.defaultRichTextElements}}var QE=function(e){Ke(t,e);function t(){var n=e!==null&&e.apply(this,arguments)||this;return n.cache=fm(),n.state={cache:n.cache,intl:Ff(hu(n.props),n.cache),prevConfig:hu(n.props)},n}return t.getDerivedStateFromProps=function(n,r){var o=r.prevConfig,i=r.cache,s=hu(n);return FE(o,s)?null:{intl:Ff(s,i),prevConfig:s}},t.prototype.render=function(){return mm(this.state.intl),me.createElement(XE,{value:this.state.intl},this.props.children)},t.displayName="IntlProvider",t.defaultProps=gm,t}(me.PureComponent);xo("formatDate");xo("formatTime");xo("formatNumber");xo("formatList");xo("formatDisplayName");wm("formatDate");wm("formatTime");var Ca=function(e,t){return Ca=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])},Ca(e,t)};function wr(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Ca(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}function KE(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function u(c){try{l(r.next(c))}catch(f){s(f)}}function a(c){try{l(r.throw(c))}catch(f){s(f)}}function l(c){c.done?i(c.value):o(c.value).then(u,a)}l((r=r.apply(e,t||[])).next())})}function Tm(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,o,i,s=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return s.next=u(0),s.throw=u(1),s.return=u(2),typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function u(l){return function(c){return a([l,c])}}function a(l){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,l[0]&&(n=0)),n;)try{if(r=1,o&&(i=l[0]&2?o.return:l[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,l[1])).done)return i;switch(o=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return n.label++,{value:l[1],done:!1};case 5:n.label++,o=l[1],l=[0];continue;case 7:l=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){n=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){n.label=l[1];break}if(l[0]===6&&n.label<i[1]){n.label=i[1],i=l;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(l);break}i[2]&&n.ops.pop(),n.trys.pop();continue}l=t.call(e,n)}catch(c){l=[6,c],o=0}finally{r=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function ho(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Gi(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=r.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(s)throw s.error}}return i}function Vi(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}function tr(e){return this instanceof tr?(this.v=e,this):new tr(e)}function ZE(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(m){return function(y){return Promise.resolve(y).then(m,f)}}function u(m,y){r[m]&&(o[m]=function(v){return new Promise(function(S,h){i.push([m,v,S,h])>1||a(m,v)})},y&&(o[m]=y(o[m])))}function a(m,y){try{l(r[m](y))}catch(v){d(i[0][3],v)}}function l(m){m.value instanceof tr?Promise.resolve(m.value.v).then(c,f):d(i[0][2],m)}function c(m){a("next",m)}function f(m){a("throw",m)}function d(m,y){m(y),i.shift(),i.length&&a(i[0][0],i[0][1])}}function qE(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof ho=="function"?ho(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(u,a){s=e[i](s),o(u,a,s.done,s.value)})}}function o(i,s,u,a){Promise.resolve(a).then(function(l){i({value:l,done:u})},s)}}function te(e){return typeof e=="function"}function Yl(e){var t=function(r){Error.call(r),r.stack=new Error().stack},n=e(t);return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var mu=Yl(function(e){return function(n){e(this),this.message=n?n.length+` errors occurred during unsubscription:
`+n.map(function(r,o){return o+1+") "+r.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=n}});function Ia(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ks=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,n,r,o,i;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var u=ho(s),a=u.next();!a.done;a=u.next()){var l=a.value;l.remove(this)}}catch(v){t={error:v}}finally{try{a&&!a.done&&(n=u.return)&&n.call(u)}finally{if(t)throw t.error}}else s.remove(this);var c=this.initialTeardown;if(te(c))try{c()}catch(v){i=v instanceof mu?v.errors:[v]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var d=ho(f),m=d.next();!m.done;m=d.next()){var y=m.value;try{Hf(y)}catch(v){i=i??[],v instanceof mu?i=Vi(Vi([],Gi(i)),Gi(v.errors)):i.push(v)}}}catch(v){r={error:v}}finally{try{m&&!m.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}}if(i)throw new mu(i)}},e.prototype.add=function(t){var n;if(t&&t!==this)if(this.closed)Hf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}},e.prototype._hasParent=function(t){var n=this._parentage;return n===t||Array.isArray(n)&&n.includes(t)},e.prototype._addParent=function(t){var n=this._parentage;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t},e.prototype._removeParent=function(t){var n=this._parentage;n===t?this._parentage=null:Array.isArray(n)&&Ia(n,t)},e.prototype.remove=function(t){var n=this._finalizers;n&&Ia(n,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}(),DT=ks.EMPTY;function Cm(e){return e instanceof ks||e&&"closed"in e&&te(e.remove)&&te(e.add)&&te(e.unsubscribe)}function Hf(e){te(e)?e():e.unsubscribe()}var Im={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},km={setTimeout:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,Vi([e,t],Gi(n)))},clearTimeout:function(e){var t=km.delegate;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function Nm(e){km.setTimeout(function(){throw e})}function Wi(){}function JE(e){e()}var Ql=function(e){wr(t,e);function t(n){var r=e.call(this)||this;return r.isStopped=!1,n?(r.destination=n,Cm(n)&&n.add(r)):r.destination=r_,r}return t.create=function(n,r,o){return new Xi(n,r,o)},t.prototype.next=function(n){this.isStopped||this._next(n)},t.prototype.error=function(n){this.isStopped||(this.isStopped=!0,this._error(n))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(n){this.destination.next(n)},t.prototype._error=function(n){try{this.destination.error(n)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(ks),e_=Function.prototype.bind;function gu(e,t){return e_.call(e,t)}var t_=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var n=this.partialObserver;if(n.next)try{n.next(t)}catch(r){Ko(r)}},e.prototype.error=function(t){var n=this.partialObserver;if(n.error)try{n.error(t)}catch(r){Ko(r)}else Ko(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(n){Ko(n)}},e}(),Xi=function(e){wr(t,e);function t(n,r,o){var i=e.call(this)||this,s;if(te(n)||!n)s={next:n??void 0,error:r??void 0,complete:o??void 0};else{var u;i&&Im.useDeprecatedNextContext?(u=Object.create(n),u.unsubscribe=function(){return i.unsubscribe()},s={next:n.next&&gu(n.next,u),error:n.error&&gu(n.error,u),complete:n.complete&&gu(n.complete,u)}):s=n}return i.destination=new t_(s),i}return t}(Ql);function Ko(e){Nm(e)}function n_(e){throw e}var r_={closed:!0,next:Wi,error:n_,complete:Wi},Kl=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function o_(e){return e}function i_(e){return e.length===0?o_:e.length===1?e[0]:function(n){return e.reduce(function(r,o){return o(r)},n)}}var Ie=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(t,n,r){var o=this,i=u_(t)?t:new Xi(t,n,r);return JE(function(){var s=o,u=s.operator,a=s.source;i.add(u?u.call(i,a):a?o._subscribe(i):o._trySubscribe(i))}),i},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(n){t.error(n)}},e.prototype.forEach=function(t,n){var r=this;return n=Bf(n),new n(function(o,i){var s=new Xi({next:function(u){try{t(u)}catch(a){i(a),s.unsubscribe()}},error:i,complete:o});r.subscribe(s)})},e.prototype._subscribe=function(t){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(t)},e.prototype[Kl]=function(){return this},e.prototype.pipe=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return i_(t)(this)},e.prototype.toPromise=function(t){var n=this;return t=Bf(t),new t(function(r,o){var i;n.subscribe(function(s){return i=s},function(s){return o(s)},function(){return r(i)})})},e.create=function(t){return new e(t)},e}();function Bf(e){var t;return(t=e??Im.Promise)!==null&&t!==void 0?t:Promise}function s_(e){return e&&te(e.next)&&te(e.error)&&te(e.complete)}function u_(e){return e&&e instanceof Ql||s_(e)&&Cm(e)}function a_(e){return te(e==null?void 0:e.lift)}function ht(e){return function(t){if(a_(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Pt(e,t,n,r,o){return new l_(e,t,n,r,o)}var l_=function(e){wr(t,e);function t(n,r,o,i,s,u){var a=e.call(this,n)||this;return a.onFinalize=s,a.shouldUnsubscribe=u,a._next=r?function(l){try{r(l)}catch(c){n.error(c)}}:e.prototype._next,a._error=i?function(l){try{i(l)}catch(c){n.error(c)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=o?function(){try{o()}catch(l){n.error(l)}finally{this.unsubscribe()}}:e.prototype._complete,a}return t.prototype.unsubscribe=function(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&((n=this.onFinalize)===null||n===void 0||n.call(this))}},t}(Ql),c_={now:function(){return Date.now()},delegate:void 0},f_=function(e){wr(t,e);function t(n,r){return e.call(this)||this}return t.prototype.schedule=function(n,r){return this},t}(ks),Uf={setInterval:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setInterval.apply(void 0,Vi([e,t],Gi(n)))},clearInterval:function(e){return clearInterval(e)},delegate:void 0},d_=function(e){wr(t,e);function t(n,r){var o=e.call(this,n,r)||this;return o.scheduler=n,o.work=r,o.pending=!1,o}return t.prototype.schedule=function(n,r){var o;if(r===void 0&&(r=0),this.closed)return this;this.state=n;var i=this.id,s=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(s,i,r)),this.pending=!0,this.delay=r,this.id=(o=this.id)!==null&&o!==void 0?o:this.requestAsyncId(s,this.id,r),this},t.prototype.requestAsyncId=function(n,r,o){return o===void 0&&(o=0),Uf.setInterval(n.flush.bind(n,this),o)},t.prototype.recycleAsyncId=function(n,r,o){if(o===void 0&&(o=0),o!=null&&this.delay===o&&this.pending===!1)return r;r!=null&&Uf.clearInterval(r)},t.prototype.execute=function(n,r){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var o=this._execute(n,r);if(o)return o;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(n,r){var o=!1,i;try{this.work(n)}catch(s){o=!0,i=s||new Error("Scheduled action threw falsy error")}if(o)return this.unsubscribe(),i},t.prototype.unsubscribe=function(){if(!this.closed){var n=this,r=n.id,o=n.scheduler,i=o.actions;this.work=this.state=this.scheduler=null,this.pending=!1,Ia(i,this),r!=null&&(this.id=this.recycleAsyncId(o,r,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(f_),$f=function(){function e(t,n){n===void 0&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(t,n,r){return n===void 0&&(n=0),new this.schedulerActionCtor(this,t).schedule(r,n)},e.now=c_.now,e}(),p_=function(e){wr(t,e);function t(n,r){r===void 0&&(r=$f.now);var o=e.call(this,n,r)||this;return o.actions=[],o._active=!1,o}return t.prototype.flush=function(n){var r=this.actions;if(this._active){r.push(n);return}var o;this._active=!0;do if(o=n.execute(n.state,n.delay))break;while(n=r.shift());if(this._active=!1,o){for(;n=r.shift();)n.unsubscribe();throw o}},t}($f),h_=new p_(d_);function m_(e){return e&&te(e.schedule)}function g_(e){return e[e.length-1]}function y_(e){return m_(g_(e))?e.pop():void 0}var Pm=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function Rm(e){return te(e==null?void 0:e.then)}function Om(e){return te(e[Kl])}function Lm(e){return Symbol.asyncIterator&&te(e==null?void 0:e[Symbol.asyncIterator])}function Am(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function v_(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var bm=v_();function Dm(e){return te(e==null?void 0:e[bm])}function Mm(e){return ZE(this,arguments,function(){var n,r,o,i;return Tm(this,function(s){switch(s.label){case 0:n=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,tr(n.read())];case 3:return r=s.sent(),o=r.value,i=r.done,i?[4,tr(void 0)]:[3,5];case 4:return[2,s.sent()];case 5:return[4,tr(o)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return n.releaseLock(),[7];case 10:return[2]}})})}function Fm(e){return te(e==null?void 0:e.getReader)}function rn(e){if(e instanceof Ie)return e;if(e!=null){if(Om(e))return E_(e);if(Pm(e))return __(e);if(Rm(e))return S_(e);if(Lm(e))return Hm(e);if(Dm(e))return w_(e);if(Fm(e))return x_(e)}throw Am(e)}function E_(e){return new Ie(function(t){var n=e[Kl]();if(te(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function __(e){return new Ie(function(t){for(var n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function S_(e){return new Ie(function(t){e.then(function(n){t.closed||(t.next(n),t.complete())},function(n){return t.error(n)}).then(null,Nm)})}function w_(e){return new Ie(function(t){var n,r;try{for(var o=ho(e),i=o.next();!i.done;i=o.next()){var s=i.value;if(t.next(s),t.closed)return}}catch(u){n={error:u}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}t.complete()})}function Hm(e){return new Ie(function(t){T_(e,t).catch(function(n){return t.error(n)})})}function x_(e){return Hm(Mm(e))}function T_(e,t){var n,r,o,i;return KE(this,void 0,void 0,function(){var s,u;return Tm(this,function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),n=qE(e),a.label=1;case 1:return[4,n.next()];case 2:if(r=a.sent(),!!r.done)return[3,4];if(s=r.value,t.next(s),t.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return u=a.sent(),o={error:u},[3,11];case 6:return a.trys.push([6,,9,10]),r&&!r.done&&(i=n.return)?[4,i.call(n)]:[3,8];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function Yt(e,t,n,r,o){r===void 0&&(r=0),o===void 0&&(o=!1);var i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Bm(e,t){return t===void 0&&(t=0),ht(function(n,r){n.subscribe(Pt(r,function(o){return Yt(r,e,function(){return r.next(o)},t)},function(){return Yt(r,e,function(){return r.complete()},t)},function(o){return Yt(r,e,function(){return r.error(o)},t)}))})}function Um(e,t){return t===void 0&&(t=0),ht(function(n,r){r.add(e.schedule(function(){return n.subscribe(r)},t))})}function C_(e,t){return rn(e).pipe(Um(t),Bm(t))}function I_(e,t){return rn(e).pipe(Um(t),Bm(t))}function k_(e,t){return new Ie(function(n){var r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function N_(e,t){return new Ie(function(n){var r;return Yt(n,t,function(){r=e[bm](),Yt(n,t,function(){var o,i,s;try{o=r.next(),i=o.value,s=o.done}catch(u){n.error(u);return}s?n.complete():n.next(i)},0,!0)}),function(){return te(r==null?void 0:r.return)&&r.return()}})}function $m(e,t){if(!e)throw new Error("Iterable cannot be null");return new Ie(function(n){Yt(n,t,function(){var r=e[Symbol.asyncIterator]();Yt(n,t,function(){r.next().then(function(o){o.done?n.complete():n.next(o.value)})},0,!0)})})}function P_(e,t){return $m(Mm(e),t)}function R_(e,t){if(e!=null){if(Om(e))return C_(e,t);if(Pm(e))return k_(e,t);if(Rm(e))return I_(e,t);if(Lm(e))return $m(e,t);if(Dm(e))return N_(e,t);if(Fm(e))return P_(e,t)}throw Am(e)}function jm(e,t){return t?R_(e,t):rn(e)}function yi(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=y_(e);return jm(e,n)}function O_(e,t){var n=te(e)?e:function(){return e},r=function(o){return o.error(n())};return new Ie(r)}var L_=Yl(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function A_(e,t){return new Promise(function(n,r){var o=new Xi({next:function(i){n(i),o.unsubscribe()},error:r,complete:function(){r(new L_)}});e.subscribe(o)})}function b_(e){return e instanceof Date&&!isNaN(e)}var D_=Yl(function(e){return function(n){n===void 0&&(n=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n}});function M_(e,t){var n=b_(e)?{first:e}:typeof e=="number"?{each:e}:e,r=n.first,o=n.each,i=n.with,s=i===void 0?F_:i,u=n.scheduler,a=u===void 0?h_:u,l=n.meta,c=l===void 0?null:l;if(r==null&&o==null)throw new TypeError("No timeout provided.");return ht(function(f,d){var m,y,v=null,S=0,h=function(p){y=Yt(d,a,function(){try{m.unsubscribe(),rn(s({meta:c,lastValue:v,seen:S})).subscribe(d)}catch(g){d.error(g)}},p)};m=f.subscribe(Pt(d,function(p){y==null||y.unsubscribe(),S++,d.next(v=p),o>0&&h(o)},void 0,void 0,function(){y!=null&&y.closed||y==null||y.unsubscribe(),v=null})),!S&&h(r!=null?typeof r=="number"?r:+r-a.now():o)})}function F_(e){throw new D_(e)}function zm(e,t){return ht(function(n,r){var o=0;n.subscribe(Pt(r,function(i){r.next(e.call(t,i,o++))}))})}function H_(e,t,n,r,o,i,s,u){var a=[],l=0,c=0,f=!1,d=function(){f&&!a.length&&!l&&t.complete()},m=function(v){return l<r?y(v):a.push(v)},y=function(v){l++;var S=!1;rn(n(v,c++)).subscribe(Pt(t,function(h){t.next(h)},function(){S=!0},void 0,function(){if(S)try{l--;for(var h=function(){var p=a.shift();s||y(p)};a.length&&l<r;)h();d()}catch(p){t.error(p)}}))};return e.subscribe(Pt(t,m,function(){f=!0,d()})),function(){}}function Zl(e,t,n){return n===void 0&&(n=1/0),te(t)?Zl(function(r,o){return zm(function(i,s){return t(r,i,o,s)})(rn(e(r,o)))},n):(typeof t=="number"&&(n=t),ht(function(r,o){return H_(r,o,e,n)}))}var Gm=new Ie(Wi);function ka(e,t){return ht(function(n,r){var o=0;n.subscribe(Pt(r,function(i){return e.call(t,i,o++)&&r.next(i)}))})}function Vm(e){return ht(function(t,n){var r=null,o=!1,i;r=t.subscribe(Pt(n,void 0,void 0,function(s){i=rn(e(s,Vm(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function B_(e,t,n,r,o){return function(i,s){var u=n,a=t,l=0;i.subscribe(Pt(s,function(c){var f=l++;a=u?e(a,c,f):(u=!0,c)},function(){u&&s.next(a),s.complete()}))}}function U_(e,t){return ht(B_(e,t,arguments.length>=2,!1,!0))}var $_=function(e,t){return e.push(t),e};function j_(){return ht(function(e,t){U_($_,[])(e).subscribe(t)})}function ql(e){return ht(function(t,n){rn(e).subscribe(Pt(n,function(){return n.complete()},Wi)),!n.closed&&t.subscribe(n)})}var Na={exports:{}},yu,jf;function z_(){if(jf)return yu;jf=1;var e=1e3,t=e*60,n=t*60,r=n*24,o=r*7,i=r*365.25;yu=function(c,f){f=f||{};var d=typeof c;if(d==="string"&&c.length>0)return s(c);if(d==="number"&&isFinite(c))return f.long?a(c):u(c);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(c))};function s(c){if(c=String(c),!(c.length>100)){var f=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(f){var d=parseFloat(f[1]),m=(f[2]||"ms").toLowerCase();switch(m){case"years":case"year":case"yrs":case"yr":case"y":return d*i;case"weeks":case"week":case"w":return d*o;case"days":case"day":case"d":return d*r;case"hours":case"hour":case"hrs":case"hr":case"h":return d*n;case"minutes":case"minute":case"mins":case"min":case"m":return d*t;case"seconds":case"second":case"secs":case"sec":case"s":return d*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return d;default:return}}}}function u(c){var f=Math.abs(c);return f>=r?Math.round(c/r)+"d":f>=n?Math.round(c/n)+"h":f>=t?Math.round(c/t)+"m":f>=e?Math.round(c/e)+"s":c+"ms"}function a(c){var f=Math.abs(c);return f>=r?l(c,f,r,"day"):f>=n?l(c,f,n,"hour"):f>=t?l(c,f,t,"minute"):f>=e?l(c,f,e,"second"):c+" ms"}function l(c,f,d,m){var y=f>=d*1.5;return Math.round(c/d)+" "+m+(y?"s":"")}return yu}function G_(e){n.debug=n,n.default=n,n.coerce=a,n.disable=s,n.enable=o,n.enabled=u,n.humanize=z_(),n.destroy=l,Object.keys(e).forEach(c=>{n[c]=e[c]}),n.names=[],n.skips=[],n.formatters={};function t(c){let f=0;for(let d=0;d<c.length;d++)f=(f<<5)-f+c.charCodeAt(d),f|=0;return n.colors[Math.abs(f)%n.colors.length]}n.selectColor=t;function n(c){let f,d=null,m,y;function v(...S){if(!v.enabled)return;const h=v,p=Number(new Date),g=p-(f||p);h.diff=g,h.prev=f,h.curr=p,f=p,S[0]=n.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let E=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(x,I)=>{if(x==="%%")return"%";E++;const N=n.formatters[I];if(typeof N=="function"){const X=S[E];x=N.call(h,X),S.splice(E,1),E--}return x}),n.formatArgs.call(h,S),(h.log||n.log).apply(h,S)}return v.namespace=c,v.useColors=n.useColors(),v.color=n.selectColor(c),v.extend=r,v.destroy=n.destroy,Object.defineProperty(v,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==n.namespaces&&(m=n.namespaces,y=n.enabled(c)),y),set:S=>{d=S}}),typeof n.init=="function"&&n.init(v),v}function r(c,f){const d=n(this.namespace+(typeof f>"u"?":":f)+c);return d.log=this.log,d}function o(c){n.save(c),n.namespaces=c,n.names=[],n.skips=[];const f=(typeof c=="string"?c:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const d of f)d[0]==="-"?n.skips.push(d.slice(1)):n.names.push(d)}function i(c,f){let d=0,m=0,y=-1,v=0;for(;d<c.length;)if(m<f.length&&(f[m]===c[d]||f[m]==="*"))f[m]==="*"?(y=m,v=d,m++):(d++,m++);else if(y!==-1)m=y+1,v++,d=v;else return!1;for(;m<f.length&&f[m]==="*";)m++;return m===f.length}function s(){const c=[...n.names,...n.skips.map(f=>"-"+f)].join(",");return n.enable(""),c}function u(c){for(const f of n.skips)if(i(c,f))return!1;for(const f of n.names)if(i(c,f))return!0;return!1}function a(c){return c instanceof Error?c.stack||c.message:c}function l(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}var V_=G_;(function(e,t){var n={};t.formatArgs=o,t.save=i,t.load=s,t.useColors=r,t.storage=u(),t.destroy=(()=>{let l=!1;return()=>{l||(l=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function r(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let l;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(l=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(l[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function o(l){if(l[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+l[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const c="color: "+this.color;l.splice(1,0,c,"color: inherit");let f=0,d=0;l[0].replace(/%[a-zA-Z%]/g,m=>{m!=="%%"&&(f++,m==="%c"&&(d=f))}),l.splice(d,0,c)}t.log=console.debug||console.log||(()=>{});function i(l){try{l?t.storage.setItem("debug",l):t.storage.removeItem("debug")}catch{}}function s(){let l;try{l=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch{}return!l&&typeof process<"u"&&"env"in process&&(l=n.DEBUG),l}function u(){try{return localStorage}catch{}}e.exports=V_(t);const{formatters:a}=e.exports;a.j=function(l){try{return JSON.stringify(l)}catch(c){return"[UnexpectedJSONParseError]: "+c.message}}})(Na,Na.exports);var W_=Na.exports;const Wm=Od(W_),zf=Wm("claude:ipc");class X_{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(ql(this.onDestroy)).subscribe(t)}}function Y_(e){return new Ie(t=>e.subscribe(t))}function Q_(e){return e==null?!1:typeof e=="object"&&"then"in e}function K_(e){return e!==null&&(typeof e=="object"||typeof e=="function")&&typeof e.subscribe=="function"}let vu={};function Z_(e,t,n,r){var s,u;const o=q_(e,t,n,r),i=Xm(t);return(u=(s=vu[e])==null?void 0:s.subscription)==null||u.unsubscribe(),vu[e]={subscription:o,metadata:i},o.add(()=>delete vu[e]),o}function q_(e,t,n,r){return Y_(n).pipe(ka(o=>o.methodChain.split(".")[0]===e),ka(o=>r(o)?!0:(console.error(`Invalid message received: ${JSON.stringify(o)}`),!1)),Zl(o=>{const i=o.methodChain.split(".").splice(1),s=i.pop(),u=i.reduce((f,d)=>f[d],t),a=o.customSendMethod??n.next.bind(n);let l;try{const f=u[s];zf('Calling method "%s" with args %o',s,o.argList),l=f.call(u,...o.argList)}catch(f){return zf(`Error in API call for message: %o
%o`,o,f),yi({sendMethod:a,result:{error:f,callId:o.callId}})}let c=yi(l);return Q_(l)?c=jm(l):K_(l)&&(c=l.pipe(j_())),c.pipe(zm(f=>({sendMethod:a,result:{result:f,callId:o.callId}})),Vm(f=>yi({sendMethod:a,result:{result:null,callId:o.callId,error:f}})))}),ql(n.onDestroy)).subscribe({next:o=>o.sendMethod(o.result),error:o=>{console.error(`Error in API Handler - this should not happen! ${o}
${o.stack}`)}})}function Xm(e){return J_(e).reduce((t,n)=>(typeof e[n]=="function"&&(t[n]=!0),typeof e[n]=="object"&&e[n]!==null&&(t[n]=Xm(e[n])),t),{})}function J_(e){const t=Object.keys(e),n=Object.getOwnPropertyNames(e).filter(s=>!t.includes(s)),r=[];let o=Object.getPrototypeOf(e);for(;o&&o!==Object.prototype;)Object.getOwnPropertyNames(o).filter(s=>!["constructor"].includes(s)).forEach(s=>{!r.includes(s)&&!t.includes(s)&&!n.includes(s)&&r.push(s)}),o=Object.getPrototypeOf(o);const i=Object.getOwnPropertySymbols(e).map(s=>s.toString());return[...t,...n,...r,...i]}function eS(){return""}function tS(e){return new Ie(t=>e.subscribe(t))}const Ym=5e3,nS=Wm("claude:ipc");function MT(e,t,n=Ym){return Pa.create(e,(r,o)=>oS(t,r.join("."),o,n))}let rS=0;function oS(e,t,n,r=Ym){const o=++rS,i={sender:{},callId:`${o}`,methodChain:t,argList:n};nS("sending message %o, stack: %s",i,eS());let s=A_(tS(e).pipe(ka(u=>u.callId===`${o}`),Zl(u=>u.error?O_(()=>u.error):yi(u.result)),M_(r)));return e.next(i),s}var rr,or,ir,hn,go,vr,Qm,Km;const qi=class qi{constructor(t,n,r=null,o=null){un(this,vr);un(this,rr);un(this,or);un(this,ir);un(this,hn);un(this,go,{});Ir(this,rr,t),Ir(this,or,n),Ir(this,ir,r),Ir(this,hn,o)}static create(t,n,r=null){return new Proxy(()=>{},new qi(t,n,null,r))}get(t,n){return je(this,hn)&&n in je(this,hn)?je(this,hn)[n]:new Proxy(()=>{},Hs(this,vr,Qm).call(this,n))}apply(t,n,r){const o=[Hs(this,vr,Km).call(this,je(this,rr))];let i=je(this,ir);for(;i;)o.unshift(je(i,rr)),i=je(i,ir);return je(this,or).call(this,o,r)}};rr=new WeakMap,or=new WeakMap,ir=new WeakMap,hn=new WeakMap,go=new WeakMap,vr=new WeakSet,Qm=function(t){let n=je(this,go)[t];return n||(n=new qi(t.toString(),je(this,or),this),je(this,go)[t]=n,n)},Km=function(t){return t.replace(/_get$/,"")};let Pa=qi;class iS{constructor(t,n,r){this.outgoing=t,this.incoming=n,this.onDestroy=r}next(t){this.outgoing.next(t)}error(t){this.outgoing.error(t)}complete(){this.outgoing.complete()}subscribe(t){return this.incoming.pipe(ql(this.onDestroy)).subscribe(t)}}function sS(){return new iS({next:e=>window.rpcInternal.rpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new Ie(e=>window.rpcInternal.rpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),Gm)}function uS(){return new X_({next:e=>window.rpcInternal.reverseRpcAsyncSend(e),error:e=>{throw new Error(e)},complete:()=>{}},new Ie(e=>window.rpcInternal.reverseRpcAsyncRecv(n=>{Array.isArray(n)&&e.next(n[0])})),Gm)}function aS(e,t){return Z_(e,t,window.replyPort,()=>!0)}window.sendPort=sS();window.replyPort=uS();var Zm=(e=>(e.QuickWindow="QuickWindow",e.Find="Find",e.StartupSettings="StartupSettings",e.Filesystem="Filesystem",e.Intl="Intl",e.IntlSync="IntlSync",e.AboutWindow="AboutWindow",e.WindowControl="WindowControl",e))(Zm||{});function lS(e){const[t,n]=me.useState(window.initialLocale),[r,o]=me.useState(window.initialMessages);return me.useEffect(()=>{const i=aS(Zm.Intl,{localeChanged:(s,u)=>{n(s),o(u)}});return()=>i.unsubscribe()},[n,o]),wu.jsx(QE,{locale:t,messages:r,...e})}async function qm(e,t,n){const r=await t,o="default"in r?r.default:r,i=Gh(e),s=n??{};return i.render(wu.jsx(lS,{children:wu.jsx(o,{...s})})),()=>{i.unmount()}}window.attachReactToElement=qm;const Jm=Object.prototype.toString;function Jl(e){switch(Jm.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Tn(e,Error)}}function xr(e,t){return Jm.call(e)===`[object ${t}]`}function eg(e){return xr(e,"ErrorEvent")}function Gf(e){return xr(e,"DOMError")}function cS(e){return xr(e,"DOMException")}function xt(e){return xr(e,"String")}function ec(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function tc(e){return e===null||ec(e)||typeof e!="object"&&typeof e!="function"}function hr(e){return xr(e,"Object")}function Ns(e){return typeof Event<"u"&&Tn(e,Event)}function fS(e){return typeof Element<"u"&&Tn(e,Element)}function dS(e){return xr(e,"RegExp")}function Ps(e){return!!(e&&e.then&&typeof e.then=="function")}function pS(e){return hr(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function Tn(e,t){try{return e instanceof t}catch{return!1}}function tg(e){return!!(typeof e=="object"&&e!==null&&(e.__isVue||e._isVue))}function nr(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function Vf(e,t){if(!Array.isArray(e))return"";const n=[];for(let r=0;r<e.length;r++){const o=e[r];try{tg(o)?n.push("[VueViewModel]"):n.push(String(o))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function hS(e,t,n=!1){return xt(e)?dS(t)?t.test(e):xt(t)?n?e===t:e.includes(t):!1:!1}function Rs(e,t=[],n=!1){return t.some(r=>hS(e,r,n))}function mS(e,t,n=250,r,o,i,s){if(!i.exception||!i.exception.values||!s||!Tn(s.originalException,Error))return;const u=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;u&&(i.exception.values=gS(Ra(e,t,o,s.originalException,r,i.exception.values,u,0),n))}function Ra(e,t,n,r,o,i,s,u){if(i.length>=n+1)return i;let a=[...i];if(Tn(r[o],Error)){Wf(s,u);const l=e(t,r[o]),c=a.length;Xf(l,o,c,u),a=Ra(e,t,n,r[o],o,[l,...a],l,c)}return Array.isArray(r.errors)&&r.errors.forEach((l,c)=>{if(Tn(l,Error)){Wf(s,u);const f=e(t,l),d=a.length;Xf(f,`errors[${c}]`,d,u),a=Ra(e,t,n,l,o,[f,...a],f,d)}}),a}function Wf(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function Xf(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function gS(e,t){return e.map(n=>(n.value&&(n.value=nr(n.value,t)),n))}function ng(e){if(e!==void 0)return e>=400&&e<500?"warning":e>=500?"error":void 0}const yn="8.33.1",F=globalThis;function Os(e,t,n){const r=n||F,o=r.__SENTRY__=r.__SENTRY__||{},i=o[yn]=o[yn]||{};return i[e]||(i[e]=t())}const nc=F,yS=80;function rg(e,t={}){if(!e)return"<unknown>";try{let n=e;const r=5,o=[];let i=0,s=0;const u=" > ",a=u.length;let l;const c=Array.isArray(t)?t:t.keyAttrs,f=!Array.isArray(t)&&t.maxStringLength||yS;for(;n&&i++<r&&(l=vS(n,c),!(l==="html"||i>1&&s+o.length*a+l.length>=f));)o.push(l),s+=l.length,n=n.parentNode;return o.reverse().join(u)}catch{return"<unknown>"}}function vS(e,t){const n=e,r=[];if(!n||!n.tagName)return"";if(nc.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const o=t&&t.length?t.filter(s=>n.getAttribute(s)).map(s=>[s,n.getAttribute(s)]):null;if(o&&o.length)o.forEach(s=>{r.push(`[${s[0]}="${s[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const s=n.className;if(s&&xt(s)){const u=s.split(/\s+/);for(const a of u)r.push(`.${a}`)}}const i=["aria-label","type","name","title","alt"];for(const s of i){const u=n.getAttribute(s);u&&r.push(`[${s}="${u}"]`)}return r.join("")}function ES(){try{return nc.document.location.href}catch{return""}}function _S(e){if(!nc.HTMLElement)return null;let t=e;const n=5;for(let r=0;r<n;r++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}const To=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,SS="Sentry Logger ",Oa=["debug","info","warn","error","log","assert","trace"],Yi={};function Co(e){if(!("console"in F))return e();const t=F.console,n={},r=Object.keys(Yi);r.forEach(o=>{const i=Yi[o];n[o]=t[o],t[o]=i});try{return e()}finally{r.forEach(o=>{t[o]=n[o]})}}function wS(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return To?Oa.forEach(n=>{t[n]=(...r)=>{e&&Co(()=>{F.console[n](`${SS}[${n}]:`,...r)})}}):Oa.forEach(n=>{t[n]=()=>{}}),t}const P=Os("logger",wS),xS=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function TS(e){return e==="http"||e==="https"}function Ls(e,t=!1){const{host:n,path:r,pass:o,port:i,projectId:s,protocol:u,publicKey:a}=e;return`${u}://${a}${t&&o?`:${o}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${s}`}function CS(e){const t=xS.exec(e);if(!t){Co(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});return}const[n,r,o="",i="",s="",u=""]=t.slice(1);let a="",l=u;const c=l.split("/");if(c.length>1&&(a=c.slice(0,-1).join("/"),l=c.pop()),l){const f=l.match(/^\d+/);f&&(l=f[0])}return og({host:i,pass:o,path:a,projectId:l,port:s,protocol:n,publicKey:r})}function og(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function IS(e){if(!To)return!0;const{port:t,projectId:n,protocol:r}=e;return["protocol","publicKey","host","projectId"].find(s=>e[s]?!1:(P.error(`Invalid Sentry Dsn: ${s} missing`),!0))?!1:n.match(/^\d+$/)?TS(r)?t&&isNaN(parseInt(t,10))?(P.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):!0:(P.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(P.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function kS(e){const t=typeof e=="string"?CS(e):og(e);if(!(!t||!IS(t)))return t}class ct extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}function be(e,t,n){if(!(t in e))return;const r=e[t],o=n(r);typeof o=="function"&&ig(o,r),e[t]=o}function Cn(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{To&&P.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function ig(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,Cn(e,"__sentry_original__",t)}catch{}}function rc(e){return e.__sentry_original__}function NS(e){return Object.keys(e).map(t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`).join("&")}function sg(e){if(Jl(e))return{message:e.message,name:e.name,stack:e.stack,...Qf(e)};if(Ns(e)){const t={type:e.type,target:Yf(e.target),currentTarget:Yf(e.currentTarget),...Qf(e)};return typeof CustomEvent<"u"&&Tn(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function Yf(e){try{return fS(e)?rg(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function Qf(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}else return{}}function PS(e,t=40){const n=Object.keys(sg(e));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return nr(r,t);for(let o=n.length;o>0;o--){const i=n.slice(0,o).join(", ");if(!(i.length>t))return o===n.length?i:nr(i,t)}return""}function De(e){return La(e,new Map)}function La(e,t){if(RS(e)){const n=t.get(e);if(n!==void 0)return n;const r={};t.set(e,r);for(const o of Object.getOwnPropertyNames(e))typeof e[o]<"u"&&(r[o]=La(e[o],t));return r}if(Array.isArray(e)){const n=t.get(e);if(n!==void 0)return n;const r=[];return t.set(e,r),e.forEach(o=>{r.push(La(o,t))}),r}return e}function RS(e){if(!hr(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||t==="Object"}catch{return!0}}const ug=50,Zt="?",Kf=/\(error: (.*)\)/,Zf=/captureMessage|captureException/;function ag(...e){const t=e.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,o=0)=>{const i=[],s=n.split(`
`);for(let u=r;u<s.length;u++){const a=s[u];if(a.length>1024)continue;const l=Kf.test(a)?a.replace(Kf,"$1"):a;if(!l.match(/\S*Error: /)){for(const c of t){const f=c(l);if(f){i.push(f);break}}if(i.length>=ug+o)break}}return lg(i.slice(o))}}function OS(e){return Array.isArray(e)?ag(...e):e}function lg(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test(Zo(t).function||"")&&t.pop(),t.reverse(),Zf.test(Zo(t).function||"")&&(t.pop(),Zf.test(Zo(t).function||"")&&t.pop()),t.slice(0,ug).map(n=>({...n,filename:n.filename||Zo(t).filename,function:n.function||Zt}))}function Zo(e){return e[e.length-1]||{}}const Eu="<anonymous>";function qt(e){try{return!e||typeof e!="function"?Eu:e.name||Eu}catch{return Eu}}function qf(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const vi={},Jf={};function Ln(e,t){vi[e]=vi[e]||[],vi[e].push(t)}function An(e,t){Jf[e]||(t(),Jf[e]=!0)}function it(e,t){const n=e&&vi[e];if(n)for(const r of n)try{r(t)}catch(o){To&&P.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${qt(r)}
Error:`,o)}}function LS(e){const t="console";Ln(t,e),An(t,AS)}function AS(){"console"in F&&Oa.forEach(function(e){e in F.console&&be(F.console,e,function(t){return Yi[e]=t,function(...n){it("console",{args:n,level:e});const o=Yi[e];o&&o.apply(F.console,n)}})})}const Aa=F;function cg(){if(!("fetch"in Aa))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function ba(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function bS(){if(typeof EdgeRuntime=="string")return!0;if(!cg())return!1;if(ba(Aa.fetch))return!0;let e=!1;const t=Aa.document;if(t&&typeof t.createElement=="function")try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=ba(n.contentWindow.fetch)),t.head.removeChild(n)}catch(n){To&&P.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return e}const fg=1e3;function Io(){return Date.now()/fg}function DS(){const{performance:e}=F;if(!e||!e.now)return Io;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/fg}const Tt=DS();(()=>{const{performance:e}=F;if(!e||!e.now)return;const t=3600*1e3,n=e.now(),r=Date.now(),o=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,i=o<t,s=e.timing&&e.timing.navigationStart,a=typeof s=="number"?Math.abs(s+n-r):t,l=a<t;return i||l?o<=a?e.timeOrigin:s:r})();function MS(e,t){const n="fetch";Ln(n,e),An(n,()=>FS(void 0,t))}function FS(e,t=!1){t&&!bS()||be(F,"fetch",function(n){return function(...r){const{method:o,url:i}=HS(r),s={args:r,fetchData:{method:o,url:i},startTimestamp:Tt()*1e3};it("fetch",{...s});const u=new Error().stack;return n.apply(F,r).then(async a=>(it("fetch",{...s,endTimestamp:Tt()*1e3,response:a}),a),a=>{throw it("fetch",{...s,endTimestamp:Tt()*1e3,error:a}),Jl(a)&&a.stack===void 0&&(a.stack=u,Cn(a,"framesToPop",1)),a})}})}function Da(e,t){return!!e&&typeof e=="object"&&!!e[t]}function ed(e){return typeof e=="string"?e:e?Da(e,"url")?e.url:e.toString?e.toString():"":""}function HS(e){if(e.length===0)return{method:"GET",url:""};if(e.length===2){const[n,r]=e;return{url:ed(n),method:Da(r,"method")?String(r.method).toUpperCase():"GET"}}const t=e[0];return{url:ed(t),method:Da(t,"method")?String(t.method).toUpperCase():"GET"}}let qo=null;function BS(e){const t="error";Ln(t,e),An(t,US)}function US(){qo=F.onerror,F.onerror=function(e,t,n,r,o){return it("error",{column:r,error:o,line:n,msg:e,url:t}),qo&&!qo.__SENTRY_LOADER__?qo.apply(this,arguments):!1},F.onerror.__SENTRY_INSTRUMENTED__=!0}let Jo=null;function $S(e){const t="unhandledrejection";Ln(t,e),An(t,jS)}function jS(){Jo=F.onunhandledrejection,F.onunhandledrejection=function(e){return it("unhandledrejection",e),Jo&&!Jo.__SENTRY_LOADER__?Jo.apply(this,arguments):!0},F.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function zS(){return"npm"}function GS(){const e=typeof WeakSet=="function",t=e?new WeakSet:[];function n(o){if(e)return t.has(o)?!0:(t.add(o),!1);for(let i=0;i<t.length;i++)if(t[i]===o)return!0;return t.push(o),!1}function r(o){if(e)t.delete(o);else for(let i=0;i<t.length;i++)if(t[i]===o){t.splice(i,1);break}}return[n,r]}function Fe(){const e=F,t=e.crypto||e.msCrypto;let n=()=>Math.random()*16;try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(n=()=>{const r=new Uint8Array(1);return t.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function dg(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function bt(e){const{message:t,event_id:n}=e;if(t)return t;const r=dg(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Ma(e,t,n){const r=e.exception=e.exception||{},o=r.values=r.values||[],i=o[0]=o[0]||{};i.value||(i.value=t||""),i.type||(i.type="Error")}function mo(e,t){const n=dg(e);if(!n)return;const r={type:"generic",handled:!0},o=n.mechanism;if(n.mechanism={...r,...o,...t},t&&"data"in t){const i={...o&&o.data,...t.data};n.mechanism.data=i}}function td(e){if(e&&e.__sentry_captured__)return!0;try{Cn(e,"__sentry_captured__",!0)}catch{}return!1}function pg(e){return Array.isArray(e)?e:[e]}function Et(e,t=100,n=1/0){try{return Fa("",e,t,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function hg(e,t=3,n=100*1024){const r=Et(e,t);return YS(r)>n?hg(e,t-1,n):r}function Fa(e,t,n=1/0,r=1/0,o=GS()){const[i,s]=o;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const u=VS(e,t);if(!u.startsWith("[object "))return u;if(t.__sentry_skip_normalization__)return t;const a=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(a===0)return u.replace("object ","");if(i(t))return"[Circular ~]";const l=t;if(l&&typeof l.toJSON=="function")try{const m=l.toJSON();return Fa("",m,a-1,r,o)}catch{}const c=Array.isArray(t)?[]:{};let f=0;const d=sg(t);for(const m in d){if(!Object.prototype.hasOwnProperty.call(d,m))continue;if(f>=r){c[m]="[MaxProperties ~]";break}const y=d[m];c[m]=Fa(m,y,a-1,r,o),f++}return s(t),c}function VS(e,t){try{if(e==="domain"&&t&&typeof t=="object"&&t._events)return"[Domain]";if(e==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&t===global)return"[Global]";if(typeof window<"u"&&t===window)return"[Window]";if(typeof document<"u"&&t===document)return"[Document]";if(tg(t))return"[VueViewModel]";if(pS(t))return"[SyntheticEvent]";if(typeof t=="number"&&!Number.isFinite(t))return`[${t}]`;if(typeof t=="function")return`[Function: ${qt(t)}]`;if(typeof t=="symbol")return`[${String(t)}]`;if(typeof t=="bigint")return`[BigInt: ${String(t)}]`;const n=WS(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function WS(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}function XS(e){return~-encodeURI(e).split(/%..|./).length}function YS(e){return XS(JSON.stringify(e))}var yt;(function(e){e[e.PENDING=0]="PENDING";const n=1;e[e.RESOLVED=n]="RESOLVED";const r=2;e[e.REJECTED=r]="REJECTED"})(yt||(yt={}));function In(e){return new Ve(t=>{t(e)})}function Qi(e){return new Ve((t,n)=>{n(e)})}class Ve{constructor(t){Ve.prototype.__init.call(this),Ve.prototype.__init2.call(this),Ve.prototype.__init3.call(this),Ve.prototype.__init4.call(this),this._state=yt.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(n){this._reject(n)}}then(t,n){return new Ve((r,o)=>{this._handlers.push([!1,i=>{if(!t)r(i);else try{r(t(i))}catch(s){o(s)}},i=>{if(!n)o(i);else try{r(n(i))}catch(s){o(s)}}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new Ve((n,r)=>{let o,i;return this.then(s=>{i=!1,o=s,t&&t()},s=>{i=!0,o=s,t&&t()}).then(()=>{if(i){r(o);return}n(o)})})}__init(){this._resolve=t=>{this._setResult(yt.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(yt.REJECTED,t)}}__init3(){this._setResult=(t,n)=>{if(this._state===yt.PENDING){if(Ps(n)){n.then(this._resolve,this._reject);return}this._state=t,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===yt.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===yt.RESOLVED&&n[1](this._value),this._state===yt.REJECTED&&n[2](this._value),n[0]=!0)})}}}function QS(e){const t=[];function n(){return e===void 0||t.length<e}function r(s){return t.splice(t.indexOf(s),1)[0]||Promise.resolve(void 0)}function o(s){if(!n())return Qi(new ct("Not adding Promise because buffer limit was reached."));const u=s();return t.indexOf(u)===-1&&t.push(u),u.then(()=>r(u)).then(null,()=>r(u).then(null,()=>{})),u}function i(s){return new Ve((u,a)=>{let l=t.length;if(!l)return u(!0);const c=setTimeout(()=>{s&&s>0&&u(!1)},s);t.forEach(f=>{In(f).then(()=>{--l||(clearTimeout(c),u(!0))},a)})})}return{$:t,add:o,drain:i}}function _u(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}const KS=["fatal","error","warning","log","info","debug"];function ZS(e){return e==="warn"?"warning":KS.includes(e)?e:"log"}function qS(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&e!==void 0&&!e.includes("node_modules/")}function JS(e){const t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{const o=r.match(n);if(o){let i,s,u,a,l;if(o[1]){u=o[1];let d=u.lastIndexOf(".");if(u[d-1]==="."&&d--,d>0){i=u.slice(0,d),s=u.slice(d+1);const m=i.indexOf(".Module");m>0&&(u=u.slice(m+1),i=i.slice(0,m))}a=void 0}s&&(a=i,l=s),s==="<anonymous>"&&(l=void 0,u=void 0),u===void 0&&(l=l||Zt,u=a?`${a}.${l}`:l);let c=o[2]&&o[2].startsWith("file://")?o[2].slice(7):o[2];const f=o[5]==="native";return c&&c.match(/\/[A-Z]:/)&&(c=c.slice(1)),!c&&o[5]&&!f&&(c=o[5]),{filename:c,module:void 0,function:u,lineno:nd(o[3]),colno:nd(o[4]),in_app:qS(c||"",f)}}if(r.match(t))return{filename:r}}}function e1(e){return[90,JS()]}function nd(e){return parseInt(e||"",10)||void 0}const t1="sentry-",n1=/^sentry-/;function r1(e){const t=o1(e);if(!t)return;const n=Object.entries(t).reduce((r,[o,i])=>{if(o.match(n1)){const s=o.slice(t1.length);r[s]=i}return r},{});if(Object.keys(n).length>0)return n}function o1(e){if(!(!e||!xt(e)&&!Array.isArray(e)))return Array.isArray(e)?e.reduce((t,n)=>{const r=rd(n);return Object.entries(r).forEach(([o,i])=>{t[o]=i}),t},{}):rd(e)}function rd(e){return e.split(",").map(t=>t.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((t,[n,r])=>(n&&r&&(t[n]=r),t),{})}function ko(e,t=[]){return[e,t]}function i1(e,t){const[n,r]=e;return[n,[...r,t]]}function od(e,t){const n=e[1];for(const r of n){const o=r[0].type;if(t(r,o))return!0}return!1}function Ha(e){return F.__SENTRY__&&F.__SENTRY__.encodePolyfill?F.__SENTRY__.encodePolyfill(e):new TextEncoder().encode(e)}function s1(e){const[t,n]=e;let r=JSON.stringify(t);function o(i){typeof r=="string"?r=typeof i=="string"?r+i:[Ha(r),i]:r.push(typeof i=="string"?Ha(i):i)}for(const i of n){const[s,u]=i;if(o(`
${JSON.stringify(s)}
`),typeof u=="string"||u instanceof Uint8Array)o(u);else{let a;try{a=JSON.stringify(u)}catch{a=JSON.stringify(Et(u))}o(a)}}return typeof r=="string"?r:u1(r)}function u1(e){const t=e.reduce((o,i)=>o+i.length,0),n=new Uint8Array(t);let r=0;for(const o of e)n.set(o,r),r+=o.length;return n}function a1(e){const t=typeof e.data=="string"?Ha(e.data):e.data;return[De({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const l1={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function id(e){return l1[e]}function mg(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function c1(e,t,n,r){const o=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:Ls(r)},...o&&{trace:De({...o})}}}function f1(e,t,n){const r=[{type:"client_report"},{timestamp:Io(),discarded_events:e}];return ko(t?{dsn:t}:{},[r])}const d1=60*1e3;function p1(e,t=Date.now()){const n=parseInt(`${e}`,10);if(!isNaN(n))return n*1e3;const r=Date.parse(`${e}`);return isNaN(r)?d1:r-t}function h1(e,t){return e[t]||e.all||0}function m1(e,t,n=Date.now()){return h1(e,t)>n}function g1(e,{statusCode:t,headers:n},r=Date.now()){const o={...e},i=n&&n["x-sentry-rate-limits"],s=n&&n["retry-after"];if(i)for(const u of i.trim().split(",")){const[a,l,,,c]=u.split(":",5),f=parseInt(a,10),d=(isNaN(f)?60:f)*1e3;if(!l)o.all=r+d;else for(const m of l.split(";"))m==="metric_bucket"?(!c||c.split(";").includes("custom"))&&(o[m]=r+d):o[m]=r+d}else s?o.all=r+p1(s,r):t===429&&(o.all=r+60*1e3);return o}function sd(){return{traceId:Fe(),spanId:Fe().substring(16)}}const ei=F;function y1(){const e=ei.chrome,t=e&&e.app&&e.app.runtime,n="history"in ei&&!!ei.history.pushState&&!!ei.history.replaceState;return!t&&n}const U=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function As(){return oc(F),F}function oc(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||yn,t[yn]=t[yn]||{}}function v1(e){const t=Tt(),n={sid:Fe(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>_1(n)};return e&&mr(n,e),n}function mr(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||Tt(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:Fe()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function E1(e,t){let n={};e.status==="ok"&&(n={status:"exited"}),mr(e,n)}function _1(e){return De({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}const Ba="_sentrySpan";function ud(e,t){t?Cn(e,Ba,t):delete e[Ba]}function ad(e){return e[Ba]}const S1=100;class ic{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=sd()}clone(){const t=new ic;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,ud(t,ad(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&mr(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,[r,o]=n instanceof kn?[n.getScopeData(),n.getRequestSession()]:hr(n)?[t,t.requestSession]:[],{tags:i,extra:s,user:u,contexts:a,level:l,fingerprint:c=[],propagationContext:f}=r||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...s},this._contexts={...this._contexts,...a},u&&Object.keys(u).length&&(this._user=u),l&&(this._level=l),c.length&&(this._fingerprint=c),f&&(this._propagationContext=f),o&&(this._requestSession=o),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,ud(this,void 0),this._attachments=[],this._propagationContext=sd(),this._notifyScopeListeners(),this}addBreadcrumb(t,n){const r=typeof n=="number"?n:S1;if(r<=0)return this;const o={timestamp:Io(),...t},i=this._breadcrumbs;return i.push(o),this._breadcrumbs=i.length>r?i.slice(-r):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:ad(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,n){const r=n&&n.event_id?n.event_id:Fe();if(!this._client)return P.warn("No client configured on scope - will not capture exception!"),r;const o=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:o,...n,event_id:r},this),r}captureMessage(t,n,r){const o=r&&r.event_id?r.event_id:Fe();if(!this._client)return P.warn("No client configured on scope - will not capture message!"),o;const i=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:i,...r,event_id:o},this),o}captureEvent(t,n){const r=n&&n.event_id?n.event_id:Fe();return this._client?(this._client.captureEvent(t,{...n,event_id:r},this),r):(P.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}const kn=ic;function w1(){return Os("defaultCurrentScope",()=>new kn)}function x1(){return Os("defaultIsolationScope",()=>new kn)}class T1{constructor(t,n){let r;t?r=t:r=new kn;let o;n?o=n:o=new kn,this._stack=[{scope:r}],this._isolationScope=o}withScope(t){const n=this._pushScope();let r;try{r=t(n)}catch(o){throw this._popScope(),o}return Ps(r)?r.then(o=>(this._popScope(),o),o=>{throw this._popScope(),o}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function gr(){const e=As(),t=oc(e);return t.stack=t.stack||new T1(w1(),x1())}function C1(e){return gr().withScope(e)}function I1(e,t){const n=gr();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function ld(e){return gr().withScope(()=>e(gr().getIsolationScope()))}function k1(){return{withIsolationScope:ld,withScope:C1,withSetScope:I1,withSetIsolationScope:(e,t)=>ld(t),getCurrentScope:()=>gr().getScope(),getIsolationScope:()=>gr().getIsolationScope()}}function sc(e){const t=oc(e);return t.acs?t.acs:k1()}function mt(){const e=As();return sc(e).getCurrentScope()}function bn(){const e=As();return sc(e).getIsolationScope()}function N1(){return Os("globalScope",()=>new kn)}function P1(...e){const t=As(),n=sc(t);if(e.length===2){const[r,o]=e;return r?n.withSetScope(r,o):n.withScope(o)}return n.withScope(e[0])}function ye(){return mt().getClient()}const R1="_sentryMetrics";function O1(e){const t=e[R1];if(!t)return;const n={};for(const[,[r,o]]of t)(n[r]||(n[r]=[])).push(De(o));return n}const L1="sentry.source",A1="sentry.sample_rate",b1="sentry.op",D1="sentry.origin",M1=0,F1=1,H1=1;function B1(e){const{spanId:t,traceId:n}=e.spanContext(),{parent_span_id:r}=Ki(e);return De({parent_span_id:r,span_id:t,trace_id:n})}function cd(e){return typeof e=="number"?fd(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?fd(e.getTime()):Tt()}function fd(e){return e>9999999999?e/1e3:e}function Ki(e){if($1(e))return e.getSpanJSON();try{const{spanId:t,traceId:n}=e.spanContext();if(U1(e)){const{attributes:r,startTime:o,name:i,endTime:s,parentSpanId:u,status:a}=e;return De({span_id:t,trace_id:n,data:r,description:i,parent_span_id:u,start_timestamp:cd(o),timestamp:cd(s)||void 0,status:z1(a),op:r[b1],origin:r[D1],_metrics_summary:O1(e)})}return{span_id:t,trace_id:n}}catch{return{}}}function U1(e){const t=e;return!!t.attributes&&!!t.startTime&&!!t.name&&!!t.endTime&&!!t.status}function $1(e){return typeof e.getSpanJSON=="function"}function j1(e){const{traceFlags:t}=e.spanContext();return t===H1}function z1(e){if(!(!e||e.code===M1))return e.code===F1?"ok":e.message||"unknown_error"}const G1="_sentryRootSpan";function gg(e){return e[G1]||e}function V1(e){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=ye(),n=t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const uc="production",W1="_frozenDsc";function yg(e,t){const n=t.getOptions(),{publicKey:r}=t.getDsn()||{},o=De({environment:n.environment||uc,release:n.release,public_key:r,trace_id:e});return t.emit("createDsc",o),o}function X1(e){const t=ye();if(!t)return{};const n=yg(Ki(e).trace_id||"",t),r=gg(e),o=r[W1];if(o)return o;const i=r.spanContext().traceState,s=i&&i.get("sentry.dsc"),u=s&&r1(s);if(u)return u;const a=Ki(r),l=a.data||{},c=l[A1];c!=null&&(n.sample_rate=`${c}`);const f=l[L1],d=a.description;return f!=="url"&&d&&(n.transaction=d),V1()&&(n.sampled=String(j1(r))),t.emit("createDsc",n,r),n}function Y1(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;if(typeof t!="number"||isNaN(t)||t<0||t>1){U&&P.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`);return}return t}function Q1(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function K1(e,t,n,r){const o=mg(n),i={sent_at:new Date().toISOString(),...o&&{sdk:o},...!!r&&t&&{dsn:Ls(t)}},s="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return ko(i,[s])}function Z1(e,t,n,r){const o=mg(n),i=e.type&&e.type!=="replay_event"?e.type:"event";Q1(e,n&&n.sdk);const s=c1(e,o,r,t);return delete e.sdkProcessingMetadata,ko(s,[[{type:i},e]])}function Ua(e,t,n,r=0){return new Ve((o,i)=>{const s=e[r];if(t===null||typeof s!="function")o(t);else{const u=s({...t},n);U&&s.id&&u===null&&P.log(`Event processor "${s.id}" dropped event`),Ps(u)?u.then(a=>Ua(e,a,n,r+1).then(o)).then(null,i):Ua(e,u,n,r+1).then(o).then(null,i)}})}function q1(e,t){const{fingerprint:n,span:r,breadcrumbs:o,sdkProcessingMetadata:i}=t;J1(e,t),r&&nw(e,r),rw(e,n),ew(e,o),tw(e,i)}function $a(e,t){const{extra:n,tags:r,user:o,contexts:i,level:s,sdkProcessingMetadata:u,breadcrumbs:a,fingerprint:l,eventProcessors:c,attachments:f,propagationContext:d,transactionName:m,span:y}=t;Dr(e,"extra",n),Dr(e,"tags",r),Dr(e,"user",o),Dr(e,"contexts",i),Dr(e,"sdkProcessingMetadata",u),s&&(e.level=s),m&&(e.transactionName=m),y&&(e.span=y),a.length&&(e.breadcrumbs=[...e.breadcrumbs,...a]),l.length&&(e.fingerprint=[...e.fingerprint,...l]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),f.length&&(e.attachments=[...e.attachments,...f]),e.propagationContext={...e.propagationContext,...d}}function Dr(e,t,n){if(n&&Object.keys(n).length){e[t]={...e[t]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[t][r]=n[r])}}function J1(e,t){const{extra:n,tags:r,user:o,contexts:i,level:s,transactionName:u}=t,a=De(n);a&&Object.keys(a).length&&(e.extra={...a,...e.extra});const l=De(r);l&&Object.keys(l).length&&(e.tags={...l,...e.tags});const c=De(o);c&&Object.keys(c).length&&(e.user={...c,...e.user});const f=De(i);f&&Object.keys(f).length&&(e.contexts={...f,...e.contexts}),s&&(e.level=s),u&&e.type!=="transaction"&&(e.transaction=u)}function ew(e,t){const n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}function tw(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}function nw(e,t){e.contexts={trace:B1(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:X1(t),...e.sdkProcessingMetadata};const n=gg(t),r=Ki(n).description;r&&!e.transaction&&e.type==="transaction"&&(e.transaction=r)}function rw(e,t){e.fingerprint=e.fingerprint?pg(e.fingerprint):[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}function ow(e,t,n,r,o,i){const{normalizeDepth:s=3,normalizeMaxBreadth:u=1e3}=e,a={...t,event_id:t.event_id||n.event_id||Fe(),timestamp:t.timestamp||Io()},l=n.integrations||e.integrations.map(S=>S.name);iw(a,e),aw(a,l),o&&o.emit("applyFrameMetadata",t),t.type===void 0&&sw(a,e.stackParser);const c=cw(r,n.captureContext);n.mechanism&&mo(a,n.mechanism);const f=o?o.getEventProcessors():[],d=N1().getScopeData();if(i){const S=i.getScopeData();$a(d,S)}if(c){const S=c.getScopeData();$a(d,S)}const m=[...n.attachments||[],...d.attachments];m.length&&(n.attachments=m),q1(a,d);const y=[...f,...d.eventProcessors];return Ua(y,a,n).then(S=>(S&&uw(S),typeof s=="number"&&s>0?lw(S,s,u):S))}function iw(e,t){const{environment:n,release:r,dist:o,maxValueLength:i=250}=t;"environment"in e||(e.environment="environment"in t?n:uc),e.release===void 0&&r!==void 0&&(e.release=r),e.dist===void 0&&o!==void 0&&(e.dist=o),e.message&&(e.message=nr(e.message,i));const s=e.exception&&e.exception.values&&e.exception.values[0];s&&s.value&&(s.value=nr(s.value,i));const u=e.request;u&&u.url&&(u.url=nr(u.url,i))}const dd=new WeakMap;function sw(e,t){const n=F._sentryDebugIds;if(!n)return;let r;const o=dd.get(t);o?r=o:(r=new Map,dd.set(t,r));const i=Object.entries(n).reduce((s,[u,a])=>{let l;const c=r.get(u);c?l=c:(l=t(u),r.set(u,l));for(let f=l.length-1;f>=0;f--){const d=l[f];if(d.filename){s[d.filename]=a;break}}return s},{});try{e.exception.values.forEach(s=>{s.stacktrace.frames.forEach(u=>{u.filename&&(u.debug_id=i[u.filename])})})}catch{}}function uw(e){const t={};try{e.exception.values.forEach(r=>{r.stacktrace.frames.forEach(o=>{o.debug_id&&(o.abs_path?t[o.abs_path]=o.debug_id:o.filename&&(t[o.filename]=o.debug_id),delete o.debug_id)})})}catch{}if(Object.keys(t).length===0)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.entries(t).forEach(([r,o])=>{n.push({type:"sourcemap",code_file:r,debug_id:o})})}function aw(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}function lw(e,t,n){if(!e)return null;const r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(o=>({...o,...o.data&&{data:Et(o.data,t,n)}}))},...e.user&&{user:Et(e.user,t,n)},...e.contexts&&{contexts:Et(e.contexts,t,n)},...e.extra&&{extra:Et(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=Et(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(o=>({...o,...o.data&&{data:Et(o.data,t,n)}}))),r}function cw(e,t){if(!t)return e;const n=e?e.clone():new kn;return n.update(t),n}function fw(e,t){return mt().captureException(e,void 0)}function vg(e,t){return mt().captureEvent(e,t)}function pd(e){const t=ye(),n=bn(),r=mt(),{release:o,environment:i=uc}=t&&t.getOptions()||{},{userAgent:s}=F.navigator||{},u=v1({release:o,environment:i,user:r.getUser()||n.getUser(),...s&&{userAgent:s},...e}),a=n.getSession();return a&&a.status==="ok"&&mr(a,{status:"exited"}),Eg(),n.setSession(u),r.setSession(u),u}function Eg(){const e=bn(),t=mt(),n=t.getSession()||e.getSession();n&&E1(n),_g(),e.setSession(),t.setSession()}function _g(){const e=bn(),t=mt(),n=ye(),r=t.getSession()||e.getSession();r&&n&&n.captureSession(r)}function hd(e=!1){if(e){Eg();return}_g()}const dw="7";function pw(e){const t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function hw(e){return`${pw(e)}${e.projectId}/envelope/`}function mw(e,t){return NS({sentry_key:e.publicKey,sentry_version:dw,...t&&{sentry_client:`${t.name}/${t.version}`}})}function gw(e,t,n){return t||`${hw(e)}?${mw(e,n)}`}const md=[];function yw(e){const t={};return e.forEach(n=>{const{name:r}=n,o=t[r];o&&!o.isDefaultInstance&&n.isDefaultInstance||(t[r]=n)}),Object.values(t)}function vw(e){const t=e.defaultIntegrations||[],n=e.integrations;t.forEach(s=>{s.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...t,...n]:typeof n=="function"?r=pg(n(t)):r=t;const o=yw(r),i=o.findIndex(s=>s.name==="Debug");if(i>-1){const[s]=o.splice(i,1);o.push(s)}return o}function Ew(e,t){const n={};return t.forEach(r=>{r&&Sg(e,r,n)}),n}function gd(e,t){for(const n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function Sg(e,t,n){if(n[t.name]){U&&P.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,md.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),md.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(o,i)=>r(o,i,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),o=Object.assign((i,s)=>r(i,s,e),{id:t.name});e.addEventProcessor(o)}U&&P.log(`Integration installed: ${t.name}`)}const yd="Not capturing exception because it's already been captured.";class _w{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=kS(t.dsn):U&&P.warn("No DSN provided, client will not send events."),this._dsn){const n=gw(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const o=Fe();if(td(t))return U&&P.log(yd),o;const i={event_id:o,...n};return this._process(this.eventFromException(t,i).then(s=>this._captureEvent(s,i,r))),i.event_id}captureMessage(t,n,r,o){const i={event_id:Fe(),...r},s=ec(t)?t:String(t),u=tc(t)?this.eventFromMessage(s,n,i):this.eventFromException(t,i);return this._process(u.then(a=>this._captureEvent(a,i,o))),i.event_id}captureEvent(t,n,r){const o=Fe();if(n&&n.originalException&&td(n.originalException))return U&&P.log(yd),o;const i={event_id:o,...n},u=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,i,u||r)),i.event_id}captureSession(t){typeof t.release!="string"?U&&P.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),mr(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(o=>r&&o))):In(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];Sg(this,t,this._integrations),n||gd(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=Z1(t,this._dsn,this._options._metadata,this._options.tunnel);for(const i of n.attachments||[])r=i1(r,a1(i));const o=this.sendEnvelope(r);o&&o.then(i=>this.emit("afterSendEvent",t,i),null)}sendSession(t){const n=K1(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(t,n,r){if(this._options.sendClientReports){const o=typeof r=="number"?r:1,i=`${t}:${n}`;U&&P.log(`Recording outcome: "${i}"${o>1?` (${o} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+o}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const o=r.indexOf(n);o>-1&&r.splice(o,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(o=>o(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(U&&P.error("Error while sending event:",n),n)):(U&&P.error("Transport disabled"),In({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=Ew(this,t),gd(this,t)}_updateSessionFromEvent(t,n){let r=!1,o=!1;const i=n.exception&&n.exception.values;if(i){o=!0;for(const a of i){const l=a.mechanism;if(l&&l.handled===!1){r=!0;break}}}const s=t.status==="ok";(s&&t.errors===0||s&&r)&&(mr(t,{...r&&{status:"crashed"},errors:t.errors||Number(o||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new Ve(n=>{let r=0;const o=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=o,t&&r>=t&&(clearInterval(i),n(!1)))},o)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,o=bn()){const i=this.getOptions(),s=Object.keys(this._integrations);return!n.integrations&&s.length>0&&(n.integrations=s),this.emit("preprocessEvent",t,n),t.type||o.setLastEventId(t.event_id||n.event_id),ow(i,t,n,r,this,o).then(u=>{if(u===null)return u;const a={...o.getPropagationContext(),...r?r.getPropagationContext():void 0};if(!(u.contexts&&u.contexts.trace)&&a){const{traceId:c,spanId:f,parentSpanId:d,dsc:m}=a;u.contexts={trace:De({trace_id:c,span_id:f,parent_span_id:d}),...u.contexts};const y=m||yg(c,this);u.sdkProcessingMetadata={dynamicSamplingContext:y,...u.sdkProcessingMetadata}}return u})}_captureEvent(t,n={},r){return this._processEvent(t,n,r).then(o=>o.event_id,o=>{if(U){const i=o;i.logLevel==="log"?P.log(i.message):P.warn(i)}})}_processEvent(t,n,r){const o=this.getOptions(),{sampleRate:i}=o,s=xg(t),u=wg(t),a=t.type||"error",l=`before send for type \`${a}\``,c=typeof i>"u"?void 0:Y1(i);if(u&&typeof c=="number"&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",t),Qi(new ct(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));const f=a==="replay_event"?"replay":a,m=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,n,r,m).then(y=>{if(y===null)throw this.recordDroppedEvent("event_processor",f,t),new ct("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return y;const S=ww(this,o,y,n);return Sw(S,l)}).then(y=>{if(y===null){if(this.recordDroppedEvent("before_send",f,t),s){const p=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",p)}throw new ct(`${l} returned \`null\`, will not send event.`,"log")}const v=r&&r.getSession();if(!s&&v&&this._updateSessionFromEvent(v,y),s){const h=y.sdkProcessingMetadata&&y.sdkProcessingMetadata.spanCountBeforeProcessing||0,p=y.spans?y.spans.length:0,g=h-p;g>0&&this.recordDroppedEvent("before_send","span",g)}const S=y.transaction_info;if(s&&S&&y.transaction!==t.transaction){const h="custom";y.transaction_info={...S,source:h}}return this.sendEvent(y,n),y}).then(null,y=>{throw y instanceof ct?y:(this.captureException(y,{data:{__sentry__:!0},originalException:y}),new ct(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${y}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[o,i]=n.split(":");return{reason:o,category:i,quantity:r}})}_flushOutcomes(){U&&P.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0){U&&P.log("No outcomes to send");return}if(!this._dsn){U&&P.log("No dsn provided, will not send outcomes");return}U&&P.log("Sending outcomes:",t);const n=f1(t,this._options.tunnel&&Ls(this._dsn));this.sendEnvelope(n)}}function Sw(e,t){const n=`${t} must return \`null\` or a valid event.`;if(Ps(e))return e.then(r=>{if(!hr(r)&&r!==null)throw new ct(n);return r},r=>{throw new ct(`${t} rejected with ${r}`)});if(!hr(e)&&e!==null)throw new ct(n);return e}function ww(e,t,n,r){const{beforeSend:o,beforeSendTransaction:i,beforeSendSpan:s}=t;if(wg(n)&&o)return o(n,r);if(xg(n)){if(n.spans&&s){const u=[];for(const a of n.spans){const l=s(a);l?u.push(l):e.recordDroppedEvent("before_send","span")}n.spans=u}if(i){if(n.spans){const u=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:u}}return i(n,r)}}return n}function wg(e){return e.type===void 0}function xg(e){return e.type==="transaction"}function xw(e,t){t.debug===!0&&(U?P.enable():Co(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),mt().update(t.initialScope);const r=new e(t);return Tw(r),r.init(),r}function Tw(e){mt().setClient(e)}const Cw=64;function Tg(e,t,n=QS(e.bufferSize||Cw)){let r={};const o=s=>n.drain(s);function i(s){const u=[];if(od(s,(f,d)=>{const m=id(d);if(m1(r,m)){const y=vd(f,d);e.recordDroppedEvent("ratelimit_backoff",m,y)}else u.push(f)}),u.length===0)return In({});const a=ko(s[0],u),l=f=>{od(a,(d,m)=>{const y=vd(d,m);e.recordDroppedEvent(f,id(m),y)})},c=()=>t({body:s1(a)}).then(f=>(f.statusCode!==void 0&&(f.statusCode<200||f.statusCode>=300)&&U&&P.warn(`Sentry responded with status code ${f.statusCode} to sent event.`),r=g1(r,f),f),f=>{throw l("network_error"),f});return n.add(c).then(f=>f,f=>{if(f instanceof ct)return U&&P.error("Skipped sending event because buffer is full."),l("queue_overflow"),In({});throw f})}return{send:i,flush:o}}function vd(e,t){if(!(t!=="event"&&t!=="transaction"))return Array.isArray(e)?e[1]:void 0}function Iw(e,t,n=[t],r="npm"){const o=e._metadata||{};o.sdk||(o.sdk={name:`sentry.javascript.${t}`,packages:n.map(i=>({name:`${r}:@sentry/${i}`,version:yn})),version:yn}),e._metadata=o}const kw=100;function Nn(e,t){const n=ye(),r=bn();if(!n)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:i=kw}=n.getOptions();if(i<=0)return;const u={timestamp:Io(),...e},a=o?Co(()=>o(u,t)):u;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,t),r.addBreadcrumb(a,i))}let Ed;const Nw="FunctionToString",_d=new WeakMap,Pw=()=>({name:Nw,setupOnce(){Ed=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=rc(this),n=_d.has(ye())&&t!==void 0?t:this;return Ed.apply(n,e)}}catch{}},setup(e){_d.set(e,!0)}}),Rw=Pw,Ow=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"],Lw="InboundFilters",Aw=(e={})=>({name:Lw,processEvent(t,n,r){const o=r.getOptions(),i=Dw(e,o);return Mw(t,i)?null:t}}),bw=Aw;function Dw(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:Ow],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function Mw(e,t){return t.ignoreInternal&&jw(e)?(U&&P.warn(`Event dropped due to being internal Sentry Error.
Event: ${bt(e)}`),!0):Fw(e,t.ignoreErrors)?(U&&P.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${bt(e)}`),!0):Gw(e)?(U&&P.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${bt(e)}`),!0):Hw(e,t.ignoreTransactions)?(U&&P.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${bt(e)}`),!0):Bw(e,t.denyUrls)?(U&&P.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${bt(e)}.
Url: ${Zi(e)}`),!0):Uw(e,t.allowUrls)?!1:(U&&P.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${bt(e)}.
Url: ${Zi(e)}`),!0)}function Fw(e,t){return e.type||!t||!t.length?!1:$w(e).some(n=>Rs(n,t))}function Hw(e,t){if(e.type!=="transaction"||!t||!t.length)return!1;const n=e.transaction;return n?Rs(n,t):!1}function Bw(e,t){if(!t||!t.length)return!1;const n=Zi(e);return n?Rs(n,t):!1}function Uw(e,t){if(!t||!t.length)return!0;const n=Zi(e);return n?Rs(n,t):!0}function $w(e){const t=[];e.message&&t.push(e.message);let n;try{n=e.exception.values[e.exception.values.length-1]}catch{}return n&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`)),t}function jw(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function zw(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Zi(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?zw(t):null}catch{return U&&P.error(`Cannot extract url for event ${bt(e)}`),null}}function Gw(e){return e.type||!e.exception||!e.exception.values||e.exception.values.length===0?!1:!e.message&&!e.exception.values.some(t=>t.stacktrace||t.type&&t.type!=="Error"||t.value)}const Vw="Dedupe",Ww=()=>{let e;return{name:Vw,processEvent(t){if(t.type)return t;try{if(Yw(t,e))return U&&P.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}},Xw=Ww;function Yw(e,t){return t?!!(Qw(e,t)||Kw(e,t)):!1}function Qw(e,t){const n=e.message,r=t.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!Ig(e,t)||!Cg(e,t))}function Kw(e,t){const n=Sd(t),r=Sd(e);return!(!n||!r||n.type!==r.type||n.value!==r.value||!Ig(e,t)||!Cg(e,t))}function Cg(e,t){let n=qf(e),r=qf(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let o=0;o<r.length;o++){const i=r[o],s=n[o];if(i.filename!==s.filename||i.lineno!==s.lineno||i.colno!==s.colno||i.function!==s.function)return!1}return!0}function Ig(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function Sd(e){return e.exception&&e.exception.values&&e.exception.values[0]}const $=F;let ja=0;function kg(){return ja>0}function Zw(){ja++,setTimeout(()=>{ja--})}function yr(e,t={},n){if(typeof e!="function")return e;try{const o=e.__sentry_wrapped__;if(o)return typeof o=="function"?o:e;if(rc(e))return e}catch{return e}const r=function(){const o=Array.prototype.slice.call(arguments);try{const i=o.map(s=>yr(s,t));return e.apply(this,i)}catch(i){throw Zw(),P1(s=>{s.addEventProcessor(u=>(t.mechanism&&(Ma(u,void 0),mo(u,t.mechanism)),u.extra={...u.extra,arguments:o},u)),fw(i)}),i}};try{for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o])}catch{}ig(r,e),Cn(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return e.name}})}catch{}return r}const No=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function ac(e,t){const n=lc(e,t),r={type:t&&t.name,value:nx(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function qw(e,t,n,r){const o=ye(),i=o&&o.getOptions().normalizeDepth,s=ux(t),u={__serialized__:hg(t,i)};if(s)return{exception:{values:[ac(e,s)]},extra:u};const a={exception:{values:[{type:Ns(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:ix(t,{isUnhandledRejection:r})}]},extra:u};if(n){const l=lc(e,n);l.length&&(a.exception.values[0].stacktrace={frames:l})}return a}function Su(e,t){return{exception:{values:[ac(e,t)]}}}function lc(e,t){const n=t.stacktrace||t.stack||"",r=ex(t),o=tx(t);try{return e(n,r,o)}catch{}return[]}const Jw=/Minified React error #\d+;/i;function ex(e){return e&&Jw.test(e.message)?1:0}function tx(e){return typeof e.framesToPop=="number"?e.framesToPop:0}function nx(e){const t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function rx(e,t,n,r){const o=n&&n.syntheticException||void 0,i=cc(e,t,o,r);return mo(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),In(i)}function ox(e,t,n="info",r,o){const i=r&&r.syntheticException||void 0,s=za(e,t,i,o);return s.level=n,r&&r.event_id&&(s.event_id=r.event_id),In(s)}function cc(e,t,n,r,o){let i;if(eg(t)&&t.error)return Su(e,t.error);if(Gf(t)||cS(t)){const s=t;if("stack"in t)i=Su(e,t);else{const u=s.name||(Gf(s)?"DOMError":"DOMException"),a=s.message?`${u}: ${s.message}`:u;i=za(e,a,n,r),Ma(i,a)}return"code"in s&&(i.tags={...i.tags,"DOMException.code":`${s.code}`}),i}return Jl(t)?Su(e,t):hr(t)||Ns(t)?(i=qw(e,t,n,o),mo(i,{synthetic:!0}),i):(i=za(e,t,n,r),Ma(i,`${t}`),mo(i,{synthetic:!0}),i)}function za(e,t,n,r){const o={};if(r&&n){const i=lc(e,n);i.length&&(o.exception={values:[{value:t,stacktrace:{frames:i}}]})}if(ec(t)){const{__sentry_template_string__:i,__sentry_template_values__:s}=t;return o.logentry={message:i,params:s},o}return o.message=t,o}function ix(e,{isUnhandledRejection:t}){const n=PS(e),r=t?"promise rejection":"exception";return eg(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:Ns(e)?`Event \`${sx(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function sx(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch{}}function ux(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const n=e[t];if(n instanceof Error)return n}}function ax(e,{metadata:t,tunnel:n,dsn:r}){const o={event_id:e.event_id,sent_at:new Date().toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!r&&{dsn:Ls(r)}},i=lx(e);return ko(o,[i])}function lx(e){return[{type:"user_report"},e]}class cx extends _w{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t},r=$.SENTRY_SDK_SOURCE||zS();Iw(n,"browser",["browser"],r),super(n),n.sendClientReports&&$.document&&$.document.addEventListener("visibilitychange",()=>{$.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,n){return rx(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return ox(this._options.stackParser,t,n,r,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled()){No&&P.warn("SDK not enabled, will not capture user feedback.");return}const n=ax(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}_prepareEvent(t,n,r){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r)}}const fx=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,he=F,dx=1e3;let wd,Ga,Va;function px(e){const t="dom";Ln(t,e),An(t,hx)}function hx(){if(!he.document)return;const e=it.bind(null,"dom"),t=xd(e,!0);he.document.addEventListener("click",t,!1),he.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{const r=he[n]&&he[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(be(r,"addEventListener",function(o){return function(i,s,u){if(i==="click"||i=="keypress")try{const a=this,l=a.__sentry_instrumentation_handlers__=a.__sentry_instrumentation_handlers__||{},c=l[i]=l[i]||{refCount:0};if(!c.handler){const f=xd(e);c.handler=f,o.call(this,i,f,u)}c.refCount++}catch{}return o.call(this,i,s,u)}}),be(r,"removeEventListener",function(o){return function(i,s,u){if(i==="click"||i=="keypress")try{const a=this,l=a.__sentry_instrumentation_handlers__||{},c=l[i];c&&(c.refCount--,c.refCount<=0&&(o.call(this,i,c.handler,u),c.handler=void 0,delete l[i]),Object.keys(l).length===0&&delete a.__sentry_instrumentation_handlers__)}catch{}return o.call(this,i,s,u)}}))})}function mx(e){if(e.type!==Ga)return!1;try{if(!e.target||e.target._sentryId!==Va)return!1}catch{}return!0}function gx(e,t){return e!=="keypress"?!1:!t||!t.tagName?!0:!(t.tagName==="INPUT"||t.tagName==="TEXTAREA"||t.isContentEditable)}function xd(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=yx(n);if(gx(n.type,r))return;Cn(n,"_sentryCaptured",!0),r&&!r._sentryId&&Cn(r,"_sentryId",Fe());const o=n.type==="keypress"?"input":n.type;mx(n)||(e({event:n,name:o,global:t}),Ga=n.type,Va=r?r._sentryId:void 0),clearTimeout(wd),wd=he.setTimeout(()=>{Va=void 0,Ga=void 0},dx)}}function yx(e){try{return e.target}catch{return null}}let ti;function Ng(e){const t="history";Ln(t,e),An(t,vx)}function vx(){if(!y1())return;const e=he.onpopstate;he.onpopstate=function(...n){const r=he.location.href,o=ti;if(ti=r,it("history",{from:o,to:r}),e)try{return e.apply(this,n)}catch{}};function t(n){return function(...r){const o=r.length>2?r[2]:void 0;if(o){const i=ti,s=String(o);ti=s,it("history",{from:i,to:s})}return n.apply(this,r)}}be(he.history,"pushState",t),be(he.history,"replaceState",t)}const Ei={};function Ex(e){const t=Ei[e];if(t)return t;let n=he[e];if(ba(n))return Ei[e]=n.bind(he);const r=he.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o);const i=o.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(o)}catch(o){fx&&P.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,o)}return n&&(Ei[e]=n.bind(he))}function Td(e){Ei[e]=void 0}const Ur="__sentry_xhr_v3__";function _x(e){const t="xhr";Ln(t,e),An(t,Sx)}function Sx(){if(!he.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const o=Tt()*1e3,i=xt(r[0])?r[0].toUpperCase():void 0,s=wx(r[1]);if(!i||!s)return t.apply(n,r);n[Ur]={method:i,url:s,request_headers:{}},i==="POST"&&s.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const u=()=>{const a=n[Ur];if(a&&n.readyState===4){try{a.status_code=n.status}catch{}const l={endTimestamp:Tt()*1e3,startTimestamp:o,xhr:n};it("xhr",l)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(a,l,c){return u(),a.apply(l,c)}}):n.addEventListener("readystatechange",u),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(a,l,c){const[f,d]=c,m=l[Ur];return m&&xt(f)&&xt(d)&&(m.request_headers[f.toLowerCase()]=d),a.apply(l,c)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const o=n[Ur];if(!o)return t.apply(n,r);r[0]!==void 0&&(o.body=r[0]);const i={startTimestamp:Tt()*1e3,xhr:n};return it("xhr",i),t.apply(n,r)}})}function wx(e){if(xt(e))return e;try{return e.toString()}catch{}}function xx(e,t=Ex("fetch")){let n=0,r=0;function o(i){const s=i.body.length;n+=s,r++;const u={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return Td("fetch"),Qi("No fetch implementation available");try{return t(e.url,u).then(a=>(n-=s,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return Td("fetch"),n-=s,r--,Qi(a)}}return Tg(e,o)}const Tx=30,Cx=50;function Wa(e,t,n,r){const o={filename:e,function:t==="<anonymous>"?Zt:t,in_app:!0};return n!==void 0&&(o.lineno=n),r!==void 0&&(o.colno=r),o}const Ix=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,kx=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Nx=/\((\S*)(?::(\d+))(?::(\d+))\)/,Px=e=>{const t=Ix.exec(e);if(t){const[,r,o,i]=t;return Wa(r,Zt,+o,+i)}const n=kx.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const s=Nx.exec(n[2]);s&&(n[2]=s[1],n[3]=s[2],n[4]=s[3])}const[o,i]=Rg(n[1]||Zt,n[2]);return Wa(i,o,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},Pg=[Tx,Px],Rx=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Ox=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Lx=e=>{const t=Rx.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const i=Ox.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}let r=t[3],o=t[1]||Zt;return[o,r]=Rg(o,r),Wa(r,o,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},Ax=[Cx,Lx],bx=[Pg,Ax],Dx=ag(...bx),Rg=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:Zt,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},ni=1024,Mx="Breadcrumbs",Fx=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:Mx,setup(n){t.console&&LS($x(n)),t.dom&&px(Ux(n,t.dom)),t.xhr&&_x(jx(n)),t.fetch&&MS(zx(n)),t.history&&Ng(Gx(n)),t.sentry&&n.on("beforeSendEvent",Bx(n))}}},Hx=Fx;function Bx(e){return function(n){ye()===e&&Nn({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:bt(n)},{event:n})}}function Ux(e,t){return function(r){if(ye()!==e)return;let o,i,s=typeof t=="object"?t.serializeAttribute:void 0,u=typeof t=="object"&&typeof t.maxStringLength=="number"?t.maxStringLength:void 0;u&&u>ni&&(No&&P.warn(`\`dom.maxStringLength\` cannot exceed ${ni}, but a value of ${u} was configured. Sentry will use ${ni} instead.`),u=ni),typeof s=="string"&&(s=[s]);try{const l=r.event,c=Vx(l)?l.target:l;o=rg(c,{keyAttrs:s,maxStringLength:u}),i=_S(c)}catch{o="<unknown>"}if(o.length===0)return;const a={category:`ui.${r.name}`,message:o};i&&(a.data={"ui.component_name":i}),Nn(a,{event:r.event,name:r.name,global:r.global})}}function $x(e){return function(n){if(ye()!==e)return;const r={category:"console",data:{arguments:n.args,logger:"console"},level:ZS(n.level),message:Vf(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)r.message=`Assertion failed: ${Vf(n.args.slice(1)," ")||"console.assert"}`,r.data.arguments=n.args.slice(1);else return;Nn(r,{input:n.args,level:n.level})}}function jx(e){return function(n){if(ye()!==e)return;const{startTimestamp:r,endTimestamp:o}=n,i=n.xhr[Ur];if(!r||!o||!i)return;const{method:s,url:u,status_code:a,body:l}=i,c={method:s,url:u,status_code:a},f={xhr:n.xhr,input:l,startTimestamp:r,endTimestamp:o},d=ng(a);Nn({category:"xhr",data:c,type:"http",level:d},f)}}function zx(e){return function(n){if(ye()!==e)return;const{startTimestamp:r,endTimestamp:o}=n;if(o&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.error){const i=n.fetchData,s={data:n.error,input:n.args,startTimestamp:r,endTimestamp:o};Nn({category:"fetch",data:i,level:"error",type:"http"},s)}else{const i=n.response,s={...n.fetchData,status_code:i&&i.status},u={input:n.args,response:i,startTimestamp:r,endTimestamp:o},a=ng(s.status_code);Nn({category:"fetch",data:s,type:"http",level:a},u)}}}function Gx(e){return function(n){if(ye()!==e)return;let r=n.from,o=n.to;const i=_u($.location.href);let s=r?_u(r):void 0;const u=_u(o);(!s||!s.path)&&(s=i),i.protocol===u.protocol&&i.host===u.host&&(o=u.relative),i.protocol===s.protocol&&i.host===s.host&&(r=s.relative),Nn({category:"navigation",data:{from:r,to:o}})}}function Vx(e){return!!e&&!!e.target}const Wx=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Xx="BrowserApiErrors",Yx=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:Xx,setupOnce(){t.setTimeout&&be($,"setTimeout",Cd),t.setInterval&&be($,"setInterval",Cd),t.requestAnimationFrame&&be($,"requestAnimationFrame",Kx),t.XMLHttpRequest&&"XMLHttpRequest"in $&&be(XMLHttpRequest.prototype,"send",Zx);const n=t.eventTarget;n&&(Array.isArray(n)?n:Wx).forEach(qx)}}},Qx=Yx;function Cd(e){return function(...t){const n=t[0];return t[0]=yr(n,{mechanism:{data:{function:qt(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function Kx(e){return function(t){return e.apply(this,[yr(t,{mechanism:{data:{function:"requestAnimationFrame",handler:qt(e)},handled:!1,type:"instrument"}})])}}function Zx(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(o=>{o in n&&typeof n[o]=="function"&&be(n,o,function(i){const s={mechanism:{data:{function:o,handler:qt(i)},handled:!1,type:"instrument"}},u=rc(i);return u&&(s.mechanism.data.handler=qt(u)),yr(i,s)})}),e.apply(this,t)}}function qx(e){const t=$,n=t[e]&&t[e].prototype;!n||!n.hasOwnProperty||!n.hasOwnProperty("addEventListener")||(be(n,"addEventListener",function(r){return function(o,i,s){try{typeof i.handleEvent=="function"&&(i.handleEvent=yr(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:qt(i),target:e},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[o,yr(i,{mechanism:{data:{function:"addEventListener",handler:qt(i),target:e},handled:!1,type:"instrument"}}),s])}}),be(n,"removeEventListener",function(r){return function(o,i,s){const u=i;try{const a=u&&u.__sentry_wrapped__;a&&r.call(this,o,a,s)}catch{}return r.call(this,o,u,s)}}))}const Jx="GlobalHandlers",eT=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:Jx,setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(nT(n),Id("onerror")),t.onunhandledrejection&&(rT(n),Id("onunhandledrejection"))}}},tT=eT;function nT(e){BS(t=>{const{stackParser:n,attachStacktrace:r}=Og();if(ye()!==e||kg())return;const{msg:o,url:i,line:s,column:u,error:a}=t,l=sT(cc(n,a||o,void 0,r,!1),i,s,u);l.level="error",vg(l,{originalException:a,mechanism:{handled:!1,type:"onerror"}})})}function rT(e){$S(t=>{const{stackParser:n,attachStacktrace:r}=Og();if(ye()!==e||kg())return;const o=oT(t),i=tc(o)?iT(o):cc(n,o,void 0,r,!0);i.level="error",vg(i,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function oT(e){if(tc(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}function iT(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function sT(e,t,n,r){const o=e.exception=e.exception||{},i=o.values=o.values||[],s=i[0]=i[0]||{},u=s.stacktrace=s.stacktrace||{},a=u.frames=u.frames||[],l=isNaN(parseInt(r,10))?void 0:r,c=isNaN(parseInt(n,10))?void 0:n,f=xt(t)&&t.length>0?t:ES();return a.length===0&&a.push({colno:l,filename:f,function:Zt,in_app:!0,lineno:c}),e}function Id(e){No&&P.log(`Global Handler attached: ${e}`)}function Og(){const e=ye();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const uT=()=>({name:"HttpContext",preprocessEvent(e){if(!$.navigator&&!$.location&&!$.document)return;const t=e.request&&e.request.url||$.location&&$.location.href,{referrer:n}=$.document||{},{userAgent:r}=$.navigator||{},o={...e.request&&e.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},i={...e.request,...t&&{url:t},headers:o};e.request=i}}),aT="cause",lT=5,cT="LinkedErrors",fT=(e={})=>{const t=e.limit||lT,n=e.key||aT;return{name:cT,preprocessEvent(r,o,i){const s=i.getOptions();mS(ac,s.stackParser,s.maxValueLength,n,t,r,o)}}},dT=fT;function Lg(e){return[bw(),Rw(),Qx(),Hx(),tT(),dT(),Xw(),uT()]}function pT(e={}){const t={defaultIntegrations:Lg(),release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:$.SENTRY_RELEASE&&$.SENTRY_RELEASE.id?$.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return e.defaultIntegrations==null&&delete e.defaultIntegrations,{...t,...e}}function hT(){const e=typeof $.window<"u"&&$;if(!e)return!1;const t=e.chrome?"chrome":"browser",n=e[t],r=n&&n.runtime&&n.runtime.id,o=$.location&&$.location.href||"",i=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],s=!!r&&$===$.top&&i.some(a=>o.startsWith(`${a}//`)),u=typeof e.nw<"u";return!!r&&!s&&!u}function mT(e={}){const t=pT(e);if(hT()){Co(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});return}No&&(cg()||P.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...t,stackParser:OS(t.stackParser||Dx),integrations:vw(t),transport:t.transport||xx},r=xw(cx,n);return t.autoSessionTracking&&gT(),r}function gT(){if(typeof $.document>"u"){No&&P.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}pd({ignoreDuration:!0}),hd(),Ng(({from:e,to:t})=>{e!==void 0&&e!==t&&(pd({ignoreDuration:!0}),hd())})}function kd(){const e=bn().getScopeData();return $a(e,mt().getScopeData()),e.eventProcessors=[],e}function yT(e){bn().addScopeListener(t=>{const n=kd();e(n,t)}),mt().addScopeListener(t=>{const n=kd();e(n,t)})}var Nd;(function(e){e[e.Classic=1]="Classic",e[e.Protocol=2]="Protocol",e[e.Both=3]="Both"})(Nd||(Nd={}));const vT="sentry-ipc";var Dt;(function(e){e.RENDERER_START="sentry-electron.renderer-start",e.EVENT="sentry-electron.event",e.SCOPE="sentry-electron.scope",e.ENVELOPE="sentry-electron.envelope",e.STATUS="sentry-electron.status",e.ADD_METRIC="sentry-electron.add-metric"})(Dt||(Dt={}));const ET="sentry-electron-renderer-id";function Mn(e){return`${vT}://${e}/sentry_key`}function _T(){if(window.__SENTRY_IPC__)return window.__SENTRY_IPC__;{P.log("IPC was not configured in preload script, falling back to custom protocol and fetch");const e=window.__SENTRY_RENDERER_ID__=Fe(),t={[ET]:e};return{sendRendererStart:()=>{fetch(Mn(Dt.RENDERER_START),{method:"POST",body:"",headers:t}).catch(()=>{console.error(`Sentry SDK failed to establish connection with the Electron main process.
  - Ensure you have initialized the SDK in the main process
  - If your renderers use custom sessions, be sure to set 'getSessions' in the main process options
  - If you are bundling your main process code and using Electron < v5, you'll need to manually configure a preload script`)})},sendScope:n=>{fetch(Mn(Dt.SCOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEvent:n=>{fetch(Mn(Dt.EVENT),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEnvelope:n=>{fetch(Mn(Dt.ENVELOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendStatus:n=>{fetch(Mn(Dt.STATUS),{method:"POST",body:JSON.stringify({status:n}),headers:t}).catch(()=>{})},sendAddMetric:n=>{fetch(Mn(Dt.ADD_METRIC),{method:"POST",body:JSON.stringify(n),headers:t}).catch(()=>{})}}}}let ri;function fc(){return ri||(ri=_T(),ri.sendRendererStart()),ri}const ST=()=>({name:"ScopeToMain",setup(){const e=fc();yT((t,n)=>{e.sendScope(JSON.stringify(Et(t,20,2e3))),n.clearBreadcrumbs(),n.clearAttachments()})}});function wT(e){const t=fc();return Tg(e,async n=>(t.sendEnvelope(n.body),{statusCode:200}))}function xT(e){const t={pollInterval:1e3,anrThreshold:5e3,captureStackTrace:!1,...e},n=fc();document.addEventListener("visibilitychange",()=>{n.sendStatus({status:document.visibilityState,config:t})}),n.sendStatus({status:document.visibilityState,config:t}),setInterval(()=>{n.sendStatus({status:"alive",config:t})},t.pollInterval)}const TT=50,[,CT]=Pg,[,IT]=e1(),kT=(e,t=0)=>{const n=[];for(const r of e.split(`
`).slice(t)){const o=CT(r),i=IT(r);if(o&&(i==null?void 0:i.in_app)!==!1?n.push(o):i&&n.push(De(i)),n.length>=TT)break}return lg(n)};function NT(e){return[...Lg(),ST()]}function PT(e={},t=mT){if(window!=null&&window.__SENTRY__RENDERER_INIT__){P.warn(`The browser SDK has already been initialized.
If init has been called in the preload and contextIsolation is disabled, is not required to call init in the renderer`);return}window.__SENTRY__RENDERER_INIT__=!0,e.autoSessionTracking===void 0&&(e.autoSessionTracking=!1),e.sendClientReports=!1,e.defaultIntegrations===void 0&&(e.defaultIntegrations=NT()),e.stackParser===void 0&&(e.stackParser=kT),e.dsn===void 0&&(e.dsn="https://<EMAIL>/12345"),e.transport===void 0&&(e.transport=wT),e.anrDetection&&xT(e.anrDetection===!0?{}:e.anrDetection),delete e.initialScope,t(e)}PT();window.mainProcess.titleBarApi.onUpdateDarkMode(e=>{document.body.className=e=="dark"?"darkTheme":""});const RT="modulepreload",OT=function(e,t){return new URL(e,t).href},Pd={},LT=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const s=document.getElementsByTagName("link"),u=document.querySelector("meta[property=csp-nonce]"),a=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));o=Promise.allSettled(n.map(l=>{if(l=OT(l,r),l in Pd)return;Pd[l]=!0;const c=l.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(!!r)for(let y=s.length-1;y>=0;y--){const v=s[y];if(v.href===l&&(!c||v.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${f}`))return;const m=document.createElement("link");if(m.rel=c?"stylesheet":RT,c||(m.as="script"),m.crossOrigin="",m.href=l,a&&m.setAttribute("nonce",a),document.head.appendChild(m),c)return new Promise((y,v)=>{m.addEventListener("load",y),m.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${l}`)))})}))}function i(s){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=s,window.dispatchEvent(u),!u.defaultPrevented)throw s}return o.then(s=>{for(const u of s||[])u.status==="rejected"&&i(u.reason);return t().catch(i)})};qm(document.querySelector("body"),LT(()=>import("./FindInPage-DJBYCPPH.js"),[],import.meta.url));export{DT as E,Zm as K,Ie as O,bT as R,ks as S,wr as _,Ia as a,ho as b,Yl as c,Pt as d,JE as e,h_ as f,ka as g,MT as h,rn as i,wu as j,ht as o,me as r,Sm as u};
