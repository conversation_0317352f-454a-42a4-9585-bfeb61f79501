import{c as ae,_ as D,E as T,S as oe,a as se,O as S,e as w,b as ie,o as z,d as _,f as le,i as ce,R as ue,r as f,g as L,j as x,h as de,K as fe,u as pe}from"./main-DmJDDWTb.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new t.Error().stack;r&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[r]="6b9d219e-0113-45de-8057-d9297e1727c1",t._sentryDebugIdIdentifier="sentry-dbid-6b9d219e-0113-45de-8057-d9297e1727c1")}catch{}})();var he=ae(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),H=function(t){D(r,t);function r(){var e=t.call(this)||this;return e.closed=!1,e.currentObservers=null,e.observers=[],e.isStopped=!1,e.hasError=!1,e.thrownError=null,e}return r.prototype.lift=function(e){var n=new I(this,this);return n.operator=e,n},r.prototype._throwIfClosed=function(){if(this.closed)throw new he},r.prototype.next=function(e){var n=this;w(function(){var o,l;if(n._throwIfClosed(),!n.isStopped){n.currentObservers||(n.currentObservers=Array.from(n.observers));try{for(var i=ie(n.currentObservers),s=i.next();!s.done;s=i.next()){var c=s.value;c.next(e)}}catch(d){o={error:d}}finally{try{s&&!s.done&&(l=i.return)&&l.call(i)}finally{if(o)throw o.error}}}})},r.prototype.error=function(e){var n=this;w(function(){if(n._throwIfClosed(),!n.isStopped){n.hasError=n.isStopped=!0,n.thrownError=e;for(var o=n.observers;o.length;)o.shift().error(e)}})},r.prototype.complete=function(){var e=this;w(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var n=e.observers;n.length;)n.shift().complete()}})},r.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(r.prototype,"observed",{get:function(){var e;return((e=this.observers)===null||e===void 0?void 0:e.length)>0},enumerable:!1,configurable:!0}),r.prototype._trySubscribe=function(e){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,e)},r.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},r.prototype._innerSubscribe=function(e){var n=this,o=this,l=o.hasError,i=o.isStopped,s=o.observers;return l||i?T:(this.currentObservers=null,s.push(e),new oe(function(){n.currentObservers=null,se(s,e)}))},r.prototype._checkFinalizedStatuses=function(e){var n=this,o=n.hasError,l=n.thrownError,i=n.isStopped;o?e.error(l):i&&e.complete()},r.prototype.asObservable=function(){var e=new S;return e.source=this,e},r.create=function(e,n){return new I(e,n)},r}(S),I=function(t){D(r,t);function r(e,n){var o=t.call(this)||this;return o.destination=e,o.source=n,o}return r.prototype.next=function(e){var n,o;(o=(n=this.destination)===null||n===void 0?void 0:n.next)===null||o===void 0||o.call(n,e)},r.prototype.error=function(e){var n,o;(o=(n=this.destination)===null||n===void 0?void 0:n.error)===null||o===void 0||o.call(n,e)},r.prototype.complete=function(){var e,n;(n=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||n===void 0||n.call(e)},r.prototype._subscribe=function(e){var n,o;return(o=(n=this.source)===null||n===void 0?void 0:n.subscribe(e))!==null&&o!==void 0?o:T},r}(H);function ve(t,r){return r===void 0&&(r=le),z(function(e,n){var o=null,l=null,i=null,s=function(){if(o){o.unsubscribe(),o=null;var d=l;l=null,n.next(d)}};function c(){var d=i+t,u=r.now();if(u<d){o=this.schedule(void 0,d-u),n.add(o);return}s()}e.subscribe(_(n,function(d){l=d,i=r.now(),o||(o=r.schedule(c,t),n.add(o))},function(){s(),n.complete()},void 0,function(){l=o=null}))})}function P(t,r){return z(function(e,n){var o=null,l=0,i=!1,s=function(){return i&&!o&&n.complete()};e.subscribe(_(n,function(c){o==null||o.unsubscribe();var d=0,u=l++;ce(t(c,u)).subscribe(o=_(n,function(h){return n.next(r?r(c,h,u,d++):h)},function(){o=null,s()}))},function(){i=!0,s()}))})}var B={exports:{}},g={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $;function be(){if($)return g;$=1;var t=ue,r=Symbol.for("react.element"),e=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,o=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function i(s,c,d){var u,h={},p=null,v=null;d!==void 0&&(p=""+d),c.key!==void 0&&(p=""+c.key),c.ref!==void 0&&(v=c.ref);for(u in c)n.call(c,u)&&!l.hasOwnProperty(u)&&(h[u]=c[u]);if(s&&s.defaultProps)for(u in c=s.defaultProps,c)h[u]===void 0&&(h[u]=c[u]);return{$$typeof:r,type:s,key:p,ref:v,props:h,_owner:o.current}}return g.Fragment=e,g.jsx=i,g.jsxs=i,g}B.exports=be();var a=B.exports,xe=Object.defineProperty,ge=Object.defineProperties,ye=Object.getOwnPropertyDescriptors,j=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable,E=(t,r,e)=>r in t?xe(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,je=(t,r)=>{for(var e in r||(r={}))U.call(r,e)&&E(t,e,r[e]);if(j)for(var e of j(r))K.call(r,e)&&E(t,e,r[e]);return t},me=(t,r)=>ge(t,ye(r)),we=(t,r)=>{var e={};for(var n in t)U.call(t,n)&&r.indexOf(n)<0&&(e[n]=t[n]);if(t!=null&&j)for(var n of j(t))r.indexOf(n)<0&&K.call(t,n)&&(e[n]=t[n]);return e};const Y=f.forwardRef((t,r)=>{const e=t,{alt:n,color:o="currentColor",size:l="1em",weight:i="regular",mirrored:s=!1,children:c,weights:d}=e,u=we(e,["alt","color","size","weight","mirrored","children","weights"]);return a.jsxs("svg",me(je({ref:r,xmlns:"http://www.w3.org/2000/svg",width:l,height:l,fill:o,viewBox:"0 0 256 256",transform:s?"scale(-1, 1)":void 0},u),{children:[!!n&&a.jsx("title",{children:n}),c,d.get(i)]}))});Y.displayName="SSRBase";const Oe=Y,_e=new Map([["bold",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M208.49,152.49l-72,72a12,12,0,0,1-17,0l-72-72a12,12,0,0,1,17-17L116,187V40a12,12,0,0,1,24,0V187l51.51-51.52a12,12,0,0,1,17,17Z"})})],["duotone",a.jsxs(a.Fragment,{children:[a.jsx("path",{d:"M200,144l-72,72L56,144Z",opacity:"0.2"}),a.jsx("path",{d:"M207.39,140.94A8,8,0,0,0,200,136H136V40a8,8,0,0,0-16,0v96H56a8,8,0,0,0-5.66,13.66l72,72a8,8,0,0,0,11.32,0l72-72A8,8,0,0,0,207.39,140.94ZM128,204.69,75.31,152H180.69Z"})]})],["fill",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M205.66,149.66l-72,72a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,56,136h64V40a8,8,0,0,1,16,0v96h64a8,8,0,0,1,5.66,13.66Z"})})],["light",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M204.24,148.24l-72,72a6,6,0,0,1-8.48,0l-72-72a6,6,0,0,1,8.48-8.48L122,201.51V40a6,6,0,0,1,12,0V201.51l61.76-61.75a6,6,0,0,1,8.48,8.48Z"})})],["regular",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M205.66,149.66l-72,72a8,8,0,0,1-11.32,0l-72-72a8,8,0,0,1,11.32-11.32L120,196.69V40a8,8,0,0,1,16,0V196.69l58.34-58.35a8,8,0,0,1,11.32,11.32Z"})})],["thin",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M202.83,146.83l-72,72a4,4,0,0,1-5.66,0l-72-72a4,4,0,0,1,5.66-5.66L124,206.34V40a4,4,0,0,1,8,0V206.34l65.17-65.17a4,4,0,0,1,5.66,5.66Z"})})]]);var Se=Object.defineProperty,Le=Object.defineProperties,Ie=Object.getOwnPropertyDescriptors,M=Object.getOwnPropertySymbols,Pe=Object.prototype.hasOwnProperty,$e=Object.prototype.propertyIsEnumerable,C=(t,r,e)=>r in t?Se(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,Ee=(t,r)=>{for(var e in r||(r={}))Pe.call(r,e)&&C(t,e,r[e]);if(M)for(var e of M(r))$e.call(r,e)&&C(t,e,r[e]);return t},Me=(t,r)=>Le(t,Ie(r));const W=f.forwardRef((t,r)=>a.jsx(Oe,Me(Ee({ref:r},t),{weights:_e})));W.displayName="ArrowDown";const Ce=new Map([["bold",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M208.49,120.49a12,12,0,0,1-17,0L140,69V216a12,12,0,0,1-24,0V69L64.49,120.49a12,12,0,0,1-17-17l72-72a12,12,0,0,1,17,0l72,72A12,12,0,0,1,208.49,120.49Z"})})],["duotone",a.jsxs(a.Fragment,{children:[a.jsx("path",{d:"M200,112H56l72-72Z",opacity:"0.2"}),a.jsx("path",{d:"M205.66,106.34l-72-72a8,8,0,0,0-11.32,0l-72,72A8,8,0,0,0,56,120h64v96a8,8,0,0,0,16,0V120h64a8,8,0,0,0,5.66-13.66ZM75.31,104,128,51.31,180.69,104Z"})]})],["fill",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M207.39,115.06A8,8,0,0,1,200,120H136v96a8,8,0,0,1-16,0V120H56a8,8,0,0,1-5.66-13.66l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,207.39,115.06Z"})})],["light",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M204.24,116.24a6,6,0,0,1-8.48,0L134,54.49V216a6,6,0,0,1-12,0V54.49L60.24,116.24a6,6,0,0,1-8.48-8.48l72-72a6,6,0,0,1,8.48,0l72,72A6,6,0,0,1,204.24,116.24Z"})})],["regular",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M205.66,117.66a8,8,0,0,1-11.32,0L136,59.31V216a8,8,0,0,1-16,0V59.31L61.66,117.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,205.66,117.66Z"})})],["thin",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M202.83,114.83a4,4,0,0,1-5.66,0L132,49.66V216a4,4,0,0,1-8,0V49.66L58.83,114.83a4,4,0,0,1-5.66-5.66l72-72a4,4,0,0,1,5.66,0l72,72A4,4,0,0,1,202.83,114.83Z"})})]]),Ae=new Map([["bold",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M208.49,191.51a12,12,0,0,1-17,17L128,145,64.49,208.49a12,12,0,0,1-17-17L111,128,47.51,64.49a12,12,0,0,1,17-17L128,111l63.51-63.52a12,12,0,0,1,17,17L145,128Z"})})],["duotone",a.jsxs(a.Fragment,{children:[a.jsx("path",{d:"M216,56V200a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V56A16,16,0,0,1,56,40H200A16,16,0,0,1,216,56Z",opacity:"0.2"}),a.jsx("path",{d:"M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"})]})],["fill",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM181.66,170.34a8,8,0,0,1-11.32,11.32L128,139.31,85.66,181.66a8,8,0,0,1-11.32-11.32L116.69,128,74.34,85.66A8,8,0,0,1,85.66,74.34L128,116.69l42.34-42.35a8,8,0,0,1,11.32,11.32L139.31,128Z"})})],["light",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M204.24,195.76a6,6,0,1,1-8.48,8.48L128,136.49,60.24,204.24a6,6,0,0,1-8.48-8.48L119.51,128,51.76,60.24a6,6,0,0,1,8.48-8.48L128,119.51l67.76-67.75a6,6,0,0,1,8.48,8.48L136.49,128Z"})})],["regular",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"})})],["thin",a.jsx(a.Fragment,{children:a.jsx("path",{d:"M202.83,197.17a4,4,0,0,1-5.66,5.66L128,133.66,58.83,202.83a4,4,0,0,1-5.66-5.66L122.34,128,53.17,58.83a4,4,0,0,1,5.66-5.66L128,122.34l69.17-69.17a4,4,0,1,1,5.66,5.66L133.66,128Z"})})]]),Fe=f.createContext({color:"currentColor",size:"1em",weight:"regular",mirrored:!1});var Ve=Object.defineProperty,Ze=Object.defineProperties,Re=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,Q=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable,A=(t,r,e)=>r in t?Ve(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,F=(t,r)=>{for(var e in r||(r={}))Q.call(r,e)&&A(t,e,r[e]);if(m)for(var e of m(r))X.call(r,e)&&A(t,e,r[e]);return t},Ne=(t,r)=>Ze(t,Re(r)),V=(t,r)=>{var e={};for(var n in t)Q.call(t,n)&&r.indexOf(n)<0&&(e[n]=t[n]);if(t!=null&&m)for(var n of m(t))r.indexOf(n)<0&&X.call(t,n)&&(e[n]=t[n]);return e};const q=f.forwardRef((t,r)=>{const e=t,{alt:n,color:o,size:l,weight:i,mirrored:s,children:c,weights:d}=e,u=V(e,["alt","color","size","weight","mirrored","children","weights"]),h=f.useContext(Fe),{color:p="currentColor",size:v,weight:b="regular",mirrored:te=!1}=h,ne=V(h,["color","size","weight","mirrored"]);return a.jsxs("svg",Ne(F(F({ref:r,xmlns:"http://www.w3.org/2000/svg",width:l??v,height:l??v,fill:o??p,viewBox:"0 0 256 256",transform:s||te?"scale(-1, 1)":void 0},ne),u),{children:[!!n&&a.jsx("title",{children:n}),c,d.get(i??b)]}))});q.displayName="IconBase";const G=q;var ke=Object.defineProperty,De=Object.defineProperties,Te=Object.getOwnPropertyDescriptors,Z=Object.getOwnPropertySymbols,ze=Object.prototype.hasOwnProperty,He=Object.prototype.propertyIsEnumerable,R=(t,r,e)=>r in t?ke(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,Be=(t,r)=>{for(var e in r||(r={}))ze.call(r,e)&&R(t,e,r[e]);if(Z)for(var e of Z(r))He.call(r,e)&&R(t,e,r[e]);return t},Ue=(t,r)=>De(t,Te(r));const J=f.forwardRef((t,r)=>a.jsx(G,Ue(Be({ref:r},t),{weights:Ce})));J.displayName="ArrowUp";var Ke=Object.defineProperty,Ye=Object.defineProperties,We=Object.getOwnPropertyDescriptors,N=Object.getOwnPropertySymbols,Qe=Object.prototype.hasOwnProperty,Xe=Object.prototype.propertyIsEnumerable,k=(t,r,e)=>r in t?Ke(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,qe=(t,r)=>{for(var e in r||(r={}))Qe.call(r,e)&&k(t,e,r[e]);if(N)for(var e of N(r))Xe.call(r,e)&&k(t,e,r[e]);return t},Ge=(t,r)=>Ye(t,We(r));const ee=f.forwardRef((t,r)=>a.jsx(G,Ge(qe({ref:r},t),{weights:Ae})));ee.displayName="X";function re(t){var r,e,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(e=re(t[r]))&&(n&&(n+=" "),n+=e)}else for(e in t)t[e]&&(n&&(n+=" "),n+=e);return n}function Je(){for(var t,r,e=0,n="",o=arguments.length;e<o;e++)(t=arguments[e])&&(r=re(t))&&(n&&(n+=" "),n+=r);return n}const y=de(fe.Find,window.sendPort);function O({bordered:t,children:r,...e}){return x.jsx("button",{type:"button",className:Je("w-[30px] h-[30px] rounded-lg text-text-300 border flex items-center justify-center disabled:opacity-50 disabled:pointer-events-none",t?"border-border-300 hover:border-border-200":"border-transparent hover:bg-bg-200"),...e,children:r})}function tr(){const t=f.useRef(null),[r]=f.useState(new H),[e,n]=f.useState(""),[o,l]=f.useState(0),[i,s]=f.useState(null);f.useEffect(()=>{const p=()=>{document.visibilityState==="visible"&&setTimeout(()=>{var v;return(v=t.current)==null?void 0:v.focus()},100)};return document.addEventListener("visibilitychange",p),()=>{document.removeEventListener("visibilitychange",p)}},[]),f.useEffect(()=>{const p=r.pipe(L(b=>b!==null&&b.length>2),ve(250),P(b=>y.findInPage(b,{findNext:!0}))).subscribe(b=>{l(b.activeMatchOrdinal),s(b.matches)}),v=r.pipe(L(b=>b===null||b.length<2),P(()=>y.stopFindInPage("clearSelection"))).subscribe(()=>{l(0),s(0)});return()=>{p.unsubscribe(),v.unsubscribe()}},[r]);const c=f.useCallback(p=>{n(p.target.value),s(null),r.next(p.target.value)},[n,r]),d=f.useCallback(()=>{setTimeout(()=>{n(""),r.next("")},100),y.endFindSession()},[n,r]),u=f.useCallback(async p=>{const v=await y.findInPage(e,{findNext:!1,forward:p});l(v.activeMatchOrdinal),s(v.matches)},[e]),h=f.useMemo(()=>e.length>2&&i!==null?`${o}/${i}`:"",[e,i,o]);return x.jsxs("div",{className:"font-styrene fixed inset-0 m-2 rounded-lg p-3 flex items-center gap-2 bg-bg-000 shadow-element",children:[x.jsx("div",{className:"flex-1",children:x.jsx(er,{value:e,onChange:c,onSearchNext:()=>u(!0),onDismissSearchRequest:d,inputRef:t})}),x.jsx("span",{id:"findInPageCount",className:"text-xs text-nowrap text-text-500 tabular-nums select-none",children:h}),x.jsx(O,{onClick:()=>u(!1),disabled:h===""||o<=1,children:x.jsx(J,{})}),x.jsx(O,{onClick:()=>u(!0),disabled:h===""||!i||o>=i,children:x.jsx(W,{})}),x.jsx(O,{bordered:!0,onClick:d,children:x.jsx(ee,{})})]})}function er({value:t,onChange:r,onSearchNext:e,onDismissSearchRequest:n,inputRef:o}){const l=pe(),i=f.useCallback(s=>{if(s.key==="Enter"){e();return}if(s.key==="Escape"){n();return}},[e,n]);return x.jsx("input",{ref:o,type:"text",className:"p-0 pl-1 w-full border-none outline-none focus:ring-0 text-text-200 bg-transparent placeholder:text-text-400 placeholder:opacity-70",placeholder:l.formatMessage({id:"sNnRQsIEYz",defaultMessage:"Find in page",description:"Placeholder text shown in the search input field that allows users to search for text within the current page"}),value:t,onKeyUp:i,onChange:r})}export{tr as default};
