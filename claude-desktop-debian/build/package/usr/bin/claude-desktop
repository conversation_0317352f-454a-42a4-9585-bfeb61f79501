#!/bin/bash
LOG_FILE="$HOME/claude-desktop-launcher.log"
echo "--- <PERSON> Launcher Start ---" >> "$LOG_FILE"
echo "Timestamp: $(date)" >> "$LOG_FILE"
echo "Arguments: $@" >> "$LOG_FILE"

# Detect if Wayland is likely running
IS_WAYLAND=false
if [ ! -z "$WAYLAND_DISPLAY" ]; then
  IS_WAYLAND=true
  echo "Wayland detected" >> "$LOG_FILE"
fi

# Determine Electron executable path
ELECTRON_EXEC="electron" # Default to global
LOCAL_ELECTRON_PATH="/usr/lib/claude-desktop/node_modules/electron/dist/electron" # Correct path to executable
if [ -f "$LOCAL_ELECTRON_PATH" ]; then
    ELECTRON_EXEC="$LOCAL_ELECTRON_PATH"
    echo "Using local Electron: $ELECTRON_EXEC" >> "$LOG_FILE"
else
    # Check if global electron exists before declaring it as the choice
    if command -v electron &> /dev/null; then
        echo "Using global Electron: $ELECTRON_EXEC" >> "$LOG_FILE"
    else
        echo "Error: Electron executable not found (checked local $LOCAL_ELECTRON_PATH and global path)." >> "$LOG_FILE" # Log the correct path checked
        # Optionally, display an error to the user via zenity or kdialog if available
        if command -v zenity &> /dev/null; then
            zenity --error --text="Claude Desktop cannot start because the Electron framework is missing. Please ensure Electron is installed globally or reinstall Claude Desktop."
        elif command -v kdialog &> /dev/null; then
            kdialog --error "Claude Desktop cannot start because the Electron framework is missing. Please ensure Electron is installed globally or reinstall Claude Desktop."
        fi
        exit 1
    fi
fi

# Base command arguments array, starting with app path
APP_PATH="/usr/lib/claude-desktop/app.asar"
ELECTRON_ARGS=("$APP_PATH")

# Add Wayland flags if Wayland is detected
if [ "$IS_WAYLAND" = true ]; then
  echo "Adding Wayland flags" >> "$LOG_FILE"
  ELECTRON_ARGS+=("--enable-features=UseOzonePlatform,WaylandWindowDecorations" "--ozone-platform=wayland" "--enable-wayland-ime" "--wayland-text-input-version=3")
fi

# Change to the application directory
APP_DIR="/usr/lib/claude-desktop"
echo "Changing directory to $APP_DIR" >> "$LOG_FILE"
cd "$APP_DIR" || { echo "Failed to cd to $APP_DIR" >> "$LOG_FILE"; exit 1; }

# Execute Electron with app path, flags, and script arguments
# Redirect stdout and stderr to the log file
FINAL_CMD="\"$ELECTRON_EXEC\" \"${ELECTRON_ARGS[@]}\" \"$@\""
echo "Executing: $FINAL_CMD" >> "$LOG_FILE"
"$ELECTRON_EXEC" "${ELECTRON_ARGS[@]}" "$@" >> "$LOG_FILE" 2>&1
EXIT_CODE=$?
echo "Electron exited with code: $EXIT_CODE" >> "$LOG_FILE"
echo "--- Claude Desktop Launcher End ---" >> "$LOG_FILE"
exit $EXIT_CODE
