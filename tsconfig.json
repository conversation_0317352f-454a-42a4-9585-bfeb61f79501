{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "incremental": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["src/**/*", "tests/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules"]}