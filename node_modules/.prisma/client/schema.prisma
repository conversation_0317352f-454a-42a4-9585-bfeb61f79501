generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String    @id @default(cuid())
  name             String?
  email            String    @unique
  emailVerified    DateTime?
  image            String?
  password         String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  role             String    @default("user")
  subscription     String    @default("free")
  projects         Project[]
  stripeCustomerId String?
  accounts         Account[]
  sessions         Session[]

  @@index([email])
  @@index([subscription])
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  molecules   Json // Używamy typu Json zamiast String dla lepszej obsługi w PostgreSQL
  settings    Json? // Używamy typu Json zamiast String dla lepszej obsługi w PostgreSQL

  @@index([userId])
  @@index([createdAt])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Nowy model do przechowywania molekuł w bazie danych
model Molecule {
  id         String     @id @default(cuid())
  name       String
  formula    String
  category   String
  structure  Json // Struktura molekuły w formacie JSONB
  properties Json? // Właściwości molekuły w formacie JSONB
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  isPublic   Boolean    @default(true)
  createdBy  String? // ID użytkownika, który dodał molekułę (opcjonalne)
  analyses   Analysis[]

  @@index([category])
  @@index([name])
  @@index([formula])
  @@index([isPublic])
  @@index([createdAt])
  @@index([createdBy])
}

// Nowy model do przechowywania analiz AI
model Analysis {
  id         String   @id @default(cuid())
  moleculeId String
  molecule   Molecule @relation(fields: [moleculeId], references: [id], onDelete: Cascade)
  type       String // Typ analizy (np. "lipinski", "druglikeness")
  results    Json // Wyniki analizy w formacie JSONB
  createdAt  DateTime @default(now())

  @@index([moleculeId])
  @@index([type])
  @@index([createdAt])
}
