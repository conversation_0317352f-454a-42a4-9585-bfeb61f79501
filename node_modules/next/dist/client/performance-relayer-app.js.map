{"version": 3, "sources": ["../../src/client/performance-relayer-app.ts"], "names": ["WEB_VITALS", "initialHref", "location", "href", "isRegistered", "userReportHandler", "onReport", "metric", "process", "env", "NODE_ENV", "__NEXT_ANALYTICS_ID", "window", "body", "dsn", "id", "page", "__NEXT_DATA__", "event_name", "name", "value", "toString", "speed", "navigator", "blob", "Blob", "URLSearchParams", "type", "vitalsUrl", "send", "sendBeacon", "bind", "fallbackSend", "fetch", "method", "credentials", "keepalive", "catch", "console", "error", "err", "onPerfEntry", "attributions", "__NEXT_WEB_VITALS_ATTRIBUTION", "webVital", "mod", "__NEXT_HAS_WEB_VITALS_ATTRIBUTION", "includes", "require", "warn"], "mappings": "AAAA,yCAAyC;AACzC,mBAAmB;;;;+BAqEnB;;;eAAA;;;AAlEA,kDAAkD;AAClD,MAAMA,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAE9D,MAAMC,cAAcC,SAASC,IAAI;AACjC,IAAIC,eAAe;AACnB,IAAIC;AAGJ,SAASC,SAASC,MAAc;IAC9B,IAAIF,mBAAmB;QACrBA,kBAAkBE;IACpB;IAEA,oEAAoE;IACpE,mEAAmE;IACnE,EAAE;IACF,uEAAuE;IACvE,2CAA2C;IAC3C,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzB,0DAA0D;IAC1DF,QAAQC,GAAG,CAACE,mBAAmB,EAC/B;YAIQC;QAHR,MAAMC,OAA+B;YACnCC,KAAKN,QAAQC,GAAG,CAACE,mBAAmB;YACpCI,IAAIR,OAAOQ,EAAE;YACbC,IAAI,GAAEJ,wBAAAA,OAAOK,aAAa,qBAApBL,sBAAsBI,IAAI;YAChCb,MAAMF;YACNiB,YAAYX,OAAOY,IAAI;YACvBC,OAAOb,OAAOa,KAAK,CAACC,QAAQ;YAC5BC,OACE,gBAAgBC,aAChB,AAACA,SAAiB,CAAC,aAAa,IAChC,mBAAmB,AAACA,SAAiB,CAAC,aAAa,GAC9C,AAACA,SAAiB,CAAC,aAAa,CAAC,gBAAgB,GAClD;QACR;QAEA,MAAMC,OAAO,IAAIC,KAAK;YAAC,IAAIC,gBAAgBb,MAAMQ,QAAQ;SAAG,EAAE;YAC5D,mDAAmD;YACnDM,MAAM;QACR;QACA,MAAMC,YAAY;QAClB,yEAAyE;QACzE,0FAA0F;QAC1F,MAAMC,OAAON,UAAUO,UAAU,IAAIP,UAAUO,UAAU,CAACC,IAAI,CAACR;QAE/D,SAASS;YACPC,MAAML,WAAW;gBACff,MAAMW;gBACNU,QAAQ;gBACRC,aAAa;gBACbC,WAAW;YAEb,GAAGC,KAAK,CAACC,QAAQC,KAAK;QACxB;QAEA,IAAI;YACF,2EAA2E;YAC3EV,KAAMD,WAAWJ,SAASQ;QAC5B,EAAE,OAAOQ,KAAK;YACZR;QACF;IACF;AACF;MAEA,WAAe,CAACS;IACd,IAAIjC,QAAQC,GAAG,CAACE,mBAAmB,EAAE;QACnC,iCAAiC;QACjCN,oBAAoBoC;QAEpB,gCAAgC;QAChC,IAAIrC,cAAc;YAChB;QACF;QACAA,eAAe;QAEf,MAAMsC,eAA0ClC,QAAQC,GAAG,CACxDkC,6BAA6B;QAEhC,KAAK,MAAMC,YAAY5C,WAAY;YACjC,IAAI;gBACF,IAAI6C;gBAEJ,IAAIrC,QAAQC,GAAG,CAACqC,iCAAiC,EAAE;oBACjD,IAAIJ,gCAAAA,aAAcK,QAAQ,CAACH,WAAW;wBACpCC,MAAMG,QAAQ;oBAChB;gBACF;gBACA,IAAI,CAACH,KAAK;oBACRA,MAAMG,QAAQ;gBAChB;gBACAH,GAAG,CAAC,AAAC,OAAID,SAAW,CAACtC;YACvB,EAAE,OAAOkC,KAAK;gBACZ,yCAAyC;gBACzCF,QAAQW,IAAI,CAAC,AAAC,qBAAkBL,WAAS,cAAaJ;YACxD;QACF;IACF;AACF"}