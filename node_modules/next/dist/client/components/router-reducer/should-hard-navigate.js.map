{"version": 3, "sources": ["../../../../src/client/components/router-reducer/should-hard-navigate.ts"], "names": ["shouldHardNavigate", "flightSegmentPath", "flightRouterState", "segment", "parallelRoutes", "currentSegment", "parallelRouteKey", "matchSegment", "Array", "isArray", "lastSegment", "length", "slice"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;+BAHa;AAGtB,SAASA,mBACdC,iBAAiC,EACjCC,iBAAoC;IAEpC,MAAM,CAACC,SAASC,eAAe,GAAGF;IAClC,2CAA2C;IAC3C,MAAM,CAACG,gBAAgBC,iBAAiB,GAAGL;IAK3C,yDAAyD;IACzD,IAAI,CAACM,IAAAA,2BAAY,EAACF,gBAAgBF,UAAU;QAC1C,kGAAkG;QAClG,IAAIK,MAAMC,OAAO,CAACJ,iBAAiB;YACjC,OAAO;QACT;QAEA,sEAAsE;QACtE,OAAO;IACT;IACA,MAAMK,cAAcT,kBAAkBU,MAAM,IAAI;IAEhD,IAAID,aAAa;QACf,OAAO;IACT;IAEA,OAAOV,mBACLC,kBAAkBW,KAAK,CAAC,IACxBR,cAAc,CAACE,iBAAiB;AAEpC"}