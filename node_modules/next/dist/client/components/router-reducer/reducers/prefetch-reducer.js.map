{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts"], "names": ["prefetchQueue", "prefetchReducer", "PromiseQueue", "state", "action", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "getOrCreatePrefetchCacheEntry", "nextUrl", "kind", "tree", "buildId"], "mappings": ";;;;;;;;;;;;;;;IAYaA,aAAa;eAAbA;;IAEGC,eAAe;eAAfA;;;kCATqB;8BACR;oCAItB;AAEA,MAAMD,gBAAgB,IAAIE,0BAAY,CAAC;AAEvC,SAASD,gBACdE,KAA2B,EAC3BC,MAAsB;IAEtB,4DAA4D;IAC5DC,IAAAA,sCAAkB,EAACF,MAAMG,aAAa;IAEtC,MAAM,EAAEC,GAAG,EAAE,GAAGH;IAChBG,IAAIC,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAE5CC,IAAAA,iDAA6B,EAAC;QAC5BJ;QACAK,SAAST,MAAMS,OAAO;QACtBN,eAAeH,MAAMG,aAAa;QAClCO,MAAMT,OAAOS,IAAI;QACjBC,MAAMX,MAAMW,IAAI;QAChBC,SAASZ,MAAMY,OAAO;IACxB;IAEA,OAAOZ;AACT"}