{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["serverActionReducer", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "fetchServerAction", "state", "nextUrl", "actionId", "actionArgs", "body", "res", "fetch", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION", "NEXT_ROUTER_STATE_TREE", "encodeURIComponent", "JSON", "stringify", "tree", "NEXT_DEPLOYMENT_ID", "NEXT_URL", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "addBasePath", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "callServer", "actionFlightData", "actionResult", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "hasInterceptionRouteInCurrentTree", "inFlightServerAction", "then", "flightData", "pushRef", "pendingPush", "handleExternalUrl", "newHref", "createHrefFromUrl", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "cacheNodeSeedData", "head", "slice", "rsc", "cache", "createEmptyCacheNode", "prefetchRsc", "fillLazyItemsTillLeafWithHead", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "includeNextUrl", "Boolean", "prefetchCache", "Map", "patchedTree", "handleMutable"], "mappings": ";;;;+BA<PERSON>Jg<PERSON>;;;eAAAA;;;+BA/IW;kCAMpB;6BAmBqB;mCACM;iCACA;6CACU;6CACA;+BAEd;+CACgB;2BACT;mDACa;uCACZ;iDACU;AA7BhD,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAiCd,eAAeC,kBACbC,KAA2B,EAC3BC,OAAwC,EACxC,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMV,YAAYS;IAE/B,MAAME,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQC,yCAAuB;YAC/B,CAACC,wBAAM,CAAC,EAAET;YACV,CAACU,wCAAsB,CAAC,EAAEC,mBAAmBC,KAAKC,SAAS,CAACf,MAAMgB,IAAI;YACtE,GAAIrB,QAAQC,GAAG,CAACqB,kBAAkB,GAC9B;gBACE,mBAAmBtB,QAAQC,GAAG,CAACqB,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIhB,UACA;gBACE,CAACiB,0BAAQ,CAAC,EAAEjB;YACd,IACA,CAAC,CAAC;QACR;QACAG;IACF;IAEA,MAAMe,WAAWd,IAAIG,OAAO,CAACY,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBR,KAAKS,KAAK,CAClClB,IAAIG,OAAO,CAACY,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFC,IAAAA,wBAAW,EAACX,WACZ,sFAAsF;IACtF,IAAIU,IAAI7B,MAAM+B,YAAY,EAAEC,OAAOb,QAAQ,CAACc,IAAI,KAElDC;IAEJ,IAAIC,mBACF9B,IAAIG,OAAO,CAACY,GAAG,CAAC,oBAAoBV,yCAAuB;IAE7D,IAAIyB,kBAAkB;QACpB,MAAMC,WAAiC,MAAM3C,gBAC3C4C,QAAQC,OAAO,CAACjC,MAChB;YACEkC,YAAAA,yBAAU;QACZ;QAGF,IAAIpB,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGqB,iBAAiB,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLI,kBAAkBA;gBAClBZ;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACoB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLK;YACAD;YACAZ;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAMO,SAAS7B,oBACdQ,KAA2B,EAC3B0C,MAA0B;IAE1B,MAAM,EAAEJ,OAAO,EAAEK,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IACtC,MAAMX,OAAOjC,MAAM+B,YAAY;IAE/B,IAAIc,cAAc7C,MAAMgB,IAAI;IAE5B4B,QAAQE,0BAA0B,GAAG;IAErC,2GAA2G;IAC3G,mEAAmE;IACnE,4EAA4E;IAC5E,wDAAwD;IACxD,MAAM7C,UACJD,MAAMC,OAAO,IAAI8C,IAAAA,oEAAiC,EAAC/C,MAAMgB,IAAI,IACzDhB,MAAMC,OAAO,GACb;IAEN2C,QAAQI,oBAAoB,GAAGjD,kBAAkBC,OAAOC,SAASyC;IAEjE,OAAOE,QAAQI,oBAAoB,CAACC,IAAI,CACtC;YAAO,EACLR,YAAY,EACZD,kBAAkBU,UAAU,EAC5BtB,gBAAgB,EACjB;QACC,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIA,kBAAkB;YACpB5B,MAAMmD,OAAO,CAACC,WAAW,GAAG;YAC5BR,QAAQQ,WAAW,GAAG;QACxB;QAEA,IAAI,CAACF,YAAY;YACfZ,QAAQG;YAER,2EAA2E;YAC3E,IAAIb,kBAAkB;gBACpB,OAAOyB,IAAAA,kCAAiB,EACtBrD,OACA4C,SACAhB,iBAAiBK,IAAI,EACrBjC,MAAMmD,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOpD;QACT;QAEA,IAAI,OAAOkD,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOG,IAAAA,kCAAiB,EACtBrD,OACA4C,SACAM,YACAlD,MAAMmD,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DR,QAAQI,oBAAoB,GAAG;QAE/B,IAAIpB,kBAAkB;YACpB,MAAM0B,UAAUC,IAAAA,oCAAiB,EAAC3B,kBAAkB;YACpDgB,QAAQb,YAAY,GAAGuB;QACzB;QAEA,KAAK,MAAME,kBAAkBN,WAAY;YACvC,oFAAoF;YACpF,IAAIM,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO3D;YACT;YAEA,mGAAmG;YACnG,MAAM,CAAC4D,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJjB,aACAe,WACAhC,mBACI2B,IAAAA,oCAAiB,EAAC3B,oBAClB5B,MAAM+B,YAAY;YAGxB,IAAI8B,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAAC/D,OAAO0C,QAAQkB;YAC9C;YAEA,IAAII,IAAAA,wDAA2B,EAACnB,aAAagB,UAAU;gBACrD,OAAOR,IAAAA,kCAAiB,EACtBrD,OACA4C,SACAX,MACAjC,MAAMmD,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACa,mBAAmBC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;YACxD,MAAMC,MAAMH,sBAAsB,OAAOA,iBAAiB,CAAC,EAAE,GAAG;YAEhE,8FAA8F;YAC9F,IAAIG,QAAQ,MAAM;gBAChB,MAAMC,QAAmBC,IAAAA,+BAAoB;gBAC7CD,MAAMD,GAAG,GAAGA;gBACZC,MAAME,WAAW,GAAG;gBACpBC,IAAAA,4DAA6B,EAC3BH,OACA,4FAA4F;gBAC5FnC,WACA0B,WACAK,mBACAC;gBAGF,MAAMO,IAAAA,gEAA+B,EAAC;oBACpCzE;oBACA0E,aAAab;oBACbc,cAAcN;oBACdO,gBAAgBC,QAAQ5E;oBACxB8B,cAAca,QAAQb,YAAY,IAAI/B,MAAM+B,YAAY;gBAC1D;gBAEAa,QAAQyB,KAAK,GAAGA;gBAChBzB,QAAQkC,aAAa,GAAG,IAAIC;YAC9B;YAEAnC,QAAQoC,WAAW,GAAGnB;YACtBhB,cAAcgB;QAChB;QAEAvB,QAAQG;QAER,OAAOwC,IAAAA,4BAAa,EAACjF,OAAO4C;IAC9B,GACA,CAACjB;QACC,mHAAmH;QACnHgB,OAAOhB;QAEP,OAAO3B;IACT;AAEJ"}