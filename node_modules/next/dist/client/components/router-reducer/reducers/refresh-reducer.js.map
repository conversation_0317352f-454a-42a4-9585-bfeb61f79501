{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "lazyData", "fetchServerResponse", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "cacheNodeSeedData", "head", "slice", "rsc", "prefetchRsc", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "patchedTree", "handleMutable"], "mappings": ";;;;+BAmBgBA;;;eAAAA;;;qCAnBoB;mCACF;6CACU;6CACA;iCAOV;+BACJ;+CAEgB;2BACT;uCACC;mDACY;iDACF;AAEzC,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAE7C,sFAAsF;IACtF,sHAAsH;IACtH,MAAMC,iBAAiBC,IAAAA,oEAAiC,EAACZ,MAAMO,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCE,MAAMI,QAAQ,GAAGC,IAAAA,wCAAmB,EAClC,IAAIC,IAAIX,MAAMF,SACd;QAACI,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAE;KAAU,EAC3DK,iBAAiBX,MAAMgB,OAAO,GAAG,MACjChB,MAAMiB,OAAO;IAGf,OAAOR,MAAMI,QAAQ,CAACK,IAAI,CACxB;YAAO,CAACC,YAAYC,qBAAqB;QACvC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBrB,OACAG,SACAgB,YACAnB,MAAMsB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/Dd,MAAMI,QAAQ,GAAG;QAEjB,KAAK,MAAMW,kBAAkBL,WAAY;YACvC,oFAAoF;YACpF,IAAIK,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO3B;YACT;YAEA,mGAAmG;YACnG,MAAM,CAAC4B,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJxB,aACAsB,WACA5B,MAAMK,YAAY;YAGpB,IAAIwB,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAAC/B,OAAOC,QAAQ2B;YAC9C;YAEA,IAAII,IAAAA,wDAA2B,EAAC1B,aAAauB,UAAU;gBACrD,OAAOR,IAAAA,kCAAiB,EACtBrB,OACAG,SACAC,MACAJ,MAAMsB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMU,2BAA2Bb,uBAC7Bc,IAAAA,oCAAiB,EAACd,wBAClBe;YAEJ,IAAIf,sBAAsB;gBACxBjB,QAAQE,YAAY,GAAG4B;YACzB;YAEA,0DAA0D;YAC1D,MAAM,CAACG,mBAAmBC,KAAK,GAAGb,eAAec,KAAK,CAAC,CAAC;YAExD,8FAA8F;YAC9F,IAAIF,sBAAsB,MAAM;gBAC9B,MAAMG,MAAMH,iBAAiB,CAAC,EAAE;gBAChC3B,MAAM8B,GAAG,GAAGA;gBACZ9B,MAAM+B,WAAW,GAAG;gBACpBC,IAAAA,4DAA6B,EAC3BhC,OACA,4FAA4F;gBAC5F0B,WACAP,WACAQ,mBACAC;gBAEFlC,QAAQuC,aAAa,GAAG,IAAIC;YAC9B;YAEA,MAAMC,IAAAA,gEAA+B,EAAC;gBACpC5C;gBACA6C,aAAahB;gBACbiB,cAAcrC;gBACdE;gBACAN,cAAcF,QAAQE,YAAY,IAAIL,MAAMK,YAAY;YAC1D;YAEAF,QAAQM,KAAK,GAAGA;YAChBN,QAAQ4C,WAAW,GAAGlB;YACtB1B,QAAQE,YAAY,GAAGD;YAEvBE,cAAcuB;QAChB;QAEA,OAAOmB,IAAAA,4BAAa,EAAChD,OAAOG;IAC9B,GACA,IAAMH;AAEV"}