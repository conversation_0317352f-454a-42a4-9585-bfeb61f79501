{"version": 3, "sources": ["../../src/server/server-utils.ts"], "names": ["getUtils", "interpolateDynamicPath", "normalizeDynamicRouteParams", "normalizeVercelUrl", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "parseUrl", "url", "search", "key", "Object", "keys", "query", "isNextQueryPrefix", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "isNextInterceptionMarkerPrefix", "NEXT_INTERCEPTION_MARKER_PREFIX", "groups", "includes", "formatUrl", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "slice", "length", "ignoreOptional", "defaultRouteMatches", "hasValidParams", "reduce", "prev", "normalizeRscURL", "val", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "undefined", "split", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "getNamedRouteRegex", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "isCatchAll", "_val", "item", "toLowerCase", "locale", "splice", "every", "name", "keyName", "paramName", "pos", "parseInt", "headers"], "mappings": ";;;;;;;;;;;;;;;;;IAkLgBA,QAAQ;eAARA;;IAzHAC,sBAAsB;eAAtBA;;IAuCAC,2BAA2B;eAA3BA;;IAzEAC,kBAAkB;eAAlBA;;;qBAhBuC;qCACnB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BAIzB;AAEA,SAASA,mBACdC,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAaC,IAAAA,UAAQ,EAACN,IAAIO,GAAG,EAAG;QACtC,OAAO,AAACF,WAAmBG,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACN,WAAWO,KAAK,EAAG;YAC/C,MAAMC,oBACJJ,QAAQK,kCAAuB,IAC/BL,IAAIM,UAAU,CAACD,kCAAuB;YAExC,MAAME,iCACJP,QAAQQ,0CAA+B,IACvCR,IAAIM,UAAU,CAACE,0CAA+B;YAEhD,IACEJ,qBACAG,kCACA,AAACd,CAAAA,aAAaQ,OAAOC,IAAI,CAACP,kBAAkBc,MAAM,CAAA,EAAGC,QAAQ,CAACV,MAC9D;gBACA,OAAOJ,WAAWO,KAAK,CAACH,IAAI;YAC9B;QACF;QACAT,IAAIO,GAAG,GAAGa,IAAAA,WAAS,EAACf;IACtB;AACF;AAEO,SAASR,uBACdwB,QAAgB,EAChBC,MAAsB,EACtBlB,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOiB;IAE/B,KAAK,MAAME,SAASb,OAAOC,IAAI,CAACP,kBAAkBc,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGrB,kBAAkBc,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,GAAG,EAAEF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,MAAMC,WAAWN,SAAUO,OAAO,CAACF;QAEnC,IAAIC,WAAW,CAAC,GAAG;YACjB,IAAIE;YACJ,MAAMC,QAAQR,MAAM,CAACC,MAAM;YAE3B,IAAIQ,MAAMC,OAAO,CAACF,QAAQ;gBACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;YACjE,OAAO,IAAIN,OAAO;gBAChBD,aAAaM,mBAAmBL;YAClC,OAAO;gBACLD,aAAa;YACf;YAEAR,WACEA,SAASgB,KAAK,CAAC,GAAGV,YAClBE,aACAR,SAASgB,KAAK,CAACV,WAAWD,WAAWY,MAAM;QAC/C;IACF;IAEA,OAAOjB;AACT;AAEO,SAASvB,4BACdwB,MAAsB,EACtBiB,cAAwB,EACxBnC,iBAAqE,EACrEoC,mBAAgD;IAEhD,IAAIC,iBAAiB;IACrB,IAAI,CAACrC,mBAAmB,OAAO;QAAEkB;QAAQmB,gBAAgB;IAAM;IAE/DnB,SAASZ,OAAOC,IAAI,CAACP,kBAAkBc,MAAM,EAAEwB,MAAM,CAAC,CAACC,MAAMlC;QAC3D,IAAIqB,QAAuCR,MAAM,CAACb,IAAI;QAEtD,IAAI,OAAOqB,UAAU,UAAU;YAC7BA,QAAQc,IAAAA,yBAAe,EAACd;QAC1B;QACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxBA,QAAQA,MAAMG,GAAG,CAAC,CAACY;gBACjB,IAAI,OAAOA,QAAQ,UAAU;oBAC3BA,MAAMD,IAAAA,yBAAe,EAACC;gBACxB;gBACA,OAAOA;YACT;QACF;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeN,mBAAoB,CAAC/B,IAAI;QAC9C,MAAMsC,aAAa3C,kBAAmBc,MAAM,CAACT,IAAI,CAACe,QAAQ;QAE1D,MAAMwB,iBAAiBjB,MAAMC,OAAO,CAACc,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOnB,MAAMC,OAAO,CAACF,SACjBA,MAAMmB,IAAI,CAAC,CAACJ,MAAQA,IAAI1B,QAAQ,CAAC+B,eACjCpB,yBAAAA,MAAOX,QAAQ,CAAC+B;QACtB,KACApB,yBAAAA,MAAOX,QAAQ,CAAC2B;QAEpB,IACEE,kBACC,OAAOlB,UAAU,eAAe,CAAEiB,CAAAA,cAAcR,cAAa,GAC9D;YACAE,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEM,cACC,CAAA,CAACjB,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMQ,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9CR,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAErB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAqB,QAAQqB;YACR,OAAO7B,MAAM,CAACb,IAAI;QACpB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEqB,SACA,OAAOA,UAAU,YACjB1B,kBAAmBc,MAAM,CAACT,IAAI,CAACgB,MAAM,EACrC;YACAK,QAAQA,MAAMsB,KAAK,CAAC;QACtB;QAEA,IAAItB,OAAO;YACTa,IAAI,CAAClC,IAAI,GAAGqB;QACd;QACA,OAAOa;IACT,GAAG,CAAC;IAEJ,OAAO;QACLrB;QACAmB;IACF;AACF;AAEO,SAAS7C,SAAS,EACvByD,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRrD,aAAa,EACbsD,aAAa,EACbC,aAAa,EAad;IACC,IAAItD;IACJ,IAAIuD;IACJ,IAAInB;IAEJ,IAAIrC,eAAe;QACjBC,oBAAoBwD,IAAAA,8BAAkB,EAACP,MAAM;QAC7CM,sBAAsBE,IAAAA,6BAAe,EAACzD;QACtCoC,sBAAsBmB,oBAAoBN;IAC5C;IAEA,SAASS,eAAe9D,GAAoB,EAAE+D,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAU1C,QAAQ;QAEnC,MAAM6C,cAAc;YAClB,MAAMC,oBAAoBC,IAAAA,wCAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,IAAAA,wCAAmB,EAACf,UAC1CM,uCAAAA,oBAAsBQ;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIhB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEiB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAAClB;YACf;YAEF,IAAIpC,SAASiD,QAAQR,UAAU1C,QAAQ;YAEvC,IAAI,AAACiD,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAO,AAAD,KAAMxD,QAAQ;gBAC9C,MAAMyD,YAAYC,IAAAA,4BAAQ,EACxBhF,KACA+D,UAAUnD,KAAK,EACf0D,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACbrE,OAAOuE,MAAM,CAAC3D,QAAQyD;gBACxB,OAAO;oBACLzD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAE4D,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAahB,QAAQgB,WAAW;oBAChChE,QAAQA;oBACRV,OAAOmD,UAAUnD,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIsE,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEA7E,OAAOuE,MAAM,CAACjB,eAAemB,WAAW7D;gBACxCZ,OAAOuE,MAAM,CAAClB,UAAUnD,KAAK,EAAEsE,kBAAkBtE,KAAK;gBACtD,OAAO,AAACsE,kBAA0BtE,KAAK;gBAEvCF,OAAOuE,MAAM,CAAClB,WAAWmB;gBAEzBjB,aAAaF,UAAU1C,QAAQ;gBAE/B,IAAIkC,UAAU;oBACZU,aACEA,WAAYuB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAElC,SAAS,CAAC,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAMoC,uBAAuBC,IAAAA,wCAAmB,EAC9C1B,YACAX,KAAKsC,OAAO;oBAEd3B,aAAayB,qBAAqBrE,QAAQ;oBAC1C0C,UAAUnD,KAAK,CAACiF,kBAAkB,GAChCH,qBAAqBI,cAAc,IAAIxE,OAAOuE,kBAAkB;gBACpE;gBAEA,IAAI5B,eAAeZ,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAIlD,iBAAiBwD,qBAAqB;oBACxC,MAAMoC,gBAAgBpC,oBAAoBM;oBAC1C,IAAI8B,eAAe;wBACjBhC,UAAUnD,KAAK,GAAG;4BAChB,GAAGmD,UAAUnD,KAAK;4BAClB,GAAGmF,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMzB,WAAWd,SAASwC,WAAW,IAAI,EAAE,CAAE;YAChD3B,aAAaC;QACf;QAEA,IAAIL,eAAeZ,MAAM;YACvB,IAAI4C,WAAW;YAEf,KAAK,MAAM3B,WAAWd,SAAS0C,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAW5B,aAAaC;gBACxB,IAAI2B,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC/B,eAAe;gBAC/B,KAAK,MAAMI,WAAWd,SAAS2C,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAW5B,aAAaC;oBACxB,IAAI2B,UAAU;gBAChB;YACF;QACF;QACA,OAAOjC;IACT;IAEA,SAASoC,0BACPpG,GAAoB,EACpBqG,UAAgB,EAChBP,cAAuB;QAEvB,OAAOjC,IAAAA,6BAAe,EACpB,AAAC;YACC,MAAM,EAAE3C,MAAM,EAAEoF,SAAS,EAAE,GAAGlG;YAE9B,OAAO;gBACLmG,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAMhG,OAAOiG,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJvD,QAAQwC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAMrF,OAAOC,OAAOC,IAAI,CAAC+F,KAAM;4BAClC,MAAM5E,QAAQ4E,GAAG,CAACjG,IAAI;4BAEtB,IACEA,QAAQK,kCAAuB,IAC/BL,IAAIM,UAAU,CAACD,kCAAuB,GACtC;gCACA,MAAMgG,gBAAgBrG,IAAIsG,SAAS,CACjCjG,kCAAuB,CAACwB,MAAM;gCAEhCoE,GAAG,CAACI,cAAc,GAAGhF;gCACrB,OAAO4E,GAAG,CAACjG,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAMuG,gBAAgBtG,OAAOC,IAAI,CAAC2F,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAACpE;4BACxB,IAAIS,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAM4D,aAAanF,MAAMC,OAAO,CAACa;gCACjC,MAAMsE,OAAOD,aAAarE,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOsE,SAAS,YAChB7D,KAAKsC,OAAO,CAAC3C,IAAI,CAAC,CAACmE;oCACjB,IAAIA,KAAKC,WAAW,OAAOF,KAAKE,WAAW,IAAI;wCAC7CvB,iBAAiBsB;wCACjBf,WAAWiB,MAAM,GAAGxB;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIoB,YAAY;wCACZrE,IAAiB0E,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAOL,aAAarE,IAAIP,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAI0E,cAAcQ,KAAK,CAAC,CAACC,OAASf,GAAG,CAACe,KAAK,GAAG;4BAC5C,OAAOT,cAActE,MAAM,CAAC,CAACC,MAAM+E;gCACjC,MAAMC,YAAYrB,6BAAAA,SAAW,CAACoB,QAAQ;gCAEtC,IAAIC,aAAa,CAACV,iBAAiBP,GAAG,CAACgB,QAAQ,GAAG;oCAChD/E,IAAI,CAACzB,MAAM,CAACyG,UAAU,CAACC,GAAG,CAAC,GAAGlB,GAAG,CAACgB,QAAQ;gCAC5C;gCACA,OAAO/E;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAOjC,OAAOC,IAAI,CAAC+F,KAAKhE,MAAM,CAAC,CAACC,MAAMlC;4BACpC,IAAI,CAACwG,iBAAiBP,GAAG,CAACjG,IAAI,GAAG;gCAC/B,IAAIqG,gBAAgBrG;gCAEpB,IAAIoG,kBAAkB;oCACpBC,gBAAgBe,SAASpH,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAOuE,MAAM,CAACtC,MAAM;oCACzB,CAACmE,cAAc,EAAEJ,GAAG,CAACjG,IAAI;gCAC3B;4BACF;4BACA,OAAOkC;wBACT,GAAG,CAAC;oBACN;gBACF;gBACAzB;YACF;QACF,KACAlB,IAAI8H,OAAO,CAAC,sBAAsB;IACtC;IAEA,OAAO;QACLhE;QACA1D;QACAuD;QACAnB;QACA4D;QACAtG,6BAA6B,CAC3BwB,QACAiB,iBAEAzC,4BACEwB,QACAiB,gBACAnC,mBACAoC;QAEJzC,oBAAoB,CAClBC,KACAC,YACAC,YAEAH,mBACEC,KACAC,YACAC,WACAC,eACAC;QAEJP,wBAAwB,CACtBwB,UACAC,SACGzB,uBAAuBwB,UAAUC,QAAQlB;IAChD;AACF"}