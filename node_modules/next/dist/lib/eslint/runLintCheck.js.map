{"version": 3, "sources": ["../../../src/lib/eslint/runLintCheck.ts"], "names": ["runLintCheck", "VALID_SEVERITY", "isValidSeverity", "severity", "includes", "requiredPackages", "file", "pkg", "exportsRestrict", "cliPrompt", "cwd", "console", "log", "bold", "cyan", "cliSelect", "Promise", "resolve", "require", "default", "value", "values", "getESLintPromptValues", "valueR<PERSON><PERSON>", "title", "recommended", "selected", "name", "underline", "yellow", "unselected", "config", "lint", "baseDir", "lintDirs", "eslintrcFile", "pkgJsonPath", "lintDuringBuild", "eslintOptions", "reportErrorsOnly", "maxWarnings", "formatter", "outputFile", "mod", "ESLint", "deps", "hasNecessaryDependencies", "packageManager", "getPkgManager", "missing", "some", "dep", "Log", "error", "resolved", "get", "eslintVersion", "version", "CLIEngine", "semver", "lt", "red", "options", "useEslintrc", "baseConfig", "errorOnUnmatchedPattern", "extensions", "cache", "eslint", "nextEslintPluginIsEnabled", "nextRulesEnabled", "Map", "configFile", "completeConfig", "calculateConfigForFile", "plugins", "Object", "entries", "rules", "startsWith", "length", "set", "pagesDir", "findPagesDir", "pagesDirRules", "updatedPagesDir", "rule", "replace", "warn", "lintStart", "process", "hrtime", "results", "lintFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fix", "outputFixes", "getErrorResults", "loadFormatter", "formattedResult", "formatResults", "format", "lintEnd", "totalWarnings", "reduce", "sum", "warningCount", "writeOutputFile", "output", "outputWithMessages", "isError", "eventInfo", "durationInSeconds", "lintedFilesCount", "lintFix", "nextEslintPluginVersion", "has", "path", "join", "dirname", "nextEslintPluginErrorsCount", "totalNextPluginErrorCount", "nextEslintPluginWarningsCount", "totalNextPluginWarningCount", "fromEntries", "err", "message", "getProperError", "opts", "strict", "findUp", "packageJsonConfig", "pkgJsonContent", "fs", "readFile", "encoding", "CommentJson", "parse", "hasEslintConfiguration", "exists", "emptyPkgJsonConfig", "emptyEslintrc", "selectedConfig", "getESLintStrictValue", "for<PERSON>ach", "installDependencies", "dir", "existsSync", "writeDefaultConfig", "ready"], "mappings": ";;;;+BA0RsBA;;;eAAAA;;;oBA1RqB;4BACQ;6DAClC;+DAEE;+DACA;qEACU;iCAEC;oCAEK;wCACI;iCACP;8BAEH;qCACO;0CACK;6DAEpB;iEAEmB;+BACV;uCAIvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOP,8GAA8G;AAC9G,MAAMC,iBAAiB;IAAC;IAAO;IAAQ;CAAQ;AAG/C,SAASC,gBAAgBC,QAAgB;IACvC,OAAOF,eAAeG,QAAQ,CAACD;AACjC;AAEA,MAAME,mBAAmB;IACvB;QAAEC,MAAM;QAAUC,KAAK;QAAUC,iBAAiB;IAAM;IACxD;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAED,eAAeC,UAAUC,GAAW;IAClCC,QAAQC,GAAG,CACTC,IAAAA,gBAAI,EACF,CAAC,EAAEC,IAAAA,gBAAI,EACL,KACA,sFAAsF,CAAC;IAI7F,IAAI;QACF,MAAMC,YAAY,AAChB,CAAA,MAAMC,QAAQC,OAAO,CAACC,QAAQ,iCAAgC,EAC9DC,OAAO;QACT,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAML,UAAU;YAChCM,QAAQ,MAAMC,IAAAA,4CAAqB,EAACZ;YACpCa,eAAe,CACb,EACEC,KAAK,EACLC,WAAW,EAC2C,EACxDC;gBAEA,MAAMC,OAAOD,WAAWb,IAAAA,gBAAI,EAACe,IAAAA,qBAAS,EAACd,IAAAA,gBAAI,EAACU,WAAWA;gBACvD,OAAOG,OAAQF,CAAAA,cAAcZ,IAAAA,gBAAI,EAACgB,IAAAA,kBAAM,EAAC,qBAAqB,EAAC;YACjE;YACAH,UAAUZ,IAAAA,gBAAI,EAAC;YACfgB,YAAY;QACd;QAEA,OAAO;YAAEC,QAAQX,CAAAA,yBAAAA,MAAOW,MAAM,KAAI;QAAK;IACzC,EAAE,OAAM;QACN,OAAO;YAAEA,QAAQ;QAAK;IACxB;AACF;AAEA,eAAeC,KACbC,OAAe,EACfC,QAAkB,EAClBC,YAA2B,EAC3BC,WAA0B,EAC1B,EACEC,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EAQlB;IAUD,IAAI;YAyBqCC,gBA8GnCC;QAtIJ,0CAA0C;QAC1C,MAAMC,OAAO,MAAMC,IAAAA,kDAAwB,EAACb,SAAS5B;QACrD,MAAM0C,iBAAiBC,IAAAA,4BAAa,EAACf;QAErC,IAAIY,KAAKI,OAAO,CAACC,IAAI,CAAC,CAACC,MAAQA,IAAI5C,GAAG,KAAK,WAAW;YACpD6C,KAAIC,KAAK,CACP,CAAC,wBAAwB,EACvBhB,kBAAkB,oCAAoC,IACvD,CAAC,EAAExB,IAAAA,gBAAI,EACNC,IAAAA,gBAAI,EACF,AAACiC,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IAAK,YAElC,CAAC;YAEL,OAAO;QACT;QAEA,MAAMJ,MAAM,MAAM3B,QAAQC,OAAO,CAACC,QAAQ2B,KAAKS,QAAQ,CAACC,GAAG,CAAC;QAE5D,MAAM,EAAEX,MAAM,EAAE,GAAGD;QACnB,IAAIa,gBAAgBZ,CAAAA,0BAAAA,OAAQa,OAAO,OAAId,iBAAAA,IAAIe,SAAS,qBAAbf,eAAec,OAAO;QAE7D,IAAI,CAACD,iBAAiBG,eAAM,CAACC,EAAE,CAACJ,eAAe,UAAU;YACvD,OAAO,CAAC,EAAEK,IAAAA,eAAG,EACX,SACA,wDAAwD,EACxDL,gBAAgB,OAAOA,gBAAgB,MAAM,GAC9C,6CAA6C,CAAC;QACjD;QAEA,IAAIM,UAAe;YACjBC,aAAa;YACbC,YAAY,CAAC;YACbC,yBAAyB;YACzBC,YAAY;gBAAC;gBAAO;gBAAQ;gBAAO;aAAO;YAC1CC,OAAO;YACP,GAAG7B,aAAa;QAClB;QAEA,IAAI8B,SAAS,IAAIxB,OAAOkB;QAExB,IAAIO,4BAA4B;QAChC,MAAMC,mBAAmB,IAAIC;QAE7B,KAAK,MAAMC,cAAc;YAACrC;YAAcC;SAAY,CAAE;gBAOhDqC;YANJ,IAAI,CAACD,YAAY;YAEjB,MAAMC,iBAAyB,MAAML,OAAOM,sBAAsB,CAChEF;YAGF,KAAIC,0BAAAA,eAAeE,OAAO,qBAAtBF,wBAAwBrE,QAAQ,CAAC,eAAe;gBAClDiE,4BAA4B;gBAC5B,KAAK,MAAM,CAAC1C,MAAM,CAACxB,SAAS,CAAC,IAAIyE,OAAOC,OAAO,CAACJ,eAAeK,KAAK,EAAG;oBACrE,IAAI,CAACnD,KAAKoD,UAAU,CAAC,gBAAgB;wBACnC;oBACF;oBACA,IACE,OAAO5E,aAAa,YACpBA,YAAY,KACZA,WAAWF,eAAe+E,MAAM,EAChC;wBACAV,iBAAiBW,GAAG,CAACtD,MAAM1B,cAAc,CAACE,SAAS;oBACrD,OAAO,IACL,OAAOA,aAAa,YACpBD,gBAAgBC,WAChB;wBACAmE,iBAAiBW,GAAG,CAACtD,MAAMxB;oBAC7B;gBACF;gBACA;YACF;QACF;QAEA,MAAM+E,WAAWC,IAAAA,0BAAY,EAAClD,SAASiD,QAAQ;QAC/C,MAAME,gBAAgBF,WAAW;YAAC;SAAoC,GAAG,EAAE;QAE3E,IAAIb,2BAA2B;YAC7B,IAAIgB,kBAAkB;YAEtB,KAAK,MAAMC,QAAQF,cAAe;oBAE7BtB,2BACAA;gBAFH,IACE,GAACA,4BAAAA,QAAQE,UAAU,CAAEc,KAAK,qBAAzBhB,yBAA2B,CAACwB,KAAK,KAClC,GAACxB,6BAAAA,QAAQE,UAAU,CAAEc,KAAK,qBAAzBhB,0BAA2B,CAC1BwB,KAAKC,OAAO,CAAC,cAAc,2BAC5B,GACD;oBACA,IAAI,CAACzB,QAAQE,UAAU,CAAEc,KAAK,EAAE;wBAC9BhB,QAAQE,UAAU,CAAEc,KAAK,GAAG,CAAC;oBAC/B;oBACAhB,QAAQE,UAAU,CAAEc,KAAK,CAACQ,KAAK,GAAG;wBAAC;wBAAGJ;qBAAS;oBAC/CG,kBAAkB;gBACpB;YACF;YAEA,IAAIA,iBAAiB;gBACnBjB,SAAS,IAAIxB,OAAOkB;YACtB;QACF,OAAO;YACLV,KAAIoC,IAAI,CAAC;YACTpC,KAAIoC,IAAI,CACN;QAEJ;QAEA,MAAMC,YAAYC,QAAQC,MAAM;QAEhC,IAAIC,UAAU,MAAMxB,OAAOyB,SAAS,CAAC3D;QACrC,IAAI4D,oBAAoB;QAExB,IAAIhC,QAAQiC,GAAG,EAAE,MAAMnD,OAAOoD,WAAW,CAACJ;QAC1C,IAAIrD,kBAAkBqD,UAAU,MAAMhD,OAAOqD,eAAe,CAACL,SAAS,6CAA6C;;QAEnH,IAAInD,WAAWqD,oBAAoB,MAAM1B,OAAO8B,aAAa,CAACzD;QAC9D,MAAM0D,kBAAkBC,IAAAA,8BAAa,EACnCnE,SACA2D,SACAE,qCAAAA,kBAAmBO,MAAM;QAE3B,MAAMC,UAAUZ,QAAQC,MAAM,CAACF;QAC/B,MAAMc,gBAAgBX,QAAQY,MAAM,CAClC,CAACC,KAAanG,OAAqBmG,MAAMnG,KAAKoG,YAAY,EAC1D;QAGF,IAAIhE,YAAY,MAAMiE,IAAAA,gCAAe,EAACjE,YAAYyD,gBAAgBS,MAAM;QAExE,OAAO;YACLA,QAAQT,gBAAgBU,kBAAkB;YAC1CC,SACElE,EAAAA,0BAAAA,OAAOqD,eAAe,CAACL,6BAAvBhD,wBAAiCoC,MAAM,IAAG,KACzCxC,eAAe,KAAK+D,gBAAgB/D;YACvCuE,WAAW;gBACTC,mBAAmBV,OAAO,CAAC,EAAE;gBAC7B9C,eAAeA;gBACfyD,kBAAkBrB,QAAQZ,MAAM;gBAChCkC,SAAS,CAAC,CAACpD,QAAQiC,GAAG;gBACtBoB,yBACE9C,6BAA6BxB,KAAKS,QAAQ,CAAC8D,GAAG,CAAC,wBAC3ClG,QAAQmG,aAAI,CAACC,IAAI,CACfD,aAAI,CAACE,OAAO,CAAC1E,KAAKS,QAAQ,CAACC,GAAG,CAAC,wBAC/B,iBACCE,OAAO,GACV;gBACN+D,6BAA6BrB,gBAAgBsB,yBAAyB;gBACtEC,+BACEvB,gBAAgBwB,2BAA2B;gBAC7CrD,kBAAkBM,OAAOgD,WAAW,CAACtD;YACvC;QACF;IACF,EAAE,OAAOuD,KAAK;QACZ,IAAIxF,iBAAiB;YACnBe,KAAIC,KAAK,CACP,CAAC,QAAQ,EACPyD,IAAAA,gBAAO,EAACe,QAAQA,IAAIC,OAAO,GAAGD,IAAIC,OAAO,CAACvC,OAAO,CAAC,OAAO,OAAOsC,IACjE,CAAC;YAEJ,OAAO;QACT,OAAO;YACL,MAAME,IAAAA,uBAAc,EAACF;QACvB;IACF;AACF;AAEO,eAAe7H,aACpBiC,OAAe,EACfC,QAAkB,EAClB8F,IAQC;IAED,MAAM,EACJ3F,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EACjBuF,SAAS,KAAK,EACf,GAAGD;IACJ,IAAI;QACF,6BAA6B;QAC7B,qGAAqG;QACrG,MAAM7F,eACJ,AAAC,MAAM+F,IAAAA,eAAM,EACX;YACE;YACA;YACA;YACA;YACA;YACA;SACD,EACD;YACExH,KAAKuB;QACP,MACI;QAER,MAAMG,cAAc,AAAC,MAAM8F,IAAAA,eAAM,EAAC,gBAAgB;YAAExH,KAAKuB;QAAQ,MAAO;QACxE,IAAIkG,oBAAoB;QACxB,IAAI/F,aAAa;YACf,MAAMgG,iBAAiB,MAAMC,YAAE,CAACC,QAAQ,CAAClG,aAAa;gBACpDmG,UAAU;YACZ;YACAJ,oBAAoBK,aAAYC,KAAK,CAACL;QACxC;QAEA,MAAMrG,SAAS,MAAM2G,IAAAA,8CAAsB,EAACvG,cAAcgG;QAC1D,IAAItF;QAEJ,IAAId,OAAO4G,MAAM,EAAE;YACjB,8BAA8B;YAC9B,OAAO,MAAM3G,KAAKC,SAASC,UAAUC,cAAcC,aAAa;gBAC9DC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;YACF;QACF,OAAO;YACL,+DAA+D;YAC/D,4DAA4D;YAC5D,8BAA8B;YAC9B,IAAIL,iBAAiB;gBACnB,IAAIN,OAAO6G,kBAAkB,IAAI7G,OAAO8G,aAAa,EAAE;oBACrDzF,KAAIoC,IAAI,CACN,CAAC,sCAAsC,EAAE3E,IAAAA,gBAAI,EAC3CC,IAAAA,gBAAI,EAAC,cACL,eAAe,CAAC;gBAEtB;gBACA,OAAO;YACT,OAAO;gBACL,sFAAsF;gBACtF,MAAM,EAAEiB,QAAQ+G,cAAc,EAAE,GAAGb,SAC/B,MAAMc,IAAAA,2CAAoB,EAAC9G,WAC3B,MAAMxB,UAAUwB;gBAEpB,IAAI6G,kBAAkB,MAAM;oBAC1B,oDAAoD;oBACpD1F,KAAIoC,IAAI,CACN;oBAEF,OAAO;gBACT,OAAO;oBACL,sEAAsE;oBACtE3C,OAAO,MAAMC,IAAAA,kDAAwB,EAACb,SAAS5B;oBAC/C,IAAIwC,KAAKI,OAAO,CAAC+B,MAAM,GAAG,GAAG;wBAC3BnC,KAAKI,OAAO,CAAC+F,OAAO,CAAC,CAAC7F;4BACpB,IAAIA,IAAI5C,GAAG,KAAK,UAAU;gCACxB,0FAA0F;gCAC1F4C,IAAI5C,GAAG,GAAG;4BACZ;wBACF;wBAEA,MAAM0I,IAAAA,wCAAmB,EAAChH,SAASY,KAAKI,OAAO,EAAE;oBACnD;oBAEA,+BAA+B;oBAC/B,gFAAgF;oBAChF,IACE;wBAAC;wBAAO;wBAAW;wBAAS;qBAAY,CAACC,IAAI,CAAC,CAACgG,MAC7CC,IAAAA,cAAU,EAAC9B,aAAI,CAACC,IAAI,CAACrF,SAASiH,QAEhC;wBACA,MAAME,IAAAA,sCAAkB,EACtBnH,SACAF,QACA+G,gBACA3G,cACAC,aACA+F;oBAEJ;gBACF;gBAEA/E,KAAIiG,KAAK,CACP,CAAC,6CAA6C,EAAExI,IAAAA,gBAAI,EAClDC,IAAAA,gBAAI,EAAC,cACL,mCAAmC,CAAC;gBAGxC,OAAO;YACT;QACF;IACF,EAAE,OAAO+G,KAAK;QACZ,MAAMA;IACR;AACF"}