{"version": 3, "sources": ["../../../src/lib/helpers/get-registry.ts"], "names": ["getRegistry", "baseDir", "process", "cwd", "registry", "pkgManager", "getPkgManager", "output", "execSync", "env", "NODE_OPTIONS", "getNodeOptionsWithoutInspect", "toString", "trim", "startsWith", "endsWith"], "mappings": ";;;;+BASgBA;;;eAAAA;;;+BATS;+BACK;uBACe;AAOtC,SAASA,YAAYC,UAAkBC,QAAQC,GAAG,EAAE;IACzD,IAAIC,WAAW,CAAC,2BAA2B,CAAC;IAC5C,IAAI;QACF,MAAMC,aAAaC,IAAAA,4BAAa,EAACL;QACjC,MAAMM,SAASC,IAAAA,uBAAQ,EAAC,CAAC,EAAEH,WAAW,oBAAoB,CAAC,EAAE;YAC3DI,KAAK;gBACH,GAAGP,QAAQO,GAAG;gBACdC,cAAcC,IAAAA,mCAA4B;YAC5C;QACF,GACGC,QAAQ,GACRC,IAAI;QAEP,IAAIN,OAAOO,UAAU,CAAC,SAAS;YAC7BV,WAAWG,OAAOQ,QAAQ,CAAC,OAAOR,SAAS,CAAC,EAAEA,OAAO,CAAC,CAAC;QACzD;IACF,SAAU;QACR,OAAOH;IACT;AACF"}