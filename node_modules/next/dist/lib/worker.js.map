{"version": 3, "sources": ["../../src/lib/worker.ts"], "names": ["Worker", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "_restarting", "timeout", "onRestart", "logger", "console", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "process", "on", "close", "createWorker", "JestWorker", "forkOptions", "env", "IS_NEXT_WORKER", "Promise", "resolve", "enableWorkerThreads", "code", "signal", "error", "exit", "getStdout", "pipe", "stdout", "getStderr", "stderr", "onHanging", "warn", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAdwB;AAIrC,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEO,MAAMT;IAIXU,YACEC,UAAkB,EAClBC,OAMC,CACD;aAXMC,cAAuB;QAY7B,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAASC,OAAO,EAAE,GAAGC,aAAa,GAAGN;QAE/D,IAAIO;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,oDAAoD;QACpDC,QAAQC,EAAE,CAAC,QAAQ;YACjB,IAAI,CAACC,KAAK;QACZ;QAEA,MAAMC,eAAe;gBAMRT;YALX,IAAI,CAACI,OAAO,GAAG,IAAIM,kBAAU,CAACjB,YAAY;gBACxC,GAAGO,WAAW;gBACdW,aAAa;oBACX,GAAGX,YAAYW,WAAW;oBAC1BC,KAAK;wBACH,GAAKZ,EAAAA,2BAAAA,YAAYW,WAAW,qBAAvBX,yBAAyBY,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGN,QAAQM,GAAG;wBACdC,gBAAgB;oBAClB;gBACF;YACF;YACA,IAAI,CAAClB,WAAW,GAAG;YACnBM,iBAAiB,IAAIa,QACnB,CAACC,UAAab,wBAAwBa;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAACf,YAAYgB,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM9B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACkB,OAAO,CAAShB,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH;qBAAAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAeqB,EAAE,CAAC,QAAQ,CAACU,MAAMC;wBAC/B,IAAI,AAACD,CAAAA,QAASC,UAAUA,WAAW,QAAQ,KAAM,IAAI,CAACd,OAAO,EAAE;4BAC7DN,OAAOqB,KAAK,CACV,CAAC,uCAAuC,EAAEF,KAAK,aAAa,EAAEC,OAAO,CAAC;4BAGxE,2EAA2E;4BAC3E,IAAI,CAAC,IAAI,CAACvB,WAAW,EAAE;gCACrB,uGAAuG;gCACvGW,QAAQc,IAAI,CAACH,QAAQ;4BACvB;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,CAACb,OAAO,CAACiB,SAAS,GAAGC,IAAI,CAAChB,QAAQiB,MAAM;YAC5C,IAAI,CAACnB,OAAO,CAACoB,SAAS,GAAGF,IAAI,CAAChB,QAAQmB,MAAM;QAC9C;QACAhB;QAEA,MAAMiB,YAAY;YAChB,MAAMxC,SAAS,IAAI,CAACkB,OAAO;YAC3B,IAAI,CAAClB,QAAQ;YACb,MAAM6B,UAAUb;YAChBJ,OAAO6B,IAAI,CACT,CAAC,6DAA6D,EAC5D/B,UAAU,CAAC,IAAI,EAAEA,UAAU,KAAK,QAAQ,CAAC,GAAG,GAC7C,0DAA0D,CAAC;YAG9D,IAAI,CAACD,WAAW,GAAG;YAEnBT,OAAO0C,GAAG,GAAGC,IAAI,CAAC;gBAChBd,QAAQhC;gBACR0B;YACF;QACF;QAEA,IAAIqB,eAAuC;QAE3C,MAAMC,aAAa;YACjB,IAAID,cAAcE,aAAaF;YAC/BA,eAAe3B,cAAc,KAAK8B,WAAWP,WAAW9B;QAC1D;QAEA,KAAK,MAAMsC,UAAUlC,YAAYmC,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGtC,UAErB,OAAO,GAAGyC;gBACRlC;gBACA,IAAI;oBACF,IAAImC,WAAW;oBACf,OAAS;wBACPP;wBACA,MAAMQ,SAAS,MAAMzB,QAAQ0B,IAAI,CAAC;4BAC/B,IAAI,CAACpC,OAAO,AAAQ,CAAC8B,OAAO,IAAIG;4BACjCpC;yBACD;wBAED,IAAIsC,WAAWxD,WAAW,OAAOwD;wBACjC,IAAI1C,WAAWA,UAAUqC,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACRnC;oBACA4B;gBACF;YACF,IACA,AAAC,IAAI,CAAC3B,OAAO,AAAQ,CAAC8B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACrC,OAAO;QACrD;IACF;IAEAwB,MAAqC;QACnC,MAAM1C,SAAS,IAAI,CAACkB,OAAO;QAC3B,IAAI,CAAClB,QAAQ;YACX,MAAM,IAAIwD,MAAM;QAClB;QACAzD,eAAeC;QACf,IAAI,CAACkB,OAAO,GAAGC;QACf,OAAOnB,OAAO0C,GAAG;IACnB;IAEA;;GAEC,GACDpB,QAAc;QACZ,IAAI,IAAI,CAACJ,OAAO,EAAE;YAChBnB,eAAe,IAAI,CAACmB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACwB,GAAG;QAClB;IACF;AACF"}