{"version": 3, "sources": ["../../../src/lib/memory/shutdown.ts"], "names": ["disableMemoryDebuggingMode", "stopPeriodicMemoryUsageTracing", "stopObservingGc", "info", "bold", "gcEvents", "getGcEvents", "totalTimeInGcMs", "reduce", "acc", "event", "duration", "toFixed", "allMemoryUsage", "getAllMemoryUsageSpans", "peakHeapUsage", "Math", "max", "map", "usage", "peakRssUsage"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;qBALK;4BACA;4BACwB;uBAC0B;AAEhE,SAASA;IACdC,IAAAA,qCAA8B;IAC9BC,IAAAA,2BAAe;IAEfC,IAAAA,SAAI,EAACC,IAAAA,gBAAI,EAAC;IAEV,MAAMC,WAAWC,IAAAA,uBAAW;IAC5B,MAAMC,kBAAkBF,SAASG,MAAM,CACrC,CAACC,KAAKC,QAAUD,MAAMC,MAAMC,QAAQ,EACpC;IAEFR,IAAAA,SAAI,EAAC,CAAC,2BAA2B,EAAEI,gBAAgBK,OAAO,CAAC,GAAG,EAAE,CAAC;IAEjE,MAAMC,iBAAiBC,IAAAA,6BAAsB;IAC7C,MAAMC,gBAAgBC,KAAKC,GAAG,IACzBJ,eAAeK,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,kBAAkB;IAE3D,MAAMC,eAAeJ,KAAKC,GAAG,IACxBJ,eAAeK,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,aAAa;IAEtDhB,IAAAA,SAAI,EAAC,CAAC,oBAAoB,EAAE,AAACY,CAAAA,gBAAgB,OAAO,IAAG,EAAGH,OAAO,CAAC,GAAG,GAAG,CAAC;IACzET,IAAAA,SAAI,EAAC,CAAC,mBAAmB,EAAE,AAACiB,CAAAA,eAAe,OAAO,IAAG,EAAGR,OAAO,CAAC,GAAG,GAAG,CAAC;AACzE"}