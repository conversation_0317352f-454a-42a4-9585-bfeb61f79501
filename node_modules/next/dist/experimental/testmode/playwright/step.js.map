{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/step.ts"], "names": ["step", "isWithRunAsStep", "testInfo", "props", "handler", "_runAsStep", "complete", "result", "reportedError", "console", "log", "title", "test", "error"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;sBA1BD;AAoBrB,SAASC,gBACPC,QAAkB;IAElB,OAAO,gBAAgBA;AACzB;AAEO,eAAeF,KACpBE,QAAkB,EAClBC,KAAgB,EAChBC,OAAoD;IAEpD,IAAIH,gBAAgBC,WAAW;QAC7B,OAAOA,SAASG,UAAU,CAACF,OAAO,CAAC,EAAEG,QAAQ,EAAE,GAAKF,QAAQE;IAC9D;IAEA,iCAAiC;IACjC,IAAIC;IACJ,IAAIC;IACJ,IAAI;QACFC,QAAQC,GAAG,CAACP,MAAMQ,KAAK,EAAER;QACzB,MAAMS,UAAI,CAACZ,IAAI,CAACG,MAAMQ,KAAK,EAAE;YAC3BJ,SAAS,MAAMH,QAAQ,CAAC,EAAES,KAAK,EAAE;gBAC/BL,gBAAgBK;gBAChB,IAAIL,eAAe;oBACjB,MAAMA;gBACR;YACF;QACF;IACF,EAAE,OAAOK,OAAO;QACd,IAAIA,UAAUL,eAAe;YAC3B,MAAMK;QACR;IACF;IACA,OAAON;AACT"}