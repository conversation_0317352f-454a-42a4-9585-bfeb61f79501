{"version": 3, "sources": ["../../src/build/load-entrypoint.ts"], "names": ["loadEntrypoint", "PACKAGE_ROOT", "path", "normalize", "join", "__dirname", "TEMPLATE_FOLDER", "TEMPLATES_ESM_FOLDER", "entrypoint", "replacements", "injections", "imports", "filepath", "resolve", "file", "fs", "readFile", "count", "replaceAll", "_", "fromRequest", "importRequest", "relative", "replace", "startsWith", "Error", "JSON", "stringify", "replaced", "Set", "RegExp", "Object", "keys", "map", "k", "match", "key", "parse", "add", "matches", "size", "length", "difference", "filter", "has", "injected", "importsAdded", "asNamespace"], "mappings": ";;;;+BA0BsBA;;;eAAAA;;;iEA1BP;6DACE;;;;;;AAEjB,6DAA6D;AAC7D,MAAMC,eAAeC,aAAI,CAACC,SAAS,CAACD,aAAI,CAACE,IAAI,CAACC,WAAW;AACzD,MAAMC,kBAAkBJ,aAAI,CAACE,IAAI,CAACC,WAAW;AAC7C,MAAME,uBAAuBL,aAAI,CAACC,SAAS,CACzCD,aAAI,CAACE,IAAI,CAACC,WAAW;AAmBhB,eAAeL,eACpBQ,UAQe,EACfC,YAA6C,EAC7CC,UAAmC,EACnCC,OAAuC;IAEvC,MAAMC,WAAWV,aAAI,CAACW,OAAO,CAC3BX,aAAI,CAACE,IAAI,CAACG,sBAAsB,CAAC,EAAEC,WAAW,GAAG,CAAC;IAGpD,IAAIM,OAAO,MAAMC,iBAAE,CAACC,QAAQ,CAACJ,UAAU;IAEvC,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAIK,QAAQ;IACZH,OAAOA,KAAKI,UAAU,CACpB,kCACA,SAAUC,CAAC,EAAEC,WAAW,EAAEC,aAAa;QACrCJ;QAEA,MAAMK,WAAWpB,aAAI,CAClBoB,QAAQ,CACPrB,cACAC,aAAI,CAACW,OAAO,CAACP,iBAAiBc,eAAeC,eAE/C,2DAA2D;SAC1DE,OAAO,CAAC,OAAO;QAElB,0EAA0E;QAC1E,uEAAuE;QACvE,oCAAoC;QACpC,IAAI,CAACD,SAASE,UAAU,CAAC,UAAU;YACjC,MAAM,IAAIC,MACR,CAAC,kEAAkE,EAAEH,SAAS,CAAC,CAAC;QAEpF;QAEA,OAAOF,cACH,CAAC,KAAK,EAAEM,KAAKC,SAAS,CAACL,UAAU,CAAC,GAClC,CAAC,OAAO,EAAEI,KAAKC,SAAS,CAACL,UAAU,CAAC;IAC1C;IAGF,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,iBAAiB;IACjB,IAAIL,UAAU,GAAG;QACf,MAAM,IAAIQ,MAAM;IAClB;IAEA,MAAMG,WAAW,IAAIC;IAErB,2EAA2E;IAC3E,uCAAuC;IACvCf,OAAOA,KAAKI,UAAU,CACpB,IAAIY,OACF,CAAC,EAAEC,OAAOC,IAAI,CAACvB,cACZwB,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnB9B,IAAI,CAAC,KAAK,CAAC,EACd,MAEF,CAAC+B;QACC,MAAMC,MAAMV,KAAKW,KAAK,CAACF;QAEvB,IAAI,CAAEC,CAAAA,OAAO3B,YAAW,GAAI;YAC1B,MAAM,IAAIgB,MAAM,CAAC,wCAAwC,EAAEW,IAAI,CAAC;QAClE;QAEAR,SAASU,GAAG,CAACF;QAEb,OAAOV,KAAKC,SAAS,CAAClB,YAAY,CAAC2B,IAAI;IACzC;IAGF,4DAA4D;IAC5D,IAAIG,UAAUzB,KAAKqB,KAAK,CAAC;IACzB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,6DAA6D,EAAEc,QAAQnC,IAAI,CAC1E,MACA,CAAC;IAEP;IAEA,mEAAmE;IACnE,IAAIwB,SAASY,IAAI,KAAKT,OAAOC,IAAI,CAACvB,cAAcgC,MAAM,EAAE;QACtD,yEAAyE;QACzE,uEAAuE;QACvE,kDAAkD;QAClD,MAAMC,aAAaX,OAAOC,IAAI,CAACvB,cAAckC,MAAM,CACjD,CAACP,MAAQ,CAACR,SAASgB,GAAG,CAACR;QAGzB,MAAM,IAAIX,MACR,CAAC,+DAA+D,EAAEiB,WAAWtC,IAAI,CAC/E,MACA,YAAY,CAAC;IAEnB;IAEA,0BAA0B;IAC1B,MAAMyC,WAAW,IAAIhB;IACrB,IAAInB,YAAY;QACd,iEAAiE;QACjEI,OAAOA,KAAKI,UAAU,CACpB,IAAIY,OAAO,CAAC,WAAW,EAAEC,OAAOC,IAAI,CAACtB,YAAYN,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAC/D,CAACe,GAAGiB;YACF,IAAI,CAAEA,CAAAA,OAAO1B,UAAS,GAAI;gBACxB,MAAM,IAAIe,MAAM,CAAC,gCAAgC,EAAEW,IAAI,CAAC;YAC1D;YAEAS,SAASP,GAAG,CAACF;YAEb,OAAO,CAAC,MAAM,EAAEA,IAAI,GAAG,EAAE1B,UAAU,CAAC0B,IAAI,CAAC,CAAC;QAC5C;IAEJ;IAEA,oDAAoD;IACpDG,UAAUzB,KAAKqB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,oDAAoD,EAAEc,QAAQnC,IAAI,CACjE,MACA,CAAC;IAEP;IAEA,2DAA2D;IAC3D,IAAIyC,SAASL,IAAI,KAAKT,OAAOC,IAAI,CAACtB,cAAc,CAAC,GAAG+B,MAAM,EAAE;QAC1D,uEAAuE;QACvE,2EAA2E;QAC3E,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACtB,cAAc,CAAC,GAAGiC,MAAM,CACrD,CAACP,MAAQ,CAACS,SAASD,GAAG,CAACR;QAGzB,MAAM,IAAIX,MACR,CAAC,sDAAsD,EAAEiB,WAAWtC,IAAI,CACtE,MACA,YAAY,CAAC;IAEnB;IAEA,gCAAgC;IAChC,MAAM0C,eAAe,IAAIjB;IACzB,IAAIlB,SAAS;QACX,8DAA8D;QAC9DG,OAAOA,KAAKI,UAAU,CACpB,IAAIY,OACF,CAAC,8BAA8B,EAAEC,OAAOC,IAAI,CAACrB,SAASP,IAAI,CAAC,KAAK,CAAC,CAAC,EAClE,MAEF,CAACe,GAAG4B,cAAc,EAAE,EAAEX;YACpB,IAAI,CAAEA,CAAAA,OAAOzB,OAAM,GAAI;gBACrB,MAAM,IAAIc,MAAM,CAAC,sCAAsC,EAAEW,IAAI,CAAC;YAChE;YAEAU,aAAaR,GAAG,CAACF;YAEjB,IAAIzB,OAAO,CAACyB,IAAI,EAAE;gBAChB,OAAO,CAAC,OAAO,EAAEW,YAAY,EAAEX,IAAI,MAAM,EAAEV,KAAKC,SAAS,CACvDhB,OAAO,CAACyB,IAAI,EACZ,CAAC;YACL,OAAO;gBACL,OAAO,CAAC,MAAM,EAAEA,IAAI,OAAO,CAAC;YAC9B;QACF;IAEJ;IAEA,iDAAiD;IACjDG,UAAUzB,KAAKqB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,iDAAiD,EAAEc,QAAQnC,IAAI,CAAC,MAAM,CAAC;IAE5E;IAEA,wDAAwD;IACxD,IAAI0C,aAAaN,IAAI,KAAKT,OAAOC,IAAI,CAACrB,WAAW,CAAC,GAAG8B,MAAM,EAAE;QAC3D,oEAAoE;QACpE,qEAAqE;QACrE,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACrB,WAAW,CAAC,GAAGgC,MAAM,CAClD,CAACP,MAAQ,CAACU,aAAaF,GAAG,CAACR;QAG7B,MAAM,IAAIX,MACR,CAAC,mDAAmD,EAAEiB,WAAWtC,IAAI,CACnE,MACA,YAAY,CAAC;IAEnB;IAEA,OAAOU;AACT"}