{"version": 3, "sources": ["../../../src/build/swc/jest-transformer.ts"], "names": ["vm", "transformSync", "getJestSWCOptions", "isSupportEsm", "getJestConfig", "jestConfig", "config", "isEsm", "isEsmProject", "filename", "test", "extensionsToTreatAsEsm", "some", "ext", "endsWith", "createTransformer", "inputOptions", "process", "src", "jestOptions", "swcTransformOpts", "isServer", "testEnvironment", "includes", "jsConfig", "resolvedBaseUrl", "pagesDir", "serverComponents", "modularizeImports", "swcPlugins", "compilerOptions", "esm", "Boolean", "module", "exports"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAEA,OAAOA,QAAQ,KAAI;AACnB,SAASC,aAAa,QAAQ,UAAS;AACvC,SAASC,iBAAiB,QAAQ,YAAW;AAsB7C,6FAA6F;AAC7F,mDAAmD;AACnD,MAAMC,eAAe,YAAYH;AAEjC,SAASI,cACPC,UAAmD;IAEnD,OAAO,YAAYA,aAEfA,WAAWC,MAAM,GAEhBD;AACP;AAEA,SAASE,MACPC,YAAqB,EACrBC,QAAgB,EAChBJ,UAAgC;QAI9BA;IAFF,OACE,AAAC,UAAUK,IAAI,CAACD,aAAaD,kBAC7BH,qCAAAA,WAAWM,sBAAsB,qBAAjCN,mCAAmCO,IAAI,CAAC,CAACC,MACvCJ,SAASK,QAAQ,CAACD;AAGxB;AAEA,MAAME,oBAGF,CAACC,eAAkB,CAAA;QACrBC,SAAQC,GAAG,EAAET,QAAQ,EAAEU,WAAW;YAChC,MAAMd,aAAaD,cAAce;YAEjC,MAAMC,mBAAmBlB,kBAAkB;gBACzCmB,UACEhB,WAAWiB,eAAe,KAAK,UAC/BjB,WAAWiB,eAAe,CAACC,QAAQ,CAAC;gBACtCd;gBACAe,QAAQ,EAAER,gCAAAA,aAAcQ,QAAQ;gBAChCC,eAAe,EAAET,gCAAAA,aAAcS,eAAe;gBAC9CC,QAAQ,EAAEV,gCAAAA,aAAcU,QAAQ;gBAChCC,gBAAgB,EAAEX,gCAAAA,aAAcW,gBAAgB;gBAChDC,iBAAiB,EAAEZ,gCAAAA,aAAcY,iBAAiB;gBAClDC,UAAU,EAAEb,gCAAAA,aAAca,UAAU;gBACpCC,eAAe,EAAEd,gCAAAA,aAAcc,eAAe;gBAC9CC,KACE5B,gBACAI,MAAMyB,QAAQhB,gCAAAA,aAAcR,YAAY,GAAGC,UAAUJ;YACzD;YAEA,OAAOJ,cAAciB,KAAK;gBAAE,GAAGE,gBAAgB;gBAAEX;YAAS;QAC5D;IACF,CAAA;AAEAwB,OAAOC,OAAO,GAAG;IAAEnB;AAAkB"}