{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/helpers.ts"], "names": ["fs", "path", "envProxy", "tcpProxy", "TurborepoAccessTraceResult", "turborepoTraceAccess", "f", "parent", "process", "env", "TURBOREPO_TRACE_FILE", "withTurborepoTraceAccess", "then", "result", "proxy", "merge", "writeTurborepoAccessTraceResult", "distDir", "traces", "configTraceFile", "length", "accessTrace", "otherTraces", "trace", "mkdir", "dirname", "recursive", "writeFile", "JSON", "stringify", "outputs", "accessed", "toPublicTrace", "err", "Error", "cause", "envVars", "Set", "addresses", "fsPaths", "restoreTCP", "restoreEnv", "functionResult", "traceResult"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AAEvB,SAASC,QAAQ,QAAQ,QAAO;AAChC,SAASC,QAAQ,QAAQ,QAAO;AAChC,SAASC,0BAA0B,QAAQ,WAAU;AAErD;;;;;;;CAOC,GACD,OAAO,SAASC,qBACdC,CAAuB,EACvBC,MAAkC;IAElC,sEAAsE;IACtE,YAAY;IACZ,IAAI,CAACC,QAAQC,GAAG,CAACC,oBAAoB,EAAE,OAAOJ;IAE9C,6EAA6E;IAC7E,4EAA4E;IAC5E,uBAAuB;IACvB,OAAOK,yBAAyBL,GAAGM,IAAI,CAAC,CAAC,CAACC,QAAQC,MAAM;QACtDP,OAAOQ,KAAK,CAACD;QAEb,qCAAqC;QACrC,OAAOD;IACT;AACF;AAEA;;;;;CAKC,GACD,OAAO,eAAeG,gCAAgC,EACpDC,OAAO,EACPC,MAAM,EAIP;IACC,MAAMC,kBAAkBX,QAAQC,GAAG,CAACC,oBAAoB;IAExD,IAAI,CAACS,mBAAmBD,OAAOE,MAAM,KAAK,GAAG;IAE7C,eAAe;IACf,MAAM,CAACC,aAAa,GAAGC,YAAY,GAAGJ;IACtC,KAAK,MAAMK,SAASD,YAAa;QAC/BD,YAAYN,KAAK,CAACQ;IACpB;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAMvB,GAAGwB,KAAK,CAACvB,KAAKwB,OAAO,CAACN,kBAAkB;YAAEO,WAAW;QAAK;QAChE,MAAM1B,GAAG2B,SAAS,CAChBR,iBACAS,KAAKC,SAAS,CAAC;YACbC,SAAS;gBAAC,CAAC,EAAEb,QAAQ,GAAG,CAAC;gBAAE,CAAC,CAAC,EAAEA,QAAQ,SAAS,CAAC;aAAC;YAClDc,UAAUV,YAAYW,aAAa;QACrC;IAEJ,EAAE,OAAOC,KAAK;QACZ,gEAAgE;QAChE,qDAAqD;QACrD,MAAM,IAAIC,MAAM,CAAC,2CAA2C,CAAC,EAAE;YAC7DC,OAAOF;QACT;IACF;AACF;AAEA,eAAetB,yBACbL,CAAuB;IAEvB,MAAM8B,UAAmB,IAAIC,IAAI,EAAE;IACnC,wDAAwD;IACxD,MAAMC,YAAuB,EAAE;IAC/B,iEAAiE;IACjE,MAAMC,UAAc,IAAIF;IAExB,gBAAgB;IAChB,MAAMG,aAAarC,SAASmC;IAC5B,MAAMG,aAAavC,SAASkC;IAE5B,IAAIM;IAEJ,yFAAyF;IACzF,IAAI;QACF,4BAA4B;QAC5BA,iBAAiB,MAAMpC;IACzB,SAAU;QACR,iBAAiB;QACjBkC;QACAC;IACF;IAEA,MAAME,cAAc,IAAIvC,2BACtBgC,SACAE,WACAC;IAGF,OAAO;QAACG;QAAgBC;KAAY;AACtC"}