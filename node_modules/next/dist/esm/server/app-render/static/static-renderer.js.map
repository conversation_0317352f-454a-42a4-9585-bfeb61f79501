{"version": 3, "sources": ["../../../../src/server/app-render/static/static-renderer.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "prerender", "process", "env", "__NEXT_EXPERIMENTAL_REACT", "require", "render", "children", "prelude", "postponed", "stream", "StaticResumeRenderer", "resume", "resumed", "<PERSON><PERSON><PERSON><PERSON>", "renderToReadableStream", "Void<PERSON><PERSON><PERSON>", "_children", "ReadableStream", "start", "controller", "close", "DYNAMIC_DATA", "DYNAMIC_HTML", "getDynamicHTMLPostponedState", "data", "getDynamicDataPostponedState", "createStatic<PERSON><PERSON><PERSON>", "ppr", "isStaticGeneration", "streamOptions", "signal", "onError", "onPostpone", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonce", "bootstrapScripts", "formState", "reactPostponedState"], "mappings": "AAgBA,MAAMA;IAMJC,YAAY,AAAiBC,OAAyB,CAAE;aAA3BA,UAAAA;aAJZC,YAAaC,QAAQC,GAAG,CAACC,yBAAyB,GAC/DC,QAAQ,yBAAyBJ,SAAS,GAC1C;IAEqD;IAEzD,MAAaK,OAAOC,QAAqB,EAAE;QACzC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAG,MAAM,IAAI,CAACR,SAAS,CAACM,UAAU,IAAI,CAACP,OAAO;QAE1E,OAAO;YAAEU,QAAQF;YAASC;QAAU;IACtC;AACF;AAEA,MAAME;IAIJZ,YACE,AAAiBU,SAAiB,EAClC,AAAiBT,OAAsB,CACvC;aAFiBS,YAAAA;aACAT,UAAAA;aALFY,SAASP,QAAQ,yBAC/BO,MAAM;IAKN;IAEH,MAAaN,OAAOC,QAAqB,EAAE;QACzC,MAAMG,SAAS,MAAM,IAAI,CAACE,MAAM,CAACL,UAAU,IAAI,CAACE,SAAS,EAAE,IAAI,CAACT,OAAO;QAEvE,OAAO;YAAEU;YAAQG,SAAS;QAAK;IACjC;AACF;AAEA,OAAO,MAAMC;IAIXf,YAAY,AAAiBC,OAAsC,CAAE;aAAxCA,UAAAA;aAHZe,yBAAyBV,QAAQ,yBAC/CU,sBAAsB;IAE6C;IAEtE,MAAaT,OAAOC,QAAqB,EAAyB;QAChE,MAAMG,SAAS,MAAM,IAAI,CAACK,sBAAsB,CAACR,UAAU,IAAI,CAACP,OAAO;QACvE,OAAO;YAAEU;QAAO;IAClB;AACF;AAEA,OAAO,MAAMM;IACX,MAAaV,OAAOW,SAAsB,EAAyB;QACjE,OAAO;YACLP,QAAQ,IAAIQ,eAAe;gBACzBC,OAAMC,UAAU;oBACd,+BAA+B;oBAC/BA,WAAWC,KAAK;gBAClB;YACF;YACAR,SAAS;QACX;IACF;AACF;AAqBA,OAAO,MAAMS,eAAe,EAAU;AACtC,OAAO,MAAMC,eAAe,EAAU;AAQtC,OAAO,SAASC,6BACdC,IAAY;IAEZ,OAAO;QAACF;QAAcE;KAAK;AAC7B;AAEA,OAAO,SAASC;IACd,OAAOJ;AACT;AA8BA,OAAO,SAASK,qBAAqB,EACnCC,GAAG,EACHC,kBAAkB,EAClBpB,SAAS,EACTqB,eAAe,EACbC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,gBAAgB,EAChBC,SAAS,EACV,EACO;IACR,IAAIV,KAAK;QACP,IAAIC,oBAAoB;YACtB,sBAAsB;YACtB,OAAO,IAAI/B,eAAe;gBACxBiC;gBACAC;gBACAC;gBACA,oEAAoE;gBACpE,wDAAwD;gBACxDC;gBACAC;gBACAE;YACF;QACF,OAAO;YACL,mBAAmB;YACnB,IAAI5B,cAAca,cAAc;gBAC9B,mEAAmE;gBACnE,OAAO,IAAIN;YACb,OAAO,IAAIP,WAAW;gBACpB,MAAM8B,sBAAsB9B,SAAS,CAAC,EAAE;gBACxC,sDAAsD;gBACtD,OAAO,IAAIE,qBAAqB4B,qBAAqB;oBACnDR;oBACAC;oBACAC;oBACAG;gBACF;YACF;QACF;IACF;IAEA,IAAIP,oBAAoB;QACtB,wCAAwC;QACxC,OAAO,IAAIf,eAAe;YACxBiB;YACAC;YACA,0EAA0E;YAC1E,gFAAgF;YAChF,8EAA8E;YAC9E,uBAAuB;YACvBI;YACAC;YACAC;QACF;IACF;IAEA,yCAAyC;IACzC,OAAO,IAAIxB,eAAe;QACxBiB;QACAC;QACA,sEAAsE;QACtE,0EAA0E;QAC1EE;QACAC;QACAC;QACAC;QACAC;IACF;AACF"}