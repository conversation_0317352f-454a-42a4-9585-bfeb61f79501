{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["React", "isNotFoundError", "getURLFromRedirectError", "isRedirectError", "getRedirectStatusCodeFromError", "renderToReadableStream", "streamToString", "RedirectStatusCode", "addPathPrefix", "makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "serverCapturedErrors", "basePath", "flushedErrorMetaTagsUntilIndex", "hasUnflushedPolyfills", "length", "getServerInsertedHTML", "errorMetaTags", "error", "push", "meta", "name", "content", "digest", "process", "env", "NODE_ENV", "redirectUrl", "statusCode", "isPermanent", "PermanentRedirect", "id", "httpEquiv", "serverInsertedHTML", "Array", "isArray", "stream", "map", "polyfill", "script", "src", "progressiveChunkSize"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SACEC,uBAAuB,EACvBC,eAAe,EACfC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,sBAAsB,QAAQ,wBAAuB;AAC9D,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,aAAa,QAAQ,gDAA+C;AAE7E,OAAO,SAASC,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,oBAAoB,EACpBC,QAAQ,EAMT;IACC,IAAIC,iCAAiC;IACrC,IAAIC,wBAAwBL,UAAUM,MAAM,KAAK;IAEjD,OAAO,eAAeC;QACpB,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOJ,iCAAiCF,qBAAqBI,MAAM,CAAE;YACnE,MAAMG,QAAQP,oBAAoB,CAACE,+BAA+B;YAClEA;YAEA,IAAIb,gBAAgBkB,QAAQ;gBAC1BD,cAAcE,IAAI,eAChB,KAACC;oBAAKC,MAAK;oBAASC,SAAQ;mBAAeJ,MAAMK,MAAM,GACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,KAACN;oBAAKC,MAAK;oBAAaC,SAAQ;mBAAgB,gBAC9C;YAER,OAAO,IAAIpB,gBAAgBgB,QAAQ;gBACjC,MAAMS,cAAcpB,cAClBN,wBAAwBiB,QACxBN;gBAEF,MAAMgB,aAAazB,+BAA+Be;gBAClD,MAAMW,cACJD,eAAetB,mBAAmBwB,iBAAiB,GAAG,OAAO;gBAC/D,IAAIH,aAAa;oBACfV,cAAcE,IAAI,eAChB,KAACC;wBACCW,IAAG;wBACHC,WAAU;wBACVV,SAAS,CAAC,EAAEO,cAAc,IAAI,EAAE,KAAK,EAAEF,YAAY,CAAC;uBAC/CT,MAAMK,MAAM;gBAGvB;YACF;QACF;QAEA,MAAMU,qBAAqBvB;QAE3B,wDAAwD;QACxD,IACE,CAACI,yBACDG,cAAcF,MAAM,KAAK,KACzBmB,MAAMC,OAAO,CAACF,uBACdA,mBAAmBlB,MAAM,KAAK,GAC9B;YACA,OAAO;QACT;QAEA,MAAMqB,SAAS,MAAMhC,qCACnB;;gBAEI,0DAA0D,GAC1DU,yBACEL,UAAU4B,GAAG,CAAC,CAACC;oBACb,qBAAO,KAACC;wBAA2B,GAAGD,QAAQ;uBAA1BA,SAASE,GAAG;gBAClC;gBAEHP;gBACAhB;;YAEH;YACE,yDAAyD;YACzD,uBAAuB;YACvBwB,sBAAsB,OAAO;QAC/B;QAGF3B,wBAAwB;QAExB,qDAAqD;QACrD,qEAAqE;QACrE,oEAAoE;QACpE,OAAOT,eAAe+B;IACxB;AACF"}