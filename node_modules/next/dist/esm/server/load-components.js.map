{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE", "join", "requirePage", "interopDefault", "getTracer", "LoadComponentsSpan", "evalManifest", "loadManifest", "wait", "setReferenceManifestsSingleton", "createServerModuleMap", "loadManifestWithRetries", "manifestPath", "attempts", "err", "evalManifestWithRetries", "loadClientReferenceManifest", "entryName", "context", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "isDev", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "hasClientManifest", "endsWith", "manifestLoadAttempts", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "replace", "catch", "serverModuleMap", "pageName", "ComponentMod", "Component", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "loadComponents", "wrap"], "mappings": "AAgBA,SACEA,cAAc,EACdC,uBAAuB,EACvBC,yBAAyB,EACzBC,yBAAyB,EACzBC,0BAA0B,QACrB,0BAAyB;AAChC,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,WAAW,QAAQ,YAAW;AACvC,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,kBAAiB;AAC5D,SAASC,IAAI,QAAQ,cAAa;AAClC,SAASC,8BAA8B,QAAQ,gCAA+B;AAC9E,SAASC,qBAAqB,QAAQ,4BAA2B;AAyCjE;;CAEC,GACD,OAAO,eAAeC,wBACpBC,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAON,aAAgBK;QACzB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMN,KAAK;QACb;IACF;AACF;AAEA;;CAEC,GACD,OAAO,eAAeO,wBACpBH,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOP,aAAgBM;QACzB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMN,KAAK;QACb;IACF;AACF;AAEA,eAAeQ,4BACbJ,YAAoB,EACpBK,SAAiB,EACjBJ,QAAiB;IAEjB,IAAI;QACF,MAAMK,UAAU,MAAMH,wBAEnBH,cAAcC;QACjB,OAAOK,QAAQC,cAAc,CAACF,UAAU;IAC1C,EAAE,OAAOH,KAAK;QACZ,OAAOM;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EAMN;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACH,WAAW;QACb,CAACE,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAM7B,YAAY,cAAcoB,SAAS;YAChEM,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAM7B,YAAY,SAASoB,SAAS;SAC5D;IACH;IAEA,6DAA6D;IAC7D,MAAMU,oBACJR,aAAcD,CAAAA,KAAKU,QAAQ,CAAC,YAAYV,SAASvB,0BAAyB;IAE5E,0EAA0E;IAC1E,6EAA6E;IAC7E,qEAAqE;IACrE,sBAAsB;IACtB,MAAMkC,uBAAuBT,QAAQ,IAAI;IAEzC,gCAAgC;IAChC,MAAM,CACJU,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMV,QAAQC,GAAG,CAAC;QACpBlB,wBACEV,KAAKqB,SAAS1B,iBACdsC;QAEFvB,wBACEV,KAAKqB,SAASzB,0BACdqC;QAEFF,oBACIhB,4BACEf,KACEqB,SACA,UACA,OACAC,KAAKgB,OAAO,CAAC,QAAQ,OAAO,MAAMzC,4BAA4B,QAEhEyB,KAAKgB,OAAO,CAAC,QAAQ,MACrBL,wBAEFd;QACJI,YACIb,wBACEV,KAAKqB,SAAS,UAAUvB,4BAA4B,UACpDmC,sBACAM,KAAK,CAAC,IAAM,QACd;KACL;IAED,iFAAiF;IACjF,4EAA4E;IAC5E,uCAAuC;IACvC,IAAIF,yBAAyBD,yBAAyB;QACpD5B,+BAA+B;YAC7B4B;YACAC;YACAG,iBAAiB/B,sBAAsB;gBACrC4B;gBACAI,UAAUnB;YACZ;QACF;IACF;IAEA,MAAMoB,eAAe,MAAMf,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChD7B,YAAYqB,MAAMD,SAASE;IAG7B,MAAMoB,YAAYzC,eAAewC;IACjC,MAAME,WAAW1C,eAAeuB;IAChC,MAAMoB,MAAM3C,eAAewB;IAE3B,MAAM,EAAEoB,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvEP;IAEF,OAAO;QACLG;QACAD;QACAD;QACAT;QACAC;QACAe,YAAYR,aAAaS,MAAM,IAAI,CAAC;QACpCT;QACAI;QACAC;QACAC;QACAZ;QACAC;QACAd;QACAD;QACA2B;IACF;AACF;AAEA,OAAO,MAAMG,iBAAiBjD,YAAYkD,IAAI,CAC5CjD,mBAAmBgD,cAAc,EACjChC,oBACD"}