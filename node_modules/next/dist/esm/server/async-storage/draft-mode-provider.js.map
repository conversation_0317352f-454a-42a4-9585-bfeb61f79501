{"version": 3, "sources": ["../../../src/server/async-storage/draft-mode-provider.ts"], "names": ["COOKIE_NAME_PRERENDER_BYPASS", "checkIsOnDemandRevalidate", "DraftModeProvider", "constructor", "previewProps", "req", "cookies", "mutableCookies", "isOnDemandRevalidate", "cookieValue", "get", "value", "isEnabled", "Boolean", "previewModeId", "process", "env", "NODE_ENV", "_previewModeId", "_mutableCookies", "enable", "Error", "set", "name", "httpOnly", "sameSite", "secure", "path", "disable", "expires", "Date"], "mappings": "AAMA,SACEA,4BAA4B,EAC5BC,yBAAyB,QACpB,eAAc;AAGrB,OAAO,MAAMC;IAaXC,YACEC,YAA2C,EAC3CC,GAA6D,EAC7DC,OAA+B,EAC/BC,cAA+B,CAC/B;YAOoBD;QANpB,mEAAmE;QACnE,4DAA4D;QAC5D,MAAME,uBACJJ,gBACAH,0BAA0BI,KAAKD,cAAcI,oBAAoB;QAEnE,MAAMC,eAAcH,eAAAA,QAAQI,GAAG,CAACV,kDAAZM,aAA2CK,KAAK;QAEpE,IAAI,CAACC,SAAS,GAAGC,QACf,CAACL,wBACCC,eACAL,gBACCK,CAAAA,gBAAgBL,aAAaU,aAAa,IACzC,mHAAmH;QAClHC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACxBb,aAAaU,aAAa,KAAK,gBAAgB;QAGvD,IAAI,CAACI,cAAc,GAAGd,gCAAAA,aAAcU,aAAa;QACjD,IAAI,CAACK,eAAe,GAAGZ;IACzB;IAEAa,SAAS;QACP,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;YACxB,MAAM,IAAIG,MACR;QAEJ;QAEA,IAAI,CAACF,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMvB;YACNW,OAAO,IAAI,CAACO,cAAc;YAC1BM,UAAU;YACVC,UAAUV,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DS,QAAQX,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCU,MAAM;QACR;IACF;IAEAC,UAAU;QACR,2DAA2D;QAC3D,oDAAoD;QACpD,wEAAwE;QACxE,IAAI,CAACT,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMvB;YACNW,OAAO;YACPa,UAAU;YACVC,UAAUV,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DS,QAAQX,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCU,MAAM;YACNE,SAAS,IAAIC,KAAK;QACpB;IACF;AACF"}