{"version": 3, "sources": ["../../src/server/next.ts"], "names": ["log", "loadConfig", "path", "resolve", "NON_STANDARD_NODE_ENV", "PHASE_DEVELOPMENT_SERVER", "SERVER_FILES_MANIFEST", "PHASE_PRODUCTION_SERVER", "getTracer", "NextServerSpan", "formatUrl", "checkNodeDebugType", "ServerImpl", "getServerImpl", "undefined", "Promise", "require", "default", "SYMBOL_LOAD_CONFIG", "Symbol", "NextServer", "constructor", "options", "hostname", "port", "getRequestHandler", "req", "res", "parsedUrl", "trace", "requestHandler", "getServerRequestHandler", "getUpgradeHandler", "socket", "head", "server", "getServer", "handleUpgrade", "apply", "setAssetPrefix", "assetPrefix", "preparedAssetPrefix", "logError", "args", "render", "renderToHTML", "renderError", "renderErrorToHTML", "render404", "prepare", "serverFields", "standaloneMode", "Object", "assign", "dev", "close", "createServer", "ServerImplementation", "dir", "config", "preloadedConfig", "customConfig", "conf", "silent", "process", "env", "NODE_ENV", "serializedConfig", "join", "experimental", "isExperimentalCompile", "_", "serverPromise", "then", "__NEXT_PRIVATE_STANDALONE_CONFIG", "JSON", "stringify", "output", "warn", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reqHandlerPromise", "wrap", "bind", "NextCustomServer", "getRequestHandlers", "isNodeDebugging", "initResult", "isDev", "minimalMode", "upgradeHandler", "renderServer", "setupWebSocketHandler", "customServer", "_req", "didWebSocketSetup", "on", "httpServer", "url", "pathname", "query", "startsWith", "console", "error", "typescript", "createTSPlugin", "includes", "module", "exports"], "mappings": "AAWA,OAAO,iBAAgB;AACvB,OAAO,yBAAwB;AAG/B,YAAYA,SAAS,sBAAqB;AAC1C,OAAOC,gBAAgB,WAAU;AACjC,OAAOC,QAAQC,OAAO,QAAQ,OAAM;AACpC,SAASC,qBAAqB,QAAQ,mBAAkB;AACxD,SACEC,wBAAwB,EACxBC,qBAAqB,QAChB,0BAAyB;AAChC,SAASC,uBAAuB,QAAQ,0BAAyB;AACjE,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,kBAAkB,QAAQ,cAAa;AAEhD,IAAIC;AAEJ,MAAMC,gBAAgB;IACpB,IAAID,eAAeE,WAAW;QAC5BF,aAAa,AAAC,CAAA,MAAMG,QAAQZ,OAAO,CAACa,QAAQ,iBAAgB,EAAGC,OAAO;IACxE;IACA,OAAOL;AACT;AAoBA,MAAMM,qBAAqBC,OAAO;AAElC,OAAO,MAAMC;IAWXC,YAAYC,OAA0B,CAAE;QACtC,IAAI,CAACA,OAAO,GAAGA;IACjB;IAEA,IAAIC,WAAW;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;IAC9B;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI;IAC1B;IAEAC,oBAAoC;QAClC,OAAO,OACLC,KACAC,KACAC;YAEA,OAAOpB,YAAYqB,KAAK,CAACpB,eAAegB,iBAAiB,EAAE;gBACzD,MAAMK,iBAAiB,MAAM,IAAI,CAACC,uBAAuB;gBACzD,OAAOD,eAAeJ,KAAKC,KAAKC;YAClC;QACF;IACF;IAEAI,oBAAoB;QAClB,OAAO,OAAON,KAAsBO,QAAaC;YAC/C,MAAMC,SAAS,MAAM,IAAI,CAACC,SAAS;YACnC,mDAAmD;YACnD,uBAAuB;YACvB,OAAOD,OAAOE,aAAa,CAACC,KAAK,CAACH,QAAQ;gBAACT;gBAAKO;gBAAQC;aAAK;QAC/D;IACF;IAEAK,eAAeC,WAAmB,EAAE;QAClC,IAAI,IAAI,CAACL,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACI,cAAc,CAACC;QAC7B,OAAO;YACL,IAAI,CAACC,mBAAmB,GAAGD;QAC7B;IACF;IAEAE,SAAS,GAAGC,IAAoC,EAAE;QAChD,IAAI,IAAI,CAACR,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACO,QAAQ,IAAIC;QAC1B;IACF;IAEA,MAAMC,OAAO,GAAGD,IAAkC,EAAE;QAClD,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOS,MAAM,IAAID;IAC1B;IAEA,MAAME,aAAa,GAAGF,IAAwC,EAAE;QAC9D,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOU,YAAY,IAAIF;IAChC;IAEA,MAAMG,YAAY,GAAGH,IAAuC,EAAE;QAC5D,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOW,WAAW,IAAIH;IAC/B;IAEA,MAAMI,kBAAkB,GAAGJ,IAA6C,EAAE;QACxE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOY,iBAAiB,IAAIJ;IACrC;IAEA,MAAMK,UAAU,GAAGL,IAAqC,EAAE;QACxD,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOa,SAAS,IAAIL;IAC7B;IAEA,MAAMM,QAAQC,YAAkB,EAAE;QAChC,IAAI,IAAI,CAACC,cAAc,EAAE;QAEzB,MAAMhB,SAAS,MAAM,IAAI,CAACC,SAAS;QAEnC,IAAIc,cAAc;YAChBE,OAAOC,MAAM,CAAClB,QAAQe;QACxB;QACA,iDAAiD;QACjD,oDAAoD;QACpD,IAAI,IAAI,CAAC5B,OAAO,CAACgC,GAAG,EAAE;YACpB,MAAMnB,OAAOc,OAAO;QACtB;IACF;IAEA,MAAMM,QAAQ;QACZ,MAAMpB,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAO,AAACD,OAAeoB,KAAK;IAC9B;IAEA,MAAcC,aACZlC,OAAyC,EACxB;QACjB,IAAImC;QACJ,IAAInC,QAAQgC,GAAG,EAAE;YACfG,uBAAuBzC,QAAQ,yBAAyBC,OAAO;QACjE,OAAO;YACLwC,uBAAuB,MAAM5C;QAC/B;QACA,MAAMsB,SAAS,IAAIsB,qBAAqBnC;QAExC,OAAOa;IACT;IAEA,MAAc,CAACjB,mBAAmB,GAAG;QACnC,MAAMwC,MAAMvD,QAAQ,IAAI,CAACmB,OAAO,CAACoC,GAAG,IAAI;QAExC,MAAMC,SACJ,IAAI,CAACrC,OAAO,CAACsC,eAAe,IAC3B,MAAM3D,WACL,IAAI,CAACqB,OAAO,CAACgC,GAAG,GAAGjD,2BAA2BE,yBAC9CmD,KACA;YACEG,cAAc,IAAI,CAACvC,OAAO,CAACwC,IAAI;YAC/BC,QAAQ;QACV;QAGJ,+CAA+C;QAC/C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI;gBACF,MAAMC,mBAAmBnD,QAAQd,KAAKkE,IAAI,CACxCV,KACA,SACApD,wBACCqD,MAAM;gBAET,kCAAkC;gBAClCA,OAAOU,YAAY,CAACC,qBAAqB,GACvCH,iBAAiBE,YAAY,CAACC,qBAAqB;YACvD,EAAE,OAAOC,GAAG;YACV,kDAAkD;YAClD,oDAAoD;YACpD,sBAAsB;YACxB;QACF;QAEA,OAAOZ;IACT;IAEA,MAAcvB,YAAY;QACxB,IAAI,CAAC,IAAI,CAACoC,aAAa,EAAE;YACvB,IAAI,CAACA,aAAa,GAAG,IAAI,CAACtD,mBAAmB,GAAGuD,IAAI,CAAC,OAAOX;gBAC1D,IAAI,IAAI,CAACX,cAAc,EAAE;oBACvBa,QAAQC,GAAG,CAACS,gCAAgC,GAAGC,KAAKC,SAAS,CAACd;gBAChE;gBAEA,IAAI,CAAC,IAAI,CAACxC,OAAO,CAACgC,GAAG,EAAE;oBACrB,IAAIQ,KAAKe,MAAM,KAAK,cAAc;wBAChC,IAAI,CAACb,QAAQC,GAAG,CAACS,gCAAgC,EAAE;4BACjD1E,IAAI8E,IAAI,CACN,CAAC,kHAAkH,CAAC;wBAExH;oBACF,OAAO,IAAIhB,KAAKe,MAAM,KAAK,UAAU;wBACnC,MAAM,IAAIE,MACR,CAAC,mGAAmG,CAAC;oBAEzG;gBACF;gBAEA,IAAI,CAAC5C,MAAM,GAAG,MAAM,IAAI,CAACqB,YAAY,CAAC;oBACpC,GAAG,IAAI,CAAClC,OAAO;oBACfwC;gBACF;gBACA,IAAI,IAAI,CAACrB,mBAAmB,EAAE;oBAC5B,IAAI,CAACN,MAAM,CAACI,cAAc,CAAC,IAAI,CAACE,mBAAmB;gBACrD;gBACA,OAAO,IAAI,CAACN,MAAM;YACpB;QACF;QACA,OAAO,IAAI,CAACqC,aAAa;IAC3B;IAEA,MAAczC,0BAA0B;QACtC,IAAI,IAAI,CAACiD,UAAU,EAAE,OAAO,IAAI,CAACA,UAAU;QAE3C,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;YAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAAC7C,SAAS,GAAGqC,IAAI,CAAC,CAACtC;gBAC9C,IAAI,CAAC6C,UAAU,GAAGxE,YAAY0E,IAAI,CAChCzE,eAAesB,uBAAuB,EACtCI,OAAOV,iBAAiB,GAAG0D,IAAI,CAAChD;gBAElC,OAAO,IAAI,CAAC8C,iBAAiB;gBAC7B,OAAO,IAAI,CAACD,UAAU;YACxB;QACF;QACA,OAAO,IAAI,CAACC,iBAAiB;IAC/B;AACF;AAEA,MAAMG,yBAAyBhE;IAW7B,MAAM6B,UAAU;QACd,MAAM,EAAEoC,kBAAkB,EAAE,GAC1BrE,QAAQ;QAEV,MAAMsE,kBAAkB,CAAC,CAAC3E;QAE1B,MAAM4E,aAAa,MAAMF,mBAAmB;YAC1C3B,KAAK,IAAI,CAACpC,OAAO,CAACoC,GAAG;YACrBlC,MAAM,IAAI,CAACF,OAAO,CAACE,IAAI,IAAI;YAC3BgE,OAAO,CAAC,CAAC,IAAI,CAAClE,OAAO,CAACgC,GAAG;YACzB/B,UAAU,IAAI,CAACD,OAAO,CAACC,QAAQ,IAAI;YACnCkE,aAAa,IAAI,CAACnE,OAAO,CAACmE,WAAW;YACrCH,iBAAiB,CAAC,CAACA;QACrB;QACA,IAAI,CAACxD,cAAc,GAAGyD,UAAU,CAAC,EAAE;QACnC,IAAI,CAACG,cAAc,GAAGH,UAAU,CAAC,EAAE;QACnC,IAAI,CAACI,YAAY,GAAGJ,UAAU,CAAC,EAAE;IACnC;IAEQK,sBACNC,YAAoC,EACpCC,IAAsB,EACtB;QACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;gBAEKD;YADhC,IAAI,CAACC,iBAAiB,GAAG;YACzBF,eAAeA,iBAAiBC,yBAAAA,cAAAA,KAAM7D,MAAM,qBAAb,AAAC6D,YAAsB3D,MAAM;YAE5D,IAAI0D,cAAc;gBAChBA,aAAaG,EAAE,CAAC,WAAW,OAAOtE,KAAKO,QAAQC;oBAC7C,IAAI,CAACwD,cAAc,CAAChE,KAAKO,QAAQC;gBACnC;YACF;QACF;IACF;IAEAT,oBAAoB;QAClB,OAAO,OACLC,KACAC,KACAC;YAEA,IAAI,CAACgE,qBAAqB,CAAC,IAAI,CAACtE,OAAO,CAAC2E,UAAU,EAAEvE;YAEpD,IAAIE,WAAW;gBACbF,IAAIwE,GAAG,GAAGxF,UAAUkB;YACtB;YAEA,OAAO,IAAI,CAACE,cAAc,CAACJ,KAAKC;QAClC;IACF;IAEA,MAAMiB,OAAO,GAAGD,IAAkC,EAAE;QAClD,IAAI,CAACjB,KAAKC,KAAKwE,UAAUC,OAAOxE,UAAU,GAAGe;QAC7C,IAAI,CAACiD,qBAAqB,CAAC,IAAI,CAACtE,OAAO,CAAC2E,UAAU,EAAEvE;QAEpD,IAAI,CAACyE,SAASE,UAAU,CAAC,MAAM;YAC7BC,QAAQC,KAAK,CAAC,CAAC,8BAA8B,EAAEJ,SAAS,CAAC,CAAC;YAC1DA,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC;QAC3B;QACAA,WAAWA,aAAa,WAAW,MAAMA;QAEzCzE,IAAIwE,GAAG,GAAGxF,UAAU;YAClB,GAAGkB,SAAS;YACZuE;YACAC;QACF;QAEA,MAAM,IAAI,CAACtE,cAAc,CAACJ,KAAYC;QACtC;IACF;IAEAY,eAAeC,WAAmB,EAAQ;QACxC,KAAK,CAACD,eAAeC;QACrB,IAAI,CAACmD,YAAY,CAACpD,cAAc,CAACC;IACnC;;;aApFUW,iBAAiB;aACnB4C,oBAA6B;;AAoFvC;AAEA,yDAAyD;AACzD,SAASvC,aAAalC,OAA0B;IAC9C,8CAA8C;IAC9C,IACEA,WACA,gBAAgBA,WAChB,aAAa,AAACA,QAAgBkF,UAAU,EACxC;QACA,OAAOxF,QAAQ,qBAAqByF,cAAc,CAACnF;IACrD;IAEA,IAAIA,WAAW,MAAM;QACnB,MAAM,IAAIyD,MACR;IAEJ;IAEA,IACE,CAAE,CAAA,sBAAsBzD,OAAM,KAC9B0C,QAAQC,GAAG,CAACC,QAAQ,IACpB,CAAC;QAAC;QAAc;QAAe;KAAO,CAACwC,QAAQ,CAAC1C,QAAQC,GAAG,CAACC,QAAQ,GACpE;QACAlE,IAAI8E,IAAI,CAAC1E;IACX;IAEA,IAAIkB,QAAQgC,GAAG,IAAI,OAAOhC,QAAQgC,GAAG,KAAK,WAAW;QACnDgD,QAAQxB,IAAI,CACV;IAEJ;IAEA,qDAAqD;IACrD,IAAIxD,QAAQuE,YAAY,KAAK,OAAO;QAClC,MAAMnC,MAAMvD,QAAQmB,QAAQoC,GAAG,IAAI;QAEnC,OAAO,IAAI0B,iBAAiB;YAC1B,GAAG9D,OAAO;YACVoC;QACF;IACF;IAEA,+EAA+E;IAC/E,OAAO,IAAItC,WAAWE;AACxB;AAEA,qCAAqC;AACrCqF,OAAOC,OAAO,GAAGpD;AACjB,2BAA2B;AAE3B,oCAAoC;AACpC,eAAeA,aAAY"}