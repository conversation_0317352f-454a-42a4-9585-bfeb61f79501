{"version": 3, "sources": ["../../../src/server/base-http/node.ts"], "names": ["SYMBOL_CLEARED_COOKIES", "NEXT_REQUEST_META", "BaseNextRequest", "BaseNextResponse", "NodeNextRequest", "originalRequest", "_req", "url", "cookies", "value", "constructor", "method", "toUpperCase", "headers", "fetchMetrics", "NodeNextResponse", "originalResponse", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end"], "mappings": "AAGA,SAASA,sBAAsB,QAAQ,eAAc;AAGrD,SAASC,iBAAiB,QAAQ,kBAAiB;AAGnD,SAASC,eAAe,EAAEC,gBAAgB,QAA0B,UAAS;IAa1EF,qBAAAA;AAJH,OAAO,MAAMG,wBAAwBF;IAMnC,IAAIG,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACC,IAAI,CAACL,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;QACtD,IAAI,CAACK,IAAI,CAACC,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACF,IAAI;IAClB;IAEA,IAAID,gBAAgBI,KAAU,EAAE;QAC9B,IAAI,CAACH,IAAI,GAAGG;IACd;IAEAC,YAAY,AAAQJ,IAAS,CAAE;YAjBO;QAkBpC,KAAK,CAACA,KAAKK,MAAM,CAAEC,WAAW,IAAIN,KAAKC,GAAG,EAAGD;aAD3BA,OAAAA;aAlBbO,UAAU,IAAI,CAACP,IAAI,CAACO,OAAO;aAC3BC,gBAA+B,aAAA,IAAI,CAACR,IAAI,qBAAT,WAAWQ,YAAY;YAE7D,CAACb,mBAAkB,GAAgB,IAAI,CAACK,IAAI,CAACL,kBAAkB,IAAI,CAAC;IAiBpE;AACF;AAEA,OAAO,MAAMc,yBAAyBZ;IAKpC,IAAIa,mBAAmB;QACrB,IAAIhB,0BAA0B,IAAI,EAAE;YAClC,IAAI,CAACiB,IAAI,CAACjB,uBAAuB,GAAG,IAAI,CAACA,uBAAuB;QAClE;QAEA,OAAO,IAAI,CAACiB,IAAI;IAClB;IAEAP,YACE,AAAQO,IAA6D,CACrE;QACA,KAAK,CAACA;aAFEA,OAAAA;aAbFC,WAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWd,KAAa,EAAE;QAC5B,IAAI,CAACQ,IAAI,CAACM,UAAU,GAAGd;IACzB;IAEA,IAAIe,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAAcf,KAAa,EAAE;QAC/B,IAAI,CAACQ,IAAI,CAACO,aAAa,GAAGf;IAC5B;IAEAgB,UAAUC,IAAY,EAAEjB,KAAwB,EAAQ;QACtD,IAAI,CAACQ,IAAI,CAACQ,SAAS,CAACC,MAAMjB;QAC1B,OAAO,IAAI;IACb;IAEAkB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAO,AAACY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAO,AAAD,EAAGI,GAAG,CAAC,CAACxB,QACtDA,MAAMyB,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAEjB,KAAa,EAAQ;QAC9C,MAAM8B,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAAC/B,QAAQ;YAClC,IAAI,CAACQ,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAe9B;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEAgC,KAAKhC,KAAa,EAAE;QAClB,IAAI,CAACS,QAAQ,GAAGT;QAChB,OAAO,IAAI;IACb;IAEAiC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;AACF"}