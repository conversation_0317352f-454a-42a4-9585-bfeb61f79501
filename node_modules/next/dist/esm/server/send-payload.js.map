{"version": 3, "sources": ["../../src/server/send-payload.ts"], "names": ["isResSent", "generateETag", "fresh", "formatRevalidate", "RSC_CONTENT_TYPE_HEADER", "sendEtagResponse", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "headers", "statusCode", "end", "sendRenderResult", "result", "type", "generateEtags", "poweredByHeader", "revalidate", "swr<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "payload", "isDynamic", "toUnchunkedString", "etagPayload", "split", "sort", "join", "includes", "parse", "require", "root", "scriptTags", "querySelector", "querySelectorAll", "filter", "node", "hasAttribute", "innerHTML", "a", "b", "localeCompare", "for<PERSON>ach", "script", "remove", "append<PERSON><PERSON><PERSON>", "toString", "err", "console", "error", "undefined", "contentType", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": "AAIA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,YAAY,QAAQ,aAAY;AACzC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,gBAAgB,QAAQ,mBAAkB;AACnD,SAASC,uBAAuB,QAAQ,0CAAyC;AAEjF,OAAO,SAASC,iBACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIN,MAAMI,IAAII,OAAO,EAAE;QAAEF;IAAK,IAAI;QAChCD,IAAII,UAAU,GAAG;QACjBJ,IAAIK,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,eAAeC,iBAAiB,EACrCP,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,UAAU,EACVC,QAAQ,EAUT;IACC,IAAInB,UAAUO,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAI,OAAOS,eAAe,eAAe,CAACX,IAAIa,SAAS,CAAC,kBAAkB;QACxEb,IAAIE,SAAS,CACX,iBACAN,iBAAiB;YACfe;YACAC;QACF;IAEJ;IAEA,MAAME,UAAUP,OAAOQ,SAAS,GAAG,OAAOR,OAAOS,iBAAiB;IAElE,IAAIF,YAAY,MAAM;QACpB,IAAIG,cAAcH;QAClB,IAAIN,SAAS,OAAO;YAClB,6CAA6C;YAC7C,iDAAiD;YACjD,kBAAkB;YAClBS,cAAcH,QAAQI,KAAK,CAAC,MAAMC,IAAI,GAAGC,IAAI,CAAC;QAChD,OAAO,IAAIZ,SAAS,UAAUM,QAAQO,QAAQ,CAAC,aAAa;YAC1D,MAAM,EAAEC,KAAK,EAAE,GACbC,QAAQ;YAEV,IAAI;oBAKeC;gBAJjB,iBAAiB;gBACjB,IAAIA,OAAOF,MAAMR;gBAEjB,sCAAsC;gBACtC,IAAIW,cAAaD,sBAAAA,KACdE,aAAa,CAAC,4BADAF,oBAEbG,gBAAgB,CAAC,UAClBC,MAAM,CACL,CAACC;wBAC8BA;2BAA7B,CAACA,KAAKC,YAAY,CAAC,YAAUD,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBR,QAAQ,CAAC;;gBAG5D,2CAA2C;gBAC3CI,8BAAAA,WAAYN,IAAI,CAAC,CAACa,GAAGC,IAAMD,EAAED,SAAS,CAACG,aAAa,CAACD,EAAEF,SAAS;gBAEhE,kCAAkC;gBAClCN,8BAAAA,WAAYU,OAAO,CAAC,CAACC,SAAgBA,OAAOC,MAAM;gBAElD,4CAA4C;gBAC5CZ,8BAAAA,WAAYU,OAAO,CAAC,CAACC;wBACnBZ;4BAAAA,sBAAAA,KAAKE,aAAa,CAAC,4BAAnBF,oBAA4Bc,WAAW,CAACF;;gBAG1C,yBAAyB;gBACzBnB,cAAcO,KAAKe,QAAQ;YAC7B,EAAE,OAAOC,KAAK;gBACZC,QAAQC,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAEF;YAC9C;QACF;QAEA,MAAMvC,OAAOQ,gBAAgBf,aAAauB,eAAe0B;QACzD,IAAI7C,iBAAiBC,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIa,SAAS,CAAC,iBAAiB;QAClCb,IAAIE,SAAS,CACX,gBACAK,OAAOqC,WAAW,GACdrC,OAAOqC,WAAW,GAClBpC,SAAS,QACTX,0BACAW,SAAS,SACT,qBACA;IAER;IAEA,IAAIM,SAAS;QACXd,IAAIE,SAAS,CAAC,kBAAkB2C,OAAOC,UAAU,CAAChC;IACpD;IAEA,IAAIf,IAAIgD,MAAM,KAAK,QAAQ;QACzB/C,IAAIK,GAAG,CAAC;QACR;IACF;IAEA,IAAIS,YAAY,MAAM;QACpBd,IAAIK,GAAG,CAACS;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMP,OAAOyC,kBAAkB,CAAChD;AAClC"}