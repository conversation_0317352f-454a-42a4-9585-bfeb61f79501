{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts"], "names": ["invalidateCacheByRouterState", "fillLazyItemsTillLeafWithHead", "createRouterCache<PERSON>ey", "fillCacheWithNewSubTreeData", "newCache", "existingCache", "flightDataPath", "prefetchEntry", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "lazyData", "seedData", "rsc", "loading", "prefetchRsc", "head", "prefetchHead", "lazyDataResolved", "slice"], "mappings": "AAKA,SAASA,4BAA4B,QAAQ,qCAAoC;AACjF,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,oBAAoB,QAAQ,4BAA2B;AAGhE;;CAEC,GACD,OAAO,SAASC,4BACdC,QAAmB,EACnBC,aAAwB,EACxBC,cAA8B,EAC9BC,aAAkC;IAElC,MAAMC,cAAcF,eAAeG,MAAM,IAAI;IAC7C,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IAEpC,MAAMM,WAAWV,qBAAqBS;IAEtC,MAAME,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAI,CAACG,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACL;IAClD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,MAAMG,yBAAyBN,wBAAwBE,GAAG,CAACH;IAC3D,IAAIQ,iBAAiBJ,gBAAgBD,GAAG,CAACH;IAEzC,IAAIJ,aAAa;QACf,IACE,CAACY,kBACD,CAACA,eAAeC,QAAQ,IACxBD,mBAAmBD,wBACnB;YACA,MAAMG,WAA8BhB,cAAc,CAAC,EAAE;YACrD,MAAMiB,MAAMD,QAAQ,CAAC,EAAE;YACvB,MAAME,UAAUF,QAAQ,CAAC,EAAE;YAC3BF,iBAAiB;gBACfC,UAAU;gBACVE;gBACAE,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdH;gBACA,oEAAoE;gBACpEV,gBAAgBK,yBACZ,IAAIF,IAAIE,uBAAuBL,cAAc,IAC7C,IAAIG;gBACRW,kBAAkB;YACpB;YAEA,IAAIT,wBAAwB;gBAC1BnB,6BACEoB,gBACAD,wBACAb,cAAc,CAAC,EAAE;YAErB;YAEAL,8BACEmB,gBACAD,wBACAb,cAAc,CAAC,EAAE,EACjBgB,UACAhB,cAAc,CAAC,EAAE,EACjBC;YAGFS,gBAAgBE,GAAG,CAACN,UAAUQ;QAChC;QACA;IACF;IAEA,IAAI,CAACA,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCE,KAAKH,eAAeG,GAAG;YACvBE,aAAaL,eAAeK,WAAW;YACvCC,MAAMN,eAAeM,IAAI;YACzBC,cAAcP,eAAeO,YAAY;YACzCb,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;YACrDc,kBAAkB;YAClBJ,SAASJ,eAAeI,OAAO;QACjC;QACAR,gBAAgBE,GAAG,CAACN,UAAUQ;IAChC;IAEAjB,4BACEiB,gBACAD,wBACAb,eAAeuB,KAAK,CAAC,IACrBtB;AAEJ"}