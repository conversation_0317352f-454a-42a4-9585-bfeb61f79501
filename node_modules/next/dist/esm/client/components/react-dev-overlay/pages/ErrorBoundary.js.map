{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/ErrorBoundary.tsx"], "names": ["React", "Error<PERSON>ou<PERSON><PERSON>", "PureComponent", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "props", "onError", "componentStack", "globalOverlay", "setState", "render", "state", "isMounted", "html", "head", "body", "children"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAU9B,OAAO,MAAMC,sBAAsBD,MAAME,aAAa;IAMpD,OAAOC,yBAAyBC,KAAY,EAAE;QAC5C,OAAO;YAAEA;QAAM;IACjB;IAEAC,kBACED,KAAY,EACZ,gEAAgE;IAChE,0CAA0C;IAC1CE,SAA8C,EAC9C;QACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAOE,CAAAA,6BAAAA,UAAWG,cAAc,KAAI;QACvD,IAAI,CAAC,IAAI,CAACF,KAAK,CAACG,aAAa,EAAE;YAC7B,IAAI,CAACC,QAAQ,CAAC;gBAAEP;YAAM;QACxB;IACF;IAEA,0IAA0I;IAC1IQ,SAA0B;QACxB,uEAAuE;QACvE,OAAO,IAAI,CAACC,KAAK,CAACT,KAAK,IACpB,IAAI,CAACG,KAAK,CAACG,aAAa,IAAI,IAAI,CAACH,KAAK,CAACO,SAAS,GACjD,6FAA6F;QAC7F,wFAAwF;QACxF,IAAI,CAACP,KAAK,CAACG,aAAa,iBACtB,MAACK;;8BACC,KAACC;8BACD,KAACC;;aAED,OAEJ,IAAI,CAACV,KAAK,CAACW,QAAQ;IAEvB;;;aAlCAL,QAAQ;YAAET,OAAO;QAAK;;AAmCxB"}