{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/parse-component-stack.ts"], "names": ["LocationType", "getLocationType", "location", "startsWith", "parseStackFrameLocation", "locationType", "modulePath", "replace", "file", "lineNumber", "column", "match", "canOpenInEditor", "Number", "undefined", "parseComponentStack", "componentStack", "componentStackFrames", "line", "trim", "split", "exec", "component", "push", "includes", "frameLocation"], "mappings": ";UAQKA;;;;;;GAAAA,iBAAAA;AAQL;;CAEC,GACD,SAASC,gBAAgBC,QAAgB;IACvC,IAAIA,SAASC,UAAU,CAAC,YAAY;QAClC;IACF;IACA,IAAID,SAASC,UAAU,CAAC,wBAAwB;QAC9C;IACF;IACA,IAAID,SAASC,UAAU,CAAC,cAAcD,SAASC,UAAU,CAAC,aAAa;QACrE;IACF;IACA,IAAID,SAASC,UAAU,CAAC,OAAO;QAC7B;IACF;IACA;AACF;AAEA,SAASC,wBACPF,QAAgB;IAEhB,MAAMG,eAAeJ,gBAAgBC;IAErC,MAAMI,aAAaJ,4BAAAA,SAAUK,OAAO,CAClC,mDACA;QAGAD;IADF,MAAM,GAAGE,MAAMC,YAAYC,OAAO,GAChCJ,CAAAA,oBAAAA,8BAAAA,WAAYK,KAAK,CAAC,gCAAlBL,oBAA0C,EAAE;IAE9C,OAAQD;QACN;QACA;YACE,OAAO;gBACLO,iBAAiB;gBACjBJ;gBACAC,YAAYA,aAAaI,OAAOJ,cAAcK;gBAC9CJ,QAAQA,SAASG,OAAOH,UAAUI;YACpC;QACF,mDAAmD;QACnD,gDAAgD;QAChD;QACA;QACA;QACA;YAAS;gBACP,OAAO;oBACLF,iBAAiB;gBACnB;YACF;IACF;AACF;AAEA,OAAO,SAASG,oBACdC,cAAsB;IAEtB,MAAMC,uBAA8C,EAAE;IACtD,KAAK,MAAMC,QAAQF,eAAeG,IAAI,GAAGC,KAAK,CAAC,MAAO;QACpD,uDAAuD;QACvD,MAAMT,QAAQ,yBAAyBU,IAAI,CAACH;QAC5C,IAAIP,yBAAAA,KAAO,CAAC,EAAE,EAAE;YACd,MAAMW,YAAYX,KAAK,CAAC,EAAE;YAC1B,MAAMT,WAAWS,KAAK,CAAC,EAAE;YAEzB,IAAI,CAACT,UAAU;gBACbe,qBAAqBM,IAAI,CAAC;oBACxBX,iBAAiB;oBACjBU;gBACF;gBACA;YACF;YAEA,mEAAmE;YACnE,IAAIpB,4BAAAA,SAAUsB,QAAQ,CAAC,cAAc;gBACnC;YACF;YAEA,MAAMC,gBAAgBrB,wBAAwBF;YAC9Ce,qBAAqBM,IAAI,CAAC;gBACxBD;gBACA,GAAGG,aAAa;YAClB;QACF;IACF;IAEA,OAAOR;AACT"}