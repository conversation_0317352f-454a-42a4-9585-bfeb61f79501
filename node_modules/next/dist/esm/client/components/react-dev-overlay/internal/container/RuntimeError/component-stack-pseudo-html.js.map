{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.tsx"], "names": ["useMemo", "Fragment", "useState", "CollapseIcon", "getAdjacentProps", "isAdj", "PseudoHtmlDiff", "componentStackFrames", "firstContent", "second<PERSON><PERSON>nt", "hydrationMismatchType", "props", "isHtmlTagsWarning", "MAX_NON_COLLAPSED_FRAMES", "shouldCollapse", "length", "isHtmlCollapsed", "toggleCollapseHtml", "htmlComponents", "tagNames", "replace", "nestedHtmlStack", "lastText", "componentStack", "map", "frame", "component", "reverse", "matchedIndex", "i", "for<PERSON>ach", "index", "componentList", "spaces", "repeat", "isHighlightedTag", "includes", "isAdjacentTag", "Math", "abs", "isLastFewFrames", "adjProps", "codeLine", "span", "undefined", "wrappedCodeLine", "data-nextjs-container-errors-pseudo-html--hint", "push", "key", "data-nextjs-container-errors-pseudo-html--diff-remove", "data-nextjs-container-errors-pseudo-html--diff-add", "data-nextjs-container-errors-pseudo-html--tag-adjacent", "div", "data-nextjs-container-errors-pseudo-html", "button", "tabIndex", "data-nextjs-container-errors-pseudo-html-collapse", "onClick", "collapsed", "pre", "code"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,QAAO;AAEnD,SAASC,YAAY,QAAQ,2BAA0B;AAEvD,SAASC,iBAAiBC,KAAc;IACtC,OAAO;QAAE,0DAA0DA;IAAM;AAC3E;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GACD,OAAO,SAASC,eAAe,KAWS;IAXT,IAAA,EAC7BC,oBAAoB,EACpBC,YAAY,EACZC,aAAa,EACbC,qBAAqB,EACrB,GAAGC,OAMmC,GAXT;IAY7B,MAAMC,oBAAoBF,0BAA0B;IACpD,+FAA+F;IAC/F,MAAMG,2BAA2BD,oBAAoB,IAAI;IACzD,MAAME,iBAAiBP,qBAAqBQ,MAAM,GAAGF;IACrD,MAAM,CAACG,iBAAiBC,mBAAmB,GAAGf,SAASY;IAEvD,MAAMI,iBAAiBlB,QAAQ;QAC7B,MAAMmB,WAAWP,oBAEb;YAACJ,aAAaY,OAAO,CAAC,QAAQ;YAAKX,cAAcW,OAAO,CAAC,QAAQ;SAAI,GACrE,EAAE;QACN,MAAMC,kBAAqC,EAAE;QAC7C,IAAIC,WAAW;QAEf,MAAMC,iBAAiBhB,qBACpBiB,GAAG,CAAC,CAACC,QAAUA,MAAMC,SAAS,EAC9BC,OAAO;QAEV,8BAA8B;QAC9B,MAAMC,eAAe;YAAC,CAAC;YAAG,CAAC;SAAE;QAC7B,IAAIhB,mBAAmB;YACrB,mCAAmC;YACnC,IAAK,IAAIiB,IAAIN,eAAeR,MAAM,GAAG,GAAGc,KAAK,GAAGA,IAAK;gBACnD,IAAIN,cAAc,CAACM,EAAE,KAAKV,QAAQ,CAAC,EAAE,EAAE;oBACrCS,YAAY,CAAC,EAAE,GAAGC;oBAClB;gBACF;YACF;YACA,kDAAkD;YAClD,IAAK,IAAIA,IAAID,YAAY,CAAC,EAAE,GAAG,GAAGC,KAAK,GAAGA,IAAK;gBAC7C,IAAIN,cAAc,CAACM,EAAE,KAAKV,QAAQ,CAAC,EAAE,EAAE;oBACrCS,YAAY,CAAC,EAAE,GAAGC;oBAClB;gBACF;YACF;QACF;QAEAN,eAAeO,OAAO,CAAC,CAACJ,WAAWK,OAAOC;YACxC,MAAMC,SAAS,IAAIC,MAAM,CAACb,gBAAgBN,MAAM,GAAG;YACnD,iDAAiD;YACjD,iDAAiD;YACjD,gEAAgE;YAEhE,MAAMoB,mBAAmBvB,oBACrBmB,UAAUH,YAAY,CAAC,EAAE,IAAIG,UAAUH,YAAY,CAAC,EAAE,GACtDT,SAASiB,QAAQ,CAACV;YACtB,MAAMW,gBACJF,oBACAG,KAAKC,GAAG,CAACR,QAAQH,YAAY,CAAC,EAAE,KAAK,KACrCU,KAAKC,GAAG,CAACR,QAAQH,YAAY,CAAC,EAAE,KAAK;YAEvC,MAAMY,kBACJ,CAAC5B,qBAAqBmB,SAASC,cAAcjB,MAAM,GAAG;YAExD,MAAM0B,WAAWrC,iBAAiBiC;YAElC,IAAI,AAACzB,qBAAqByB,iBAAkBG,iBAAiB;gBAC3D,MAAME,yBACJ,MAACC;;wBACEV;sCACD,KAACU;4BACE,GAAGF,QAAQ;4BAEV,GAAIN,mBACA;gCACE,uDACE;4BACJ,IACAS,SAAS;sCAGd,AAAC,MAAGlB,YAAU;;;;gBAIrBJ,WAAWI;gBAEX,MAAMmB,gCACJ,MAAC5C;;wBACEyC;wBAEAP,kCACC,KAACQ;4BAAKG,gDAA8C;sCACjDb,SAAS,IAAIC,MAAM,CAACR,UAAUX,MAAM,GAAG,KAAK;;;mBALpCM,gBAAgBN,MAAM;gBAUvCM,gBAAgB0B,IAAI,CAACF;YACvB,OAAO;gBACL,IACExB,gBAAgBN,MAAM,IAAIF,4BAC1BG,iBACA;oBACA;gBACF;gBAEA,IAAI,CAACA,mBAAmBwB,iBAAiB;oBACvCnB,gBAAgB0B,IAAI,eAClB,eAACJ;wBAAM,GAAGF,QAAQ;wBAAEO,KAAK3B,gBAAgBN,MAAM;;4BAC5CkB;4BACA,MAAMP,YAAY;;;gBAGzB,OAAO,IAAIV,mBAAmBM,aAAa,OAAO;oBAChDA,WAAW;oBACXD,gBAAgB0B,IAAI,eAClB,eAACJ;wBAAM,GAAGF,QAAQ;wBAAEO,KAAK3B,gBAAgBN,MAAM;;4BAC5CkB;4BACA;;;gBAGP;YACF;QACF;QAEA,uCAAuC;QACvC,IAAI,CAACrB,mBAAmB;YACtB,MAAMqB,SAAS,IAAIC,MAAM,CAACb,gBAAgBN,MAAM,GAAG;YACnD,IAAI8B;YACJ,IAAInC,0BAA0B,QAAQ;gBACpC,uEAAuE;gBACvEmC,gCACE,MAAC5C;;sCACC,KAAC0C;4BAAKM,uDAAqD;sCACxDhB,SAAS,CAAA,AAAC,MAAGzB,eAAa,KAAG;;sCAEhC,KAACmC;4BAAKO,oDAAkD;sCACrDjB,SAAS,CAAA,AAAC,MAAGxB,gBAAc,KAAG;;;mBALpBY,gBAAgBN,MAAM;YASzC,OAAO;gBACL,4EAA4E;gBAC5E8B,gCACE,MAAC5C;;sCACC,KAAC0C;4BAAKQ,wDAAsD;sCACzDlB,SAAS,CAAA,AAAC,MAAGxB,gBAAc,KAAG;;sCAEjC,KAACkC;4BAAKM,uDAAqD;sCACxDhB,SAAS,CAAA,AAAC,QAAKzB,eAAa,KAAG;;;mBALrBa,gBAAgBN,MAAM;YASzC;YACAM,gBAAgB0B,IAAI,CAACF;QACvB;QAEA,OAAOxB;IACT,GAAG;QACDd;QACAS;QACAR;QACAC;QACAG;QACAF;QACAG;KACD;IAED,qBACE,MAACuC;QAAIC,0CAAwC;;0BAC3C,KAACC;gBACCC,UAAU;gBACVC,mDAAiD;gBACjDC,SAAS,IAAMxC,mBAAmB,CAACD;0BAEnC,cAAA,KAACb;oBAAauD,WAAW1C;;;0BAE3B,KAAC2C;gBAAK,GAAGhD,KAAK;0BACZ,cAAA,KAACiD;8BAAM1C;;;;;AAIf"}