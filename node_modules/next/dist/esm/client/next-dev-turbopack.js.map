{"version": 3, "sources": ["../../src/client/next-dev-turbopack.ts"], "names": ["initialize", "version", "router", "emitter", "initHMR", "pageBootrap", "connect", "window", "next", "self", "__next_set_public_path__", "__webpack_hash__", "devClient", "then", "assetPrefix", "__turbopack_load_page_chunks__", "page", "chunksData", "chunkPromises", "map", "__turbopack_load__", "Promise", "all", "catch", "err", "console", "error", "addMessageListener", "cb", "addTurbopackMessageListener", "sendMessage", "sendTurbopackMessage", "onUpdateError", "handleUpdateError"], "mappings": "AAAA,kCAAkC;AAClC,SAASA,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,QAAQ,KAAI;AACzD,OAAOC,aAAa,8BAA6B;AAEjD,OAAO,4BAA2B;AAClC,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,+FAA+F;AAC/F,SAASC,OAAO,QAAQ,gEAA+D;AAGvFC,OAAOC,IAAI,GAAG;IACZP,SAAS,AAAC,KAAEA,UAAQ;IACpB,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA;IACT;IACAC;AACF;AACEM,KAAaC,wBAAwB,GAAG,KAAO;AAC/CD,KAAaE,gBAAgB,GAAG;AAKlC,MAAMC,YAAYR,QAAQ;AAC1BJ,WAAW;IACTY;AACF,GACGC,IAAI,CAAC;QAAC,EAAEC,WAAW,EAAE;IAElBL,KAAaM,8BAA8B,GAAG,CAC9CC,MACAC;QAEA,MAAMC,gBAAgBD,WAAWE,GAAG,CAACC;QAErCC,QAAQC,GAAG,CAACJ,eAAeK,KAAK,CAAC,CAACC,MAChCC,QAAQC,KAAK,CAAC,oCAAoCV,MAAMQ;IAE5D;IAEAlB,QAAQ;QACNqB,oBAAmBC,EAAwC;YACzDhB,UAAUiB,2BAA2B,CAACD;QACxC;QACAE,aAAalB,UAAUmB,oBAAoB;QAC3CC,eAAepB,UAAUqB,iBAAiB;IAC5C;IAEA,OAAO5B,YAAYS;AACrB,GACCS,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC"}