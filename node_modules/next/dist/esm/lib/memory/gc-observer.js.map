{"version": 3, "sources": ["../../../src/lib/memory/gc-observer.ts"], "names": ["PerformanceObserver", "warn", "bold", "LONG_RUNNING_GC_THRESHOLD_MS", "gcEvents", "obs", "list", "entry", "getEntries", "push", "duration", "toFixed", "startObservingGc", "observe", "entryTypes", "stopObservingGc", "disconnect", "getGcEvents"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,aAAY;AAChD,SAASC,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,gBAAe;AAEpC,MAAMC,+BAA+B;AAErC,MAAMC,WAA+B,EAAE;AACvC,MAAMC,MAAM,IAAIL,oBAAoB,CAACM;IACnC,MAAMC,QAAQD,KAAKE,UAAU,EAAE,CAAC,EAAE;IAClCJ,SAASK,IAAI,CAACF;IAEd,IAAIA,MAAMG,QAAQ,GAAGP,8BAA8B;QACjDF,KAAKC,KAAK,CAAC,0BAA0B,EAAEK,MAAMG,QAAQ,CAACC,OAAO,CAAC,GAAG,EAAE,CAAC;IACtE;AACF;AAEA;;;CAGC,GACD,OAAO,SAASC;IACdP,IAAIQ,OAAO,CAAC;QAAEC,YAAY;YAAC;SAAK;IAAC;AACnC;AAEA,OAAO,SAASC;IACdV,IAAIW,UAAU;AAChB;AAEA;;;;CAIC,GACD,OAAO,SAASC;IACd,OAAOb;AACT"}