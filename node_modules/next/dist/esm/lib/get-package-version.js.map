{"version": 3, "sources": ["../../src/lib/get-package-version.ts"], "names": ["promises", "fs", "findUp", "JSON5", "path", "cachedDeps", "getDependencies", "cwd", "configurationPath", "dependencies", "devDependencies", "content", "readFile", "packageJson", "parse", "getPackageVersion", "name", "cwd2", "endsWith", "posix", "sep", "win32", "targetPath", "require", "resolve", "paths", "targetContent", "version"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,YAAY,6BAA4B;AAC/C,OAAOC,WAAW,2BAA0B;AAC5C,YAAYC,UAAU,OAAM;AAO5B,IAAIC;AAEJ,OAAO,SAASC,gBAAgB,EAC9BC,GAAG,EAGJ;IACC,IAAIF,YAAY;QACd,OAAOA;IACT;IAEA,OAAQA,aAAa,AAAC,CAAA;QACpB,MAAMG,oBAAwC,MAAMN,OAAO,gBAAgB;YACzEK;QACF;QACA,IAAI,CAACC,mBAAmB;YACtB,OAAO;gBAAEC,cAAc,CAAC;gBAAGC,iBAAiB,CAAC;YAAE;QACjD;QAEA,MAAMC,UAAU,MAAMV,GAAGW,QAAQ,CAACJ,mBAAmB;QACrD,MAAMK,cAAmBV,MAAMW,KAAK,CAACH;QAErC,MAAM,EAAEF,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGG,eAAe,CAAC;QACpE,OAAO;YAAEJ;YAAcC;QAAgB;IACzC,CAAA;AACF;AAEA,OAAO,eAAeK,kBAAkB,EACtCR,GAAG,EACHS,IAAI,EAIL;IACC,MAAM,EAAEP,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMJ,gBAAgB;QAAEC;IAAI;IACtE,IAAI,CAAEE,CAAAA,YAAY,CAACO,KAAK,IAAIN,eAAe,CAACM,KAAK,AAAD,GAAI;QAClD,OAAO;IACT;IAEA,MAAMC,OACJV,IAAIW,QAAQ,CAACd,KAAKe,KAAK,CAACC,GAAG,KAAKb,IAAIW,QAAQ,CAACd,KAAKiB,KAAK,CAACD,GAAG,IACvDb,MACA,CAAC,EAAEA,IAAI,CAAC,CAAC;IAEf,IAAI;QACF,MAAMe,aAAaC,QAAQC,OAAO,CAAC,CAAC,EAAER,KAAK,aAAa,CAAC,EAAE;YACzDS,OAAO;gBAACR;aAAK;QACf;QACA,MAAMS,gBAAgB,MAAMzB,GAAGW,QAAQ,CAACU,YAAY;QACpD,OAAOnB,MAAMW,KAAK,CAACY,eAAeC,OAAO,IAAI;IAC/C,EAAE,OAAM;QACN,OAAO;IACT;AACF"}