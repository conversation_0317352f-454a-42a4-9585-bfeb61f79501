{"version": 3, "sources": ["../../src/lib/build-custom-route.ts"], "names": ["pathToRegexp", "normalizeRouteRegex", "getRedirectStatus", "modifyRouteRegex", "buildCustomRoute", "type", "route", "restrictedRedirectPaths", "compiled", "source", "strict", "sensitive", "delimiter", "internal", "undefined", "regex", "statusCode", "permanent"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oCAAmC;AAMhE,SACEC,mBAAmB,QAKd,uBAAsB;AAC7B,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,oBAAmB;AAevE,OAAO,SAASC,iBACdC,IAAe,EACfC,KAAkC,EAClCC,uBAAkC;IAElC,MAAMC,WAAWR,aAAaM,MAAMG,MAAM,EAAE,EAAE,EAAE;QAC9CC,QAAQ;QACRC,WAAW;QACXC,WAAW;IACb;IAEA,IAAIH,SAASD,SAASC,MAAM;IAC5B,IAAI,CAACH,MAAMO,QAAQ,EAAE;QACnBJ,SAASN,iBACPM,QACAJ,SAAS,aAAaE,0BAA0BO;IAEpD;IAEA,MAAMC,QAAQd,oBAAoBQ;IAElC,IAAIJ,SAAS,YAAY;QACvB,OAAO;YAAE,GAAGC,KAAK;YAAES;QAAM;IAC3B;IAEA,OAAO;QACL,GAAGT,KAAK;QACRU,YAAYd,kBAAkBI;QAC9BW,WAAWH;QACXC;IACF;AACF"}