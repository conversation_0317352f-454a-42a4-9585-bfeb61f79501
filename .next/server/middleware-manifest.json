{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|.*\\..*).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "XMcPL_UDyczcuAFYavvn8", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "B6cELIHUX4gjURl30yZTuqQdMR1QxwD6T+kLEGEkGy4=", "__NEXT_PREVIEW_MODE_ID": "203de461dcecdab56ba5789908d132b3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "805cebe49d8ef4120ea80954be39f5d580e9ffeeb27aef69c618f3aca5035671", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8e34edc9c6f67c77bc0d6d65f159d1ccaa2754bfccb2186f5825d9c03a075acb"}}}, "functions": {}, "sortedMiddleware": ["/"]}