exports.id=678,exports.ids=[678],exports.modules={5162:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},3907:()=>{},5362:(e,t,s)=>{"use strict";s.d(t,{Z:()=>p});var r=s(326),a=s(6162),l=s(7577);let i=e=>{let t={H:1.008,C:12.011,N:14.007,O:15.999,F:18.998,P:30.974,S:32.065,Cl:35.453,Br:79.904,I:126.904,Na:22.99,Mg:24.305,K:39.098,Ca:40.078,Fe:55.845},s=0;return e.atoms.forEach(e=>{s+=t[e.element]||0}),parseFloat(s.toFixed(2))},n=e=>{let t={};e.atoms.forEach(e=>{t[e.element]=(t[e.element]||0)+1});let s=t.C||0,r=t.O||0;return parseFloat((.5*s-.3*(r+(t.N||0))+.1*((t.F||0)+(t.Cl||0)+(t.Br||0)+(t.I||0))).toFixed(2))},o=e=>Math.min(5,Math.floor((e.atoms.filter(e=>"O"===e.element).length+e.atoms.filter(e=>"N"===e.element).length)*.7)),d=e=>e.atoms.filter(e=>"O"===e.element).length+e.atoms.filter(e=>"N"===e.element).length,c=e=>parseFloat((20*e.atoms.filter(e=>"O"===e.element).length+15*e.atoms.filter(e=>"N"===e.element).length).toFixed(1)),m=e=>{let t=0;return e.bonds.forEach(s=>{if("single"===s.type){let r=e.atoms[s.from],a=e.atoms[s.to];r&&a&&"H"!==r.element&&"H"!==a.element&&t++}}),t},x=e=>{let t=i(e),s=n(e),r=o(e),a=d(e),l=[t>500,s>5,r>5,a>10].filter(Boolean).length;return{molecularWeight:t,logP:s,hDonors:r,hAcceptors:a,tpsa:c(e),rotatableBonds:m(e),lipinskiViolations:l,drugLikeness:Math.max(0,Math.min(1,1-.25*l)),isPotentialDrug:l<=1}},h=e=>{let t=e.atoms.reduce((e,t)=>e+(({H:1,C:6,N:7,O:8,F:9,P:15,S:16,Cl:17,Br:35,I:53,Na:11,Mg:12,K:19,Ca:20,Fe:26})[t.element]||0),0),s=20*e.atoms.length,r=i(e);return{totalElectrons:t,volume:s,molarMass:r,density:parseFloat((r/s).toFixed(3))}},p=({molecule:e})=>{let t=(0,a.T_)("analysis"),[s,i]=(0,l.useState)(!1),[n,o]=(0,l.useState)(null);return((0,l.useEffect)(()=>{if(!e){o(null);return}i(!0),setTimeout(()=>{try{let t=x(e),s=h(e);o({...t,...s})}catch(e){console.error("Błąd podczas analizy:",e),o(null)}finally{i(!1)}},500)},[e]),e)?(0,r.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:t("title")}),s?r.jsx("div",{className:"flex justify-center py-8",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"})}):n?(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-md font-medium mb-2",children:t("lipinski.title")}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.mw")}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("p",{className:"font-medium",children:[n.molecularWeight," Da"]}),r.jsx("span",{className:`ml-2 w-3 h-3 rounded-full ${n.molecularWeight<=500?"bg-green-500":"bg-red-500"}`})]}),r.jsx("p",{className:"text-xs text-gray-500",children:t("lipinski.should_be",{value:500})})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.logp")}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("p",{className:"font-medium",children:n.logP}),r.jsx("span",{className:`ml-2 w-3 h-3 rounded-full ${n.logP<=5?"bg-green-500":"bg-red-500"}`})]}),r.jsx("p",{className:"text-xs text-gray-500",children:t("lipinski.should_be",{value:5})})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.hbd")}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("p",{className:"font-medium",children:n.hDonors}),r.jsx("span",{className:`ml-2 w-3 h-3 rounded-full ${n.hDonors<=5?"bg-green-500":"bg-red-500"}`})]}),r.jsx("p",{className:"text-xs text-gray-500",children:t("lipinski.should_be",{value:5})})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.hba")}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("p",{className:"font-medium",children:n.hAcceptors}),r.jsx("span",{className:`ml-2 w-3 h-3 rounded-full ${n.hAcceptors<=10?"bg-green-500":"bg-red-500"}`})]}),r.jsx("p",{className:"text-xs text-gray-500",children:t("lipinski.should_be",{value:10})})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.tpsa")}),(0,r.jsxs)("p",{className:"font-medium",children:[n.tpsa," \xc5\xb2"]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.violations")}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("p",{className:"font-medium",children:n.lipinskiViolations}),r.jsx("span",{className:`ml-2 w-3 h-3 rounded-full ${0===n.lipinskiViolations?"bg-green-500":1===n.lipinskiViolations?"bg-yellow-500":"bg-red-500"}`})]}),r.jsx("p",{className:"text-xs text-gray-500",children:t("lipinski.should_be",{value:1})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("lipinski.druglikeness")}),r.jsx("div",{className:"w-full bg-dark-600 rounded-full h-2.5 mt-2 mb-1",children:r.jsx("div",{className:`h-2.5 rounded-full ${n.drugLikeness>.7?"bg-green-500":n.drugLikeness>.4?"bg-yellow-500":"bg-red-500"}`,style:{width:`${100*n.drugLikeness}%`}})}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[r.jsx("span",{children:"0"}),r.jsx("span",{children:"1"})]})]}),r.jsx("h3",{className:"text-md font-medium mb-2",children:t("additional.title")}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("additional.electrons")}),r.jsx("p",{className:"font-medium",children:n.totalElectrons})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("additional.volume")}),(0,r.jsxs)("p",{className:"font-medium",children:[n.volume," \xc5\xb3"]})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("additional.density")}),(0,r.jsxs)("p",{className:"font-medium",children:[n.density," g/cm\xb3"]})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-400",children:t("additional.rotatable")}),r.jsx("p",{className:"font-medium",children:n.rotatableBonds})]})]})]}):r.jsx("p",{className:"text-gray-400 text-center",children:t("error")})]}):r.jsx("div",{className:"bg-dark-700 rounded-lg p-4",children:r.jsx("p",{className:"text-gray-400 text-center",children:t("empty")})})}},8074:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(326),a=s(6162),l=s(7577),i=s(2122);let n=({molecule:e,isPro:t=!1})=>{let s=(0,a.T_)("export"),[n,o]=(0,l.useState)(!1),[d,c]=(0,l.useState)(t?"4k":"720p");(0,l.useRef)(null);let m=async()=>{if(e){o(!0);try{let t=document.querySelector("canvas");if(!t)throw Error("Nie znaleziono elementu canvas");let r=await (0,i.f)(t,"4k"===d?"4k":"hd"),a=document.createElement("a");a.href=r,a.download=`${e.name.toLowerCase().replace(/\s+/g,"-")}.png`,document.body.appendChild(a),a.click(),document.body.removeChild(a),alert(s("success",{name:e.name,resolution:d}))}catch(e){console.error("Błąd podczas eksportu:",e),alert(s("error"))}finally{o(!1)}}};return(0,r.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:s("title")}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-sm text-gray-400 mb-2",children:s("resolution")}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx("button",{className:`px-3 py-1.5 rounded-md text-sm ${"720p"===d?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>c("720p"),children:"720p"}),(0,r.jsxs)("button",{className:`px-3 py-1.5 rounded-md text-sm ${"4k"===d?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>c("4k"),disabled:!t,children:["4K ",!t&&r.jsx("span",{className:"text-xs ml-1",children:"(Pro)"})]})]})]}),r.jsx("button",{className:`w-full py-2 px-4 rounded-md text-white font-medium ${e&&!n?"bg-secondary-500 hover:bg-secondary-600":"bg-gray-600 cursor-not-allowed"}`,onClick:m,disabled:!e||n,children:n?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s("loading")]}):s("button")}),!t&&r.jsx("div",{className:"mt-4 bg-dark-800 rounded-md p-3 text-sm",children:r.jsx("p",{className:"text-gray-300",children:r.jsx("span",{className:"text-secondary-400 font-medium",children:s("upgrade")})})})]})}},8139:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(326),a=s(6162),l=s(7577);let i=({molecules:e,onSelect:t,selectedMoleculeId:s})=>{let i=(0,a.T_)("molecules.selector"),[n,o]=(0,l.useState)(""),[d,c]=(0,l.useState)("all"),[m,x]=(0,l.useState)(e),h={all:()=>!0,pharmaceutical:e=>["aspirin","ibuprofen","paracetamol","penicillin_g","omeprazole","atorvastatin","metformin","warfarin","sildenafil"].includes(e.id),neurotransmitters:e=>["dopamine","serotonin","adrenaline"].includes(e.id),hormones:e=>["insulin","testosterone","estradiol","cortisol","thyroxine"].includes(e.id),organic:e=>!["thyroxine"].includes(e.id),inorganic:e=>["thyroxine"].includes(e.id)};return(0,l.useEffect)(()=>{let t=h[d]||h.all;x(e.filter(e=>(!n||e.name.toLowerCase().includes(n.toLowerCase())||e.formula.toLowerCase().includes(n.toLowerCase()))&&t(e)))},[n,d,e]),(0,r.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:i("title")}),r.jsx("div",{className:"mb-4",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:"text",placeholder:i("search"),className:"w-full bg-dark-600 border border-dark-500 rounded-md py-2 px-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500",value:n,onChange:e=>o(e.target.value)}),r.jsx("svg",{className:"absolute right-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})}),r.jsx("div",{className:"mb-4",children:(0,r.jsxs)("select",{className:"w-full bg-dark-600 border border-dark-500 rounded-md py-2 px-4 text-white focus:outline-none focus:ring-2 focus:ring-primary-500",value:d,onChange:e=>c(e.target.value),children:[r.jsx("option",{value:"all",children:i("filters.all")}),r.jsx("option",{value:"pharmaceutical",children:i("filters.pharmaceutical")}),r.jsx("option",{value:"neurotransmitters",children:i("filters.neurotransmitters")}),r.jsx("option",{value:"hormones",children:i("filters.hormones")}),r.jsx("option",{value:"organic",children:i("filters.organic")}),r.jsx("option",{value:"inorganic",children:i("filters.inorganic")})]})}),r.jsx("div",{className:"max-h-[400px] overflow-y-auto pr-2 space-y-2",children:m.length>0?m.map(e=>(0,r.jsxs)("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${s===e.id?"bg-primary-500 text-white":"bg-dark-600 hover:bg-dark-500"}`,onClick:()=>t(e),children:[r.jsx("h3",{className:"font-medium",children:e.name}),r.jsx("p",{className:"text-sm text-gray-300",children:e.formula}),r.jsx("div",{className:"flex items-center mt-1",children:(0,r.jsxs)("span",{className:"text-xs px-2 py-0.5 rounded-full bg-dark-500 text-gray-300",children:[e.atoms.length," ",i("atoms")]})})]},e.id)):r.jsx("div",{className:"text-center py-4 text-gray-400",children:i("empty")})}),r.jsx("div",{className:"mt-4 pt-4 border-t border-dark-600 text-xs text-gray-400",children:r.jsx("p",{children:i("count",{count:m.length,total:e.length})})})]})}},5853:(e,t,s)=>{"use strict";s.d(t,{Z:()=>f});var r=s(326),a=s(7577),l=s(2533),i=s(8303),n=s(2688),o=s(2140),d=s(177),c=s(2122),m=s(5797);let x=({position:e,element:t,renderMode:s})=>{let a;switch(s){case"space-filling":a=(0,c._0)(t);break;case"van-der-waals":a=(0,c.nX)(t);break;case"ball-stick":default:a=.5*(0,c._0)(t);break;case"licorice":case"wireframe":a=.3*(0,c._0)(t);break;case"cartoon":a=.1*(0,c._0)(t)}let l="wireframe"===s?8:32,i=(0,c.oW)(t);return"cartoon"===s?(0,r.jsxs)("mesh",{position:e,children:[r.jsx("sphereGeometry",{args:[a,8,8]}),r.jsx("meshStandardMaterial",{color:i,transparent:!0,opacity:.1,metalness:.2,roughness:.8})]}):(0,r.jsxs)("mesh",{position:e,children:[r.jsx("sphereGeometry",{args:[a,l,l]}),r.jsx("meshStandardMaterial",{color:i,wireframe:"wireframe"===s,metalness:.2,roughness:.8}),"wireframe"!==s&&"cartoon"!==s&&(0,r.jsxs)("mesh",{children:[r.jsx("sphereGeometry",{args:[a,l,l]}),r.jsx("meshPhongMaterial",{color:i,transparent:!0,opacity:.2,side:m.BackSide,depthWrite:!1})]})]})},h=({start:e,end:t,bondType:s,renderMode:a})=>{let l,i;let n=[(e[0]+t[0])/2,(e[1]+t[1])/2,(e[2]+t[2])/2],o=new m.Vector3(t[0]-e[0],t[1]-e[1],t[2]-e[2]),d=o.length();o.normalize();let c=new m.Quaternion,x=new m.Vector3(0,1,0);switch(c.setFromUnitVectors(x,o),a){case"licorice":l=.15,i=12;break;case"wireframe":l=.05,i=4;break;case"cartoon":l=.02,i=4;break;default:l=.1,i=8}let h=new m.Vector3(1,0,0).cross(o).normalize();return(.01>h.lengthSq()&&h.set(0,0,1).cross(o).normalize(),"single"===s||"wireframe"===a)?(0,r.jsxs)("mesh",{position:n,quaternion:c,children:[r.jsx("cylinderGeometry",{args:[l,l,d,i]}),r.jsx("meshStandardMaterial",{color:"#ffffff",wireframe:"wireframe"===a,metalness:.2,roughness:.8})]}):"double"===s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("mesh",{position:[n[0]+.15*h.x,n[1]+.15*h.y,n[2]+.15*h.z],quaternion:c,children:[r.jsx("cylinderGeometry",{args:[l,l,d,i]}),r.jsx("meshStandardMaterial",{color:"#ffffff",metalness:.2,roughness:.8})]}),(0,r.jsxs)("mesh",{position:[n[0]-.15*h.x,n[1]-.15*h.y,n[2]-.15*h.z],quaternion:c,children:[r.jsx("cylinderGeometry",{args:[l,l,d,i]}),r.jsx("meshStandardMaterial",{color:"#ffffff",metalness:.2,roughness:.8})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("mesh",{position:n,quaternion:c,children:[r.jsx("cylinderGeometry",{args:[l,l,d,i]}),r.jsx("meshStandardMaterial",{color:"#ffffff",metalness:.2,roughness:.8})]}),(0,r.jsxs)("mesh",{position:[n[0]+.15*h.x,n[1]+.15*h.y,n[2]+.15*h.z],quaternion:c,children:[r.jsx("cylinderGeometry",{args:[.7*l,.7*l,d,i]}),r.jsx("meshStandardMaterial",{color:"#ffffff",transparent:!0,opacity:.7,metalness:.2,roughness:.8})]})]})},p=({molecule:e})=>{let{camera:t}=(0,l.D)(),s=(0,a.useRef)();return(0,a.useEffect)(()=>{if(!e||!e.atoms||0===e.atoms.length)return;let r=new m.Vector3;e.atoms.forEach(e=>{r.add(new m.Vector3(...e.position))}),r.divideScalar(e.atoms.length);let a=0;e.atoms.forEach(e=>{let t=new m.Vector3(...e.position).distanceTo(r);a=Math.max(a,t)});let l=Math.max(3*a,10);t.position.set(r.x,r.y,r.z+l),s.current&&(s.current.target.set(r.x,r.y,r.z),s.current.update())},[e,t]),r.jsx(n.z,{ref:s,enableDamping:!0,dampingFactor:.25})},u=({position:e,element:t})=>r.jsx(o.V,{position:e,distanceFactor:10,children:r.jsx("div",{className:"bg-dark-800/80 px-2 py-1 rounded text-xs text-white",children:t})}),g=({molecule:e})=>{(0,a.useRef)(null),(0,a.useEffect)(()=>{if(!e||!e.atoms)return;let t=(0,c.bq)(e);t.filter(e=>"helix"===e.structure).map(t=>({...e.atoms[t.atomIndex],secondaryStructure:"helix"})),t.filter(e=>"sheet"===e.structure).map(t=>({...e.atoms[t.atomIndex],secondaryStructure:"sheet"})),t.filter(e=>"loop"===e.structure).map(t=>({...e.atoms[t.atomIndex],secondaryStructure:"loop"}))},[e]);let t=(t,s,a)=>{if(t.length<2)return null;let l=t.map(t=>e.atoms[t.atomIndex]).map(e=>e.position),i=l.reduce((e,t)=>[e[0]+t[0]/l.length,e[1]+t[1]/l.length,e[2]+t[2]/l.length],[0,0,0]),n=new m.Vector3(l[l.length-1][0]-l[0][0],l[l.length-1][1]-l[0][1],l[l.length-1][2]-l[0][2]),o=n.length();n.normalize();let d=new m.Quaternion,c=new m.Vector3(0,1,0);return(d.setFromUnitVectors(c,n),"helix"===s)?(0,r.jsxs)("mesh",{position:i,quaternion:d,children:[r.jsx("cylinderGeometry",{args:[.8,.8,o,8]}),r.jsx("meshStandardMaterial",{color:"#ff6b6b",transparent:!0,opacity:.8})]},`helix-${a}`):"sheet"===s?(0,r.jsxs)("mesh",{position:i,quaternion:d,children:[r.jsx("boxGeometry",{args:[1.5,.2,o]}),r.jsx("meshStandardMaterial",{color:"#4ecdc4",transparent:!0,opacity:.8})]},`sheet-${a}`):(0,r.jsxs)("mesh",{position:i,quaternion:d,children:[r.jsx("cylinderGeometry",{args:[.1,.1,o,4]}),r.jsx("meshStandardMaterial",{color:"#95e1d3"})]},`loop-${a}`)};return r.jsx(r.Fragment,{children:(()=>{if(!e||!e.atoms)return null;let s=(0,c.bq)(e),r=[],a=[],l=null;return s.forEach((e,s)=>{e.structure!==l?(a.length>1&&r.push(t(a,l,s)),a=[e],l=e.structure):a.push(e)}),a.length>1&&l&&r.push(t(a,l,s.length)),r})()})},j=({children:e,animationSettings:t})=>{let s=(0,a.useRef)(null);return(0,l.F)((e,r)=>{if(s.current&&t?.rotation.enabled){let e=t.rotation.speed;switch(t.rotation.axis){case"x":s.current.rotation.x+=r*e;break;case"y":s.current.rotation.y+=r*e;break;case"z":s.current.rotation.z+=r*e}}}),r.jsx("group",{ref:s,children:e})},f=({molecule:e,renderMode:t="ball-stick",animationSettings:s,onAnimationSettingsChange:l})=>{let[n,o]=(0,a.useState)(!1),[c,m]=(0,a.useState)(!1);return e?(0,r.jsxs)("div",{className:"w-full h-[500px] relative bg-dark-700 rounded-lg overflow-hidden",children:[n&&r.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-dark-800/50 z-10",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"})}),(0,r.jsxs)("div",{className:"absolute top-4 right-4 z-10 flex space-x-2",children:[r.jsx("button",{className:`px-3 py-1 rounded-md text-xs ${c?"bg-primary-500 text-white":"bg-dark-600/80 text-gray-300 hover:bg-dark-500/80"}`,onClick:()=>m(!c),children:"Etykiety"}),r.jsx("button",{className:"px-3 py-1 rounded-md text-xs bg-dark-600/80 text-gray-300 hover:bg-dark-500/80",onClick:()=>{},children:"Reset widoku"})]}),(0,r.jsxs)(i.Xz,{shadows:!0,dpr:[1,2],children:[r.jsx(d.c,{makeDefault:!0,position:[0,0,15]}),r.jsx(p,{molecule:e}),r.jsx("ambientLight",{intensity:.5}),r.jsx("directionalLight",{position:[10,10,5],intensity:1,castShadow:!0}),r.jsx("directionalLight",{position:[-10,-10,-5],intensity:.5}),r.jsx("hemisphereLight",{intensity:.3,groundColor:"#000000"}),(0,r.jsxs)(j,{animationSettings:s||{rotation:{enabled:!1,speed:1,axis:"y"},transitions:{enabled:!1,currentConformation:0,speed:1}},children:[e.atoms.map((e,s)=>(0,r.jsxs)("group",{children:[r.jsx(x,{position:e.position,element:e.element,renderMode:t}),c&&r.jsx(u,{position:e.position,element:e.element})]},`atom-${s}`)),!["space-filling","van-der-waals"].includes(t)&&e.bonds&&e.bonds.map((s,a)=>{let l=e.atoms[s.from],i=e.atoms[s.to];return l&&i?r.jsx(h,{start:l.position,end:i.position,bondType:s.type,renderMode:t},`bond-${a}`):null}),"cartoon"===t&&r.jsx(g,{molecule:e})]})]}),(0,r.jsxs)("div",{className:"absolute bottom-4 left-4 bg-dark-800/80 px-3 py-2 rounded-md",children:[r.jsx("p",{className:"text-sm font-medium",children:e.name}),r.jsx("p",{className:"text-xs text-gray-400",children:e.formula})]}),r.jsx("div",{className:"absolute bottom-4 right-4 bg-dark-800/80 px-3 py-2 rounded-md text-xs text-gray-400",children:r.jsx("p",{children:"Obracanie: kliknij i przeciągnij | Przybliżanie: scroll"})})]}):r.jsx("div",{className:"w-full h-[500px] flex items-center justify-center bg-dark-700 rounded-lg",children:r.jsx("p",{className:"text-gray-400",children:"Wybierz molekułę do wyświetlenia"})})}},2348:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(326),a=s(6162),l=s(7577);function i(e){return({H:"#FFFFFF",C:"#909090",N:"#3050F8",O:"#FF0D0D",F:"#90E050",P:"#FF8000",S:"#FFFF30",Cl:"#1FF01F",Br:"#A62929",I:"#940094",Na:"#AB5CF2",Mg:"#8AFF00",K:"#8F40D4",Ca:"#3DFF00",Fe:"#E06633"})[e]||"#808080"}let n=({molecule:e})=>{let t=(0,a.T_)("molecules.properties"),[s,n]=(0,l.useState)("properties");return e?(0,r.jsxs)("div",{className:"bg-dark-700 rounded-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex border-b border-dark-500",children:[r.jsx("button",{className:`flex-1 py-3 px-4 text-sm font-medium ${"properties"===s?"bg-dark-600 text-primary-400 border-b-2 border-primary-500":"text-gray-400 hover:text-white"}`,onClick:()=>n("properties"),children:t("tabs.properties")}),r.jsx("button",{className:`flex-1 py-3 px-4 text-sm font-medium ${"structure"===s?"bg-dark-600 text-primary-400 border-b-2 border-primary-500":"text-gray-400 hover:text-white"}`,onClick:()=>n("structure"),children:t("tabs.structure")})]}),r.jsx("div",{className:"p-4",children:"properties"===s?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("name")}),r.jsx("p",{className:"font-medium",children:e.name})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("formula")}),r.jsx("p",{className:"font-medium",children:e.formula})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("atoms")}),r.jsx("p",{className:"font-medium",children:e.atoms.length})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("bonds")}),r.jsx("p",{className:"font-medium",children:e.bonds.length})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("elements")}),r.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:Array.from(new Set(e.atoms.map(e=>e.element))).map(e=>r.jsx("span",{className:"px-2 py-0.5 text-xs rounded-full bg-dark-500",style:{backgroundColor:`${i(e)}33`,color:i(e)},children:e},e))})]})]}):(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("atoms")}),r.jsx("div",{className:"mt-2 max-h-[200px] overflow-y-auto",children:(0,r.jsxs)("table",{className:"w-full text-sm",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{className:"text-gray-400 border-b border-dark-500",children:[r.jsx("th",{className:"pb-2 text-left",children:t("elements")}),(0,r.jsxs)("th",{className:"pb-2 text-right",children:[t("position")," X"]}),(0,r.jsxs)("th",{className:"pb-2 text-right",children:[t("position")," Y"]}),(0,r.jsxs)("th",{className:"pb-2 text-right",children:[t("position")," Z"]})]})}),(0,r.jsxs)("tbody",{children:[e.atoms.slice(0,10).map((e,t)=>(0,r.jsxs)("tr",{className:"border-b border-dark-600",children:[r.jsx("td",{className:"py-2",children:r.jsx("span",{className:"px-1.5 py-0.5 text-xs rounded-full",style:{backgroundColor:`${i(e.element)}33`,color:i(e.element)},children:e.element})}),r.jsx("td",{className:"py-2 text-right",children:e.position[0].toFixed(2)}),r.jsx("td",{className:"py-2 text-right",children:e.position[1].toFixed(2)}),r.jsx("td",{className:"py-2 text-right",children:e.position[2].toFixed(2)})]},t)),e.atoms.length>10&&r.jsx("tr",{children:r.jsx("td",{colSpan:4,className:"py-2 text-center text-gray-400",children:t("more",{count:e.atoms.length-10})})})]})]})})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm text-gray-400",children:t("bonds")}),r.jsx("div",{className:"mt-2 max-h-[150px] overflow-y-auto",children:(0,r.jsxs)("table",{className:"w-full text-sm",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{className:"text-gray-400 border-b border-dark-500",children:[r.jsx("th",{className:"pb-2 text-left",children:"Od"}),r.jsx("th",{className:"pb-2 text-left",children:"Do"}),r.jsx("th",{className:"pb-2 text-right",children:"Typ"})]})}),(0,r.jsxs)("tbody",{children:[e.bonds.slice(0,8).map((s,a)=>(0,r.jsxs)("tr",{className:"border-b border-dark-600",children:[(0,r.jsxs)("td",{className:"py-2",children:[e.atoms[s.from]?.element||"?"," (",s.from,")"]}),(0,r.jsxs)("td",{className:"py-2",children:[e.atoms[s.to]?.element||"?"," (",s.to,")"]}),r.jsx("td",{className:"py-2 text-right",children:r.jsx("span",{className:`px-2 py-0.5 text-xs rounded-full ${"single"===s.type?"bg-blue-900 text-blue-300":"double"===s.type?"bg-purple-900 text-purple-300":"bg-pink-900 text-pink-300"}`,children:t(`type.${s.type}`)})})]},a)),e.bonds.length>8&&r.jsx("tr",{children:r.jsx("td",{colSpan:3,className:"py-2 text-center text-gray-400",children:t("more",{count:e.bonds.length-8})})})]})]})})]})]})})]}):r.jsx("div",{className:"bg-dark-700 rounded-lg p-4",children:r.jsx("p",{className:"text-gray-400 text-center",children:t("empty")})})}},9072:(e,t,s)=>{"use strict";s.d(t,{zu:()=>r}),s(7577),new(s(3524)).PrismaClient;let r=[{id:"aspirin",name:"Aspiryna",formula:"C9H8O4",atoms:[{element:"C",position:[0,0,0]},{element:"C",position:[1.4,0,0]},{element:"C",position:[2.1,1.2,0]},{element:"C",position:[1.4,2.4,0]},{element:"C",position:[0,2.4,0]},{element:"C",position:[-.7,1.2,0]},{element:"C",position:[2.1,3.6,0]},{element:"O",position:[1.4,4.8,0]},{element:"O",position:[3.5,3.6,0]},{element:"C",position:[4.2,4.8,0]},{element:"C",position:[5.6,4.8,0]},{element:"O",position:[6.3,3.6,0]},{element:"O",position:[6.3,6,0]},{element:"H",position:[-.5,-.9,0]},{element:"H",position:[1.9,-.9,0]},{element:"H",position:[3.1,1.2,0]},{element:"H",position:[-.5,3.3,0]},{element:"H",position:[-1.8,1.2,0]},{element:"H",position:[3.7,5.7,0]},{element:"H",position:[6.1,5.7,0]},{element:"H",position:[6.1,4.8,.9]}],bonds:[{from:0,to:1,type:"aromatic"},{from:1,to:2,type:"aromatic"},{from:2,to:3,type:"aromatic"},{from:3,to:4,type:"aromatic"},{from:4,to:5,type:"aromatic"},{from:5,to:0,type:"aromatic"},{from:3,to:6,type:"single"},{from:6,to:7,type:"double"},{from:6,to:8,type:"single"},{from:8,to:9,type:"single"},{from:9,to:10,type:"single"},{from:10,to:11,type:"single"},{from:10,to:12,type:"double"},{from:0,to:13,type:"single"},{from:1,to:14,type:"single"},{from:2,to:15,type:"single"},{from:4,to:16,type:"single"},{from:5,to:17,type:"single"},{from:9,to:18,type:"single"},{from:9,to:19,type:"single"},{from:9,to:20,type:"single"}]}]},2122:(e,t,s)=>{"use strict";s.d(t,{_0:()=>a,bq:()=>n,f:()=>i,nX:()=>l,oW:()=>r});let r=e=>({H:"#FFFFFF",C:"#909090",N:"#3050F8",O:"#FF0D0D",F:"#90E050",P:"#FF8000",S:"#FFFF30",Cl:"#1FF01F",Br:"#A62929",I:"#940094",Na:"#AB5CF2",Mg:"#8AFF00",K:"#8F40D4",Ca:"#3DFF00",Fe:"#E06633"})[e]||"#808080",a=e=>({H:.25,C:.7,N:.65,O:.6,F:.5,P:1,S:1,Cl:1,Br:1.15,I:1.4,Na:1.8,Mg:1.5,K:2.2,Ca:1.8,Fe:1.4})[e]||.7,l=e=>({H:1.2,C:1.7,N:1.55,O:1.52,F:1.47,P:1.8,S:1.8,Cl:1.75,Br:1.85,I:1.98,Na:2.27,Mg:1.73,K:2.75,Ca:2.31,Fe:2})[e]||1.7,i=async(e,t="hd")=>{let{width:s,height:r}={hd:{width:1280,height:720},"4k":{width:3840,height:2160}}[t],a=document.createElement("canvas");a.width=s,a.height=r;let l=a.getContext("2d");if(!l)throw Error("Nie można utworzyć kontekstu canvas");return l.drawImage(e,0,0,s,r),a.toDataURL("image/png")},n=e=>{let t=[];return e.atoms.forEach((s,r)=>{let a="loop";if(e.atoms.length>50){let t=e.atoms.some(e=>"N"===e.element),r=e.atoms.some(e=>"O"===e.element);if(t&&r){let e=s.position[2];e>2?a="helix":e<-2&&(a="sheet")}}t.push({atomIndex:r,structure:a})}),t}},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>i});var r=s(9510),a=s(5036),l=s.n(a);s(4315);let i={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function n({children:e}){return r.jsx("html",{lang:"pl",className:`${l().variable}`,children:r.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},4315:()=>{}};