exports.id=751,exports.ids=[751],exports.modules={3800:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},5047:(e,t,n)=>{"use strict";var r=n(7389);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"redirect")&&n.d(t,{redirect:function(){return r.redirect}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},2822:(e,t,n)=>{"use strict";n.d(t,{Z:()=>p});var r=n(5047),a=n(7577),o=n.t(a,2),i=n(1985),c=o["use".trim()],l=n(2997),u=n(2356),s=n(5959),f=n(326),m=n(9054);function p(e){let{Link:t,config:n,getPathname:o,...p}=function(e,t){var n,o,i;let m={...n=t||{},localePrefix:"object"==typeof(i=n.localePrefix)?i:{mode:i||"always"},localeCookie:!!((o=n.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof o&&o},localeDetection:n.localeDetection??!0,alternateLinks:n.alternateLinks??!0},p=m.pathnames,h=(0,a.forwardRef)(function({href:t,locale:n,...r},a){let o,i;"object"==typeof t?(o=t.pathname,i=t.params):o=t;let s=(0,l.t3)(t),h=e(),g=(0,l.tI)(h)?c(h):h,y=s?d({locale:n||g,href:null==p?o:{pathname:o,params:i},forcePrefix:null!=n||void 0}):o;return(0,f.jsx)(u.default,{ref:a,href:"object"==typeof t?{...t,pathname:y}:y,locale:n,localeCookie:m.localeCookie,...r})});function d(e){let t;let{forcePrefix:n,href:r,locale:a}=e;return null==p?"object"==typeof r?(t=r.pathname,r.query&&(t+=(0,s.Ci)(r.query))):t=r:t=(0,s.cT)({locale:a,...(0,s.hm)(r),pathnames:m.pathnames}),(0,s.r6)(t,a,m,n)}function g(e){return function(t,...n){return e(d(t),...n)}}return{config:m,Link:h,redirect:g(r.redirect),permanentRedirect:g(r.permanentRedirect),getPathname:d}}(i.bU,e);return{...p,Link:t,usePathname:function(){let e=function(e){let t=(0,r.usePathname)(),n=(0,i.bU)();return(0,a.useMemo)(()=>{if(!t)return t;let r=t,a=(0,l.Af)(n,e.localePrefix);if((0,l.Tp)(a,t))r=(0,l.vz)(t,a);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,l.Sk)(n);(0,l.Tp)(e,t)&&(r=(0,l.vz)(t,e))}return r},[e.localePrefix,n,t])}(n),t=(0,i.bU)();return(0,a.useMemo)(()=>e&&n.pathnames?(0,s.Bv)(t,e,n.pathnames):e,[t,e])},useRouter:function(){let e=(0,r.useRouter)(),t=(0,i.bU)(),c=(0,r.usePathname)();return(0,a.useMemo)(()=>{function r(e){return function(r,a){let{locale:i,...l}=a||{},u=[o({href:r,locale:i||t})];Object.keys(l).length>0&&u.push(l),e(...u),(0,m.Z)(n.localeCookie,c,t,i)}}return{...e,push:r(e.push),replace:r(e.replace),prefetch:r(e.prefetch)}},[t,c,e])},getPathname:o}}},2356:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(434),a=n(5047),o=n(7577),i=n(1985),c=n(9054),l=n(326),u=(0,o.forwardRef)(function({href:e,locale:t,localeCookie:n,onClick:o,prefetch:u,...s},f){let m=(0,i.bU)(),p=null!=t&&t!==m,h=(0,a.usePathname)();return p&&(u=!1),(0,l.jsx)(r.default,{ref:f,href:e,hrefLang:p?t:void 0,onClick:function(e){(0,c.Z)(n,h,m,t),o&&o(e)},prefetch:u,...s})})},9054:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(5959);function a(e,t,n,a){if(!e||!(a!==n&&null!=a)||!t)return;let o=(0,r.bv)(t),{name:i,...c}=e;c.path||(c.path=""!==o?o:"/");let l=`${i}=${a};`;for(let[e,t]of Object.entries(c))l+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(l+="="+t),l+=";";document.cookie=l}},5959:(e,t,n)=>{"use strict";n.d(t,{Bv:()=>c,Ci:()=>o,bv:()=>l,cT:()=>i,hm:()=>a,r6:()=>u});var r=n(2997);function a(e){return"string"==typeof e?{pathname:e}:e}function o(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}function i({pathname:e,locale:t,params:n,pathnames:a,query:i}){function c(e){let t=a[e];return t||(t=e),t}function l(e,a){let c=(0,r.lB)(e,t,a);return n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),c=c.replace(RegExp(n,"g"),r)}),c=c.replace(/\[\[\.\.\..+\]\]/g,""),c=(0,r.RZ)(c),i&&(c+=o(i)),c}if("string"==typeof e)return l(c(e),e);{let{pathname:t,...n}=e;return{...n,pathname:l(c(t),t)}}}function c(e,t,n){let a=(0,r.kp)(Object.keys(n)),o=decodeURI(t);for(let t of a){let a=n[t];if("string"==typeof a){if((0,r.wl)(a,o))return t}else if((0,r.wl)((0,r.lB)(a,e,t),o))return t}return t}function l(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function u(e,t,n,a){let o;let{mode:i}=n.localePrefix;return void 0!==a?o=a:(0,r.t3)(e)&&("always"===i?o=!0:"as-needed"===i&&(o=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),o?(0,r.R3)((0,r.Af)(t,n.localePrefix),e):e}},4491:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{Z:()=>r})},5269:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(1985),a=n(326);function o({locale:e,...t}){if(!e)throw Error(void 0);return(0,a.jsx)(r.Pj,{locale:e,...t})}},2997:(e,t,n)=>{"use strict";function r(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function a(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function o(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function i(e,t){return t===e||t.startsWith(`${e}/`)}function c(e,t,n){return"string"==typeof e?e:e[t]||n}function l(e){let t=function(){try{return"true"===process.env._next_intl_trailing_slash}catch{return!1}}();if("/"!==e){let n=e.endsWith("/");t&&!n?e+="/":!t&&n&&(e=e.slice(0,-1))}return e}function u(e,t){let n=l(e),r=l(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(n).test(r)}function s(e,t){return"never"!==t.mode&&t.prefixes?.[e]||f(e)}function f(e){return"/"+e}function m(e){return e.includes("[[...")}function p(e){return e.includes("[...")}function h(e){return e.includes("[")}function d(e,t){let n=e.split("/"),r=t.split("/"),a=Math.max(n.length,r.length);for(let e=0;e<a;e++){let t=n[e],a=r[e];if(!t&&a)return -1;if(t&&!a)return 1;if(t||a){if(!h(t)&&h(a))return -1;if(h(t)&&!h(a))return 1;if(!p(t)&&p(a))return -1;if(p(t)&&!p(a))return 1;if(!m(t)&&m(a))return -1;if(m(t)&&!m(a))return 1}}return 0}function g(e){return e.sort(d)}function y(e){return"function"==typeof e.then}n.d(t,{Af:()=>s,R3:()=>o,RZ:()=>l,Sk:()=>f,Tp:()=>i,kp:()=>g,lB:()=>c,t3:()=>r,tI:()=>y,vz:()=>a,wl:()=>u})},1806:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var r=n(8585),a=n(1159),o=n.t(a,2)["use".trim()],i=n(3747);let c=(0,n(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/node_modules/next-intl/dist/esm/production/navigation/shared/BaseLink.js#default`);function l(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}var u=n(9510),s=n(1027);async function f(){return(await (0,s.Z)()).locale}function m(e){let{config:t,...n}=function(e,t){var n,s,f;let m={...n=t||{},localePrefix:"object"==typeof(f=n.localePrefix)?f:{mode:f||"always"},localeCookie:!!((s=n.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof s&&s},localeDetection:n.localeDetection??!0,alternateLinks:n.alternateLinks??!0},p=m.pathnames,h=(0,a.forwardRef)(function({href:t,locale:n,...r},a){let l,s;"object"==typeof t?(l=t.pathname,s=t.params):l=t;let f=(0,i.t3)(t),h=e(),g=(0,i.tI)(h)?o(h):h,y=f?d({locale:n||g,href:null==p?l:{pathname:l,params:s},forcePrefix:null!=n||void 0}):l;return(0,u.jsx)(c,{ref:a,href:"object"==typeof t?{...t,pathname:y}:y,locale:n,localeCookie:m.localeCookie,...r})});function d(e){let t;let{forcePrefix:n,href:r,locale:a}=e;return null==p?"object"==typeof r?(t=r.pathname,r.query&&(t+=l(r.query))):t=r:t=function({pathname:e,locale:t,params:n,pathnames:r,query:a}){function o(e){let t=r[e];return t||(t=e),t}function c(e,r){let o=(0,i.lB)(e,t,r);return n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),o=o.replace(RegExp(n,"g"),r)}),o=o.replace(/\[\[\.\.\..+\]\]/g,""),o=(0,i.RZ)(o),a&&(o+=l(a)),o}if("string"==typeof e)return c(o(e),e);{let{pathname:t,...n}=e;return{...n,pathname:c(o(t),t)}}}({locale:a,..."string"==typeof r?{pathname:r}:r,pathnames:m.pathnames}),function(e,t,n,r){let a;let{mode:o}=n.localePrefix;return void 0!==r?a=r:(0,i.t3)(e)&&("always"===o?a=!0:"as-needed"===o&&(a=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),a?(0,i.R3)((0,i.Af)(t,n.localePrefix),e):e}(t,a,m,n)}function g(e){return function(t,...n){return e(d(t),...n)}}return{config:m,Link:h,redirect:g(r.redirect),permanentRedirect:g(r.permanentRedirect),getPathname:d}}(f,e);function s(e){return()=>{throw Error(`\`${e}\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`)}}return{...n,usePathname:s("usePathname"),useRouter:s("useRouter")}}},4588:(e,t,n)=>{"use strict";n.d(t,{Z:()=>h});var r=n(1159),a=n(1027);let o=(0,r.cache)(async function(e){return(await (0,a.Z)(e)).now}),i=(0,r.cache)(async function(){return(await (0,a.Z)()).formats}),c=(0,n(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js#default`);var l=n(9510);let u=(0,r.cache)(async function(e){return(await (0,a.Z)(e)).timeZone});async function s(e){return u(e?.locale)}let f=(0,r.cache)(async function(e){return function(e){if(!e.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return e.messages}(await (0,a.Z)(e))});async function m(e){return f(e?.locale)}let p=(0,r.cache)(async function(){return(await (0,a.Z)()).locale});async function h({formats:e,locale:t,messages:n,now:r,timeZone:a,...u}){return(0,l.jsx)(c,{formats:void 0===e?await i():e,locale:t??await p(),messages:void 0===n?await m():n,now:r??await o(),timeZone:a??await s(),...u})}},9458:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{Z:()=>r})},1027:(e,t,n)=>{"use strict";n.d(t,{Z:()=>E});var r=n(1159);function a(e,t,n,r){var a=null==r||"number"==typeof r||"boolean"==typeof r?r:n(r),o=t.get(a);return void 0===o&&(o=e.call(this,r),t.set(a,o)),o}function o(e,t,n){var r=Array.prototype.slice.call(arguments,3),a=n(r),o=t.get(a);return void 0===o&&(o=e.apply(this,r),t.set(a,o)),o}var i=function(){return JSON.stringify(arguments)},c=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new c}},u={variadic:function(e,t){var n,r;return n=t.cache.create(),r=t.serializer,o.bind(this,e,n,r)},monadic:function(e,t){var n,r;return n=t.cache.create(),r=t.serializer,a.bind(this,e,n,r)}},s=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(s||{});function f(e){return function(...e){return e.filter(Boolean).join(".")}(e.namespace,e.key)}function m(e){console.error(e)}function p(e,t){var n,r,c,s;return n=(...t)=>new e(...t),c=(r={cache:{create:()=>({get:e=>t[e],set(e,n){t[e]=n}})},strategy:u.variadic}).cache?r.cache:l,s=r&&r.serializer?r.serializer:i,(r&&r.strategy?r.strategy:function(e,t){var n,r,i=1===e.length?a:o;return n=t.cache.create(),r=t.serializer,i.bind(this,e,n,r)})(n,{cache:c,serializer:s})}var h=n(3747),d=n(1615);let g=(0,r.cache)(function(){return{locale:void 0}}),y=(0,r.cache)(async function(){let e=(0,d.headers)();return(0,h.tI)(e)?await e:e}),v=(0,r.cache)(async function(){let e;try{e=(await y()).get("X-NEXT-INTL-LOCALE")||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function R(){return g().locale||await v()}var x=n(2226);let b=(0,r.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),I=(0,r.cache)(async function(e,t){let n=e({locale:t,get requestLocale(){return t?Promise.resolve(t):R()}});if((0,h.tI)(n)&&(n=await n),!n.locale)throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");return n}),w=(0,r.cache)(function(e){return{getDateTimeFormat:p(Intl.DateTimeFormat,e.dateTime),getNumberFormat:p(Intl.NumberFormat,e.number),getPluralRules:p(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:p(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:p(Intl.ListFormat,e.list),getDisplayNames:p(Intl.DisplayNames,e.displayNames)}}),S=(0,r.cache)(function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}),E=(0,r.cache)(async function(e){let t=await I(x.Z,e);return{...function({formats:e,getMessageFallback:t,messages:n,onError:r,...a}){return{...a,formats:e||void 0,messages:n||void 0,onError:r||m,getMessageFallback:t||f}}(t),_formatters:w(S()),timeZone:t.timeZone||b()}})},9375:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{Z:()=>r})},3747:(e,t,n)=>{"use strict";function r(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function a(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function o(e,t,n){return"string"==typeof e?e:e[t]||n}function i(e){let t=function(){try{return"true"===process.env._next_intl_trailing_slash}catch{return!1}}();if("/"!==e){let n=e.endsWith("/");t&&!n?e+="/":!t&&n&&(e=e.slice(0,-1))}return e}function c(e,t){return"never"!==t.mode&&t.prefixes?.[e]||"/"+e}function l(e){return"function"==typeof e.then}n.d(t,{Af:()=>c,R3:()=>a,RZ:()=>i,lB:()=>o,t3:()=>r,tI:()=>l})}};