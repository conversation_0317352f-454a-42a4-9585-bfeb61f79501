"use strict";exports.id=996,exports.ids=[996],exports.modules={434:(e,t,s)=>{s.d(t,{default:()=>n.a});var r=s(9404),n=s.n(r)},8618:(e,t,s)=>{s.d(t,{zB:()=>B,kP:()=>F}),s(326);var r=s(7577);class n extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let s=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${s}`}}class a extends n{}a.kind="signIn";class o extends n{}o.type="AdapterError";class i extends n{}i.type="AccessDenied";class c extends n{}c.type="CallbackRouteError";class l extends n{}l.type="ErrorPageLoop";class d extends n{}d.type="EventError";class p extends n{}p.type="InvalidCallbackUrl";class u extends a{constructor(){super(...arguments),this.code="credentials"}}u.type="CredentialsSignin";class h extends n{}h.type="InvalidEndpoints";class y extends n{}y.type="InvalidCheck";class x extends n{}x.type="JWTSessionError";class w extends n{}w.type="MissingAdapter";class f extends n{}f.type="MissingAdapterMethods";class E extends n{}E.type="MissingAuthorize";class g extends n{}g.type="MissingSecret";class v extends a{}v.type="OAuthAccountNotLinked";class U extends a{}U.type="OAuthCallbackError";class b extends n{}b.type="OAuthProfileParseError";class R extends n{}R.type="SessionTokenError";class A extends a{}A.type="OAuthSignInError";class S extends a{}S.type="EmailSignInError";class k extends n{}k.type="SignOutError";class m extends n{}m.type="UnknownAction";class L extends n{}L.type="UnsupportedStrategy";class T extends n{}T.type="InvalidProvider";class $ extends n{}$.type="UntrustedHost";class P extends n{}P.type="Verification";class C extends a{}C.type="MissingCSRF";class N extends n{}N.type="DuplicateConditionalUI";class _ extends n{}_.type="MissingWebAuthnAutocomplete";class I extends n{}I.type="WebAuthnVerificationError";class H extends a{}H.type="AccountNotLinked";class M extends n{}M.type="ExperimentalFeatureNotEnabled";class O extends n{}async function X(e,t,s,r={}){let n=`${W(t)}/${e}`;try{let e={headers:{"Content-Type":"application/json",...r?.headers?.cookie?{cookie:r.headers.cookie}:{}}};r?.body&&(e.body=JSON.stringify(r.body),e.method="POST");let t=await fetch(n,e),s=await t.json();if(!t.ok)throw s;return s}catch(e){return s.error(new O(e.message,e)),null}}function W(e){return`${e.baseUrlServer}${e.basePathServer}`}function j(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let s=new URL(e||t),r=("/"===s.pathname?t.pathname:s.pathname).replace(/\/$/,""),n=`${s.origin}${r}`;return{origin:s.origin,host:s.host,path:r,base:n,toString:()=>n}}let V={baseUrl:j(process.env.NEXTAUTH_URL??process.env.VERCEL_URL).origin,basePath:j(process.env.NEXTAUTH_URL).path,baseUrlServer:j(process.env.NEXTAUTH_URL_INTERNAL??process.env.NEXTAUTH_URL??process.env.VERCEL_URL).origin,basePathServer:j(process.env.NEXTAUTH_URL_INTERNAL??process.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},z={debug:console.debug,error:console.error,warn:console.warn},D=r.createContext?.(void 0);function F(e){if(!D)throw Error("React Context is unavailable in Server Components");let t=r.useContext(D),{required:s,onUnauthenticated:n}=e??{},a=s&&"unauthenticated"===t.status;return(r.useEffect(()=>{if(a){let e=`${V.basePath}/signin?${new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href})}`;n?n():window.location.href=e}},[a,n]),a)?{data:t.data,update:t.update,status:"loading"}:t}async function J(){let e=await X("csrf",V,z);return e?.csrfToken??""}async function q(){return X("providers",V,z)}async function B(e,t,s){let{callbackUrl:r,...n}=t??{},{redirect:a=!0,redirectTo:o=r??window.location.href,...i}=n,c=W(V),l=await q();if(!l){let e=`${c}/error`;window.location.href=e;return}if(!e||!l[e]){let e=`${c}/signin?${new URLSearchParams({callbackUrl:o})}`;window.location.href=e;return}let d=l[e].type;if("webauthn"===d)throw TypeError(`Provider id "${e}" refers to a WebAuthn provider.
Please use \`import { signIn } from "next-auth/webauthn"\` instead.`);let p=`${c}/${"credentials"===d?"callback":"signin"}/${e}`,u=await J(),h=await fetch(`${p}?${new URLSearchParams(s)}`,{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...i,csrfToken:u,callbackUrl:o})}),y=await h.json();if(a){let e=y.url??o;window.location.href=e,e.includes("#")&&window.location.reload();return}let x=new URL(y.url).searchParams.get("error")??void 0,w=new URL(y.url).searchParams.get("code")??void 0;return h.ok&&await V._getSession({event:"storage"}),{error:x,code:w,status:h.status,ok:h.ok,url:x?null:y.url}}}};