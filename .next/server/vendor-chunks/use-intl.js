"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-intl/dist/esm/development/core.js":
/*!************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/core.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   _createCache: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   createFormatter: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createTranslator: () => (/* binding */ createTranslator),\n/* harmony export */   hasLocale: () => (/* binding */ hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.i)\n/* harmony export */ });\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n\n\n\n\n\n\nfunction createTranslatorImpl({\n  messages,\n  namespace,\n  ...rest\n}, namespacePrefix) {\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.r)(namespace, namespacePrefix);\n  return (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.e)({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n// This type is slightly more loose than `AbstractIntlMessages`\n// in order to avoid a type error.\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator({\n  _cache = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d)(),\n  _formatters = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b)(_cache),\n  getMessageFallback = _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.f,\n  messages,\n  namespace,\n  onError = _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.g,\n  ...rest\n}) {\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? `!.${namespace}` : '!'\n  }, '!');\n}\n\n/**\n * Checks if a locale exists in a list of locales.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale\n */\nfunction hasLocale(locales, candidate) {\n  return locales.includes(candidate);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/esm/development/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/esm/development/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   IntlProvider: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.IntlProvider),\n/* harmony export */   _createCache: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   createFormatter: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createTranslator: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_1__.createTranslator),\n/* harmony export */   hasLocale: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_1__.hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   useFormatter: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.useFormatter),\n/* harmony export */   useLocale: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.useLocale),\n/* harmony export */   useMessages: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.useMessages),\n/* harmony export */   useNow: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.useNow),\n/* harmony export */   useTimeZone: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.useTimeZone),\n/* harmony export */   useTranslations: () => (/* reexport safe */ _react_js__WEBPACK_IMPORTED_MODULE_2__.useTranslations)\n/* harmony export */ });\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var _react_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./react.js */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWdMO0FBQ3hIO0FBQzhEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9pbmRleC5qcz8xNzcyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IEkgYXMgSW50bEVycm9yLCBhIGFzIEludGxFcnJvckNvZGUsIGQgYXMgX2NyZWF0ZUNhY2hlLCBiIGFzIF9jcmVhdGVJbnRsRm9ybWF0dGVycywgYyBhcyBjcmVhdGVGb3JtYXR0ZXIsIGkgYXMgaW5pdGlhbGl6ZUNvbmZpZyB9IGZyb20gJy4vaW5pdGlhbGl6ZUNvbmZpZy1DUkQ2ZXV1Sy5qcyc7XG5leHBvcnQgeyBjcmVhdGVUcmFuc2xhdG9yLCBoYXNMb2NhbGUgfSBmcm9tICcuL2NvcmUuanMnO1xuZXhwb3J0IHsgSW50bFByb3ZpZGVyLCB1c2VGb3JtYXR0ZXIsIHVzZUxvY2FsZSwgdXNlTWVzc2FnZXMsIHVzZU5vdywgdXNlVGltZVpvbmUsIHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gJy4vcmVhY3QuanMnO1xuXG5cblxuXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/esm/development/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I: () => (/* binding */ IntlError),\n/* harmony export */   a: () => (/* binding */ IntlErrorCode),\n/* harmony export */   b: () => (/* binding */ createIntlFormatters),\n/* harmony export */   c: () => (/* binding */ createFormatter),\n/* harmony export */   d: () => (/* binding */ createCache),\n/* harmony export */   e: () => (/* binding */ createBaseTranslator),\n/* harmony export */   f: () => (/* binding */ defaultGetMessageFallback),\n/* harmony export */   g: () => (/* binding */ defaultOnError),\n/* harmony export */   i: () => (/* binding */ initializeConfig),\n/* harmony export */   r: () => (/* binding */ resolveNamespace)\n/* harmony export */ });\n/* harmony import */ var intl_messageformat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\n\n\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.date;\n  const mfTimeDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.memoize)(fn, {\n    cache: createMemoCache(cache),\n    strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(result) ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/esm/development/react.js":
/*!*************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/react.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlProvider: () => (/* binding */ IntlProvider),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* binding */ useLocale),\n/* harmony export */   useMessages: () => (/* binding */ useMessages),\n/* harmony export */   useNow: () => (/* binding */ useNow),\n/* harmony export */   useTimeZone: () => (/* binding */ useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(ssr)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\nconst IntlContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n\nfunction IntlProvider({\n  children,\n  formats,\n  getMessageFallback,\n  locale,\n  messages,\n  now,\n  onError,\n  timeZone\n}) {\n  const prevContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return prevContext?.cache || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.d)();\n  }, [locale, prevContext?.cache]);\n  const formatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => prevContext?.formatters || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.b)(cache), [cache, prevContext?.formatters]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    ...(0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.i)({\n      locale,\n      // (required by provider)\n      formats: formats === undefined ? prevContext?.formats : formats,\n      getMessageFallback: getMessageFallback || prevContext?.getMessageFallback,\n      messages: messages === undefined ? prevContext?.messages : messages,\n      now: now || prevContext?.now,\n      onError: onError || prevContext?.onError,\n      timeZone: timeZone || prevContext?.timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, formats, formatters, getMessageFallback, locale, messages, now, onError, prevContext, timeZone]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(IntlContext.Provider, {\n    value: value,\n    children: children\n  });\n}\n\nfunction useIntlContext() {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#server-client-components' );\n  }\n  return context;\n}\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.r)(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.I(_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.a.ENVIRONMENT_FALLBACK, `There is no \\`timeZone\\` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone` ));\n  }\n  const translate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.e)({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? `!.${namespace}` : '!', '!');\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * @see https://next-intl.dev/docs/usage/dates-times#relative-times-usenow\n */\nfunction useNow(options) {\n  const updateInterval = options?.updateInterval;\n  const {\n    now: globalNow\n  } = useIntlContext();\n  const [now, setNow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalNow || getNow());\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = useIntlContext();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.c)({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/esm/development/react.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/esm/development/core.js":
/*!************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/core.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   _createCache: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   createFormatter: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createTranslator: () => (/* binding */ createTranslator),\n/* harmony export */   hasLocale: () => (/* binding */ hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.i)\n/* harmony export */ });\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n\n\n\n\n\n\nfunction createTranslatorImpl({\n  messages,\n  namespace,\n  ...rest\n}, namespacePrefix) {\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.r)(namespace, namespacePrefix);\n  return (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.e)({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n// This type is slightly more loose than `AbstractIntlMessages`\n// in order to avoid a type error.\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator({\n  _cache = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.d)(),\n  _formatters = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.b)(_cache),\n  getMessageFallback = _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.f,\n  messages,\n  namespace,\n  onError = _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_0__.g,\n  ...rest\n}) {\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? `!.${namespace}` : '!'\n  }, '!');\n}\n\n/**\n * Checks if a locale exists in a list of locales.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale\n */\nfunction hasLocale(locales, candidate) {\n  return locales.includes(candidate);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvY29yZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBb007QUFDbkU7Ozs7O0FBS2pJO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLGNBQWMsZ0VBQWdCO0FBQzlCLFNBQVMsZ0VBQW9CO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZ0VBQVc7QUFDdEIsZ0JBQWdCLGdFQUFvQjtBQUNwQyx1QkFBdUIsNERBQXlCO0FBQ2hEO0FBQ0E7QUFDQSxZQUFZLDREQUFjO0FBQzFCO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLGdDQUFnQyxVQUFVO0FBQzFDLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVtSCIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvY29yZS5qcz81MjI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHIgYXMgcmVzb2x2ZU5hbWVzcGFjZSwgZSBhcyBjcmVhdGVCYXNlVHJhbnNsYXRvciwgZiBhcyBkZWZhdWx0R2V0TWVzc2FnZUZhbGxiYWNrLCBiIGFzIGNyZWF0ZUludGxGb3JtYXR0ZXJzLCBkIGFzIGNyZWF0ZUNhY2hlLCBnIGFzIGRlZmF1bHRPbkVycm9yIH0gZnJvbSAnLi9pbml0aWFsaXplQ29uZmlnLUNSRDZldXVLLmpzJztcbmV4cG9ydCB7IEkgYXMgSW50bEVycm9yLCBhIGFzIEludGxFcnJvckNvZGUsIGMgYXMgY3JlYXRlRm9ybWF0dGVyLCBpIGFzIGluaXRpYWxpemVDb25maWcgfSBmcm9tICcuL2luaXRpYWxpemVDb25maWctQ1JENmV1dUsuanMnO1xuXG5cblxuXG5mdW5jdGlvbiBjcmVhdGVUcmFuc2xhdG9ySW1wbCh7XG4gIG1lc3NhZ2VzLFxuICBuYW1lc3BhY2UsXG4gIC4uLnJlc3Rcbn0sIG5hbWVzcGFjZVByZWZpeCkge1xuICAvLyBUaGUgYG5hbWVzcGFjZVByZWZpeGAgaXMgcGFydCBvZiB0aGUgdHlwZSBzeXN0ZW0uXG4gIC8vIFNlZSB0aGUgY29tbWVudCBpbiB0aGUgZnVuY3Rpb24gaW52b2NhdGlvbi5cbiAgbWVzc2FnZXMgPSBtZXNzYWdlc1tuYW1lc3BhY2VQcmVmaXhdO1xuICBuYW1lc3BhY2UgPSByZXNvbHZlTmFtZXNwYWNlKG5hbWVzcGFjZSwgbmFtZXNwYWNlUHJlZml4KTtcbiAgcmV0dXJuIGNyZWF0ZUJhc2VUcmFuc2xhdG9yKHtcbiAgICAuLi5yZXN0LFxuICAgIG1lc3NhZ2VzLFxuICAgIG5hbWVzcGFjZVxuICB9KTtcbn1cblxuLy8gVGhpcyB0eXBlIGlzIHNsaWdodGx5IG1vcmUgbG9vc2UgdGhhbiBgQWJzdHJhY3RJbnRsTWVzc2FnZXNgXG4vLyBpbiBvcmRlciB0byBhdm9pZCBhIHR5cGUgZXJyb3IuXG5cbi8qKlxuICogVHJhbnNsYXRlcyBtZXNzYWdlcyBmcm9tIHRoZSBnaXZlbiBuYW1lc3BhY2UgYnkgdXNpbmcgdGhlIElDVSBzeW50YXguXG4gKiBTZWUgaHR0cHM6Ly9mb3JtYXRqcy5pby9kb2NzL2NvcmUtY29uY2VwdHMvaWN1LXN5bnRheC5cbiAqXG4gKiBJZiBubyBuYW1lc3BhY2UgaXMgcHJvdmlkZWQsIGFsbCBhdmFpbGFibGUgbWVzc2FnZXMgYXJlIHJldHVybmVkLlxuICogVGhlIG5hbWVzcGFjZSBjYW4gYWxzbyBpbmRpY2F0ZSBuZXN0aW5nIGJ5IHVzaW5nIGEgZG90XG4gKiAoZS5nLiBgbmFtZXNwYWNlLkNvbXBvbmVudGApLlxuICovXG5mdW5jdGlvbiBjcmVhdGVUcmFuc2xhdG9yKHtcbiAgX2NhY2hlID0gY3JlYXRlQ2FjaGUoKSxcbiAgX2Zvcm1hdHRlcnMgPSBjcmVhdGVJbnRsRm9ybWF0dGVycyhfY2FjaGUpLFxuICBnZXRNZXNzYWdlRmFsbGJhY2sgPSBkZWZhdWx0R2V0TWVzc2FnZUZhbGxiYWNrLFxuICBtZXNzYWdlcyxcbiAgbmFtZXNwYWNlLFxuICBvbkVycm9yID0gZGVmYXVsdE9uRXJyb3IsXG4gIC4uLnJlc3Rcbn0pIHtcbiAgLy8gV2UgaGF2ZSB0byB3cmFwIHRoZSBhY3R1YWwgZnVuY3Rpb24gc28gdGhlIHR5cGUgaW5mZXJlbmNlIGZvciB0aGUgb3B0aW9uYWxcbiAgLy8gbmFtZXNwYWNlIHdvcmtzIGNvcnJlY3RseS4gU2VlIGh0dHBzOi8vc3RhY2tvdmVyZmxvdy5jb20vYS83MTUyOTU3NS8zNDMwNDVcbiAgLy8gVGhlIHByZWZpeCAoXCIhXCIpIGlzIGFyYml0cmFyeS5cbiAgLy8gQHRzLWV4cGVjdC1lcnJvciBVc2UgdGhlIGV4cGxpY2l0IGFubm90YXRpb24gaW5zdGVhZFxuICByZXR1cm4gY3JlYXRlVHJhbnNsYXRvckltcGwoe1xuICAgIC4uLnJlc3QsXG4gICAgb25FcnJvcixcbiAgICBjYWNoZTogX2NhY2hlLFxuICAgIGZvcm1hdHRlcnM6IF9mb3JtYXR0ZXJzLFxuICAgIGdldE1lc3NhZ2VGYWxsYmFjayxcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIGBtZXNzYWdlc2AgaXMgYWxsb3dlZCB0byBiZSBgdW5kZWZpbmVkYCBoZXJlIGFuZCB3aWxsIGJlIGhhbmRsZWQgaW50ZXJuYWxseVxuICAgIG1lc3NhZ2VzOiB7XG4gICAgICAnISc6IG1lc3NhZ2VzXG4gICAgfSxcbiAgICBuYW1lc3BhY2U6IG5hbWVzcGFjZSA/IGAhLiR7bmFtZXNwYWNlfWAgOiAnISdcbiAgfSwgJyEnKTtcbn1cblxuLyoqXG4gKiBDaGVja3MgaWYgYSBsb2NhbGUgZXhpc3RzIGluIGEgbGlzdCBvZiBsb2NhbGVzLlxuICpcbiAqIEBzZWUgaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvSmF2YVNjcmlwdC9SZWZlcmVuY2UvR2xvYmFsX09iamVjdHMvSW50bC9Mb2NhbGVcbiAqL1xuZnVuY3Rpb24gaGFzTG9jYWxlKGxvY2FsZXMsIGNhbmRpZGF0ZSkge1xuICByZXR1cm4gbG9jYWxlcy5pbmNsdWRlcyhjYW5kaWRhdGUpO1xufVxuXG5leHBvcnQgeyBjcmVhdGVDYWNoZSBhcyBfY3JlYXRlQ2FjaGUsIGNyZWF0ZUludGxGb3JtYXR0ZXJzIGFzIF9jcmVhdGVJbnRsRm9ybWF0dGVycywgY3JlYXRlVHJhbnNsYXRvciwgaGFzTG9jYWxlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/esm/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I: () => (/* binding */ IntlError),\n/* harmony export */   a: () => (/* binding */ IntlErrorCode),\n/* harmony export */   b: () => (/* binding */ createIntlFormatters),\n/* harmony export */   c: () => (/* binding */ createFormatter),\n/* harmony export */   d: () => (/* binding */ createCache),\n/* harmony export */   e: () => (/* binding */ createBaseTranslator),\n/* harmony export */   f: () => (/* binding */ defaultGetMessageFallback),\n/* harmony export */   g: () => (/* binding */ defaultOnError),\n/* harmony export */   i: () => (/* binding */ initializeConfig),\n/* harmony export */   r: () => (/* binding */ resolveNamespace)\n/* harmony export */ });\n/* harmony import */ var intl_messageformat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\n\n\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.date;\n  const mfTimeDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.memoize)(fn, {\n    cache: createMemoCache(cache),\n    strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(result) ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\n");

/***/ })

};
;