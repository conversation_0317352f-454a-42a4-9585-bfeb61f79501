"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createNavigation)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _shared_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var _useBasePathname_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js\");\n\n\n\n\n\n\n\n\nfunction createNavigation(routing) {\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname$1() {\n    const pathname = (0,_useBasePathname_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(config);\n    const locale = (0,use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.getRoute)(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter$1() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const curLocale = (0,use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const nextPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          (0,_shared_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname: usePathname$1,\n    useRouter: useRouter$1,\n    getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useBasePathname)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n\n\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n  const locale = (0,use_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.getLocalePrefix)(locale, config.localePrefix);\n    const isPathnamePrefixed = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.hasPathnamePrefixed)(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.unprefixPathname)(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.getLocaleAsPrefix)(locale);\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.hasPathnamePrefixed)(localeAsPrefix, pathname)) {\n        unlocalizedPathname = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.unprefixPathname)(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseLink$1)\n/* harmony export */ });\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction BaseLink({ href, locale, localeCookie, onClick, prefetch, ...rest }, ref) {\n    const curLocale = (0,use_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    function onLinkClick(event) {\n        (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== \"production\") {\n            console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\");\n        }\n        prefetch = false;\n    }\n    // Somehow the types for `next/link` don't work as expected\n    // when `moduleResolution: \"nodenext\"` is used.\n    const Link = next_link__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Link, {\n        ref: ref,\n        href: href,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch,\n        ...rest\n    });\n}\nvar BaseLink$1 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(BaseLink);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createSharedNavigationFns)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/use.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_3__.receiveRoutingConfig)(routing || {});\n  {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.validateReceivedConfig)(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isLocalizableHref)(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(localePromiseOrValue) ? (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      },\n      // Always include a prefix when changing locales\n      forcePrefix: locale != null || undefined\n    }) : pathname;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_BaseLink_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Link);\n  function getPathname(args) {\n    const {\n      forcePrefix,\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.serializeSearchParams)(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compileLocalizedPathname)({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.normalizeNameOrNameWithParams)(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.applyPathnamePrefix)(pathname, locale, config, forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect);\n  const permanentRedirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ syncLocaleCookie)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = `${name}=${nextLocale};`;\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += `${targetKey}`;\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ applyPathnamePrefix),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ compileLocalizedPathname),\n/* harmony export */   getBasePath: () => (/* binding */ getBasePath),\n/* harmony export */   getRoute: () => (/* binding */ getRoute),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ normalizeNameOrNameWithParams),\n/* harmony export */   serializeSearchParams: () => (/* binding */ serializeSearchParams),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ validateReceivedConfig)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-client/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlError),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlErrorCode),\n/* harmony export */   IntlProvider: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlProvider),\n/* harmony export */   _createCache: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createCache),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createIntlFormatters),\n/* harmony export */   createFormatter: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createFormatter),\n/* harmony export */   createTranslator: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createTranslator),\n/* harmony export */   hasLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.initializeConfig),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useLocale),\n/* harmony export */   useMessages: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useMessages),\n/* harmony export */   useNow: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useNow),\n/* harmony export */   useTimeZone: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/index.js\");\n\n\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore React attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', use_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations);\nconst useFormatter = callHook('useFormatter', use_intl__WEBPACK_IMPORTED_MODULE_1__.useFormatter);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LWNsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFnRztBQUN2RTs7QUFFekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLDBDQUEwQyxLQUFLOztBQUUvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0QscURBQWlCO0FBQ3JFLDhDQUE4QyxrREFBYzs7QUFFbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2xlY3VsYWItcHJvLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9yZWFjdC1jbGllbnQvaW5kZXguanM/OWM0ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VGb3JtYXR0ZXIgYXMgdXNlRm9ybWF0dGVyJDEsIHVzZVRyYW5zbGF0aW9ucyBhcyB1c2VUcmFuc2xhdGlvbnMkMSB9IGZyb20gJ3VzZS1pbnRsJztcbmV4cG9ydCAqIGZyb20gJ3VzZS1pbnRsJztcblxuLyoqXG4gKiBUaGlzIGlzIHRoZSBtYWluIGVudHJ5IGZpbGUgd2hlbiBub24tJ3JlYWN0LXNlcnZlcidcbiAqIGVudmlyb25tZW50cyBpbXBvcnQgZnJvbSAnbmV4dC1pbnRsJy5cbiAqXG4gKiBNYWludGFpbmVyIG5vdGVzOlxuICogLSBNYWtlIHN1cmUgdGhpcyBtaXJyb3JzIHRoZSBBUEkgZnJvbSAncmVhY3Qtc2VydmVyJy5cbiAqIC0gTWFrZSBzdXJlIGV2ZXJ5dGhpbmcgZXhwb3J0ZWQgZnJvbSB0aGlzIG1vZHVsZSBpc1xuICogICBzdXBwb3J0ZWQgaW4gYWxsIE5leHQuanMgdmVyc2lvbnMgdGhhdCBhcmUgc3VwcG9ydGVkLlxuICovXG5cblxuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtZnVuY3Rpb24tdHlwZVxuZnVuY3Rpb24gY2FsbEhvb2sobmFtZSwgaG9vaykge1xuICByZXR1cm4gKC4uLmFyZ3MpID0+IHtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIGhvb2soLi4uYXJncyk7XG4gICAgfSBjYXRjaCB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjYWxsIFxcYCR7bmFtZX1cXGAgYmVjYXVzZSB0aGUgY29udGV4dCBmcm9tIFxcYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJcXGAgd2FzIG5vdCBmb3VuZC5cblxuVGhpcyBjYW4gaGFwcGVuIGJlY2F1c2U6XG4xKSBZb3UgaW50ZW5kZWQgdG8gcmVuZGVyIHRoaXMgY29tcG9uZW50IGFzIGEgU2VydmVyIENvbXBvbmVudCwgdGhlIHJlbmRlclxuICAgZmFpbGVkLCBhbmQgdGhlcmVmb3JlIFJlYWN0IGF0dGVtcHRlZCB0byByZW5kZXIgdGhlIGNvbXBvbmVudCBvbiB0aGUgY2xpZW50XG4gICBpbnN0ZWFkLiBJZiB0aGlzIGlzIHRoZSBjYXNlLCBjaGVjayB0aGUgY29uc29sZSBmb3Igc2VydmVyIGVycm9ycy5cbjIpIFlvdSBpbnRlbmRlZCB0byByZW5kZXIgdGhpcyBjb21wb25lbnQgb24gdGhlIGNsaWVudCBzaWRlLCBidXQgbm8gY29udGV4dCB3YXMgZm91bmQuXG4gICBMZWFybiBtb3JlIGFib3V0IHRoaXMgZXJyb3IgaGVyZTogaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvZW52aXJvbm1lbnRzL3NlcnZlci1jbGllbnQtY29tcG9uZW50cyNtaXNzaW5nLWNvbnRleHRgICk7XG4gICAgfVxuICB9O1xufVxuY29uc3QgdXNlVHJhbnNsYXRpb25zID0gY2FsbEhvb2soJ3VzZVRyYW5zbGF0aW9ucycsIHVzZVRyYW5zbGF0aW9ucyQxKTtcbmNvbnN0IHVzZUZvcm1hdHRlciA9IGNhbGxIb29rKCd1c2VGb3JtYXR0ZXInLCB1c2VGb3JtYXR0ZXIkMSk7XG5cbmV4cG9ydCB7IHVzZUZvcm1hdHRlciwgdXNlVHJhbnNsYXRpb25zIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/config.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ receiveRoutingConfig)\n/* harmony export */ });\nfunction receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzP2M5MWYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVjZWl2ZVJvdXRpbmdDb25maWcoaW5wdXQpIHtcbiAgcmV0dXJuIHtcbiAgICAuLi5pbnB1dCxcbiAgICBsb2NhbGVQcmVmaXg6IHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcoaW5wdXQubG9jYWxlUHJlZml4KSxcbiAgICBsb2NhbGVDb29raWU6IHJlY2VpdmVMb2NhbGVDb29raWUoaW5wdXQubG9jYWxlQ29va2llKSxcbiAgICBsb2NhbGVEZXRlY3Rpb246IGlucHV0LmxvY2FsZURldGVjdGlvbiA/PyB0cnVlLFxuICAgIGFsdGVybmF0ZUxpbmtzOiBpbnB1dC5hbHRlcm5hdGVMaW5rcyA/PyB0cnVlXG4gIH07XG59XG5mdW5jdGlvbiByZWNlaXZlTG9jYWxlQ29va2llKGxvY2FsZUNvb2tpZSkge1xuICByZXR1cm4gbG9jYWxlQ29va2llID8/IHRydWUgPyB7XG4gICAgbmFtZTogJ05FWFRfTE9DQUxFJyxcbiAgICBzYW1lU2l0ZTogJ2xheCcsXG4gICAgLi4uKHR5cGVvZiBsb2NhbGVDb29raWUgPT09ICdvYmplY3QnICYmIGxvY2FsZUNvb2tpZSlcblxuICAgIC8vIGBwYXRoYCBuZWVkcyB0byBiZSBwcm92aWRlZCBiYXNlZCBvbiBhIGRldGVjdGVkIGJhc2UgcGF0aFxuICAgIC8vIHRoYXQgZGVwZW5kcyBvbiB0aGUgZW52aXJvbm1lbnQgd2hlbiBzZXR0aW5nIGEgY29va2llXG4gIH0gOiBmYWxzZTtcbn1cbmZ1bmN0aW9uIHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcobG9jYWxlUHJlZml4KSB7XG4gIHJldHVybiB0eXBlb2YgbG9jYWxlUHJlZml4ID09PSAnb2JqZWN0JyA/IGxvY2FsZVByZWZpeCA6IHtcbiAgICBtb2RlOiBsb2NhbGVQcmVmaXggfHwgJ2Fsd2F5cydcbiAgfTtcbn1cblxuZXhwb3J0IHsgcmVjZWl2ZVJvdXRpbmdDb25maWcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/defineRouting.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defineRouting)\n/* harmony export */ });\nfunction defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxS0FBcUssT0FBTyxnQkFBZ0IscUNBQXFDO0FBQ2pPO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcz9hY2U2Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGRlZmluZVJvdXRpbmcoY29uZmlnKSB7XG4gIGlmIChjb25maWcuZG9tYWlucykge1xuICAgIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihjb25maWcuZG9tYWlucyk7XG4gIH1cbiAgcmV0dXJuIGNvbmZpZztcbn1cbmZ1bmN0aW9uIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihkb21haW5zKSB7XG4gIGNvbnN0IGRvbWFpbnNCeUxvY2FsZSA9IG5ldyBNYXAoKTtcbiAgZm9yIChjb25zdCB7XG4gICAgZG9tYWluLFxuICAgIGxvY2FsZXNcbiAgfSBvZiBkb21haW5zKSB7XG4gICAgZm9yIChjb25zdCBsb2NhbGUgb2YgbG9jYWxlcykge1xuICAgICAgY29uc3QgbG9jYWxlRG9tYWlucyA9IGRvbWFpbnNCeUxvY2FsZS5nZXQobG9jYWxlKSB8fCBuZXcgU2V0KCk7XG4gICAgICBsb2NhbGVEb21haW5zLmFkZChkb21haW4pO1xuICAgICAgZG9tYWluc0J5TG9jYWxlLnNldChsb2NhbGUsIGxvY2FsZURvbWFpbnMpO1xuICAgIH1cbiAgfVxuICBjb25zdCBkdXBsaWNhdGVMb2NhbGVNZXNzYWdlcyA9IEFycmF5LmZyb20oZG9tYWluc0J5TG9jYWxlLmVudHJpZXMoKSkuZmlsdGVyKChbLCBsb2NhbGVEb21haW5zXSkgPT4gbG9jYWxlRG9tYWlucy5zaXplID4gMSkubWFwKChbbG9jYWxlLCBsb2NhbGVEb21haW5zXSkgPT4gYC0gXCIke2xvY2FsZX1cIiBpcyB1c2VkIGJ5OiAke0FycmF5LmZyb20obG9jYWxlRG9tYWlucykuam9pbignLCAnKX1gKTtcbiAgaWYgKGR1cGxpY2F0ZUxvY2FsZU1lc3NhZ2VzLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLndhcm4oJ0xvY2FsZXMgYXJlIGV4cGVjdGVkIHRvIGJlIHVuaXF1ZSBwZXIgZG9tYWluLCBidXQgZm91bmQgb3ZlcmxhcDpcXG4nICsgZHVwbGljYXRlTG9jYWxlTWVzc2FnZXMuam9pbignXFxuJykgKyAnXFxuUGxlYXNlIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9yb3V0aW5nI2RvbWFpbnMnKTtcbiAgfVxufVxuXG5leHBvcnQgeyBkZWZpbmVSb3V0aW5nIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider({ locale, ...rest }) {\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs2REFDOEM7QUFDTjtBQUV4QyxTQUFTRSx1QkFBdUIsRUFDOUJDLE1BQU0sRUFDTixHQUFHQyxNQUNKO0lBQ0MsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtBQUU2QyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzPzIzMzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgeyBJbnRsUHJvdmlkZXIgfSBmcm9tICd1c2UtaW50bC9yZWFjdCc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdyZWFjdC9qc3gtcnVudGltZSc7XG5cbmZ1bmN0aW9uIE5leHRJbnRsQ2xpZW50UHJvdmlkZXIoe1xuICBsb2NhbGUsXG4gIC4uLnJlc3Rcbn0pIHtcbiAgaWYgKCFsb2NhbGUpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBpbmZlciB0aGUgYGxvY2FsZWAgcHJvcCBpbiBgTmV4dEludGxDbGllbnRQcm92aWRlcmAsIHBsZWFzZSBwcm92aWRlIGl0IGV4cGxpY2l0bHkuXFxuXFxuU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbG9jYWxlXCIgKTtcbiAgfVxuICByZXR1cm4gLyojX19QVVJFX18qL2pzeChJbnRsUHJvdmlkZXIsIHtcbiAgICBsb2NhbGU6IGxvY2FsZSxcbiAgICAuLi5yZXN0XG4gIH0pO1xufVxuXG5leHBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6WyJJbnRsUHJvdmlkZXIiLCJqc3giLCJOZXh0SW50bENsaWVudFByb3ZpZGVyIiwibG9jYWxlIiwicmVzdCIsIkVycm9yIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanM/ODg0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyByZWFjdCBmcm9tICdyZWFjdCc7XG5cbi8vIEB0cy1leHBlY3QtZXJyb3IgLS0gT29vZiwgTmV4dC5qcyBkb2Vzbid0IG1ha2UgdGhpcyBlYXN5LlxuLy8gYHVzZWAgaXMgb25seSBhdmFpbGFibGUgaW4gUmVhY3QgMTkgY2FuYXJ5LCBidXQgd2UgY2FuXG4vLyB1c2UgaXQgaW4gTmV4dC5qcyBhbHJlYWR5IGFzIE5leHQuanMgXCJ2ZW5kb3JzXCIgYSBmaXhlZFxuLy8gdmVyc2lvbiBvZiBSZWFjdC4gSG93ZXZlciwgaWYgd2UnZCBzaW1wbHkgcHV0IGB1c2VgIGluXG4vLyBFU00gY29kZSwgdGhlbiB0aGUgYnVpbGQgZG9lc24ndCB3b3JrIHNpbmNlIFJlYWN0IGRvZXNcbi8vIG5vdCBleHBvcnQgYHVzZWAgb2ZmaWNpYWxseS4gVGhlcmVmb3JlLCB3ZSBoYXZlIHRvIHVzZVxuLy8gc29tZXRoaW5nIHRoYXQgaXMgbm90IHN0YXRpY2FsbHkgYW5hbHl6YWJsZS4gT25jZSBSZWFjdFxuLy8gMTkgaXMgb3V0LCB3ZSBjYW4gcmVtb3ZlIHRoaXMgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi5cbnZhciB1c2UgPSByZWFjdFsndXNlJy50cmltKCldO1xuXG5leHBvcnQgeyB1c2UgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createNavigation)\n/* harmony export */ });\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getServerLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js\");\n\n\n\nfunction createNavigation(routing) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const {\n    config,\n    ...fns\n  } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], routing);\n  function notSupported(hookName) {\n    return () => {\n      throw new Error(`\\`${hookName}\\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`);\n    };\n  }\n  return {\n    ...fns,\n    usePathname: notSupported('usePathname'),\n    useRouter: notSupported('useRouter')\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStFO0FBQzVCOztBQUVuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxFQUFFLGdGQUF5QixDQUFDLDJEQUFlO0FBQy9DO0FBQ0E7QUFDQSwyQkFBMkIsU0FBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanM/Y2VjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucyBmcm9tICcuLi9zaGFyZWQvY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucy5qcyc7XG5pbXBvcnQgZ2V0U2VydmVyTG9jYWxlIGZyb20gJy4vZ2V0U2VydmVyTG9jYWxlLmpzJztcblxuZnVuY3Rpb24gY3JlYXRlTmF2aWdhdGlvbihyb3V0aW5nKSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW51c2VkLXZhcnNcbiAgY29uc3Qge1xuICAgIGNvbmZpZyxcbiAgICAuLi5mbnNcbiAgfSA9IGNyZWF0ZVNoYXJlZE5hdmlnYXRpb25GbnMoZ2V0U2VydmVyTG9jYWxlLCByb3V0aW5nKTtcbiAgZnVuY3Rpb24gbm90U3VwcG9ydGVkKGhvb2tOYW1lKSB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtob29rTmFtZX1cXGAgaXMgbm90IHN1cHBvcnRlZCBpbiBTZXJ2ZXIgQ29tcG9uZW50cy4gWW91IGNhbiB1c2UgdGhpcyBob29rIGlmIHlvdSBjb252ZXJ0IHRoZSBjYWxsaW5nIGNvbXBvbmVudCB0byBhIENsaWVudCBDb21wb25lbnQuYCk7XG4gICAgfTtcbiAgfVxuICByZXR1cm4ge1xuICAgIC4uLmZucyxcbiAgICB1c2VQYXRobmFtZTogbm90U3VwcG9ydGVkKCd1c2VQYXRobmFtZScpLFxuICAgIHVzZVJvdXRlcjogbm90U3VwcG9ydGVkKCd1c2VSb3V0ZXInKVxuICB9O1xufVxuXG5leHBvcnQgeyBjcmVhdGVOYXZpZ2F0aW9uIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getServerLocale)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n/**\n * This is only moved to a separate module for easier mocking in\n * `../createNavigatoin.test.tsx` in order to avoid suspending.\n */\nasync function getServerLocale() {\n  const config = await (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n  return config.locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErRDs7QUFFL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw2RUFBUztBQUNoQztBQUNBOztBQUVzQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcz9jOThlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRDb25maWcgZnJvbSAnLi4vLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanMnO1xuXG4vKipcbiAqIFRoaXMgaXMgb25seSBtb3ZlZCB0byBhIHNlcGFyYXRlIG1vZHVsZSBmb3IgZWFzaWVyIG1vY2tpbmcgaW5cbiAqIGAuLi9jcmVhdGVOYXZpZ2F0b2luLnRlc3QudHN4YCBpbiBvcmRlciB0byBhdm9pZCBzdXNwZW5kaW5nLlxuICovXG5hc3luYyBmdW5jdGlvbiBnZXRTZXJ2ZXJMb2NhbGUoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZygpO1xuICByZXR1cm4gY29uZmlnLmxvY2FsZTtcbn1cblxuZXhwb3J0IHsgZ2V0U2VydmVyTG9jYWxlIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createSharedNavigationFns)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/use.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_3__.receiveRoutingConfig)(routing || {});\n  {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.validateReceivedConfig)(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isLocalizableHref)(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(localePromiseOrValue) ? (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      },\n      // Always include a prefix when changing locales\n      forcePrefix: locale != null || undefined\n    }) : pathname;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_BaseLink_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Link);\n  function getPathname(args) {\n    const {\n      forcePrefix,\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.serializeSearchParams)(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compileLocalizedPathname)({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.normalizeNameOrNameWithParams)(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.applyPathnamePrefix)(pathname, locale, config, forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect);\n  const permanentRedirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ applyPathnamePrefix),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ compileLocalizedPathname),\n/* harmony export */   getBasePath: () => (/* binding */ getBasePath),\n/* harmony export */   getRoute: () => (/* binding */ getRoute),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ normalizeNameOrNameWithParams),\n/* harmony export */   serializeSearchParams: () => (/* binding */ serializeSearchParams),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ validateReceivedConfig)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProviderServer)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getConfigNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\");\n/* harmony import */ var _server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/react-server/getFormats.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\");\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\");\n/* harmony import */ var _server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\");\n\n\n\n\n\n\n\n\nasync function NextIntlClientProviderServer({\n  formats,\n  locale,\n  messages,\n  now,\n  timeZone,\n  ...rest\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n  // We need to be careful about potentially reading from headers here.\n  // See https://github.com/amannn/next-intl/issues/631\n  , {\n    formats: formats === undefined ? await (0,_server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])() : formats,\n    locale: locale ?? (await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])()),\n    messages: messages === undefined ? await (0,_server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])() : messages\n    // Note that we don't assign a default for `now` here,\n    // we only read one from the request config - if any.\n    // Otherwise this would cause a `dynamicIO` error.\n    ,\n    now: now ?? (await (0,_server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()),\n    timeZone: timeZone ?? (await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])()),\n    ...rest\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWtFO0FBQ0o7QUFDVztBQUNqQztBQUN3QjtBQUNBO0FBQ0U7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELHNCQUFzQixzREFBRyxDQUFDLHlFQUFzQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsOEVBQVU7QUFDckQsNkJBQTZCLDZFQUFlO0FBQzVDLDZDQUE2QywrRUFBVztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBWTtBQUNuQyxpQ0FBaUMsK0VBQVc7QUFDNUM7QUFDQSxHQUFHO0FBQ0g7O0FBRW1EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvcmVhY3Qtc2VydmVyL05leHRJbnRsQ2xpZW50UHJvdmlkZXJTZXJ2ZXIuanM/YTkzZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0Q29uZmlnTm93IGZyb20gJy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzJztcbmltcG9ydCBnZXRGb3JtYXRzIGZyb20gJy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyc7XG5pbXBvcnQgTmV4dEludGxDbGllbnRQcm92aWRlciBmcm9tICcuLi9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qcyc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdyZWFjdC9qc3gtcnVudGltZSc7XG5pbXBvcnQgZ2V0VGltZVpvbmUgZnJvbSAnLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qcyc7XG5pbXBvcnQgZ2V0TWVzc2FnZXMgZnJvbSAnLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRNZXNzYWdlcy5qcyc7XG5pbXBvcnQgZ2V0TG9jYWxlQ2FjaGVkIGZyb20gJy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlcih7XG4gIGZvcm1hdHMsXG4gIGxvY2FsZSxcbiAgbWVzc2FnZXMsXG4gIG5vdyxcbiAgdGltZVpvbmUsXG4gIC4uLnJlc3Rcbn0pIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9qc3goTmV4dEludGxDbGllbnRQcm92aWRlclxuICAvLyBXZSBuZWVkIHRvIGJlIGNhcmVmdWwgYWJvdXQgcG90ZW50aWFsbHkgcmVhZGluZyBmcm9tIGhlYWRlcnMgaGVyZS5cbiAgLy8gU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9hbWFubm4vbmV4dC1pbnRsL2lzc3Vlcy82MzFcbiAgLCB7XG4gICAgZm9ybWF0czogZm9ybWF0cyA9PT0gdW5kZWZpbmVkID8gYXdhaXQgZ2V0Rm9ybWF0cygpIDogZm9ybWF0cyxcbiAgICBsb2NhbGU6IGxvY2FsZSA/PyAoYXdhaXQgZ2V0TG9jYWxlQ2FjaGVkKCkpLFxuICAgIG1lc3NhZ2VzOiBtZXNzYWdlcyA9PT0gdW5kZWZpbmVkID8gYXdhaXQgZ2V0TWVzc2FnZXMoKSA6IG1lc3NhZ2VzXG4gICAgLy8gTm90ZSB0aGF0IHdlIGRvbid0IGFzc2lnbiBhIGRlZmF1bHQgZm9yIGBub3dgIGhlcmUsXG4gICAgLy8gd2Ugb25seSByZWFkIG9uZSBmcm9tIHRoZSByZXF1ZXN0IGNvbmZpZyAtIGlmIGFueS5cbiAgICAvLyBPdGhlcndpc2UgdGhpcyB3b3VsZCBjYXVzZSBhIGBkeW5hbWljSU9gIGVycm9yLlxuICAgICxcbiAgICBub3c6IG5vdyA/PyAoYXdhaXQgZ2V0Q29uZmlnTm93KCkpLFxuICAgIHRpbWVab25lOiB0aW1lWm9uZSA/PyAoYXdhaXQgZ2V0VGltZVpvbmUoKSksXG4gICAgLi4ucmVzdFxuICB9KTtcbn1cblxuZXhwb3J0IHsgTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlciBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/config.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ receiveRoutingConfig)\n/* harmony export */ });\nfunction receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzPzE3NjYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVjZWl2ZVJvdXRpbmdDb25maWcoaW5wdXQpIHtcbiAgcmV0dXJuIHtcbiAgICAuLi5pbnB1dCxcbiAgICBsb2NhbGVQcmVmaXg6IHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcoaW5wdXQubG9jYWxlUHJlZml4KSxcbiAgICBsb2NhbGVDb29raWU6IHJlY2VpdmVMb2NhbGVDb29raWUoaW5wdXQubG9jYWxlQ29va2llKSxcbiAgICBsb2NhbGVEZXRlY3Rpb246IGlucHV0LmxvY2FsZURldGVjdGlvbiA/PyB0cnVlLFxuICAgIGFsdGVybmF0ZUxpbmtzOiBpbnB1dC5hbHRlcm5hdGVMaW5rcyA/PyB0cnVlXG4gIH07XG59XG5mdW5jdGlvbiByZWNlaXZlTG9jYWxlQ29va2llKGxvY2FsZUNvb2tpZSkge1xuICByZXR1cm4gbG9jYWxlQ29va2llID8/IHRydWUgPyB7XG4gICAgbmFtZTogJ05FWFRfTE9DQUxFJyxcbiAgICBzYW1lU2l0ZTogJ2xheCcsXG4gICAgLi4uKHR5cGVvZiBsb2NhbGVDb29raWUgPT09ICdvYmplY3QnICYmIGxvY2FsZUNvb2tpZSlcblxuICAgIC8vIGBwYXRoYCBuZWVkcyB0byBiZSBwcm92aWRlZCBiYXNlZCBvbiBhIGRldGVjdGVkIGJhc2UgcGF0aFxuICAgIC8vIHRoYXQgZGVwZW5kcyBvbiB0aGUgZW52aXJvbm1lbnQgd2hlbiBzZXR0aW5nIGEgY29va2llXG4gIH0gOiBmYWxzZTtcbn1cbmZ1bmN0aW9uIHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcobG9jYWxlUHJlZml4KSB7XG4gIHJldHVybiB0eXBlb2YgbG9jYWxlUHJlZml4ID09PSAnb2JqZWN0JyA/IGxvY2FsZVByZWZpeCA6IHtcbiAgICBtb2RlOiBsb2NhbGVQcmVmaXggfHwgJ2Fsd2F5cydcbiAgfTtcbn1cblxuZXhwb3J0IHsgcmVjZWl2ZVJvdXRpbmdDb25maWcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/defineRouting.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defineRouting)\n/* harmony export */ });\nfunction defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxS0FBcUssT0FBTyxnQkFBZ0IscUNBQXFDO0FBQ2pPO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcz9mMDk4Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGRlZmluZVJvdXRpbmcoY29uZmlnKSB7XG4gIGlmIChjb25maWcuZG9tYWlucykge1xuICAgIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihjb25maWcuZG9tYWlucyk7XG4gIH1cbiAgcmV0dXJuIGNvbmZpZztcbn1cbmZ1bmN0aW9uIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihkb21haW5zKSB7XG4gIGNvbnN0IGRvbWFpbnNCeUxvY2FsZSA9IG5ldyBNYXAoKTtcbiAgZm9yIChjb25zdCB7XG4gICAgZG9tYWluLFxuICAgIGxvY2FsZXNcbiAgfSBvZiBkb21haW5zKSB7XG4gICAgZm9yIChjb25zdCBsb2NhbGUgb2YgbG9jYWxlcykge1xuICAgICAgY29uc3QgbG9jYWxlRG9tYWlucyA9IGRvbWFpbnNCeUxvY2FsZS5nZXQobG9jYWxlKSB8fCBuZXcgU2V0KCk7XG4gICAgICBsb2NhbGVEb21haW5zLmFkZChkb21haW4pO1xuICAgICAgZG9tYWluc0J5TG9jYWxlLnNldChsb2NhbGUsIGxvY2FsZURvbWFpbnMpO1xuICAgIH1cbiAgfVxuICBjb25zdCBkdXBsaWNhdGVMb2NhbGVNZXNzYWdlcyA9IEFycmF5LmZyb20oZG9tYWluc0J5TG9jYWxlLmVudHJpZXMoKSkuZmlsdGVyKChbLCBsb2NhbGVEb21haW5zXSkgPT4gbG9jYWxlRG9tYWlucy5zaXplID4gMSkubWFwKChbbG9jYWxlLCBsb2NhbGVEb21haW5zXSkgPT4gYC0gXCIke2xvY2FsZX1cIiBpcyB1c2VkIGJ5OiAke0FycmF5LmZyb20obG9jYWxlRG9tYWlucykuam9pbignLCAnKX1gKTtcbiAgaWYgKGR1cGxpY2F0ZUxvY2FsZU1lc3NhZ2VzLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLndhcm4oJ0xvY2FsZXMgYXJlIGV4cGVjdGVkIHRvIGJlIHVuaXF1ZSBwZXIgZG9tYWluLCBidXQgZm91bmQgb3ZlcmxhcDpcXG4nICsgZHVwbGljYXRlTG9jYWxlTWVzc2FnZXMuam9pbignXFxuJykgKyAnXFxuUGxlYXNlIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9yb3V0aW5nI2RvbWFpbnMnKTtcbiAgfVxufVxuXG5leHBvcnQgeyBkZWZpbmVSb3V0aW5nIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ getRequestLocale)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\n\nasync function getHeadersImpl() {\n  const promiseOrValue = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n\n  // Compatibility with Next.js <15\n  return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || (await getLocaleFromHeader());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBdUM7QUFDVDtBQUNpQztBQUNiO0FBQ2U7O0FBRWpFO0FBQ0EseUJBQXlCLHFEQUFPOztBQUVoQztBQUNBLFNBQVMsMkRBQVM7QUFDbEI7QUFDQSxtQkFBbUIsNENBQUs7QUFDeEI7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLG9FQUFrQjtBQUN4RCxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw0Q0FBSztBQUNqQztBQUNBLFNBQVMsOEVBQXNCO0FBQy9COztBQUU0QiIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcz8xZWEzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGhlYWRlcnMgfSBmcm9tICduZXh0L2hlYWRlcnMnO1xuaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBIRUFERVJfTE9DQUxFX05BTUUgfSBmcm9tICcuLi8uLi9zaGFyZWQvY29uc3RhbnRzLmpzJztcbmltcG9ydCB7IGlzUHJvbWlzZSB9IGZyb20gJy4uLy4uL3NoYXJlZC91dGlscy5qcyc7XG5pbXBvcnQgeyBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlIH0gZnJvbSAnLi9SZXF1ZXN0TG9jYWxlQ2FjaGUuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRIZWFkZXJzSW1wbCgpIHtcbiAgY29uc3QgcHJvbWlzZU9yVmFsdWUgPSBoZWFkZXJzKCk7XG5cbiAgLy8gQ29tcGF0aWJpbGl0eSB3aXRoIE5leHQuanMgPDE1XG4gIHJldHVybiBpc1Byb21pc2UocHJvbWlzZU9yVmFsdWUpID8gYXdhaXQgcHJvbWlzZU9yVmFsdWUgOiBwcm9taXNlT3JWYWx1ZTtcbn1cbmNvbnN0IGdldEhlYWRlcnMgPSBjYWNoZShnZXRIZWFkZXJzSW1wbCk7XG5hc3luYyBmdW5jdGlvbiBnZXRMb2NhbGVGcm9tSGVhZGVySW1wbCgpIHtcbiAgbGV0IGxvY2FsZTtcbiAgdHJ5IHtcbiAgICBsb2NhbGUgPSAoYXdhaXQgZ2V0SGVhZGVycygpKS5nZXQoSEVBREVSX0xPQ0FMRV9OQU1FKSB8fCB1bmRlZmluZWQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IuZGlnZXN0ID09PSAnRFlOQU1JQ19TRVJWRVJfVVNBR0UnKSB7XG4gICAgICBjb25zdCB3cmFwcGVkRXJyb3IgPSBuZXcgRXJyb3IoJ1VzYWdlIG9mIG5leHQtaW50bCBBUElzIGluIFNlcnZlciBDb21wb25lbnRzIGN1cnJlbnRseSBvcHRzIGludG8gZHluYW1pYyByZW5kZXJpbmcuIFRoaXMgbGltaXRhdGlvbiB3aWxsIGV2ZW50dWFsbHkgYmUgbGlmdGVkLCBidXQgYXMgYSBzdG9wZ2FwIHNvbHV0aW9uLCB5b3UgY2FuIHVzZSB0aGUgYHNldFJlcXVlc3RMb2NhbGVgIEFQSSB0byBlbmFibGUgc3RhdGljIHJlbmRlcmluZywgc2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2dldHRpbmctc3RhcnRlZC9hcHAtcm91dGVyL3dpdGgtaTE4bi1yb3V0aW5nI3N0YXRpYy1yZW5kZXJpbmcnLCB7XG4gICAgICAgIGNhdXNlOiBlcnJvclxuICAgICAgfSk7XG4gICAgICB3cmFwcGVkRXJyb3IuZGlnZXN0ID0gZXJyb3IuZGlnZXN0O1xuICAgICAgdGhyb3cgd3JhcHBlZEVycm9yO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGxvY2FsZTtcbn1cbmNvbnN0IGdldExvY2FsZUZyb21IZWFkZXIgPSBjYWNoZShnZXRMb2NhbGVGcm9tSGVhZGVySW1wbCk7XG5hc3luYyBmdW5jdGlvbiBnZXRSZXF1ZXN0TG9jYWxlKCkge1xuICByZXR1cm4gZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSgpIHx8IChhd2FpdCBnZXRMb2NhbGVGcm9tSGVhZGVyKCkpO1xufVxuXG5leHBvcnQgeyBnZXRSZXF1ZXN0TG9jYWxlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ getCachedRequestLocale),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ setCachedRequestLocale)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNENBQUs7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwRCIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzPzY5OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5cbi8vIFNlZSBodHRwczovL2dpdGh1Yi5jb20vdmVyY2VsL25leHQuanMvZGlzY3Vzc2lvbnMvNTg4NjJcbmZ1bmN0aW9uIGdldENhY2hlSW1wbCgpIHtcbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgbG9jYWxlOiB1bmRlZmluZWRcbiAgfTtcbiAgcmV0dXJuIHZhbHVlO1xufVxuY29uc3QgZ2V0Q2FjaGUgPSBjYWNoZShnZXRDYWNoZUltcGwpO1xuZnVuY3Rpb24gZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSgpIHtcbiAgcmV0dXJuIGdldENhY2hlKCkubG9jYWxlO1xufVxuZnVuY3Rpb24gc2V0Q2FjaGVkUmVxdWVzdExvY2FsZShsb2NhbGUpIHtcbiAgZ2V0Q2FjaGUoKS5sb2NhbGUgPSBsb2NhbGU7XG59XG5cbmV4cG9ydCB7IGdldENhY2hlZFJlcXVlc3RMb2NhbGUsIHNldENhY2hlZFJlcXVlc3RMb2NhbGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./src/i18n/config.ts\");\n/* harmony import */ var _validateLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\");\n\n\n\n\n\n\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__.getRequestLocale)();\n    }\n  };\n  let result = getConfig(params);\n  if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    (0,_validateLocale_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(receiveRuntimeConfigImpl);\nconst getFormatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.b);\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.d);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(next_intl_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"], localeOverride);\n  return {\n    ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.i)(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfigNow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getConfigNowImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.now;\n}\nconst getConfigNow = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigNowImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EscUJBQXFCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWdOb3cuanM/OWRkMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRDb25maWdOb3dJbXBsKGxvY2FsZSkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcobG9jYWxlKTtcbiAgcmV0dXJuIGNvbmZpZy5ub3c7XG59XG5jb25zdCBnZXRDb25maWdOb3cgPSBjYWNoZShnZXRDb25maWdOb3dJbXBsKTtcblxuZXhwb3J0IHsgZ2V0Q29uZmlnTm93IGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getFormatsCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.formats;\n}\nconst getFormats = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatsCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDUzs7QUFFdkM7QUFDQSx1QkFBdUIseURBQVM7QUFDaEM7QUFDQTtBQUNBLG1CQUFtQiw0Q0FBSzs7QUFFUyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcz9jMzZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldEZvcm1hdHNDYWNoZWRJbXBsKCkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcoKTtcbiAgcmV0dXJuIGNvbmZpZy5mb3JtYXRzO1xufVxuY29uc3QgZ2V0Rm9ybWF0cyA9IGNhY2hlKGdldEZvcm1hdHNDYWNoZWRJbXBsKTtcblxuZXhwb3J0IHsgZ2V0Rm9ybWF0cyBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getLocaleCached)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getLocaleCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.locale;\n}\nconst getLocaleCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getLocaleCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0Esd0JBQXdCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2VydmVyL3JlYWN0LXNlcnZlci9nZXRMb2NhbGUuanM/MWU5NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRMb2NhbGVDYWNoZWRJbXBsKCkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcoKTtcbiAgcmV0dXJuIGNvbmZpZy5sb2NhbGU7XG59XG5jb25zdCBnZXRMb2NhbGVDYWNoZWQgPSBjYWNoZShnZXRMb2NhbGVDYWNoZWRJbXBsKTtcblxuZXhwb3J0IHsgZ2V0TG9jYWxlQ2FjaGVkIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getMessages),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ getMessagesFromConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nfunction getMessagesFromConfig(config) {\n  if (!config.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');\n  }\n  return config.messages;\n}\nasync function getMessagesCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return getMessagesFromConfig(config);\n}\nconst getMessagesCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getMessagesCachedImpl);\nasync function getMessages(opts) {\n  return getMessagesCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EsMEJBQTBCLDRDQUFLO0FBQy9CO0FBQ0E7QUFDQTs7QUFFeUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2xlY3VsYWItcHJvLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldE1lc3NhZ2VzLmpzPzkwMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuZnVuY3Rpb24gZ2V0TWVzc2FnZXNGcm9tQ29uZmlnKGNvbmZpZykge1xuICBpZiAoIWNvbmZpZy5tZXNzYWdlcykge1xuICAgIHRocm93IG5ldyBFcnJvcignTm8gbWVzc2FnZXMgZm91bmQuIEhhdmUgeW91IGNvbmZpZ3VyZWQgdGhlbSBjb3JyZWN0bHk/IFNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9jb25maWd1cmF0aW9uI21lc3NhZ2VzJyk7XG4gIH1cbiAgcmV0dXJuIGNvbmZpZy5tZXNzYWdlcztcbn1cbmFzeW5jIGZ1bmN0aW9uIGdldE1lc3NhZ2VzQ2FjaGVkSW1wbChsb2NhbGUpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKGxvY2FsZSk7XG4gIHJldHVybiBnZXRNZXNzYWdlc0Zyb21Db25maWcoY29uZmlnKTtcbn1cbmNvbnN0IGdldE1lc3NhZ2VzQ2FjaGVkID0gY2FjaGUoZ2V0TWVzc2FnZXNDYWNoZWRJbXBsKTtcbmFzeW5jIGZ1bmN0aW9uIGdldE1lc3NhZ2VzKG9wdHMpIHtcbiAgcmV0dXJuIGdldE1lc3NhZ2VzQ2FjaGVkKG9wdHM/LmxvY2FsZSk7XG59XG5cbmV4cG9ydCB7IGdldE1lc3NhZ2VzIGFzIGRlZmF1bHQsIGdldE1lc3NhZ2VzRnJvbUNvbmZpZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getRequestConfig)\n/* harmony export */ });\n/**\n * Should be called in `i18n/request.ts` to create the configuration for the current request.\n */\nfunction getRequestConfig(createRequestConfig) {\n  return createRequestConfig;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcz8zYmFhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU2hvdWxkIGJlIGNhbGxlZCBpbiBgaTE4bi9yZXF1ZXN0LnRzYCB0byBjcmVhdGUgdGhlIGNvbmZpZ3VyYXRpb24gZm9yIHRoZSBjdXJyZW50IHJlcXVlc3QuXG4gKi9cbmZ1bmN0aW9uIGdldFJlcXVlc3RDb25maWcoY3JlYXRlUmVxdWVzdENvbmZpZykge1xuICByZXR1cm4gY3JlYXRlUmVxdWVzdENvbmZpZztcbn1cblxuZXhwb3J0IHsgZ2V0UmVxdWVzdENvbmZpZyBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTimeZone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getTimeZoneCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.timeZone;\n}\nconst getTimeZoneCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTimeZoneCachedImpl);\nasync function getTimeZone(opts) {\n  return getTimeZoneCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBQ1M7O0FBRXZDO0FBQ0EsdUJBQXVCLHlEQUFTO0FBQ2hDO0FBQ0E7QUFDQSwwQkFBMEIsNENBQUs7QUFDL0I7QUFDQTtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanM/YzQ5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRUaW1lWm9uZUNhY2hlZEltcGwobG9jYWxlKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZyhsb2NhbGUpO1xuICByZXR1cm4gY29uZmlnLnRpbWVab25lO1xufVxuY29uc3QgZ2V0VGltZVpvbmVDYWNoZWQgPSBjYWNoZShnZXRUaW1lWm9uZUNhY2hlZEltcGwpO1xuYXN5bmMgZnVuY3Rpb24gZ2V0VGltZVpvbmUob3B0cykge1xuICByZXR1cm4gZ2V0VGltZVpvbmVDYWNoZWQob3B0cz8ubG9jYWxlKTtcbn1cblxuZXhwb3J0IHsgZ2V0VGltZVpvbmUgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ validateLocale)\n/* harmony export */ });\nfunction validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvdmFsaWRhdGVMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixzREFBc0QsT0FBTztBQUM3RDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvdmFsaWRhdGVMb2NhbGUuanM/MDdlNSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB2YWxpZGF0ZUxvY2FsZShsb2NhbGUpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBjb25zdHJ1Y3RlZCA9IG5ldyBJbnRsLkxvY2FsZShsb2NhbGUpO1xuICAgIGlmICghY29uc3RydWN0ZWQubGFuZ3VhZ2UpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTGFuZ3VhZ2UgaXMgcmVxdWlyZWQnKTtcbiAgICB9XG4gIH0gY2F0Y2gge1xuICAgIGNvbnNvbGUuZXJyb3IoYEFuIGludmFsaWQgbG9jYWxlIHdhcyBwcm92aWRlZDogXCIke2xvY2FsZX1cIlxcblBsZWFzZSBlbnN1cmUgeW91J3JlIHVzaW5nIGEgdmFsaWQgVW5pY29kZSBsb2NhbGUgaWRlbnRpZmllciAoZS5nLiBcImVuLVVTXCIpLmApO1xuICB9XG59XG5cbmV4cG9ydCB7IHZhbGlkYXRlTG9jYWxlIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/constants.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ HEADER_LOCALE_NAME)\n/* harmony export */ });\n// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcz82YzA5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFVzZWQgdG8gcmVhZCB0aGUgbG9jYWxlIGZyb20gdGhlIG1pZGRsZXdhcmVcbmNvbnN0IEhFQURFUl9MT0NBTEVfTkFNRSA9ICdYLU5FWFQtSU5UTC1MT0NBTEUnO1xuXG5leHBvcnQgeyBIRUFERVJfTE9DQUxFX05BTUUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanM/ZWEyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyByZWFjdCBmcm9tICdyZWFjdCc7XG5cbi8vIEB0cy1leHBlY3QtZXJyb3IgLS0gT29vZiwgTmV4dC5qcyBkb2Vzbid0IG1ha2UgdGhpcyBlYXN5LlxuLy8gYHVzZWAgaXMgb25seSBhdmFpbGFibGUgaW4gUmVhY3QgMTkgY2FuYXJ5LCBidXQgd2UgY2FuXG4vLyB1c2UgaXQgaW4gTmV4dC5qcyBhbHJlYWR5IGFzIE5leHQuanMgXCJ2ZW5kb3JzXCIgYSBmaXhlZFxuLy8gdmVyc2lvbiBvZiBSZWFjdC4gSG93ZXZlciwgaWYgd2UnZCBzaW1wbHkgcHV0IGB1c2VgIGluXG4vLyBFU00gY29kZSwgdGhlbiB0aGUgYnVpbGQgZG9lc24ndCB3b3JrIHNpbmNlIFJlYWN0IGRvZXNcbi8vIG5vdCBleHBvcnQgYHVzZWAgb2ZmaWNpYWxseS4gVGhlcmVmb3JlLCB3ZSBoYXZlIHRvIHVzZVxuLy8gc29tZXRoaW5nIHRoYXQgaXMgbm90IHN0YXRpY2FsbHkgYW5hbHl6YWJsZS4gT25jZSBSZWFjdFxuLy8gMTkgaXMgb3V0LCB3ZSBjYW4gcmVtb3ZlIHRoaXMgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi5cbnZhciB1c2UgPSByZWFjdFsndXNlJy50cmltKCldO1xuXG5leHBvcnQgeyB1c2UgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ })

};
;