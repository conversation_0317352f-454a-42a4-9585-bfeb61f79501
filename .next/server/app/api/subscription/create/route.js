(()=>{var e={};e.id=1,e.ids=[1],e.modules={3524:e=>{"use strict";e.exports=require("@prisma/client")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4770:e=>{"use strict";e.exports=require("crypto")},1764:e=>{"use strict";e.exports=require("util")},8978:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>oD,patchFetch:()=>oN,requestAsyncStorage:()=>ok,routeModule:()=>oj,serverHooks:()=>oI,staticGenerationAsyncStorage:()=>oG});var o={};r.r(o),r.d(o,{StripeAPIError:()=>M,StripeAuthenticationError:()=>U,StripeCardError:()=>D,StripeConnectionError:()=>L,StripeError:()=>I,StripeIdempotencyError:()=>$,StripeInvalidGrantError:()=>B,StripeInvalidRequestError:()=>N,StripePermissionError:()=>q,StripeRateLimitError:()=>F,StripeSignatureVerificationError:()=>H,StripeUnknownError:()=>W,generate:()=>G});var n={};r.r(n),r.d(n,{Account:()=>t$,AccountLinks:()=>tW,Accounts:()=>t$,ApplePayDomains:()=>tz,ApplicationFees:()=>tJ,Apps:()=>oa,Balance:()=>tX,BalanceTransactions:()=>tZ,BillingPortal:()=>os,Charges:()=>t0,Checkout:()=>ol,CountrySpecs:()=>t8,Coupons:()=>t3,CreditNotes:()=>t9,Customers:()=>t7,Disputes:()=>rt,EphemeralKeys:()=>ro,Events:()=>ri,ExchangeRates:()=>rs,FileLinks:()=>ru,Files:()=>rd,FinancialConnections:()=>ou,Identity:()=>oc,InvoiceItems:()=>rf,Invoices:()=>ry,Issuing:()=>op,Mandates:()=>rP,OAuth:()=>rT,PaymentIntents:()=>rb,PaymentLinks:()=>rO,PaymentMethods:()=>rw,Payouts:()=>rR,Plans:()=>rj,Prices:()=>rG,Products:()=>rD,PromotionCodes:()=>rM,Quotes:()=>rq,Radar:()=>od,Refunds:()=>rL,Reporting:()=>oh,Reviews:()=>r$,SetupAttempts:()=>rW,SetupIntents:()=>rz,ShippingRates:()=>rJ,Sigma:()=>of,Sources:()=>rX,SubscriptionItems:()=>rZ,SubscriptionSchedules:()=>r0,Subscriptions:()=>r8,Tax:()=>om,TaxCodes:()=>r3,TaxRates:()=>r9,Terminal:()=>oy,TestHelpers:()=>ov,Tokens:()=>r7,Topups:()=>ot,Transfers:()=>oo,Treasury:()=>oP,WebhookEndpoints:()=>oi});var i={};r.r(i),r.d(i,{POST:()=>oC});var a=r(9303),s=r(8716),l=r(670),u=r(7070),c=r(3524),p=r(4770);let d=require("events");class h{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}}class f extends Error{}class m extends h{computeHMACSignature(e,t){return p.createHmac("sha256",t).update(e,"utf8").digest("hex")}async computeHMACSignatureAsync(e,t){return await this.computeHMACSignature(e,t)}}let y=require("http");var v=r.t(y,2);let P=require("https");var g=r.t(P,2);class _{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(_.TIMEOUT_ERROR_CODE);return e.code=_.TIMEOUT_ERROR_CODE,e}}_.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],_.TIMEOUT_ERROR_CODE="ETIMEDOUT";class T{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}let E=y||v,b=P||g,S=new E.Agent({keepAlive:!0}),O=new b.Agent({keepAlive:!0});class x extends _{constructor(e){super(),this._agent=e}getClientName(){return"node"}makeRequest(e,t,r,o,n,i,a,s){let l="http"===a,u=this._agent;return u||(u=l?S:O),new Promise((a,c)=>{let p=(l?E:b).request({host:e,port:t,path:r,method:o,agent:u,headers:n,ciphers:"DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5"});p.setTimeout(s,()=>{p.destroy(_.makeTimeoutError())}),p.on("response",e=>{a(new w(e))}),p.on("error",e=>{c(e)}),p.once("socket",e=>{e.connecting?e.once(l?"connect":"secureConnect",()=>{p.write(i),p.end()}):(p.write(i),p.end())})})}}class w extends T{constructor(e){super(e.statusCode,e.headers||{}),this._res=e}getRawResponse(){return this._res}toStream(e){return this._res.once("end",()=>e()),this._res}toJSON(){return new Promise((e,t)=>{let r="";this._res.setEncoding("utf8"),this._res.on("data",e=>{r+=e}),this._res.once("end",()=>{try{e(JSON.parse(r))}catch(e){t(e)}})})}}class A extends _{constructor(e){super(),this._fetchFn=e}getClientName(){return"fetch"}makeRequest(e,t,r,o,n,i,a,s){let l;let u=new URL(r,`${"http"===a?"http":"https"}://${e}`);u.port=t;let c="POST"==o||"PUT"==o||"PATCH"==o;return Promise.race([(this._fetchFn||fetch)(u.toString(),{method:o,headers:n,body:i||(c?"":void 0)}),new Promise((e,t)=>{l=setTimeout(()=>{l=null,t(_.makeTimeoutError())},s)})]).then(e=>new R(e)).finally(()=>{l&&clearTimeout(l)})}}class R extends T{constructor(e){super(e.status,R._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class C extends h{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new f("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=j[n[e]];return i.join("")}}let j=Array(256);for(let e=0;e<j.length;e++)j[e]=e.toString(16).padStart(2,"0");class k{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new A(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new C(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}let G=e=>{switch(e.type){case"card_error":return new D(e);case"invalid_request_error":return new N(e);case"api_error":return new M(e);case"authentication_error":return new U(e);case"rate_limit_error":return new F(e);case"idempotency_error":return new $(e);case"invalid_grant":return new B(e);default:return new W(e)}};class I extends Error{constructor(e={}){super(e.message),this.type=this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}I.generate=G;class D extends I{}class N extends I{}class M extends I{}class U extends I{}class q extends I{}class F extends I{}class L extends I{}class H extends I{constructor(e,t,r={}){super(r),this.header=e,this.payload=t}}class $ extends I{}class B extends I{}class W extends I{}var K=r(6684);let z=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host"];function V(e){return e&&"object"==typeof e&&z.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function J(e){return K.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString()}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let Q=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function X(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!V(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>z.includes(e));return r.length>0&&r.length!==t.length&&ee(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function Y(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function Z(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function ee(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function et(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}let er=require("child_process");class eo extends I{}class en extends k{constructor(){super(),this._exec=er.exec,this._UNAME_CACHE=null}uuid4(){return p.randomUUID?p.randomUUID():super.uuid4()}getUname(){return this._UNAME_CACHE||(this._UNAME_CACHE=new Promise((e,t)=>{try{this._exec("uname -a",(t,r)=>{if(t)return e(null);e(r)})}catch(t){e(null)}})),this._UNAME_CACHE}secureCompare(e,t){if(!e||!t)throw Error("secureCompare must receive two arguments");if(e.length!==t.length)return!1;if(p.timingSafeEqual){let r=new TextEncoder,o=r.encode(e),n=r.encode(t);return p.timingSafeEqual(o,n)}return super.secureCompare(e,t)}createEmitter(){return new d.EventEmitter}tryBufferData(e){if(!(e.file.data instanceof d.EventEmitter))return Promise.resolve(e);let t=[];return new Promise((r,o)=>{e.file.data.on("data",e=>{t.push(e)}).once("end",()=>{let o=Object.assign({},e);o.file.data=function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}(t),r(o)}).on("error",e=>{o(new eo({message:"An error occurred while attempting to process the file for upload.",detail:e}))})})}createNodeHttpClient(e){return new x(e)}createDefaultHttpClient(){return new x}createNodeCryptoProvider(){return new m}createDefaultCryptoProvider(){return this.createNodeCryptoProvider()}}function ei(e,t){for(let r in t){let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function ea(e,t){return function(e){return new ei(e,t)}}class es{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=ed(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class el extends es{getNextPage(e){let t=ed(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class eu extends es{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}let ec=(e,t,r,o)=>"search"===r.methodType?ep(new eu(o,t,r,e)):"list"===r.methodType?ep(new el(o,t,r,e)):null,ep=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),o=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return Z(new Promise((e,o)=>{t().then(function o(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?o({done:!0,value:void 0}):t().then(o))}).catch(o)}),o)}),o={autoPagingEach:r,autoPagingToArray:function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return Z(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>o};return o};function ed(e){return!!X([].slice.call(e)).ending_before}function eh(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=Q(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=Q(this.path),this.initialize(...arguments)}eh.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},eh.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=Z(this._makeRequest(t,e,{}),r);return Object.assign(o,ec(this,t,e,o)),o}},eh.MAX_BUFFERED_REQUEST_METRICS=100,eh.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){let o=(t.method||"GET").toUpperCase(),n=t.urlParams||[],i=t.encode||(e=>e),a=!!t.fullPath,s=Q(a?t.fullPath:t.path||""),l=a?t.fullPath:this.createResourcePathWithSymbols(t.path),u=[].slice.call(e),c=n.reduce((e,t)=>{let r=u.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${o} ${l}\`)`);return e[t]=r,e},{}),p=i(Object.assign({},X(u),r)),d=function(e){let t={auth:null,host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.auth=e.pop();else if(V(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!z.includes(e));o.length&&ee(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.auth=r.apiKey),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host)}}return t}(u),h=d.host||t.host,f=!!t.streaming;if(u.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${u}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${o} \`${l}\`)`);let m=a?s(c):this.createFullPath(s,c),y=Object.assign(d.headers,t.headers);t.validator&&t.validator(p,{headers:y});let v="GET"===t.method||"DELETE"===t.method;return{requestMethod:o,requestPath:m,bodyData:v?{}:p,queryData:v?p:{},auth:d.auth,headers:y,host:null!=h?h:null,streaming:f,settings:d.settings}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",J(a.queryData)].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.auth,{headers:u,settings:c,streaming:a.streaming},function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let ef=eh.method,em=eh.extend({retrieve:ef({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:ef({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:ef({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:ef({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:ef({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"})}),ey=eh.method,ev=eh.extend({retrieve:ey({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:ey({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:ey({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:ey({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:ey({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),eP=eh.method,eg=eh.extend({create:eP({method:"POST",fullPath:"/v1/tax/calculations"}),listLineItems:eP({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),e_=eh.method,eT=eh.extend({create:e_({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:e_({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:e_({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:e_({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eE=eh.method,eb=eh.extend({deliverCard:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"})}),eS=eh.method,eO=eh.extend({create:eS({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:eS({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:eS({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:eS({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),ex=eh.method,ew=eh.extend({create:ex({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:ex({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:ex({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:ex({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),eA=eh.method,eR=eh.extend({create:eA({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:eA({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:eA({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:eA({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:eA({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),eC=eh.method,ej=eh.extend({create:eC({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),ek=eh.method,eG=eh.extend({create:ek({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:ek({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:ek({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),eI=eh.method,eD=eh.extend({fundCashBalance:eI({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),eN=eh.method,eM=eh.extend({create:eN({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:eN({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:eN({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),eU=eh.method,eq=eh.extend({create:eU({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:eU({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:eU({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:eU({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:eU({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),eF=eh.method,eL=eh.extend({retrieve:eF({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:eF({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),eH=eh.method,e$=eh.extend({create:eH({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:eH({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:eH({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:eH({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),retrieveFeatures:eH({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:eH({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),eB=eh.method,eW=eh.extend({fail:eB({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:eB({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:eB({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),eK=eh.method,ez=eh.extend({create:eK({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:eK({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:eK({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:eK({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),eV=eh.method,eJ=eh.extend({create:eV({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:eV({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:eV({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:eV({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:eV({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),eQ=eh.method,eX=eh.extend({fail:eQ({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:eQ({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:eQ({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),eY=eh.method,eZ=eh.extend({create:eY({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:eY({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:eY({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:eY({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),e1=eh.method,e0=eh.extend({fail:e1({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:e1({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:e1({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),e2=eh.method,e8=eh.extend({create:e2({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:e2({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:e2({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:e2({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),e6=eh.method,e3=eh.extend({presentPaymentMethod:e6({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),e4=eh.method,e9=eh.extend({create:e4({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:e4({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:e4({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:e4({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:e4({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:e4({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:e4({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:e4({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:e4({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:e4({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),e5=eh.method,e7=eh.extend({create:e5({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),te=eh.method,tt=eh.extend({retrieve:te({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:te({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),tr=eh.method,to=eh.extend({create:tr({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),tn=eh.method,ti=eh.extend({retrieve:tn({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:tn({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),ta=eh.method,ts=eh.extend({expire:ta({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),tl=eh.method,tu=eh.extend({create:tl({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:tl({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:tl({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),tc=eh.method,tp=eh.extend({retrieve:tc({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:tc({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),td=eh.method,th=eh.extend({retrieve:td({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:td({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),tf=eh.method,tm=eh.extend({create:tf({method:"POST",fullPath:"/v1/apps/secrets"}),list:tf({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:tf({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:tf({method:"GET",fullPath:"/v1/apps/secrets/find"})}),ty=eh.method,tv=eh.extend({create:ty({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),tP=eh.method,tg=eh.extend({create:tP({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:tP({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),list:tP({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:tP({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:tP({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),t_=eh.method,tT=eh.extend({create:t_({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:t_({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),tE=eh.method,tb=eh.extend({retrieve:tE({method:"GET",fullPath:"/v1/tax/settings"}),update:tE({method:"POST",fullPath:"/v1/tax/settings"})}),tS=eh.method,tO=eh.extend({create:tS({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:tS({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:tS({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:tS({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:tS({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),tx=eh.method,tw=eh.extend({retrieve:tx({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:tx({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),tA=eh.method,tR=eh.extend({retrieve:tA({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:tA({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:tA({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),tC=eh.method,tj=eh.extend({retrieve:tC({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:tC({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:tC({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:tC({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),tk=eh.method,tG=eh.extend({retrieve:tk({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:tk({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),tI=eh.method,tD=eh.extend({create:tI({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:tI({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:tI({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:tI({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),tN=eh.method,tM=eh.extend({create:tN({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:tN({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:tN({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:tN({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:tN({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),tU=eh.method,tq=eh.extend({retrieve:tU({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:tU({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),tF=eh.method,tL=eh.extend({create:tF({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:tF({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:tF({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:tF({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:tF({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:tF({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),tH=eh.method,t$=eh.extend({create:tH({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?tH({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),tH({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:tH({method:"POST",fullPath:"/v1/accounts/{account}"}),list:tH({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:tH({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:tH({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:tH({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:tH({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:tH({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:tH({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:tH({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:tH({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:tH({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:tH({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCapability:tH({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:tH({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:tH({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:tH({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:tH({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:tH({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),tB=eh.method,tW=eh.extend({create:tB({method:"POST",fullPath:"/v1/account_links"})}),tK=eh.method,tz=eh.extend({create:tK({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:tK({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:tK({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:tK({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),tV=eh.method,tJ=eh.extend({retrieve:tV({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:tV({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:tV({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:tV({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:tV({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:tV({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),tQ=eh.method,tX=eh.extend({retrieve:tQ({method:"GET",fullPath:"/v1/balance"})}),tY=eh.method,tZ=eh.extend({retrieve:tY({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:tY({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),t1=eh.method,t0=eh.extend({create:t1({method:"POST",fullPath:"/v1/charges"}),retrieve:t1({method:"GET",fullPath:"/v1/charges/{charge}"}),update:t1({method:"POST",fullPath:"/v1/charges/{charge}"}),list:t1({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:t1({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:t1({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),t2=eh.method,t8=eh.extend({retrieve:t2({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:t2({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),t6=eh.method,t3=eh.extend({create:t6({method:"POST",fullPath:"/v1/coupons"}),retrieve:t6({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:t6({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:t6({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:t6({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),t4=eh.method,t9=eh.extend({create:t4({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:t4({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:t4({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:t4({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:t4({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:t4({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:t4({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:t4({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),t5=eh.method,t7=eh.extend({create:t5({method:"POST",fullPath:"/v1/customers"}),retrieve:t5({method:"GET",fullPath:"/v1/customers/{customer}"}),update:t5({method:"POST",fullPath:"/v1/customers/{customer}"}),list:t5({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:t5({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createFundingInstructions:t5({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createBalanceTransaction:t5({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createSource:t5({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:t5({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:t5({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:t5({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:t5({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listPaymentMethods:t5({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listBalanceTransactions:t5({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:t5({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listSources:t5({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:t5({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrievePaymentMethod:t5({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveBalanceTransaction:t5({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:t5({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:t5({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrieveSource:t5({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:t5({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:t5({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:t5({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:t5({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:t5({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:t5({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),re=eh.method,rt=eh.extend({retrieve:re({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:re({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:re({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:re({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),rr=eh.method,ro=eh.extend({create:rr({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:rr({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),rn=eh.method,ri=eh.extend({retrieve:rn({method:"GET",fullPath:"/v1/events/{id}"}),list:rn({method:"GET",fullPath:"/v1/events",methodType:"list"})}),ra=eh.method,rs=eh.extend({retrieve:ra({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:ra({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),rl=eh.method,ru=eh.extend({create:rl({method:"POST",fullPath:"/v1/file_links"}),retrieve:rl({method:"GET",fullPath:"/v1/file_links/{link}"}),update:rl({method:"POST",fullPath:"/v1/file_links/{link}"}),list:rl({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),rc=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.keys(e).forEach(n=>{let i=e[n],a=o?`${o}[${n}]`:n;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(i)){if(!(i instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(i,"data"))return r(i,a);t[a]=i}else t[a]=String(i)})};return r(e,null),t}(t);for(let e in u){let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},rp=eh.method,rd=eh.extend({create:rp({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:rp({method:"GET",fullPath:"/v1/files/{file}"}),list:rp({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,J(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,rc(e,t,r))).catch(e=>o(e,null))}}),rh=eh.method,rf=eh.extend({create:rh({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:rh({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:rh({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:rh({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:rh({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),rm=eh.method,ry=eh.extend({create:rm({method:"POST",fullPath:"/v1/invoices"}),retrieve:rm({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:rm({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:rm({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:rm({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),finalizeInvoice:rm({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:rm({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),listUpcomingLines:rm({method:"GET",fullPath:"/v1/invoices/upcoming/lines",methodType:"list"}),markUncollectible:rm({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:rm({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),retrieveUpcoming:rm({method:"GET",fullPath:"/v1/invoices/upcoming"}),search:rm({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:rm({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),voidInvoice:rm({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),rv=eh.method,rP=eh.extend({retrieve:rv({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),rg=eh.method,r_="connect.stripe.com",rT=eh.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${r_}/${r}?${J(e)}`},token:rg({method:"POST",path:"oauth/token",host:r_}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),rg({method:"POST",path:"oauth/deauthorize",host:r_}).apply(this,[e,...t])}}),rE=eh.method,rb=eh.extend({create:rE({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:rE({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:rE({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:rE({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:rE({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),rS=eh.method,rO=eh.extend({create:rS({method:"POST",fullPath:"/v1/payment_links"}),retrieve:rS({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:rS({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:rS({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:rS({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),rx=eh.method,rw=eh.extend({create:rx({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:rx({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:rx({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:rx({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:rx({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:rx({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),rA=eh.method,rR=eh.extend({create:rA({method:"POST",fullPath:"/v1/payouts"}),retrieve:rA({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:rA({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:rA({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:rA({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:rA({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),rC=eh.method,rj=eh.extend({create:rC({method:"POST",fullPath:"/v1/plans"}),retrieve:rC({method:"GET",fullPath:"/v1/plans/{plan}"}),update:rC({method:"POST",fullPath:"/v1/plans/{plan}"}),list:rC({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:rC({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),rk=eh.method,rG=eh.extend({create:rk({method:"POST",fullPath:"/v1/prices"}),retrieve:rk({method:"GET",fullPath:"/v1/prices/{price}"}),update:rk({method:"POST",fullPath:"/v1/prices/{price}"}),list:rk({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:rk({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),rI=eh.method,rD=eh.extend({create:rI({method:"POST",fullPath:"/v1/products"}),retrieve:rI({method:"GET",fullPath:"/v1/products/{id}"}),update:rI({method:"POST",fullPath:"/v1/products/{id}"}),list:rI({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:rI({method:"DELETE",fullPath:"/v1/products/{id}"}),search:rI({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),rN=eh.method,rM=eh.extend({create:rN({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:rN({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:rN({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:rN({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),rU=eh.method,rq=eh.extend({create:rU({method:"POST",fullPath:"/v1/quotes"}),retrieve:rU({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:rU({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:rU({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:rU({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:rU({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:rU({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:rU({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:rU({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:rU({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),rF=eh.method,rL=eh.extend({create:rF({method:"POST",fullPath:"/v1/refunds"}),retrieve:rF({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:rF({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:rF({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:rF({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),rH=eh.method,r$=eh.extend({retrieve:rH({method:"GET",fullPath:"/v1/reviews/{review}"}),list:rH({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:rH({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),rB=eh.method,rW=eh.extend({list:rB({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),rK=eh.method,rz=eh.extend({create:rK({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:rK({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:rK({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:rK({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:rK({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:rK({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:rK({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),rV=eh.method,rJ=eh.extend({create:rV({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:rV({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:rV({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:rV({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),rQ=eh.method,rX=eh.extend({create:rQ({method:"POST",fullPath:"/v1/sources"}),retrieve:rQ({method:"GET",fullPath:"/v1/sources/{source}"}),update:rQ({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:rQ({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:rQ({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),rY=eh.method,rZ=eh.extend({create:rY({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:rY({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:rY({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:rY({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:rY({method:"DELETE",fullPath:"/v1/subscription_items/{item}"}),createUsageRecord:rY({method:"POST",fullPath:"/v1/subscription_items/{subscription_item}/usage_records"}),listUsageRecordSummaries:rY({method:"GET",fullPath:"/v1/subscription_items/{subscription_item}/usage_record_summaries",methodType:"list"})}),r1=eh.method,r0=eh.extend({create:r1({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:r1({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:r1({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:r1({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:r1({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:r1({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),r2=eh.method,r8=eh.extend({create:r2({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:r2({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:r2({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:r2({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:r2({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),del:r2({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:r2({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:r2({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:r2({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),r6=eh.method,r3=eh.extend({retrieve:r6({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:r6({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),r4=eh.method,r9=eh.extend({create:r4({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:r4({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:r4({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:r4({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),r5=eh.method,r7=eh.extend({create:r5({method:"POST",fullPath:"/v1/tokens"}),retrieve:r5({method:"GET",fullPath:"/v1/tokens/{token}"})}),oe=eh.method,ot=eh.extend({create:oe({method:"POST",fullPath:"/v1/topups"}),retrieve:oe({method:"GET",fullPath:"/v1/topups/{topup}"}),update:oe({method:"POST",fullPath:"/v1/topups/{topup}"}),list:oe({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:oe({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),or=eh.method,oo=eh.extend({create:or({method:"POST",fullPath:"/v1/transfers"}),retrieve:or({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:or({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:or({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:or({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:or({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:or({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:or({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),on=eh.method,oi=eh.extend({create:on({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:on({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:on({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:on({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:on({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),oa=ea("apps",{Secrets:tm}),os=ea("billingPortal",{Configurations:ew,Sessions:tv}),ol=ea("checkout",{Sessions:tg}),ou=ea("financialConnections",{Accounts:em,Sessions:tT}),oc=ea("identity",{VerificationReports:tq,VerificationSessions:tL}),op=ea("issuing",{Authorizations:ev,Cardholders:eT,Cards:eO,Disputes:eq,Transactions:tR}),od=ea("radar",{EarlyFraudWarnings:eL,ValueListItems:tD,ValueLists:tM}),oh=ea("reporting",{ReportRuns:tu,ReportTypes:tp}),of=ea("sigma",{ScheduledQueryRuns:th}),om=ea("tax",{Calculations:eg,Settings:tb,Transactions:tj}),oy=ea("terminal",{Configurations:eR,ConnectionTokens:ej,Locations:eJ,Readers:e9}),ov=ea("testHelpers",{Customers:eD,Refunds:ts,TestClocks:tO,Issuing:ea("issuing",{Cards:eb}),Terminal:ea("terminal",{Readers:e3}),Treasury:ea("treasury",{InboundTransfers:eW,OutboundPayments:eX,OutboundTransfers:e0,ReceivedCredits:e7,ReceivedDebits:to})}),oP=ea("treasury",{CreditReversals:eG,DebitReversals:eM,FinancialAccounts:e$,InboundTransfers:ez,OutboundPayments:eZ,OutboundTransfers:e8,ReceivedCredits:tt,ReceivedDebits:ti,TransactionEntries:tw,Transactions:tG});class og{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return Y({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t){return r=>{let o=r.getHeaders(),n=r.toStream(()=>{let t=this._makeResponseEvent(e,r.getStatusCode(),o);this._stripe._emitter.emit("response",t),this._recordRequestMetrics(this._getRequestId(o),t.elapsed)});return this._addHeadersDirectlyToObject(n,o),t(null,n)}}_jsonResponseHandler(e,t){return r=>{let o=r.getHeaders(),n=this._getRequestId(o),i=r.getStatusCode(),a=this._makeResponseEvent(e,i,o);this._stripe._emitter.emit("response",a),r.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=o,e.error.statusCode=i,e.error.requestId=n,401===i?new U(e.error):403===i?new q(e.error):429===i?new F(e.error):I.generate(e.error);return e},e=>{throw new M({message:"Invalid JSON received from the Stripe API",exception:e,requestId:o["request-id"]})}).then(e=>{this._recordRequestMetrics(n,a.elapsed);let i=r.getRawResponse();this._addHeadersDirectlyToObject(i,o),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:i}),t(null,e)},e=>t(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&_.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(e-1,2),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t){let r=this._getMaxNetworkRetries(t);return"POST"===e&&r>0?`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`:null}_makeHeaders(e,t,r,o,n,i,a){let s={Authorization:e?`Bearer ${e}`:this._stripe.getApiField("auth"),Accept:"application/json","Content-Type":"application/x-www-form-urlencoded","User-Agent":this._getUserAgentString(),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":this._stripe.getApiField("stripeAccount"),"Idempotency-Key":this._defaultIdempotencyKey(n,a)},l="POST"==n||"PUT"==n||"PATCH"==n;return(l||t)&&(l||ee(`${n} method had non-zero contentLength but no payload is expected for this verb`),s["Content-Length"]=t),Object.assign(Y(s),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(){let e=this._stripe.getConstant("PACKAGE_VERSION"),t=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/v1 NodeBindings/${e} ${t}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t){this._stripe.getTelemetryEnabled()&&e&&(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric?ee("Request metrics buffer is full, dropping telemetry message."):this._stripe._prevRequestMetrics.push({request_id:e,request_duration_ms:t}))}_request(e,t,r,o,n,i={},a,s=null){let l;let u=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),c=(o,n,s)=>{let p=i.settings&&i.settings.timeout&&Number.isInteger(i.settings.timeout)&&i.settings.timeout>=0?i.settings.timeout:this._stripe.getApiField("timeout"),d=this._stripe.getApiField("httpClient").makeRequest(t||this._stripe.getApiField("host"),this._stripe.getApiField("port"),r,e,n,l,this._stripe.getApiField("protocol"),p),h=Date.now(),f=Y({api_version:o,account:n["Stripe-Account"],idempotency_key:n["Idempotency-Key"],method:e,path:r,request_start_time:h}),m=s||0,y=this._getMaxNetworkRetries(i.settings||{});this._stripe._emitter.emit("request",f),d.then(e=>og._shouldRetry(e,m,y)?u(c,o,n,m,e.getHeaders()["retry-after"]):i.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(f,a)(e):this._jsonResponseHandler(f,a)(e)).catch(e=>og._shouldRetry(null,m,y,e)?u(c,o,n,m,null):a(new L({message:e.code&&e.code===_.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${p}ms)`:og._generateConnectionErrorMessage(m),detail:e})))},p=(t,r)=>{if(t)return a(t);l=r,this._stripe.getClientUserAgent(t=>{var r,o;let a=this._stripe.getApiField("version"),s=this._makeHeaders(n,l.length,a,t,e,null!==(r=i.headers)&&void 0!==r?r:null,null!==(o=i.settings)&&void 0!==o?o:{});c(a,s,0)})};s?s(e,o,i.headers,p):p(null,J(o||{}))}}function o_(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof f&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){if(!e)throw new I({message:"Options are required"});return e.timestamp=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),e.scheme=e.scheme||r.EXPECTED_SCHEME,e.cryptoProvider=e.cryptoProvider||s(),e.signature=e.signature||e.cryptoProvider.computeHMACSignature(e.timestamp+"."+e.payload,e.secret),["t="+e.timestamp,e.scheme+"="+e.signature].join(",")}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:p,details:d,suspectPayloadType:h}=n(e,t,this.EXPECTED_SCHEME),f=/\s/.test(r),m=(l=l||s()).computeHMACSignature(o(p,d),r);return i(p,c,d,m,a,h,f,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:p,details:d,suspectPayloadType:h}=n(e,t,this.EXPECTED_SCHEME),f=/\s/.test(r);l=l||s();let m=await l.computeHMACSignatureAsync(o(p,d),r);return i(p,c,d,m,a,h,f,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new H(t,e,{message:"No webhook payload was provided."});let o="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new H(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,s="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let o=t.split("=");return"t"===o[0]&&(e.timestamp=parseInt(o[1],10)),o[0]===r&&e.signatures.push(o[1]),e},{timestamp:-1,signatures:[]});if(!s||-1===s.timestamp)throw new H(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!s.signatures.length)throw new H(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:s,suspectPayloadType:o}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://github.com/stripe/stripe-node#webhook-signing",p=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new H(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+p});throw new H(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n"+c+"\n"+p})}let d=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&d>i)throw new H(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}return t.signature=r,t}let oT="api.stripe.com",oE="/v1/",ob="2022-11-15",oS=["name","version","url","partner_id"],oO=["apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount"],ox=e=>new og(e,eh.MAX_BUFFERED_REQUEST_METRICS),ow=function(e,t=ox){function r(n,i={}){if(!(this instanceof r))return new r(n,i);let a=this._getPropsFromConfig(i);if(this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=r.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter),a.protocol&&"https"!==a.protocol&&(!a.host||/\.stripe\.com$/.test(a.host)))throw Error("The `https` protocol must be used when sending requests to `*.stripe.com`");let s=a.httpAgent||null;this._api={auth:null,host:a.host||oT,port:a.port||"443",protocol:a.protocol||"https",basePath:oE,version:a.apiVersion||ob,timeout:et("timeout",a.timeout,8e4),maxNetworkRetries:et("maxNetworkRetries",a.maxNetworkRetries,0),agent:s,httpClient:a.httpClient||(s?this._platformFunctions.createNodeHttpClient(s):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null};let l=a.typescript||!1;l!==r.USER_AGENT.typescript&&(r.USER_AGENT.typescript=l),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setApiKey(n),this.errors=o,this.webhooks=o_(e),this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=r.StripeResource}return r.PACKAGE_VERSION="12.18.0",r.USER_AGENT=Object.assign({bindings_version:r.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),r.StripeResource=eh,r.resources=n,r.HttpClient=_,r.HttpClientResponse=T,r.CryptoProvider=h,r.errors=o,r.webhooks=o_,r.createNodeHttpClient=e.createNodeHttpClient,r.createFetchHttpClient=e.createFetchHttpClient,r.createNodeCryptoProvider=e.createNodeCryptoProvider,r.createSubtleCryptoProvider=e.createSubtleCryptoProvider,r.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,_setApiKey(e){e&&this._setApiField("auth",`Bearer ${e}`)},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=oS.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return oT;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return oE;case"DEFAULT_API_VERSION":return ob;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 2;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return r[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=et(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>2,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(r.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)n[t]=encodeURIComponent(null!==(o=e[t])&&void 0!==o?o:"null");n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this)},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!oO.includes(e)).length>0)throw Error(`Config object may only contain the following: ${oO.join(", ")}`);return e}},r}(new en),oA=new c.PrismaClient,oR=new ow(process.env.STRIPE_SECRET_KEY||"",{apiVersion:"2023-10-16"});async function oC(e){try{let{userId:t,plan:r}=await e.json();if(!t||!r)return u.NextResponse.json({message:"Brakujące dane: userId lub plan"},{status:400});let o=await oA.user.findUnique({where:{id:t}});if(!o)return u.NextResponse.json({message:"Użytkownik nie istnieje"},{status:404});if(!["pro","academic"].includes(r))return u.NextResponse.json({message:"Nieprawidłowy plan"},{status:400});let n=o.stripeCustomerId;n||(n=(await oR.customers.create({email:o.email,name:o.name||void 0})).id,await oA.user.update({where:{id:t},data:{stripeCustomerId:n}}));let i=await oR.checkout.sessions.create({customer:n,payment_method_types:["card"],line_items:[{price_data:{currency:"usd",product_data:{name:`MolecuLab Pro - Plan ${"pro"===r?"Pro":"Academic"}`,description:"pro"===r?"Pełny dostęp do wszystkich funkcji MolecuLab Pro":"Plan dla student\xf3w i naukowc\xf3w"},unit_amount:{pro:2900,academic:900}[r],recurring:{interval:"month"}},quantity:1}],mode:"subscription",success_url:`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?subscription=success`,cancel_url:`${process.env.NEXT_PUBLIC_APP_URL}/pricing?subscription=canceled`,metadata:{userId:t,plan:r}});return u.NextResponse.json({url:i.url})}catch(e){return console.error("Błąd tworzenia subskrypcji:",e),u.NextResponse.json({message:"Wystąpił błąd podczas tworzenia subskrypcji"},{status:500})}}let oj=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/subscription/create/route",pathname:"/api/subscription/create",filename:"route",bundlePath:"app/api/subscription/create/route"},resolvedPagePath:"/home/<USER>/Dokumenty/moleculab/src/app/api/subscription/create/route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:ok,staticGenerationAsyncStorage:oG,serverHooks:oI}=oj,oD="/api/subscription/create/route";function oN(){return(0,l.patchFetch)({serverHooks:oI,staticGenerationAsyncStorage:oG})}},1421:(e,t,r)=>{"use strict";var o=r(3425),n=r(4006),i=r(3135),a=r(3659);e.exports=a||o.call(i,n)},4006:e=>{"use strict";e.exports=Function.prototype.apply},3135:e=>{"use strict";e.exports=Function.prototype.call},602:(e,t,r)=>{"use strict";var o=r(3425),n=r(7445),i=r(3135),a=r(1421);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},3659:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},8363:(e,t,r)=>{"use strict";var o=r(2749),n=r(602),i=n([o("%String.prototype.indexOf%")]);e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n([r]):r}},2344:(e,t,r)=>{"use strict";var o,n=r(602),i=r(6737);try{o=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!o&&i&&i(Object.prototype,"__proto__"),s=Object,l=s.getPrototypeOf;e.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof l&&function(e){return l(null==e?e:s(e))}},91:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},6827:e=>{"use strict";e.exports=EvalError},6718:e=>{"use strict";e.exports=Error},7388:e=>{"use strict";e.exports=RangeError},3684:e=>{"use strict";e.exports=ReferenceError},1209:e=>{"use strict";e.exports=SyntaxError},7445:e=>{"use strict";e.exports=TypeError},6928:e=>{"use strict";e.exports=URIError},5678:e=>{"use strict";e.exports=Object},9214:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],p=0;p<u;p++)c[p]="$"+p;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var d=function(){};d.prototype=s.prototype,a.prototype=new d,d.prototype=null}return a}},3425:(e,t,r)=>{"use strict";var o=r(9214);e.exports=Function.prototype.bind||o},2749:(e,t,r)=>{"use strict";var o,n=r(5678),i=r(6718),a=r(6827),s=r(7388),l=r(3684),u=r(1209),c=r(7445),p=r(6928),d=r(5175),h=r(2334),f=r(6082),m=r(430),y=r(4210),v=r(792),P=r(2615),g=Function,_=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},T=r(6737),E=r(91),b=function(){throw new c},S=T?function(){try{return arguments.callee,b}catch(e){try{return T(arguments,"callee").get}catch(e){return b}}}():b,O=r(1976)(),x=r(3941),w=r(7209),A=r(2395),R=r(4006),C=r(3135),j={},k="undefined"!=typeof Uint8Array&&x?x(Uint8Array):o,G={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":O&&x?x([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":j,"%AsyncGenerator%":j,"%AsyncGeneratorFunction%":j,"%AsyncIteratorPrototype%":j,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":j,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":O&&x?x(x([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&O&&x?x(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":T,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":s,"%ReferenceError%":l,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&O&&x?x(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":O&&x?x(""[Symbol.iterator]()):o,"%Symbol%":O?Symbol:o,"%SyntaxError%":u,"%ThrowTypeError%":S,"%TypedArray%":k,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":p,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":C,"%Function.prototype.apply%":R,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":w,"%Math.abs%":d,"%Math.floor%":h,"%Math.max%":f,"%Math.min%":m,"%Math.pow%":y,"%Math.round%":v,"%Math.sign%":P,"%Reflect.getPrototypeOf%":A};if(x)try{null.error}catch(e){var I=x(x(e));G["%Error.prototype%"]=I}var D=function e(t){var r;if("%AsyncFunction%"===t)r=_("async function () {}");else if("%GeneratorFunction%"===t)r=_("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=_("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&x&&(r=x(n.prototype))}return G[t]=r,r},N={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=r(3425),U=r(4995),q=M.call(C,Array.prototype.concat),F=M.call(R,Array.prototype.splice),L=M.call(C,String.prototype.replace),H=M.call(C,String.prototype.slice),$=M.call(C,RegExp.prototype.exec),B=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,W=/\\(\\)?/g,K=function(e){var t=H(e,0,1),r=H(e,-1);if("%"===t&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var o=[];return L(e,B,function(e,t,r,n){o[o.length]=r?L(n,W,"$1"):t||e}),o},z=function(e,t){var r,o=e;if(U(N,o)&&(o="%"+(r=N[o])[0]+"%"),U(G,o)){var n=G[o];if(n===j&&(n=D(o)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===$(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=K(e),o=r.length>0?r[0]:"",n=z("%"+o+"%",t),i=n.name,a=n.value,s=!1,l=n.alias;l&&(o=l[0],F(r,q([0,1],l)));for(var p=1,d=!0;p<r.length;p+=1){var h=r[p],f=H(h,0,1),m=H(h,-1);if(('"'===f||"'"===f||"`"===f||'"'===m||"'"===m||"`"===m)&&f!==m)throw new u("property names with quotes must have matching quotes");if("constructor"!==h&&d||(s=!0),o+="."+h,U(G,i="%"+o+"%"))a=G[i];else if(null!=a){if(!(h in a)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(T&&p+1>=r.length){var y=T(a,h);a=(d=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[h]}else d=U(a,h),a=a[h];d&&!s&&(G[i]=a)}}return a}},7209:(e,t,r)=>{"use strict";var o=r(5678);e.exports=o.getPrototypeOf||null},2395:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},3941:(e,t,r)=>{"use strict";var o=r(2395),n=r(7209),i=r(2344);e.exports=o?function(e){return o(e)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return n(e)}:i?function(e){return i(e)}:null},2980:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},6737:(e,t,r)=>{"use strict";var o=r(2980);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},1976:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(2522);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},2522:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},4995:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,i=r(3425);e.exports=i.call(o,n)},5175:e=>{"use strict";e.exports=Math.abs},2334:e=>{"use strict";e.exports=Math.floor},1781:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},6082:e=>{"use strict";e.exports=Math.max},430:e=>{"use strict";e.exports=Math.min},4210:e=>{"use strict";e.exports=Math.pow},792:e=>{"use strict";e.exports=Math.round},2615:(e,t,r)=>{"use strict";var o=r(1781);e.exports=function(e){return o(e)||0===e?e:e<0?-1:1}},9925:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,i={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),o=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?o:`${o}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[o,n],...i]=s(e),{domain:a,expires:l,httponly:p,maxage:d,path:h,samesite:f,secure:m,partitioned:y,priority:v}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(n),domain:a,...l&&{expires:new Date(l)},...p&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:h,...f&&{sameSite:u.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...v&&{priority:c.includes(r=(r=v).toLowerCase())?r:void 0},...y&&{partitioned:!0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(i,{RequestCookies:()=>p,ResponseCookies:()=>d,parseCookie:()=>s,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,i,a,s)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let l of o(i))n.call(e,l)||l===a||t(e,l,{get:()=>i[l],enumerable:!(s=r(i,l))||s.enumerable});return e})(t({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],c=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let n=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,o,n,i,a=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;l();)if(","===(r=e.charAt(s))){for(o=s,s+=1,l(),n=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=n,a.push(e.substring(t,o)),t=s):s=o+1}else s+=1;(!i||s>=e.length)&&a.push(e.substring(t,e.length))}return a}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},2044:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies},stringifyCookie:function(){return o.stringifyCookie}});let o=r(9925)},9966:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,p="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,d="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,h="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,f=Boolean.prototype.valueOf,m=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,_=String.prototype.toUpperCase,T=String.prototype.toLowerCase,E=RegExp.prototype.test,b=Array.prototype.concat,S=Array.prototype.join,O=Array.prototype.slice,x=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,C="function"==typeof Symbol&&"object"==typeof Symbol.iterator,j="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===C?"object":"symbol")?Symbol.toStringTag:null,k=Object.prototype.propertyIsEnumerable,G=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function I(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-x(-e):x(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var D=r(1660),N=D.custom,M=B(N)?N:null,U={__proto__:null,double:'"',single:"'"},q={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function F(e,t,r){var o=U[r.quoteStyle||t];return o+e+o}function L(e){return!j||!("object"==typeof e&&(j in e||void 0!==e[j]))}function H(e){return"[object Array]"===z(e)&&L(e)}function $(e){return"[object RegExp]"===z(e)&&L(e)}function B(e){if(C)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!R)return!1;try{return R.call(e),!0}catch(e){}return!1}e.exports=function e(t,r,o,n){var s=r||{};if(K(s,"quoteStyle")&&!K(U,s.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(K(s,"maxStringLength")&&("number"==typeof s.maxStringLength?s.maxStringLength<0&&s.maxStringLength!==1/0:null!==s.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=!K(s,"customInspect")||s.customInspect;if("boolean"!=typeof l&&"symbol"!==l)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(K(s,"indent")&&null!==s.indent&&"	"!==s.indent&&!(parseInt(s.indent,10)===s.indent&&s.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(K(s,"numericSeparator")&&"boolean"!=typeof s.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var m=s.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+"... "+o+" more character"+(o>1?"s":"")}var n=q[r.quoteStyle||"single"];return n.lastIndex=0,F(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,J),"single",r)}(t,s);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var _=String(t);return m?I(t,_):_}if("bigint"==typeof t){var E=String(t)+"n";return m?I(t,E):E}var x=void 0===s.depth?5:s.depth;if(void 0===o&&(o=0),o>=x&&x>0&&"object"==typeof t)return H(t)?"[Array]":"[Object]";var A=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=S.call(Array(e.indent+1)," ")}return{base:r,prev:S.call(Array(t+1),r)}}(s,o);if(void 0===n)n=[];else if(V(n,t)>=0)return"[Circular]";function N(t,r,i){if(r&&(n=O.call(n)).push(r),i){var a={depth:s.depth};return K(s,"quoteStyle")&&(a.quoteStyle=s.quoteStyle),e(t,a,o+1,n)}return e(t,s,o+1,n)}if("function"==typeof t&&!$(t)){var W=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),et=ee(t,N);return"[Function"+(W?": "+W:" (anonymous)")+"]"+(et.length>0?" { "+S.call(et,", ")+" }":"")}if(B(t)){var er=C?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):R.call(t);return"object"!=typeof t||C?er:Q(er)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var eo,en="<"+T.call(String(t.nodeName)),ei=t.attributes||[],ea=0;ea<ei.length;ea++)en+=" "+ei[ea].name+"="+F((eo=ei[ea].value,g.call(String(eo),/"/g,"&quot;")),"double",s);return en+=">",t.childNodes&&t.childNodes.length&&(en+="..."),en+="</"+T.call(String(t.nodeName))+">"}if(H(t)){if(0===t.length)return"[]";var es=ee(t,N);return A&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(es)?"["+Z(es,A)+"]":"[ "+S.call(es,", ")+" ]"}if("[object Error]"===z(t)&&L(t)){var el=ee(t,N);return"cause"in Error.prototype||!("cause"in t)||k.call(t,"cause")?0===el.length?"["+String(t)+"]":"{ ["+String(t)+"] "+S.call(el,", ")+" }":"{ ["+String(t)+"] "+S.call(b.call("[cause]: "+N(t.cause),el),", ")+" }"}if("object"==typeof t&&l){if(M&&"function"==typeof t[M]&&D)return D(t,{depth:x-o});if("symbol"!==l&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var eu=[];return a&&a.call(t,function(e,r){eu.push(N(r,t,!0)+" => "+N(e,t))}),Y("Map",i.call(t),eu,A)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ec=[];return c&&c.call(t,function(e){ec.push(N(e,t))}),Y("Set",u.call(t),ec,A)}if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{p.call(e,p);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return X("WeakMap");if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{p.call(e,p)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return X("WeakSet");if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{return h.call(e),!0}catch(e){}return!1}(t))return X("WeakRef");if("[object Number]"===z(t)&&L(t))return Q(N(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return Q(N(w.call(t)));if("[object Boolean]"===z(t)&&L(t))return Q(f.call(t));if("[object String]"===z(t)&&L(t))return Q(N(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||"undefined"!=typeof global&&t===global)return"{ [object globalThis] }";if(!("[object Date]"===z(t)&&L(t))&&!$(t)){var ep=ee(t,N),ed=G?G(t)===Object.prototype:t instanceof Object||t.constructor===Object,eh=t instanceof Object?"":"null prototype",ef=!ed&&j&&Object(t)===t&&j in t?P.call(z(t),8,-1):eh?"Object":"",em=(ed||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(ef||eh?"["+S.call(b.call([],ef||[],eh||[]),": ")+"] ":"");return 0===ep.length?em+"{}":A?em+"{"+Z(ep,A)+"}":em+"{ "+S.call(ep,", ")+" }"}return String(t)};var W=Object.prototype.hasOwnProperty||function(e){return e in this};function K(e,t){return W.call(e,t)}function z(e){return m.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function J(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+_.call(t.toString(16))}function Q(e){return"Object("+e+")"}function X(e){return e+" { ? }"}function Y(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):S.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+S.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r,o=H(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=K(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(C){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)K(e,l)&&(!o||String(Number(l))!==l||!(l<e.length))&&(C&&r["$"+l]instanceof Symbol||(E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof A)for(var u=0;u<a.length;u++)k.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},1660:(e,t,r)=>{e.exports=r(1764).inspect},6815:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:o.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},6684:(e,t,r)=>{"use strict";var o=r(2503),n=r(3273),i=r(6815);e.exports={formats:i,parse:n,stringify:o}},3273:(e,t,r)=>{"use strict";var o=r(1847),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},s=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,t.throwOnLimitExceeded?u+1:u);if(t.throwOnLimitExceeded&&c.length>u)throw RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var p=-1,d=t.charset;if(t.charsetSentinel)for(h=0;h<c.length;++h)0===c[h].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[h]?d="utf-8":"utf8=%26%2310003%3B"===c[h]&&(d="iso-8859-1"),p=h,h=c.length);for(h=0;h<c.length;++h)if(h!==p){var h,f,m,y=c[h],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(f=t.decoder(y,a.decoder,d,"key"),m=t.strictNullHandling?null:""):(f=t.decoder(y.slice(0,P),a.decoder,d,"key"),m=o.maybeMap(s(y.slice(P+1),t,i(r[f])?r[f].length:0),function(e){return t.decoder(e,a.decoder,d,"value")})),m&&t.interpretNumericEntities&&"iso-8859-1"===d&&(m=String(m).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(m=i(m)?[m]:m);var g=n.call(r,f);g&&"combine"===t.duplicates?r[f]=o.combine(r[f],m):g&&"last"!==t.duplicates||(r[f]=m)}return r},u=function(e,t,r,n){var i=0;if(e.length>0&&"[]"===e[e.length-1]){var a=e.slice(0,-1).join("");i=Array.isArray(t)&&t[a]?t[a].length:0}for(var l=n?t:s(t,r,i),u=e.length-1;u>=0;--u){var c,p=e[u];if("[]"===p&&r.parseArrays)c=r.allowEmptyArrays&&(""===l||r.strictNullHandling&&null===l)?[]:o.combine([],l);else{c=r.plainObjects?{__proto__:null}:{};var d="["===p.charAt(0)&&"]"===p.charAt(p.length-1)?p.slice(1,-1):p,h=r.decodeDotInKeys?d.replace(/%2E/g,"."):d,f=parseInt(h,10);r.parseArrays||""!==h?!isNaN(f)&&p!==h&&String(f)===h&&f>=0&&r.parseArrays&&f<=r.arrayLimit?(c=[])[f]=l:"__proto__"!==h&&(c[h]=l):c={0:l}}l=c}return l},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var p=0;r.depth>0&&null!==(s=a.exec(i))&&p<r.depth;){if(p+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},p=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}};e.exports=function(e,t){var r=p(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],d=c(u,n[u],r,"string"==typeof e);i=o.merge(i,d,r)}return!0===r.allowSparse?i:o.compact(i)}},2503:(e,t,r)=>{"use strict";var o=r(4823),n=r(1847),i=r(6815),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},p=Date.prototype.toISOString,d=i.default,h={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:d,formatter:i.formatters[d],indices:!1,serializeDate:function(e){return p.call(e)},skipNulls:!1,strictNullHandling:!1},f={},m=function e(t,r,i,a,s,u,p,d,m,y,v,P,g,_,T,E,b,S){for(var O,x,w=t,A=S,R=0,C=!1;void 0!==(A=A.get(f))&&!C;){var j=A.get(t);if(R+=1,void 0!==j){if(j===R)throw RangeError("Cyclic object value");C=!0}void 0===A.get(f)&&(R=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return m&&!E?m(r,h.encoder,b,"key",_):r;w=""}if("string"==typeof(O=w)||"number"==typeof O||"boolean"==typeof O||"symbol"==typeof O||"bigint"==typeof O||n.isBuffer(w))return m?[T(E?r:m(r,h.encoder,b,"key",_))+"="+T(m(w,h.encoder,b,"value",_))]:[T(r)+"="+T(String(w))];var k=[];if(void 0===w)return k;if("comma"===i&&l(w))E&&m&&(w=n.maybeMap(w,m)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))x=y;else{var G=Object.keys(w);x=v?G.sort(v):G}var I=d?String(r).replace(/\./g,"%2E"):String(r),D=a&&l(w)&&1===w.length?I+"[]":I;if(s&&l(w)&&0===w.length)return D+"[]";for(var N=0;N<x.length;++N){var M=x[N],U="object"==typeof M&&M&&void 0!==M.value?M.value:w[M];if(!p||null!==U){var q=P&&d?String(M).replace(/\./g,"%2E"):String(M),F=l(w)?"function"==typeof i?i(D,q):D:D+(P?"."+q:"["+q+"]");S.set(t,R);var L=o();L.set(f,S),c(k,e(U,F,i,a,s,u,p,d,"comma"===i&&E&&l(w)?null:m,y,v,P,g,_,T,E,b,L))}}return k},y=function(e){if(!e)return h;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||h.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=h.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":h.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||h.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:h.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:h.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:h.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?h.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:h.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:h.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:h.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:h.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:h.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:h.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:h.strictNullHandling}};e.exports=function(e,t){var r,n=e,i=y(t);"function"==typeof i.filter?n=(0,i.filter)("",n):l(i.filter)&&(r=i.filter);var a=[];if("object"!=typeof n||null===n)return"";var u=s[i.arrayFormat],p="comma"===u&&i.commaRoundTrip;r||(r=Object.keys(n)),i.sort&&r.sort(i.sort);for(var d=o(),h=0;h<r.length;++h){var f=r[h],v=n[f];i.skipNulls&&null===v||c(a,m(v,f,u,p,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,d))}var P=a.join(i.delimiter),g=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),P.length>0?g+P:""}},1847:(e,t,r)=>{"use strict";var o=r(6815),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,p=[],d=0;d<c.length;++d){var h=c.charCodeAt(d);if(45===h||46===h||95===h||126===h||h>=48&&h<=57||h>=65&&h<=90||h>=97&&h<=122||i===o.RFC1738&&(40===h||41===h)){p[p.length]=c.charAt(d);continue}if(h<128){p[p.length]=a[h];continue}if(h<2048){p[p.length]=a[192|h>>6]+a[128|63&h];continue}if(h<55296||h>=57344){p[p.length]=a[224|h>>12]+a[128|h>>6&63]+a[128|63&h];continue}d+=1,h=65536+((1023&h)<<10|1023&c.charCodeAt(d)),p[p.length]=a[240|h>>18]+a[128|h>>12&63]+a[128|h>>6&63]+a[128|63&h]}l+=p.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},2185:(e,t,r)=>{"use strict";var o=r(9966),n=r(7445),i=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o},a=function(e,t){if(e){var r=i(e,t);return r&&r.value}},s=function(e,t,r){var o=i(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}},l=function(e,t){if(e)return i(e,t,!0)};e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+o(e))},delete:function(t){var r=e&&e.next,o=l(e,t);return o&&r&&r===o&&(e=void 0),!!o},get:function(t){return a(e,t)},has:function(t){var r;return!!(r=e)&&!!i(r,t)},set:function(t,r){e||(e={next:void 0}),s(e,t,r)}};return t}},417:(e,t,r)=>{"use strict";var o=r(2749),n=r(8363),i=r(9966),a=r(7445),s=o("%Map%",!0),l=n("Map.prototype.get",!0),u=n("Map.prototype.set",!0),c=n("Map.prototype.has",!0),p=n("Map.prototype.delete",!0),d=n("Map.prototype.size",!0);e.exports=!!s&&function(){var e,t={assert:function(e){if(!t.has(e))throw new a("Side channel does not contain "+i(e))},delete:function(t){if(e){var r=p(e,t);return 0===d(e)&&(e=void 0),r}return!1},get:function(t){if(e)return l(e,t)},has:function(t){return!!e&&c(e,t)},set:function(t,r){e||(e=new s),u(e,t,r)}};return t}},3457:(e,t,r)=>{"use strict";var o=r(2749),n=r(8363),i=r(9966),a=r(417),s=r(7445),l=o("%WeakMap%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),p=n("WeakMap.prototype.has",!0),d=n("WeakMap.prototype.delete",!0);e.exports=l?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new s("Side channel does not contain "+i(e))},delete:function(r){if(l&&r&&("object"==typeof r||"function"==typeof r)){if(e)return d(e,r)}else if(a&&t)return t.delete(r);return!1},get:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?u(e,r):t&&t.get(r)},has:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?p(e,r):!!t&&t.has(r)},set:function(r,o){l&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new l),c(e,r,o)):a&&(t||(t=a()),t.set(r,o))}};return r}:a},4823:(e,t,r)=>{"use strict";var o=r(7445),n=r(9966),i=r(2185),a=r(417),s=r(3457)||a||i;e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+n(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=s()),e.set(t,r)}};return t}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[276,972],()=>r(8978));module.exports=o})();