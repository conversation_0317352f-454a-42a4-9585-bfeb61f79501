"use strict";(()=>{var e={};e.id=912,e.ids=[912],e.modules={3524:e=>{e.exports=require("@prisma/client")},2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4770:e=>{e.exports=require("crypto")},837:(e,t,r)=>{let n,i,a,o,s;r.r(t),r.d(t,{originalPathname:()=>os,patchFetch:()=>oc,requestAsyncStorage:()=>oi,routeModule:()=>on,serverHooks:()=>oo,staticGenerationAsyncStorage:()=>oa});var c={};r.r(c),r.d(c,{parse:()=>tV,serialize:()=>tY});var l={};r.r(l),r.d(l,{GET:()=>or,POST:()=>or,authOptions:()=>ot});var u=r(9303),d=r(8716),p=r(670),h=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},f=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function y(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class m{constructor(e,t,r){if(rk.add(this),rA.set(this,{}),rS.set(this,void 0),rE.set(this,void 0),h(this,rE,r,"f"),h(this,rS,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(f(this,rA,"f")[e]=r)}get value(){return Object.keys(f(this,rA,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>f(this,rA,"f")[e]).join("")}chunk(e,t){let r=f(this,rk,"m",rT).call(this);for(let n of f(this,rk,"m",rx).call(this,{name:f(this,rS,"f").name,value:e,options:{...f(this,rS,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(f(this,rk,"m",rT).call(this))}}rA=new WeakMap,rS=new WeakMap,rE=new WeakMap,rk=new WeakSet,rx=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return f(this,rA,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),f(this,rA,"f")[t]=i}return f(this,rE,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rT=function(){let e={};for(let t in f(this,rA,"f"))delete f(this,rA,"f")?.[t],e[t]={name:t,value:"",options:{...f(this,rS,"f").options,maxAge:0}};return e};class w extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class g extends w{}g.kind="signIn";class b extends w{}b.type="AdapterError";class _ extends w{}_.type="AccessDenied";class v extends w{}v.type="CallbackRouteError";class k extends w{}k.type="ErrorPageLoop";class A extends w{}A.type="EventError";class S extends w{}S.type="InvalidCallbackUrl";class E extends g{constructor(){super(...arguments),this.code="credentials"}}E.type="CredentialsSignin";class x extends w{}x.type="InvalidEndpoints";class T extends w{}T.type="InvalidCheck";class C extends w{}C.type="JWTSessionError";class P extends w{}P.type="MissingAdapter";class U extends w{}U.type="MissingAdapterMethods";class R extends w{}R.type="MissingAuthorize";class O extends w{}O.type="MissingSecret";class $ extends g{}$.type="OAuthAccountNotLinked";class H extends g{}H.type="OAuthCallbackError";class I extends w{}I.type="OAuthProfileParseError";class W extends w{}W.type="SessionTokenError";class j extends g{}j.type="OAuthSignInError";class K extends g{}K.type="EmailSignInError";class D extends w{}D.type="SignOutError";class L extends w{}L.type="UnknownAction";class N extends w{}N.type="UnsupportedStrategy";class J extends w{}J.type="InvalidProvider";class M extends w{}M.type="UntrustedHost";class B extends w{}B.type="Verification";class z extends g{}z.type="MissingCSRF";let F=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class q extends w{}q.type="DuplicateConditionalUI";class V extends w{}V.type="MissingWebAuthnAutocomplete";class G extends w{}G.type="WebAuthnVerificationError";class X extends g{}X.type="AccountNotLinked";class Y extends w{}Y.type="ExperimentalFeatureNotEnabled";let Z=!1;function Q(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let ee=!1,et=!1,er=!1,en=["createVerificationToken","useVerificationToken","getUserByEmail"],ei=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],ea=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var eo=r(4770);let es=(e,t,r,n,i)=>{let a=parseInt(e.substr(3),10)>>3||20,o=(0,eo.createHmac)(e,r.byteLength?r:new Uint8Array(a)).update(t).digest(),s=Math.ceil(i/a),c=new Uint8Array(a*s+n.byteLength+1),l=0,u=0;for(let t=1;t<=s;t++)c.set(n,u),c[u+n.byteLength]=t,c.set((0,eo.createHmac)(e,o).update(c.subarray(l,u+n.byteLength+1)).digest(),u),l=u,u+=a;return c.slice(0,i)};"function"!=typeof eo.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{eo.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let ec=async(e,t,r,i,a)=>(n||es)(e,t,r,i,a);function el(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function eu(e,t,r,n,i){return ec(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=el(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),el(r,"salt"),function(e){let t=el(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let ed=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},ep=new TextEncoder,eh=new TextDecoder;function ef(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function ey(e,t,r){if(t<0||t>=4294967296)throw RangeError(`value must be >= 0 and <= ${4294967296-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function em(e){let t=new Uint8Array(8);return ey(t,Math.floor(e/4294967296),0),ey(t,e%4294967296,4),t}function ew(e){let t=new Uint8Array(4);return ey(t,e),t}function eg(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:eh.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=eh.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function eb(e){let t=e;return("string"==typeof t&&(t=ep.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class e_ extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ev extends e_{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ek extends e_{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eA extends e_{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class eS extends e_{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class eE extends e_{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class ex extends e_{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eT extends e_{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eC extends e_{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eP extends e_{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eU(e){if(!eR(e))throw Error("CryptoKey instance expected")}function eR(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function eO(e){return e?.[Symbol.toStringTag]==="KeyObject"}let e$=e=>eR(e)||eO(e),eH=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function eI(e){return eH(e)&&"string"==typeof e.kty}function eW(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let ej=(e,...t)=>eW("Key must be ",e,...t);function eK(e,t,...r){return eW(`Key for the ${e} algorithm must be `,t,...r)}async function eD(e){if(eO(e)){if("secret"!==e.type)return e.export({format:"jwk"});e=e.export()}if(e instanceof Uint8Array)return{kty:"oct",k:eb(e)};if(!eR(e))throw TypeError(ej(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...a}=await crypto.subtle.exportKey("jwk",e);return a}async function eL(e){return eD(e)}let eN=(e,t)=>{if("string"!=typeof e||!e)throw new eC(`${t} missing or invalid`)};async function eJ(e,t){let r,n;if(eI(e))r=e;else if(e$(e))r=await eL(e);else throw TypeError(ej(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":eN(r.crv,'"crv" (Curve) Parameter'),eN(r.x,'"x" (X Coordinate) Parameter'),eN(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":eN(r.crv,'"crv" (Subtype of Key Pair) Parameter'),eN(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":eN(r.e,'"e" (Exponent) Parameter'),eN(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":eN(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new eS('"kty" (Key Type) Parameter missing or unsupported')}let i=ep.encode(JSON.stringify(n));return eb(await ed(t,i))}let eM=Symbol();function eB(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new eS(`Unsupported JWE Algorithm: ${e}`)}}let ez=e=>crypto.getRandomValues(new Uint8Array(eB(e)>>3)),eF=(e,t)=>{if(t.length<<3!==eB(e))throw new ex("Invalid Initialization Vector length")},eq=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new ex(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eV(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eG(e,t){return e.name===t}function eX(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eG(e.algorithm,"AES-GCM"))throw eV("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eV(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eG(e.algorithm,"AES-KW"))throw eV("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eV(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eV("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eG(e.algorithm,"PBKDF2"))throw eV("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eG(e.algorithm,"RSA-OAEP"))throw eV("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eV(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}async function eY(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(ej(r,"Uint8Array"));let a=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(a>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,a>>3),{hash:`SHA-${a<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=ef(i,n,c,em(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,a>>3)),iv:n}}async function eZ(e,t,r,n,i){let a;r instanceof Uint8Array?a=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eX(r,e,"encrypt"),a=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},a,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let eQ=async(e,t,r,n,i)=>{if(!eR(r)&&!(r instanceof Uint8Array))throw TypeError(ej(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?eF(e,n):n=ez(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eq(r,parseInt(e.slice(-3),10)),eY(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eq(r,parseInt(e.slice(1,4),10)),eZ(e,t,r,n,i);default:throw new eS("Unsupported JWE Content Encryption Algorithm")}};function e0(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function e1(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(eX(e,t,r),e)}async function e2(e,t,r){let n=await e1(t,e,"wrapKey");e0(n,e);let i=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",i,n,"AES-KW"))}async function e5(e,t,r){let n=await e1(t,e,"unwrapKey");e0(n,e);let i=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",i))}function e6(e){return ef(ew(e.length),e)}async function e8(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(ew(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await ed("sha256",n),32*t)}return i.slice(0,t>>3)}async function e3(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let o;eX(e,"ECDH"),eX(t,"ECDH","deriveBits");let s=ef(e6(ep.encode(r)),e6(i),e6(a),ew(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e8(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e4(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e9=(e,t)=>ef(ep.encode(e),new Uint8Array([0]),t);async function e7(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new ex("PBES2 Salt Input must be 8 or more octets");let i=e9(t,e),a=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(eX(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,a))}async function te(e,t,r,n=2048,i=crypto.getRandomValues(new Uint8Array(16))){let a=await e7(i,e,n,t);return{encryptedKey:await e2(e.slice(-6),a,r),p2c:n,p2s:eb(i)}}async function tt(e,t,r,n,i){let a=await e7(i,e,n,t);return e5(e.slice(-6),a,r)}let tr=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},tn=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new eS(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function ti(e,t,r){return eX(t,e,"encrypt"),tr(e,t),new Uint8Array(await crypto.subtle.encrypt(tn(e),t,r))}async function ta(e,t,r){return eX(t,e,"decrypt"),tr(e,t),new Uint8Array(await crypto.subtle.decrypt(tn(e),t,r))}let to=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new eS('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eS('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eS('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new eS('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},ts=async(e,t,r,n=!1)=>{let a=(i||=new WeakMap).get(e);if(a?.[r])return a[r];let o=await to({...t,alg:r});return n&&Object.freeze(e),a?a[r]=o:i.set(e,{[r]:o}),o},tc=(e,t)=>{let r;let n=(i||=new WeakMap).get(e);if(n?.[t])return n[t];let a="public"===e.type,o=!!a;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,a?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[a?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,a?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[a?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,a?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:i.set(e,{[t]:r}),r},tl=async(e,t)=>{if(e instanceof Uint8Array||eR(e))return e;if(eO(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return tc(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return ts(e,r,t)}if(eI(e))return e.k?eg(e.k):ts(e,e,t,!0);throw Error("unreachable")};function tu(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new eS(`Unsupported JWE Algorithm: ${e}`)}}let td=e=>crypto.getRandomValues(new Uint8Array(tu(e)>>3));async function tp(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),i=new Uint8Array(await crypto.subtle.sign(r,n,e)),a=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=i[s]^a[s];return 0===o}async function th(e,t,r,n,i,a){let o,s;if(!(t instanceof Uint8Array))throw TypeError(ej(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=ef(a,n,r,em(a.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{o=await tp(i,p)}catch{}if(!o)throw new eE;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new eE;return s}async function tf(e,t,r,n,i,a){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eX(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,ef(r,i)))}catch{throw new eE}}let ty=async(e,t,r,n,i,a)=>{if(!eR(t)&&!(t instanceof Uint8Array))throw TypeError(ej(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new ex("JWE Initialization Vector missing");if(!i)throw new ex("JWE Authentication Tag missing");switch(eF(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eq(t,parseInt(e.slice(-3),10)),th(e,t,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eq(t,parseInt(e.slice(1,4),10)),tf(e,t,r,n,i,a);default:throw new eS("Unsupported JWE Content Encryption Algorithm")}};async function tm(e,t,r,n){let i=e.slice(0,7),a=await eQ(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:eb(a.iv),tag:eb(a.tag)}}async function tw(e,t,r,n,i){return ty(e.slice(0,7),t,r,n,i,new Uint8Array(0))}let tg=async(e,t,r,n,i={})=>{let a,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(eU(r),!e4(r))throw new eS("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=i;c=i.epk?await tl(i.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:h,kty:f}=await eL(c),y=await e3(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?tu(t):parseInt(e.slice(-5,-2),10),l,u);if(o={epk:{x:d,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),l&&(o.apu=eb(l)),u&&(o.apv=eb(u)),"ECDH-ES"===e){s=y;break}s=n||td(t);let m=e.slice(-6);a=await e2(m,y,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||td(t),eU(r),a=await ti(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||td(t);let{p2c:c,p2s:l}=i;({encryptedKey:a,...o}=await te(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||td(t),a=await e2(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||td(t);let{iv:c}=i;({encryptedKey:a,...o}=await tm(e,r,s,c));break}default:throw new eS('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:a,parameters:o}},tb=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},t_=(e,t,r,n,i)=>{let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(o))throw new eS(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tv=e=>e?.[Symbol.toStringTag],tk=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},tA=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(eI(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tk(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!e$(t))throw TypeError(eK(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tv(t)} instances for symmetric algorithms must be of type "secret"`)}},tS=(e,t,r)=>{if(eI(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tk(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tk(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!e$(t))throw TypeError(eK(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tv(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tv(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tv(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tv(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tv(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tE=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tA(e,t,r):tS(e,t,r)};class tx{#e;#t;#r;#n;#i;#a;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#i=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,i,a,o;if(!this.#t&&!this.#n&&!this.#r)throw new ex("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tb(this.#t,this.#n,this.#r))throw new ex("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(t_(ex,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new eS('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new ex('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new ex('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#a&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);tE("dir"===c?l:c,e,"encrypt");{let i;let a=await tl(e,c);({cek:n,encryptedKey:r,parameters:i}=await tg(c,l,a,this.#a,this.#s)),i&&(t&&eM in t?this.#n?this.#n={...this.#n,...i}:this.setUnprotectedHeader(i):this.#t?this.#t={...this.#t,...i}:this.setProtectedHeader(i))}a=this.#t?ep.encode(eb(JSON.stringify(this.#t))):ep.encode(""),this.#i?(o=eb(this.#i),i=ef(a,ep.encode("."),ep.encode(o))):i=a;let{ciphertext:u,tag:d,iv:p}=await eQ(l,this.#e,n,this.#o,i),h={ciphertext:eb(u)};return p&&(h.iv=eb(p)),d&&(h.tag=eb(d)),r&&(h.encrypted_key=eb(r)),o&&(h.aad=o),this.#t&&(h.protected=eh.decode(a)),this.#r&&(h.unprotected=this.#r),this.#n&&(h.header=this.#n),h}}class tT{#c;constructor(e){this.#c=new tx(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tC=e=>Math.floor(e.getTime()/1e3),tP=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tU=e=>{let t;let r=tP.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(31557600*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tR(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tO=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,t$=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tH{#l;constructor(e){if(!eH(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return ep.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=tR("setNotBefore",e):e instanceof Date?this.#l.nbf=tR("setNotBefore",tC(e)):this.#l.nbf=tC(new Date)+tU(e)}set exp(e){"number"==typeof e?this.#l.exp=tR("setExpirationTime",e):e instanceof Date?this.#l.exp=tR("setExpirationTime",tC(e)):this.#l.exp=tC(new Date)+tU(e)}set iat(e){void 0===e?this.#l.iat=tC(new Date):e instanceof Date?this.#l.iat=tR("setIssuedAt",tC(e)):"string"==typeof e?this.#l.iat=tR("setIssuedAt",tC(new Date)+tU(e)):this.#l.iat=tR("setIssuedAt",e)}}class tI{#a;#o;#s;#t;#u;#d;#p;#h;constructor(e={}){this.#h=new tH(e)}setIssuer(e){return this.#h.iss=e,this}setSubject(e){return this.#h.sub=e,this}setAudience(e){return this.#h.aud=e,this}setJti(e){return this.#h.jti=e,this}setNotBefore(e){return this.#h.nbf=e,this}setExpirationTime(e){return this.#h.exp=e,this}setIssuedAt(e){return this.#h.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#p=!0,this}async encrypt(e,t){let r=new tT(this.#h.data());return this.#t&&(this.#u||this.#d||this.#p)&&(this.#t={...this.#t,iss:this.#u?this.#h.iss:void 0,sub:this.#d?this.#h.sub:void 0,aud:this.#p?this.#h.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#a&&r.setContentEncryptionKey(this.#a),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tW(e,t,r){let n;if(!eH(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return eg(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new eS('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return to({...e,alg:t,ext:n});default:throw new eS('Unsupported "kty" (Key Type) Parameter value')}}let tj=async(e,t,r,n,i)=>{switch(e){case"dir":if(void 0!==r)throw new ex("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new ex("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!eH(n.epk))throw new ex('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eU(t),!e4(t))throw new eS("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tW(n.epk,e);if(eU(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new ex('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=eg(n.apu)}catch{throw new ex("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new ex('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=eg(n.apv)}catch{throw new ex("Failed to base64url decode the apv")}}let s=await e3(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?tu(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return s;if(void 0===r)throw new ex("JWE Encrypted Key missing");return e5(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new ex("JWE Encrypted Key missing");return eU(t),ta(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new ex("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new ex('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(n.p2c>o)throw new ex('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new ex('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=eg(n.p2s)}catch{throw new ex("Failed to base64url decode the p2s")}return tt(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new ex("JWE Encrypted Key missing");return e5(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new ex("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new ex('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new ex('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=eg(n.iv)}catch{throw new ex("Failed to base64url decode the iv")}try{a=eg(n.tag)}catch{throw new ex("Failed to base64url decode the tag")}return tw(e,t,r,i,a)}default:throw new eS('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tK=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tD(e,t,r){let n,i,a,o,s,c,l;if(!eH(e))throw new ex("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new ex("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new ex("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new ex("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new ex("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new ex("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new ex("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new ex("JWE AAD incorrect type");if(void 0!==e.header&&!eH(e.header))throw new ex("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eH(e.unprotected))throw new ex("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=eg(e.protected);n=JSON.parse(eh.decode(t))}catch{throw new ex("JWE Protected Header is invalid")}if(!tb(n,e.header,e.unprotected))throw new ex("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(t_(ex,new Map,r?.crit,n,u),void 0!==u.zip)throw new eS('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new ex("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new ex("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&tK("keyManagementAlgorithms",r.keyManagementAlgorithms),f=r&&tK("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new eA('"alg" (Algorithm) Header Parameter value not allowed');if(f&&!f.has(p))throw new eA('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=eg(e.encrypted_key)}catch{throw new ex("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(n,e),y=!0),tE("dir"===d?p:d,t,"decrypt");let m=await tl(t,d);try{a=await tj(d,m,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof ex||e instanceof eS)throw e;a=td(p)}if(void 0!==e.iv)try{o=eg(e.iv)}catch{throw new ex("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=eg(e.tag)}catch{throw new ex("Failed to base64url decode the tag")}let w=ep.encode(e.protected??"");c=void 0!==e.aad?ef(w,ep.encode("."),ep.encode(e.aad)):w;try{l=eg(e.ciphertext)}catch{throw new ex("Failed to base64url decode the ciphertext")}let g={plaintext:await ty(p,a,l,o,s,c)};if(void 0!==e.protected&&(g.protectedHeader=n),void 0!==e.aad)try{g.additionalAuthenticatedData=eg(e.aad)}catch{throw new ex("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(g.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(g.unprotectedHeader=e.header),y)?{...g,key:m}:g}async function tL(e,t,r){if(e instanceof Uint8Array&&(e=eh.decode(e)),"string"!=typeof e)throw new ex("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new ex("Invalid Compact JWE");let l=await tD({ciphertext:o,iv:a||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function tN(e,t,r){let n=await tL(e,t,r),i=function(e,t,r={}){let n,i;try{n=JSON.parse(eh.decode(t))}catch{}if(!eH(n))throw new eT("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||tO(e.typ)!==tO(a)))throw new ev('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ev(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ev('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ev('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!t$(n.aud,"string"==typeof l?[l]:l))throw new ev('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=tU(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=tC(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new ev('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ev('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>h+i)throw new ev('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ev('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=h-i)throw new ek('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=h-n.iat;if(e-i>("number"==typeof u?u:tU(u)))throw new ek('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new ev('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new ev('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new ev('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new ev('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:a};return"function"==typeof t?{...o,key:n.key}:o}let tJ=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tM=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tB=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tz=/^[\u0020-\u003A\u003D-\u007E]*$/,tF=Object.prototype.toString,tq=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tV(e,t){let r=new tq,n=e.length;if(n<2)return r;let i=t?.decode||tZ,a=0;do{let t=e.indexOf("=",a);if(-1===t)break;let o=e.indexOf(";",a),s=-1===o?n:o;if(t>s){a=e.lastIndexOf(";",t-1)+1;continue}let c=tG(e,a,t),l=tX(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=tG(e,t+1,s),a=tX(e,s,n),o=i(e.slice(n,a));r[u]=o}a=s+1}while(a<n);return r}function tG(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tX(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tY(e,t,r){let n=r?.encode||encodeURIComponent;if(!tJ.test(e))throw TypeError(`argument name is invalid: ${e}`);let i=n(t);if(!tM.test(i))throw TypeError(`argument val is invalid: ${t}`);let a=e+"="+i;if(!r)return a;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);a+="; Max-Age="+r.maxAge}if(r.domain){if(!tB.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);a+="; Domain="+r.domain}if(r.path){if(!tz.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);a+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tF.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.partitioned&&(a+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return a}function tZ(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{parse:tQ}=c,t0=()=>Date.now()/1e3|0,t1="A256CBC-HS512";async function t2(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],o=await t6(t1,a[0],i),s=await eJ({kty:"oct",k:eb(o)},`sha${o.byteLength<<3}`);return await new tI(t).setProtectedHeader({alg:"dir",enc:t1,kid:s}).setIssuedAt().setExpirationTime(t0()+n).setJti(crypto.randomUUID()).encrypt(o)}async function t5(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await tN(t,async({kid:e,enc:t})=>{for(let r of i){let i=await t6(t,r,n);if(void 0===e||e===await eJ({kty:"oct",k:eb(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[t1,"A256GCM"]});return a}async function t6(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await eu("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t8({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let t3="\x1b[31m",t4="\x1b[0m",t9={error(e){let t=e instanceof w?e.type:e.name;if(console.error(`${t3}[auth][error]${t4} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t3}[auth][cause]${t4}:`,t.stack),r&&console.error(`${t3}[auth][details]${t4}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t4}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t4} ${e}`,JSON.stringify(t,null,2))}};function t7(e){let t={...t9};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let re=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{parse:rt,serialize:rr}=c;async function rn(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function ri(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new L("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new L(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new L(`Cannot parse action at ${e}`);let[i,a]=n;if(!re.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new L(`Cannot parse action at ${e}`);return{action:i,providerId:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await rn(e):void 0,cookies:rt(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t7(t);r.error(n),r.debug("request",e)}}function ra(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=rr(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function ro(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function rs(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function rc({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await ro(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=rs(32),a=await ro(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function rl(e,t){if(!t)throw new z(`CSRF token was missing during an action ${e}`)}function ru(e){return null!==e&&"object"==typeof e}function rd(e,...t){if(!t.length)return e;let r=t.shift();if(ru(e)&&ru(r))for(let t in r)ru(r[t])?(ru(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rd(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rd(e,...t)}let rp=Symbol("skip-csrf-check"),rh=Symbol("return-type-raw"),rf=Symbol("custom-fetch"),ry=Symbol("conform-internal"),rm=e=>rg({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rw=e=>rg({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rg(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rb(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let r_={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rv({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:o,csrfDisabled:s,isPost:c}){var l;let u=t7(e),{providers:d,provider:p}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,o=i?.id??a.id,s=rd(a,i,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rb(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rb(e.token,e.issuer),n=rb(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??rm,account:e.account??rw}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rf]??(e[rf]=i?.[rf]),e}return s}),a=i.find(({id:e})=>e===t);if(t&&!a){let e=i.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:i,provider:a}}({url:n,providerId:t,config:e}),h=!1;if((p?.type==="oauth"||p?.type==="oidc")&&p.redirectProxyUrl)try{h=new URL(p.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${p.redirectProxyUrl}`)}let f={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:p,cookies:rd(y(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:d,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:t2,decode:t5,...e.jwt},events:Object.keys(l=e.events??{}).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new A(e))}},e),{}),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new b(r);throw t.error(e),e}},r),{})}(e.adapter,u),callbacks:{...r_,...e.callbacks},logger:u,callbackUrl:n.origin,isOnRedirectProxy:h,experimental:{...e.experimental}},m=[];if(s)f.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await rc({options:f,cookieValue:i?.[f.cookies.csrfToken.name],isPost:c,bodyValue:o});f.csrfToken=e,f.csrfTokenVerified=r,t&&m.push({name:f.cookies.csrfToken.name,value:t,options:f.cookies.csrfToken.options})}let{callbackUrl:w,callbackUrlCookie:g}=await t8({options:f,cookieValue:i?.[f.cookies.callbackUrl.name],paramValue:a});return f.callbackUrl=w,g&&m.push({name:f.cookies.callbackUrl.name,value:g,options:f.cookies.callbackUrl.options}),{options:f,cookies:m}}var rk,rA,rS,rE,rx,rT,rC,rP,rU,rR,rO,r$,rH,rI,rW,rj,rK={},rD=[],rL=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rN=Array.isArray;function rJ(e,t){for(var r in t)e[r]=t[r];return e}function rM(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rB(e,t,r){var n,i,a,o={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?rC.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return rz(e,o,n,i,null)}function rz(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++rU:i,__i:-1,__u:0};return null==i&&null!=rP.vnode&&rP.vnode(a),a}function rF(e){return e.children}function rq(e,t){this.props=e,this.context=t}function rV(e,t){if(null==t)return e.__?rV(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rV(e):null}function rG(e){(!e.__d&&(e.__d=!0)&&rR.push(e)&&!rX.__r++||rO!==rP.debounceRendering)&&((rO=rP.debounceRendering)||r$)(rX)}function rX(){var e,t,r,n,i,a,o,s;for(rR.sort(rH);e=rR.shift();)e.__d&&(t=rR.length,n=void 0,a=(i=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rJ({},i)).__v=i.__v+1,rP.vnode&&rP.vnode(n),r1(r.__P,n,i,r.__n,r.__P.namespaceURI,32&i.__u?[a]:null,o,null==a?rV(i):a,!!(32&i.__u),s),n.__v=i.__v,n.__.__k[n.__i]=n,r2(o,n,s),n.__e!=a&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rR.length>t&&rR.sort(rH));rX.__r=0}function rY(e,t,r,n,i,a,o,s,c,l,u){var d,p,h,f,y,m=n&&n.__k||rD,w=t.length;for(r.__d=c,function(e,t,r){var n,i,a,o,s,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(i=t[n])&&"boolean"!=typeof i&&"function"!=typeof i?(o=n+d,(i=e.__k[n]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?rz(null,i,null,null,null):rN(i)?rz(rF,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?rz(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,a=null,-1!==(s=i.__i=function(e,t,r,n){var i=e.key,a=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&i==c.key&&a===c.type&&0==(131072&c.__u))return r;if(n>(null!=c&&0==(131072&c.__u)?1:0))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return s;s++}}return -1}(i,r,o,u))&&(u--,(a=r[s])&&(a.__u|=131072)),null==a||null===a.__v?(-1==s&&d--,"function"!=typeof i.type&&(i.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,i.__u|=65536))):i=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(a=r[n])&&0==(131072&a.__u)&&(a.__e==e.__d&&(e.__d=rV(a)),function e(t,r,n){var i,a;if(rP.unmount&&rP.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||r5(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){rP.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||rM(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(a,a))}(r,t,m),c=r.__d,d=0;d<w;d++)null!=(h=r.__k[d])&&(p=-1===h.__i?rK:m[h.__i]||rK,h.__i=d,r1(e,h,p,i,a,o,s,c,l,u),f=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&r5(p.ref,null,h),u.push(h.ref,h.__c||f,h)),null==y&&null!=f&&(y=f),65536&h.__u||p.__k===h.__k?c=function e(t,r,n){var i,a;if("function"==typeof t.type){for(i=t.__k,a=0;i&&a<i.length;a++)i[a]&&(i[a].__=t,r=e(i[a],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rV(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(h,c,e):"function"==typeof h.type&&void 0!==h.__d?c=h.__d:f&&(c=f.nextSibling),h.__d=void 0,h.__u&=-196609);r.__d=c,r.__e=y}function rZ(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rL.test(t)?r:r+"px"}function rQ(e,t,r,n,i){var a;e:if("style"===t){if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rZ(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rZ(e.style,t,r[t])}}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase() in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n?r.u=n.u:(r.u=rI,e.addEventListener(t,a?rj:rW,a)):e.removeEventListener(t,a?rj:rW,a);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function r0(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rI++;else if(t.t<r.u)return;return r(rP.event?rP.event(t):t)}}}function r1(e,t,r,n,i,a,o,s,c,l){var u,d,p,h,f,y,m,w,g,b,_,v,k,A,S,E,x=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),a=[s=t.__e=r.__e]),(u=rP.__b)&&u(t);e:if("function"==typeof x)try{if(w=t.props,g="prototype"in x&&x.prototype.render,b=(u=x.contextType)&&n[u.__c],_=u?b?b.props.value:u.__:n,r.__c?m=(d=t.__c=r.__c).__=d.__E:(g?t.__c=d=new x(w,_):(t.__c=d=new rq(w,_),d.constructor=x,d.render=r6),b&&b.sub(d),d.props=w,d.state||(d.state={}),d.context=_,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),g&&null==d.__s&&(d.__s=d.state),g&&null!=x.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rJ({},d.__s)),rJ(d.__s,x.getDerivedStateFromProps(w,d.__s))),h=d.props,f=d.state,d.__v=t,p)g&&null==x.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),g&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(g&&null==x.getDerivedStateFromProps&&w!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(w,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(w,d.__s,_)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=w,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(w,d.__s,_),g&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(h,f,y)})}if(d.context=_,d.props=w,d.__P=e,d.__e=!1,k=rP.__r,A=0,g){for(d.state=d.__s,d.__d=!1,k&&k(t),u=d.render(d.props,d.state,d.context),S=0;S<d._sb.length;S++)d.__h.push(d._sb[S]);d._sb=[]}else do d.__d=!1,k&&k(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++A<25);d.state=d.__s,null!=d.getChildContext&&(n=rJ(rJ({},n),d.getChildContext())),g&&!p&&null!=d.getSnapshotBeforeUpdate&&(y=d.getSnapshotBeforeUpdate(h,f)),rY(e,rN(E=null!=u&&u.type===rF&&null==u.key?u.props.children:u)?E:[E],t,r,n,i,a,o,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),m&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=a){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;a[a.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rP.__e(e,t,r)}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,o,s,c){var l,u,d,p,h,f,y,m=r.props,w=t.props,g=t.type;if("svg"===g?i="http://www.w3.org/2000/svg":"math"===g?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=a){for(l=0;l<a.length;l++)if((h=a[l])&&"setAttribute"in h==!!g&&(g?h.localName===g:3===h.nodeType)){e=h,a[l]=null;break}}if(null==e){if(null===g)return document.createTextNode(w);e=document.createElementNS(i,g,w.is&&w),s&&(rP.__m&&rP.__m(t,a),s=!1),a=null}if(null===g)m===w||s&&e.data===w||(e.data=w);else{if(a=a&&rC.call(e.childNodes),m=r.props||rK,!s&&null!=a)for(m={},l=0;l<e.attributes.length;l++)m[(h=e.attributes[l]).name]=h.value;for(l in m)if(h=m[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=h;else if(!(l in w)){if("value"==l&&"defaultValue"in w||"checked"==l&&"defaultChecked"in w)continue;rQ(e,l,null,h,i)}for(l in w)h=w[l],"children"==l?p=h:"dangerouslySetInnerHTML"==l?u=h:"value"==l?f=h:"checked"==l?y=h:s&&"function"!=typeof h||m[l]===h||rQ(e,l,h,m[l],i);if(u)s||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),rY(e,rN(p)?p:[p],t,r,n,"foreignObject"===g?"http://www.w3.org/1999/xhtml":i,a,o,a?a[0]:r.__k&&rV(r,0),s,c),null!=a)for(l=a.length;l--;)rM(a[l]);s||(l="value","progress"===g&&null==f?e.removeAttribute("value"):void 0===f||f===e[l]&&("progress"!==g||f)&&("option"!==g||f===m[l])||rQ(e,l,f,m[l],i),l="checked",void 0!==y&&y!==e[l]&&rQ(e,l,y,m[l],i))}return e}(r.__e,t,r,n,i,a,o,c,l);(u=rP.diffed)&&u(t)}function r2(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)r5(r[n],r[++n],r[++n]);rP.__c&&rP.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rP.__e(e,t.__v)}})}function r5(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rP.__e(e,r)}}function r6(e,t,r){return this.constructor(e,r)}function r8(e,t){var r,n,i,a,o;r=e,rP.__&&rP.__(r,t),i=(n="function"==typeof r8)?null:r8&&r8.__k||t.__k,a=[],o=[],r1(t,r=(!n&&r8||t).__k=rB(rF,null,[r]),i||rK,rK,t.namespaceURI,!n&&r8?[r8]:i?null:t.firstChild?rC.call(t.childNodes):null,a,!n&&r8?r8:i?i.__e:t.firstChild,n,o),r2(a,r,o)}rC=rD.slice,rP={__e:function(e,t,r,n){for(var i,a,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},rU=0,rq.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rJ({},this.state),"function"==typeof e&&(e=e(rJ({},r),this.props)),e&&rJ(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rG(this))},rq.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rG(this))},rq.prototype.render=rF,rR=[],r$="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rH=function(e,t){return e.__v.__b-t.__v.__b},rX.__r=0,rI=0,rW=r0(!1),rj=r0(!0);var r3=/[\s\n\\/='"\0<>]/,r4=/^(xlink|xmlns|xml)([A-Z])/,r9=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r7=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,ne=new Set(["draggable","spellcheck"]),nt=/["&<]/;function nr(e){if(0===e.length||!1===nt.test(e))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var nn={},ni=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),na=/[A-Z]/g;function no(){this.__d=!0}var ns,nc,nl,nu,nd={},np=[],nh=Array.isArray,nf=Object.assign;function ny(e,t){var r,n=e.type,i=!0;return e.__c?(i=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=nd),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=nf({},r.state,n.getDerivedStateFromProps(r.props,r.state)):i&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!i&&r.componentWillUpdate&&r.componentWillUpdate(),nl&&nl(e),r.render(r.props,r.state,t)}var nm=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),nw=0;function ng(e,t,r,n,i,a){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--nw,__i:-1,__u:0,__source:i,__self:a};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rP.vnode&&rP.vnode(l),l}async function nb(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);if(!i.ok){console.error("Failed to fetch options",i);return}return i.json()}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e){console.error("Failed to fetch option for autofill authentication");return}try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t){console.error("Failed to fetch options for form submission");return}if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let n_={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},nv=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nk({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${nv}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rP.__s;rP.__s=!0,ns=rP.__b,nc=rP.diffed,nl=rP.__r,nu=rP.unmount;var i=rB(rF,null);i.__k=[e];try{var a=function e(t,r,n,i,a,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?nr(t):t+"";if(nh(t)){var l,u="";a.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var h,f=e(p,r,n,i,a,o,s);"string"==typeof f?u+=f:(l||(l=[]),u&&l.push(u),u="",nh(f)?(h=l).push.apply(h,f):l.push(f))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=a,ns&&ns(t);var y=t.type,m=t.props;if("function"==typeof y){var w,g,b,_=r;if(y===rF){if("tpl"in m){for(var v="",k=0;k<m.tpl.length;k++)if(v+=m.tpl[k],m.exprs&&k<m.exprs.length){var A=m.exprs[k];if(null==A)continue;"object"==typeof A&&(void 0===A.constructor||nh(A))?v+=e(A,r,n,i,t,o,s):v+=A}return v}if("UNSTABLE_comment"in m)return"<!--"+nr(m.UNSTABLE_comment)+"-->";g=m.children}else{if(null!=(w=y.contextType)){var S=r[w.__c];_=S?S.props.value:w.__}var E=y.prototype&&"function"==typeof y.prototype.render;if(E)g=ny(t,_),b=t.__c;else{t.__c=b={__v:t,context:_,props:t.props,setState:no,forceUpdate:no,__d:!0,__h:[]};for(var x=0;b.__d&&x++<25;)b.__d=!1,nl&&nl(t),g=y.call(b,m,_);b.__d=!0}if(null!=b.getChildContext&&(r=nf({},r,b.getChildContext())),E&&rP.errorBoundaries&&(y.getDerivedStateFromError||b.componentDidCatch)){g=null!=g&&g.type===rF&&null==g.key&&null==g.props.tpl?g.props.children:g;try{return e(g,r,n,i,t,o,s)}catch(a){return y.getDerivedStateFromError&&(b.__s=y.getDerivedStateFromError(a)),b.componentDidCatch&&b.componentDidCatch(a,nd),b.__d?(g=ny(t,r),null!=(b=t.__c).getChildContext&&(r=nf({},r,b.getChildContext())),e(g=null!=g&&g.type===rF&&null==g.key&&null==g.props.tpl?g.props.children:g,r,n,i,t,o,s)):""}finally{nc&&nc(t),t.__=null,nu&&nu(t)}}}g=null!=g&&g.type===rF&&null==g.key&&null==g.props.tpl?g.props.children:g;try{var T=e(g,r,n,i,t,o,s);return nc&&nc(t),t.__=null,rP.unmount&&rP.unmount(t),T}catch(a){if(!o&&s&&s.onError){var C=s.onError(a,t,function(a){return e(a,r,n,i,t,o,s)});if(void 0!==C)return C;var P=rP.__e;return P&&P(a,t),""}if(!o||!a||"function"!=typeof a.then)throw a;return a.then(function a(){try{return e(g,r,n,i,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(g,r,n,i,t,o,s)},a)}})}}var U,R="<"+y,O="";for(var $ in m){var H=m[$];if("function"!=typeof H||"class"===$||"className"===$){switch($){case"children":U=H;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in m)continue;$="for";break;case"className":if("class"in m)continue;$="class";break;case"defaultChecked":$="checked";break;case"defaultSelected":$="selected";break;case"defaultValue":case"value":switch($="value",y){case"textarea":U=H;continue;case"select":i=H;continue;case"option":i!=H||"selected"in m||(R+=" selected")}break;case"dangerouslySetInnerHTML":O=H&&H.__html;continue;case"style":"object"==typeof H&&(H=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var i="-"==r[0]?r:nn[r]||(nn[r]=r.replace(na,"-$&").toLowerCase()),a=";";"number"!=typeof n||i.startsWith("--")||ni.has(i)||(a="px;"),t=t+i+":"+n+a}}return t||void 0}(H));break;case"acceptCharset":$="accept-charset";break;case"httpEquiv":$="http-equiv";break;default:if(r4.test($))$=$.replace(r4,"$1:$2").toLowerCase();else{if(r3.test($))continue;("-"===$[4]||ne.has($))&&null!=H?H+="":n?r7.test($)&&($="panose1"===$?"panose-1":$.replace(/([A-Z])/g,"-$1").toLowerCase()):r9.test($)&&($=$.toLowerCase())}}null!=H&&!1!==H&&(R=!0===H||""===H?R+" "+$:R+" "+$+'="'+("string"==typeof H?nr(H):H+"")+'"')}}if(r3.test(y))throw Error(y+" is not a valid HTML tag name in "+R+">");if(O||("string"==typeof U?O=nr(U):null!=U&&!1!==U&&!0!==U&&(O=e(U,r,"svg"===y||"foreignObject"!==y&&n,i,t,o,s))),nc&&nc(t),t.__=null,nu&&nu(t),!O&&nm.has(y))return R+"/>";var I="</"+y+">",W=R+">";return nh(O)?[W].concat(O,[I]):"string"!=typeof O?[W,O,I]:W+O+I}(e,nd,!1,void 0,i,!1,void 0);return nh(a)?a.join(""):a}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rP.__c&&rP.__c(e,np),rP.__s=n,np.length=0}}(e)}</div></body></html>`}}function nA(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,s){if(t)throw new L("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nk({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:o}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=o&&(n_[o]??n_.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return ng("div",{className:"signin",children:[i?.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),ng("div",{className:"card",children:[s&&ng("div",{className:"error",children:ng("p",{children:s})}),i?.logo&&ng("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return ng("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?ng("form",{action:e.signinUrl,method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),n&&ng("input",{type:"hidden",name:"callbackUrl",value:n}),ng("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[ng("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&ng("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&ng("hr",{}),"email"===e.type&&ng("form",{action:e.signinUrl,method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),ng("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),ng("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),ng("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&ng("form",{action:e.callbackUrl,method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>ng("div",{children:[ng("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),ng("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),ng("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&ng("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[ng("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>ng("div",{children:[ng("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),ng("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),ng("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&ng("hr",{})]},e.id)})]}),c&&ng(rF,{children:ng("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nb})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:nk({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return ng("div",{className:"signout",children:[n?.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),ng("div",{className:"card",children:[n?.logo&&ng("img",{src:n.logo,alt:"Logo",className:"logo"}),ng("h1",{children:"Signout"}),ng("p",{children:"Are you sure you want to sign out?"}),ng("form",{action:t?.toString(),method:"POST",children:[ng("input",{type:"hidden",name:"csrfToken",value:r}),ng("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:`${a.verifyRequest}${t?.search??""}`,cookies:i}:nk({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return ng("div",{className:"verify-request",children:[r.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),ng("div",{className:"card",children:[r.logo&&ng("img",{src:r.logo,alt:"Logo",className:"logo"}),ng("h1",{children:"Check your email"}),ng("p",{children:"A sign in link has been sent to your email address."}),ng("p",{children:ng("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:nk({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:ng("p",{children:ng("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:ng("div",{children:[ng("p",{children:"There is a problem with the server configuration."}),ng("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:ng("div",{children:[ng("p",{children:"You do not have permission to sign in."}),ng("p",{children:ng("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:ng("div",{children:[ng("p",{children:"The sign in link is no longer valid."}),ng("p",{children:"It may have been used already or it may have expired."})]}),signin:ng("a",{className:"button",href:i,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=a[r]??a.default;return{status:o,html:ng("div",{className:"error",children:[n?.brandColor&&ng("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),ng("div",{className:"card",children:[n?.logo&&ng("img",{src:n?.logo,alt:"Logo",className:"logo"}),ng("h1",{children:s}),ng("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nS(e,t=Date.now()){return new Date(t+1e3*e)}async function nE(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:h,getUserByEmail:f,linkAccount:y,createSession:m,getSessionAndUser:w,deleteSession:g}=i,b=null,_=null,v=!1,k="jwt"===s;if(e){if(k)try{let t=n.cookies.sessionToken.name;(b=await a.decode({...a,token:e,salt:t}))&&"sub"in b&&b.sub&&(_=await p(b.sub))}catch{}else{let t=await w(e);t&&(b=t.session,_=t.user)}}if("email"===l.type){let r=await f(t.email);return r?(_?.id!==r.id&&!k&&e&&await g(e),_=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:_})):(_=await u({...t,emailVerified:new Date}),await o.createUser?.({user:_}),v=!0),{session:b=k?{}:await m({sessionToken:c(),userId:_.id,expires:nS(n.session.maxAge)}),user:_,isNewUser:v}}if("webauthn"===l.type){let e=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(_){if(e.id===_.id){let e={...l,userId:_.id};return{session:b,user:_,isNewUser:v,account:e}}throw new X("The account is already associated with another user",{provider:l.provider})}b=k?{}:await m({sessionToken:c(),userId:e.id,expires:nS(n.session.maxAge)});let t={...l,userId:e.id};return{session:b,user:e,isNewUser:v,account:t}}{if(_){await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t});let e={...l,userId:_.id};return{session:b,user:_,isNewUser:v,account:e}}if(t.email?await f(t.email):null)throw new X("Another account already exists with the same e-mail address",{provider:l.provider});_=await u({...t}),await o.createUser?.({user:_}),await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t}),b=k?{}:await m({sessionToken:c(),userId:_.id,expires:nS(n.session.maxAge)});let e={...l,userId:_.id};return{session:b,user:_,isNewUser:!0,account:e}}}let A=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(A){if(_){if(A.id===_.id)return{session:b,user:_,isNewUser:v};throw new $("The account is already associated with another user",{provider:l.provider})}return{session:b=k?{}:await m({sessionToken:c(),userId:A.id,expires:nS(n.session.maxAge)}),user:A,isNewUser:v}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:s}),_)return await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t}),{session:b,user:_,isNewUser:v};let p=t.email?await f(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)_=p,v=!1;else throw new $("Another account already exists with the same e-mail address",{provider:l.provider})}else _=await u({...t,emailVerified:null}),v=!0;return await o.createUser?.({user:_}),await y({...l,userId:_.id}),await o.linkAccount?.({user:_,account:l,profile:t}),{session:b=k?{}:await m({sessionToken:c(),userId:_.id,expires:nS(n.session.maxAge)}),user:_,isNewUser:v}}}function nx(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.5.1");let nT="ERR_INVALID_ARG_VALUE",nC="ERR_INVALID_ARG_TYPE";function nP(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nU=Symbol(),nR=Symbol(),nO=Symbol(),n$=Symbol(),nH=Symbol(),nI=Symbol(),nW=Symbol(),nj=new TextEncoder,nK=new TextDecoder;function nD(e){return"string"==typeof e?nj.encode(e):nK.decode(e)}function nL(e){return"string"==typeof e?function(e){try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nP("The input to be decoded is not correctly encoded.",nT,e)}}(e):function(e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(e)}class nN extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iq,Error.captureStackTrace?.(this,this.constructor)}}class nJ extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nM(e,t,r){return new nJ(e,{code:t,cause:r})}function nB(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nz(e){nx(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw nP('"options.headers" must not include the "authorization" header name',nT);return t}function nF(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nP('"options.signal" must return or be an instance of AbortSignal',nC);return e}function nq(e){return e.includes("//")?e.replace("//","/"):e}async function nV(e,t,r,n){if(!(e instanceof URL))throw nP(`"${t}" must be an instance of URL`,nC);ir(e,n?.[nU]!==!0);let i=r(new URL(e.href)),a=nz(n?.headers);return a.set("accept","application/json"),(n?.[n$]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:n?.signal?nF(n.signal):void 0})}async function nG(e,t){return nV(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nq(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r;r=".well-known/oauth-authorization-server","/"===e.pathname?e.pathname=r:e.pathname=nq(`${r}/${e.pathname}`);break;default:throw nP('"options.algorithm" must be "oidc" (default), or "oauth2"',nT)}return e},t)}function nX(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw nP(`${r} must be a number`,nC,i);if(e>0)return;if(t){if(0!==e)throw nP(`${r} must be a non-negative number`,nT,i);return}throw nP(`${r} must be a positive number`,nT,i)}catch(e){if(n)throw nM(e.message,n,i);throw e}}function nY(e,t,r,n){try{if("string"!=typeof e)throw nP(`${t} must be a string`,nC,n);if(0===e.length)throw nP(`${t} must not be empty`,nT,n)}catch(e){if(r)throw nM(e.message,r,n);throw e}}async function nZ(e,t){if(!(e instanceof URL)&&e!==ah)throw nP('"expectedIssuerIdentifier" must be an instance of URL',nC);if(!nx(t,Response))throw nP('"response" must be an instance of Response',nC);if(200!==t.status)throw nM('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',iQ,t);i9(t);let r=await ad(t);if(nY(r.issuer,'"response" body "issuer" property',iY,{body:r}),e!==ah&&new URL(r.issuer).href!==e.href)throw nM('"response" body "issuer" property does not match the expected value',i6,{expected:e.href,body:r,attribute:"issuer"});return r}function nQ(e){(function(e,t){if(iA(e)!==t)throw n0(e,t)})(e,"application/json")}function n0(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nM(r,iZ,e)}function n1(){return nL(crypto.getRandomValues(new Uint8Array(32)))}async function n2(e){return nY(e,"codeVerifier"),nL(await crypto.subtle.digest("SHA-256",nD(e)))}function n5(e){let t=e?.[nR];return"number"==typeof t&&Number.isFinite(t)?t:0}function n6(e){let t=e?.[nO];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n8(){return Math.floor(Date.now()/1e3)}function n3(e){if("object"!=typeof e||null===e)throw nP('"as" must be an object',nC);nY(e.issuer,'"as.issuer"')}function n4(e){if("object"!=typeof e||null===e)throw nP('"client" must be an object',nC);nY(e.client_id,'"client.client_id"')}function n9(e,t){let r=n8()+n5(t);return{jti:n1(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function n7(e,t,r){if(!r.usages.includes("sign"))throw nP('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nT);let n=`${nL(nD(JSON.stringify(e)))}.${nL(nD(JSON.stringify(t)))}`,i=nL(await crypto.subtle.sign(ar(r),r,nD(n)));return`${n}.${i}`}async function ie(e){let{kty:t,e:r,n,x:i,y:a,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:i,y:a,crv:s};return o.set(e,c),c}let it=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function ir(e,t){if(t&&"https:"!==e.protocol)throw nM("only requests to HTTPS are allowed",i0,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nM("only HTTP and HTTPS requests are allowed",i1,e)}function ii(e,t,r,n){let i;if("string"!=typeof e||!(i=it(e)))throw nM(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?i3:i4,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return ir(i,n),i}function ia(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?ii(e.mtls_endpoint_aliases[t],t,r,n):ii(e[t],t,r,n)}class io extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iF,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class is extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iV,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class ic extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iz,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let il="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",iu=RegExp("^[,\\s]*("+il+")\\s(.*)"),id=RegExp("^[,\\s]*("+il+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ip=RegExp("^[,\\s]*("+il+")\\s*=\\s*("+il+")[,\\s]*(.*)"),ih=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function iy(e){if(e.status>399&&e.status<500){i9(e),nQ(e);try{let t=await e.clone().json();if(nB(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function im(e,t,r){if(e.status!==t){let t;if(t=await iy(e))throw await e.body?.cancel(),new io("server responded with an error in the response body",{cause:t,response:e});throw nM(`"response" is not a conform ${r} response (unexpected HTTP status code)`,iQ,e)}}function iw(e){if(!iW.has(e))throw nP('"options.DPoP" is not a valid DPoPHandle',nT)}async function ig(e,t,r,n,i,a){if(nY(e,'"accessToken"'),!(r instanceof URL))throw nP('"url" must be an instance of URL',nC);ir(r,a?.[nU]!==!0),n=nz(n),a?.DPoP&&(iw(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (a?.[n$]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:a?.signal?nF(a.signal):void 0});return a?.DPoP?.cacheNonce(o),o}async function ib(e,t,r,n){n3(e),n4(t);let i=ia(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nU]!==!0),a=nz(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),ig(r,"GET",i,a,null,{...n,[nR]:n5(t)})}function i_(e,t,r,n){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n8()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function iv(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}let ik=Symbol();function iA(e){return e.headers.get("content-type")?.split(";")[0]}async function iS(e,t,r,n,i){let a;if(n3(e),n4(t),!nx(n,Response))throw nP('"response" must be an instance of Response',nC);if(iR(n),200!==n.status)throw nM('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',iQ,n);if(i9(n),"application/jwt"===iA(n)){let{claims:r,jwt:o}=await an(await n.text(),ao.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n5(t),n6(t),i?.[nI]).then(iO.bind(void 0,t.client_id)).then(iH.bind(void 0,e));iC.set(n,o),a=r}else{if(t.userinfo_signed_response_alg)throw nM("JWT UserInfo Response expected",iG,n);a=await ad(n)}if(nY(a.sub,'"response" body "sub" property',iY,{body:a}),r===ik);else if(nY(r,'"expectedSubject"'),a.sub!==r)throw nM('unexpected "response" body "sub" property value',i6,{expected:r,body:a,attribute:"sub"});return a}async function iE(e,t,r,n,i,a,o){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[n$]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:o?.signal?nF(o.signal):void 0})}async function ix(e,t,r,n,i,a){let o=ia(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[nU]!==!0);i.set("grant_type",n);let s=nz(a?.headers);s.set("accept","application/json"),a?.DPoP!==void 0&&(iw(a.DPoP),await a.DPoP.addProof(o,s,"POST"));let c=await iE(e,t,r,o,i,s,a);return a?.DPoP?.cacheNonce(c),c}let iT=new WeakMap,iC=new WeakMap;function iP(e){if(!e.id_token)return;let t=iT.get(e);if(!t)throw nP('"ref" was already garbage collected or did not resolve from the proper sources',nT);return t}async function iU(e,t,r,n,i){if(n3(e),n4(t),!nx(r,Response))throw nP('"response" must be an instance of Response',nC);iR(r),await im(r,200,"Token Endpoint"),i9(r);let a=await ad(r);if(nY(a.access_token,'"response" body "access_token" property',iY,{body:a}),nY(a.token_type,'"response" body "token_type" property',iY,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new nN("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;nX(e,!1,'"response" body "expires_in" property',iY,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&nY(a.refresh_token,'"response" body "refresh_token" property',iY,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw nM('"response" body "scope" property must be a string',iY,{body:a});if(void 0!==a.id_token){nY(a.id_token,'"response" body "id_token" property',iY,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nX(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await an(a.id_token,ao.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n5(t),n6(t),i?.[nI]).then(iD.bind(void 0,o)).then(iI.bind(void 0,e)).then(i$.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nM('ID Token "aud" (audience) claim includes additional untrusted audiences',i5,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nM('unexpected ID Token "azp" (authorized party) claim value',i5,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nX(s.auth_time,!1,'ID Token "auth_time" (authentication time)',iY,{claims:s}),iC.set(r,c),iT.set(a,s)}return a}function iR(e){let t;if(t=function(e){if(!nx(e,Response))throw nP('"response" must be an instance of Response',nC);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(iu),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(id)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(ip)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(ih)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let o={scheme:i,parameters:a};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new ic("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function iO(e,t){return void 0!==t.claims.aud?i$(e,t):t}function i$(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nM('unexpected JWT "aud" (audience) claim value',i5,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nM('unexpected JWT "aud" (audience) claim value',i5,{expected:e,claims:t.claims,claim:"aud"});return t}function iH(e,t){return void 0!==t.claims.iss?iI(e,t):t}function iI(e,t){let r=e[af]?.(t)??e.issuer;if(t.claims.iss!==r)throw nM('unexpected JWT "iss" (issuer) claim value',i5,{expected:r,claims:t.claims,claim:"iss"});return t}let iW=new WeakSet;async function ij(e,t,r,n,i,a,o){if(n3(e),n4(t),!iW.has(n))throw nP('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nT);nY(i,'"redirectUri"');let s=as(n,"code");if(!s)throw nM('no authorization code in "callbackParameters"',iY);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),a!==ap&&(nY(a,'"codeVerifier"'),c.set("code_verifier",a)),ix(e,t,r,"authorization_code",c,o)}let iK={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function iD(e,t){for(let r of e)if(void 0===t.claims[r])throw nM(`JWT "${r}" (${iK[r]}) claim missing`,iY,{claims:t.claims});return t}let iL=Symbol(),iN=Symbol();async function iJ(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?iM(e,t,r,n.expectedNonce,n.maxAge,{[nI]:n[nI]}):iB(e,t,r,n)}async function iM(e,t,r,n,i,a){let o=[];switch(n){case void 0:n=iL;break;case iL:break;default:nY(n,'"expectedNonce" argument'),o.push("nonce")}switch(i??=t.default_max_age){case void 0:i=iN;break;case iN:break;default:nX(i,!1,'"maxAge" argument'),o.push("auth_time")}let s=await iU(e,t,r,o,a);nY(s.id_token,'"response" body "id_token" property',iY,{body:s});let c=iP(s);if(i!==iN){let e=n8()+n5(t),r=n6(t);if(c.auth_time+i<e-r)throw nM("too much time has elapsed since the last End-User authentication",i2,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===iL){if(void 0!==c.nonce)throw nM('unexpected ID Token "nonce" claim value',i5,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nM('unexpected ID Token "nonce" claim value',i5,{expected:n,claims:c,claim:"nonce"});return s}async function iB(e,t,r,n){let i=await iU(e,t,r,void 0,n),a=iP(i);if(a){if(void 0!==t.default_max_age){nX(t.default_max_age,!1,'"client.default_max_age"');let e=n8()+n5(t),r=n6(t);if(a.auth_time+t.default_max_age<e-r)throw nM("too much time has elapsed since the last End-User authentication",i2,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw nM('unexpected ID Token "nonce" claim value',i5,{expected:void 0,claims:a,claim:"nonce"})}return i}let iz="OAUTH_WWW_AUTHENTICATE_CHALLENGE",iF="OAUTH_RESPONSE_BODY_ERROR",iq="OAUTH_UNSUPPORTED_OPERATION",iV="OAUTH_AUTHORIZATION_RESPONSE_ERROR",iG="OAUTH_JWT_USERINFO_EXPECTED",iX="OAUTH_PARSE_ERROR",iY="OAUTH_INVALID_RESPONSE",iZ="OAUTH_RESPONSE_IS_NOT_JSON",iQ="OAUTH_RESPONSE_IS_NOT_CONFORM",i0="OAUTH_HTTP_REQUEST_FORBIDDEN",i1="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",i2="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",i5="OAUTH_JWT_CLAIM_COMPARISON_FAILED",i6="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",i8="OAUTH_KEY_SELECTION_FAILED",i3="OAUTH_MISSING_SERVER_METADATA",i4="OAUTH_INVALID_SERVER_METADATA";function i9(e){if(e.bodyUsed)throw nP('"response" body has been used already',nT)}async function i7(e,t){n3(e);let r=ia(e,"jwks_uri",!1,t?.[nU]!==!0),n=nz(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[n$]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nF(t.signal):void 0})}async function ae(e){if(!nx(e,Response))throw nP('"response" must be an instance of Response',nC);if(200!==e.status)throw nM('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',iQ,e);i9(e);let t=await ad(e,e=>(function(e,...t){if(!t.includes(iA(e)))throw n0(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nM('"response" body "keys" property must be an array',iY,{body:t});if(!Array.prototype.every.call(t.keys,nB))throw nM('"response" body "keys" property members must be JWK formatted objects',iY,{body:t});return t}function at(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nN(`unsupported ${t.name} modulusLength`,{cause:e})}function ar(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nN("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(at(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nN("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return at(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nN("unsupported CryptoKey algorithm name",{cause:e})}async function an(e,t,r,n,i){let a,o,{0:s,1:c,length:l}=e.split(".");if(5===l){if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new nN("JWE decryption is not configured",{cause:e})}if(3!==l)throw nM("Invalid JWT",iY,e);try{a=JSON.parse(nD(nL(s)))}catch(e){throw nM("failed to parse JWT Header body as base64url encoded JSON",iX,e)}if(!nB(a))throw nM("JWT Header must be a top level object",iY,e);if(t(a),void 0!==a.crit)throw new nN('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{o=JSON.parse(nD(nL(c)))}catch(e){throw nM("failed to parse JWT Payload body as base64url encoded JSON",iX,e)}if(!nB(o))throw nM("JWT Payload must be a top level object",iY,e);let u=n8()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nM('unexpected JWT "exp" (expiration time) claim type',iY,{claims:o});if(o.exp<=u-n)throw nM('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',i2,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nM('unexpected JWT "iat" (issued at) claim type',iY,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nM('unexpected JWT "iss" (issuer) claim type',iY,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nM('unexpected JWT "nbf" (not before) claim type',iY,{claims:o});if(o.nbf>u+n)throw nM('unexpected JWT "nbf" (not before) claim value',i2,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nM('unexpected JWT "aud" (audience) claim type',iY,{claims:o});return{header:a,claims:o,jwt:e}}async function ai(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nN(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,nD(e));return nL(i.slice(0,i.byteLength/2))}async function aa(e){if(e.bodyUsed)throw nP("form_post Request instances must contain a readable body",nT,{cause:e});return e.text()}function ao(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nM('unexpected JWT "alg" header parameter',iY,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nM('unexpected JWT "alg" header parameter',iY,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nM('unexpected JWT "alg" header parameter',iY,{header:n,expected:r,reason:"default value"});return}throw nM('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function as(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nM(`"${t}" parameter must be provided only once`,iY);return r}let ac=Symbol(),al=Symbol();async function au(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nN("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}async function ad(e,t=nQ){let r;try{r=await e.json()}catch(r){throw t(e),nM('failed to parse "response" body as JSON',iX,r)}if(!nB(r))throw nM('"response" body must be a top level object',iY,{body:r});return r}let ap=Symbol(),ah=Symbol(),af=Symbol();async function ay(e,t,r){let{cookies:n,logger:i}=r,a=n[e],o=new Date;o.setTime(o.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:o});let s=await t2({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:o};return{name:a.name,value:s,options:c}}async function am(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new T(`${e} cookie was missing`);let o=await t5({...a,token:t,salt:i[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new T(`${e} value could not be parsed`,{cause:t})}}function aw(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function ag(e,t){return async function(r,n,i){let{provider:a,logger:o}=i;if(!a?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await am(t,s,i);return aw(t,i,n),c}}let ab={async create(e){let t=n1(),r=await n2(t);return{cookie:await ay("pkceCodeVerifier",t,e),value:r}},use:ag("pkce","pkceCodeVerifier")},a_="encodedState",av={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new T("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n1()},i=await t2({secret:e.jwt.secret,token:n,salt:a_,maxAge:900});return{cookie:await ay("state",i,e),value:i}},use:ag("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await t5({secret:t.jwt.secret,token:e,salt:a_});if(r)return r;throw Error("Invalid state")}catch(e){throw new T("State could not be decoded",{cause:e})}}},ak={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n1();return{cookie:await ay("nonce",t,e),value:t}},use:ag("nonce","nonce")},aA="encodedWebauthnChallenge",aS={create:async(e,t,r)=>({cookie:await ay("webauthnChallenge",await t2({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:aA,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await am("webauthnChallenge",n,e),a=await t5({secret:e.jwt.secret,token:i,salt:aA});if(aw("webauthnChallenge",e,r),!a)throw new T("WebAuthn challenge was missing");return a}};function aE(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function ax(e,t,r){let n,i,a;let{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await nG(e,{[nU]:!0,[n$]:s[rf]});if(!(n=await nZ(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=aE(e),n=aE(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nY(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;nY(e,'"clientSecret"');let n=void 0;return async(t,i,a,o)=>{r||=await crypto.subtle.importKey("raw",nD(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=n9(t,i);n?.(s,c);let l=`${nL(nD(JSON.stringify(s)))}.${nL(nD(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,nD(l));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${l}.${nL(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){let{key:r,kid:n}=e instanceof CryptoKey?{key:e}:e?.key instanceof CryptoKey?(void 0!==e.kid&&nY(e.kid,'"kid"'),{key:e.key,kid:e.kid}):{};return function(e,t){if(function(e,t){if(!(e instanceof CryptoKey))throw nP(`${t} must be a CryptoKey`,nC)}(e,t),"private"!==e.type)throw nP(`${t} must be a private CryptoKey`,nT)}(r,'"clientPrivateKey.key"'),async(e,i,a,o)=>{let s={alg:function(e){switch(e.algorithm.name){case"RSA-PSS":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nN("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"RSASSA-PKCS1-v1_5":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nN("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"ECDSA":return function(e){switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nN("unsupported EcKeyAlgorithm namedCurve",{cause:e})}}(e);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nN("unsupported CryptoKey algorithm name",{cause:e})}}(r),kid:n},c=n9(e,i);t?.[nH]?.(s,c),a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await n7(s,c,r))}}(s.token.clientPrivateKey,{[nH](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],h=await av.use(t,p,r);try{a=function(e,t,r,n){var i;if(n3(e),n4(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nP('"parameters" must be an instance of URLSearchParams, or URL',nC);if(as(r,"response"))throw nM('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',iY,{parameters:r});let a=as(r,"iss"),o=as(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw nM('response parameter "iss" (issuer) missing',iY,{parameters:r});if(a&&a!==e.issuer)throw nM('unexpected "iss" (issuer) response parameter value',iY,{expected:e.issuer,parameters:r});switch(n){case void 0:case al:if(void 0!==o)throw nM('unexpected "state" response parameter encountered',iY,{expected:void 0,parameters:r});break;case ac:break;default:if(nY(n,'"expectedState" argument'),o!==n)throw nM(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',iY,{expected:n,parameters:r})}if(as(r,"error"))throw new is("authorization response from the server is an error",{cause:r});let s=as(r,"id_token"),c=as(r,"token");if(void 0!==s||void 0!==c)throw new nN("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),iW.add(i),i}(n,u,new URLSearchParams(e),s.checks.includes("state")?h:ac)}catch(e){if(e instanceof is){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new H("OAuth Provider returned an error",t)}throw e}let f=await ab.use(t,p,r),y=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(y=s.redirectProxyUrl);let m=await ij(n,u,i,a,y,f??"decoy",{[nU]:!0,[n$]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rf]??fetch)(...e))});s.token?.conform&&(m=await s.token.conform(m.clone())??m);let w={},g="oidc"===s.type;if(s[ry])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await m.clone().json();if(e.error){let t={providerId:s.id,...e};throw new H(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eT("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new eT("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new eT("Invalid JWT");if(!n)throw new eT("JWTs must contain a payload");try{t=eg(n)}catch{throw new eT("Failed to base64url decode the payload")}try{r=JSON.parse(eh.decode(t))}catch{throw new eT("Failed to parse the decoded payload as JSON")}if(!eH(r))throw new eT("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),i=await nG(r,{[n$]:s[rf]});n=await nZ(r,i)}}}let b=await iJ(n,u,m,{expectedNonce:await ak.use(t,p,r),requireIdToken:g});if(g){let t=iP(b);if(w=t,s[ry]&&"apple"===s.id)try{w.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await ib(n,u,b.access_token,{[n$]:s[rf],[nU]:!0});w=await iS(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:b,provider:s});e instanceof Object&&(w=e)}else if(l?.url){let e=await ib(n,u,b.access_token,{[n$]:s[rf],[nU]:!0});w=await e.json()}else throw TypeError("No userinfo endpoint configured");return b.expires_in&&(b.expires_at=Math.floor(Date.now()/1e3)+Number(b.expires_in)),{...await aT(w,s,b,o),profile:w,cookies:p}}async function aT(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new I(r,{provider:t.id}))}}async function aC(e,t,r,n){let i=await a$(e,t,r),{cookie:a}=await aS.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function aP(e,t,r,n){let i=await aO(e,t,r),{cookie:a}=await aS.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function aU(e,t,r){let n;let{adapter:i,provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new w("Invalid WebAuthn Authentication response");let s=aW(aI(o.id)),c=await i.getAuthenticator(s);if(!c)throw new w(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await aS.use(e,t.cookies,r);try{let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...c,credentialDeviceType:c.credentialDeviceType,transports:aj(c.transports),credentialID:aI(c.credentialID),credentialPublicKey:aI(c.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new G(e)}let{verified:u,authenticationInfo:d}=n;if(!u)throw new G("WebAuthn authentication response could not be verified");try{let{newCounter:e}=d;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new b(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:d.newCounter})}`,e)}let p=await i.getAccount(c.providerAccountId,a.id);if(!p)throw new w(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await i.getUser(p.userId);if(!h)throw new w(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:p.userId})}`);return{account:p,user:h}}async function aR(e,t,r){var n;let i;let{provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new w("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await aS.use(e,t.cookies,r);if(!c)throw new w("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new G(e)}if(!i.verified||!i.registrationInfo)throw new G("WebAuthn registration response could not be verified");let l={providerAccountId:aW(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:aW(i.registrationInfo.credentialID),credentialPublicKey:aW(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function aO(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:a?.map(e=>({id:aI(e.credentialID),type:"public-key",transports:aj(e.transports)}))})}async function a$(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,o=rs(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:a?.map(e=>({id:aI(e.credentialID),type:"public-key",transports:aj(e.transports)}))})}function aH(e){let{provider:t,adapter:r}=e;if(!r)throw new P("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new J("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function aI(e){return new Uint8Array(Buffer.from(e,"base64"))}function aW(e){return Buffer.from(e).toString("base64")}function aj(e){return e?e.split(","):void 0}async function aK(e,t,r,n){if(!t.provider)throw new J("Callback route called without provider");let{query:i,body:a,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:h,events:f,callbacks:y,session:{strategy:m,maxAge:g},logger:b}=t,_="jwt"===m;try{if("oauth"===c.type||"oidc"===c.type){let o;let s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&s?.state){let e=await av.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return b.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let m=await ax(s,e.cookies,t);m.cookies.length&&n.push(...m.cookies),b.debug("authorization result",m);let{user:w,account:v,profile:k}=m;if(!w||!v||!k)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:v.providerAccountId,provider:c.id})}let A=await aD({user:o??w,account:v,profile:k},t);if(A)return{redirect:A,cookies:n};let{user:S,session:E,isNewUser:x}=await nE(r.value,w,v,t);if(_){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await y.jwt({token:e,user:S,account:v,profile:k,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:E.sessionToken,options:{...t.cookies.sessionToken.options,expires:E.expires}});if(await f.signIn?.({user:S,account:v,profile:k,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:a,token:await ro(`${e}${o}`)}),u=!!s,m=u&&s.expires.valueOf()<Date.now();if(!u||m||a&&s.identifier!==a)throw new B({hasInvite:u,expired:m});let{identifier:w}=s,b=await l.getUserByEmail(w)??{id:crypto.randomUUID(),email:w,emailVerified:null},v={providerAccountId:b.email,userId:b.id,type:"email",provider:c.id},k=await aD({user:b,account:v},t);if(k)return{redirect:k,cookies:n};let{user:A,session:S,isNewUser:E}=await nE(r.value,b,v,t);if(_){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},i=await y.jwt({token:e,user:A,account:v,isNewUser:E,trigger:E?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await f.signIn?.({user:A,account:v,isNewUser:E}),E&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(a)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new E;let p={providerAccountId:l.id,type:"credentials",provider:c.id},m=await aD({user:l,account:p,credentials:e},t);if(m)return{redirect:m,cookies:n};let w={name:l.name,email:l.email,picture:l.image,sub:l.id},b=await y.jwt({token:w,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===b)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:b,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*g);let o=r.chunk(i,{expires:a});n.push(...o)}return await f.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}if("webauthn"===c.type&&"POST"===o){let i,a,o;let s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new w("Invalid action parameter");let c=aH(t);switch(s){case"authenticate":{let t=await aU(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await aR(t,e,n);i=r.user,a=r.account,o=r.authenticator}}await aD({user:i,account:a},t);let{user:l,isNewUser:u,session:m,account:b}=await nE(r.value,i,a,t);if(!b)throw new w("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),_){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await y.jwt({token:e,user:l,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await f.signIn?.({user:l,account:b,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new J(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof w)throw t;let e=new v(t,{provider:c.id});throw b.debug("callback route error details",{method:o,query:i,body:a}),e}}async function aD(e,t){let r;let{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof w)throw e;throw new _(e)}if(!r)throw new _("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function aL(e,t,r,n,i){let{adapter:a,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},h=t.value;if(!h)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,a=await o.decode({...o,token:h,salt:r});if(!a)throw Error("Invalid JWT");let l=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),u=nS(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let i=await o.encode({...o,token:l,salt:r}),a=t.chunk(i,{expires:u});p.cookies?.push(...a),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new C(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=a,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(h),u=null),u){let{user:t,session:r}=u,a=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*a,f=nS(d);o<=Date.now()&&await l({sessionToken:h,expires:f});let y=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=y,p.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:f}}),await s.session?.({session:y})}else h&&p.cookies?.push(...t.clean())}catch(e){l.error(new W(e))}return p}async function aN(e,t){let r,n;let{logger:i,provider:a}=t,o=a.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(a.issuer),t=await nG(e,{[n$]:a[rf],[nU]:!0}),r=await nZ(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await av.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),a.checks?.includes("pkce")){if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await ab.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}}let p=await ak.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==a.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:o,cookies:u,provider:a}),{redirect:o.toString(),cookies:u}}async function aJ(e,t){let r;let{body:n}=e,{provider:i,callbacks:a,adapter:o}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:i.id};try{r=await a.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new _(e)}if(!r)throw new _("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,h=await i.generateVerificationToken?.()??rs(32),f=new Date(Date.now()+(i.maxAge??86400)*1e3),y=i.secret??t.secret,m=new URL(t.basePath,t.url.origin),w=i.sendVerificationRequest({identifier:s,token:h,expires:f,url:`${m}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:h,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),g=o.createVerificationToken?.({identifier:s,token:await ro(`${h}${y}`),expires:f});return await Promise.all([w,g]),{redirect:`${m}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function aM(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await aN(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await aJ(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function aB(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){o.error(new D(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function az(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function aF(e,t,r,n){let i=aH(t),{provider:a}=i,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await az(t,r),c=s?{user:s,exists:!0}:await a.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t){if(!n||i)return"authenticate";return"register"}}return null}(o,!!s,c)){case"authenticate":return aP(i,e,l,n);case"register":if("string"==typeof l?.email)return aC(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function aq(e,t){let{action:r,providerId:n,error:i,method:a}=e,o=t.skipCSRFCheck===rp,{options:s,cookies:c}=await rv({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:o}),l=new m(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===a){let t=nA({...s,query:e.query,cookies:c});switch(r){case"callback":return await aK(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await aL(s,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await aF(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&rl(r,t),await aK(e,s,l,c);case"session":return rl(r,t),await aL(s,l,c,!0,e.body?.data);case"signin":return rl(r,t),await aM(e,c,s);case"signout":return rl(r,t),await aB(c,l,s)}}throw new L(`Cannot handle action: ${r}`)}function aV(e,t,r,n,i){let a;let o=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)a=new URL(s),o&&"/"!==o&&"/"!==a.pathname&&(a.pathname!==o&&t7(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function aG(e,t){let r=t7(t),n=await ri(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!Z&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new M(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new O("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!Q(i,r.origin))return new S(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=y(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(o&&!Q(o,r.origin))return new S(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e;let{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new x(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)ee=!0;else if("email"===t.type)et=!0;else if("webauthn"===t.type){var c;if(er=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new w(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new q("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new V(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(ee){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new N("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new R("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(et||u?.strategy==="database"||!u?.strategy&&l){if(et){if(!l)return new P("Email login requires an adapter");d.push(...en)}else{if(!l)return new P("Database session requires an adapter");d.push(...ei)}}if(er){if(!t.experimental?.enableWebAuthn)return new Y("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new P("WebAuthn requires an adapter");d.push(...ea)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new U(`Required adapter methods were missing: ${e.join(", ")}`)}return Z||(Z=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new k(`The error page ${e?.error} should not require authentication`)),ra(nA({theme:a}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let a=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rh;try{let e=await aq(n,t);if(o)return e;let r=ra(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof w;if(i&&o&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof w&&F.has(d.type)?d.type:"Configuration"});d instanceof E&&s.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(a)return Response.json({url:u});return Response.redirect(u)}}var aX=r(7070);function aY(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new aX.NextRequest(n.replace(i,r),e)}function aZ(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t7(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var aQ=r(1615);async function a0(e,t){return aG(new Request(aV("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function a1(e){return"function"==typeof e}function a2(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,aQ.headers)(),n=await e(void 0);return t?.(n),a0(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),a5([n,i],a)}if(a1(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),a5(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),a0(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,aQ.headers)()).then(t=>a0(t,e).then(e=>e.json()));if(t[0]instanceof Request)return a5([t[0],t[1]],e);if(a1(t[0])){let r=t[0];return async(...t)=>a5(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return a0(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function a5(e,t,r){let n=aY(e[0]),i=await a0(n.headers,t),a=await i.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:a}));let s=aX.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(a6.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=a,s=await r(n,e[1])??aX.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=aX.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let a6=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var a8=r(8585);async function a3(e,t={},r,n){let i=new Headers(await (0,aQ.headers)()),{redirect:a=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??i.get("Referer")??"/",l=aV("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),a&&(0,a8.redirect)(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return a&&(0,a8.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),h=await aG(p,{...n,raw:rh,skipCSRFCheck:rp}),f=await (0,aQ.cookies)();for(let e of h?.cookies??[])f.set(e.name,e.value,e.options);let y=(h instanceof Response?h.headers.get("Location"):h.redirect)??u;return a?(0,a8.redirect)(y):y}async function a4(e,t){let r=new Headers(await (0,aQ.headers)());r.set("Content-Type","application/x-www-form-urlencoded");let n=aV("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),o=await aG(a,{...t,raw:rh,skipCSRFCheck:rp}),s=await (0,aQ.cookies)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,a8.redirect)(o.redirect):o}async function a9(e,t){let r=new Headers(await (0,aQ.headers)());r.set("Content-Type","application/json");let n=new Request(aV("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await aG(n,{...t,raw:rh,skipCSRFCheck:rp}),a=await (0,aQ.cookies)();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}var a7=r(3524);(function(){var e=Error("Cannot find module '@auth/prisma-adapter'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module 'bcrypt'");throw e.code="MODULE_NOT_FOUND",e}();let oe=new a7.PrismaClient,ot={adapter:Object(function(){var e=Error("Cannot find module '@auth/prisma-adapter'");throw e.code="MODULE_NOT_FOUND",e}())(oe),session:{strategy:"jwt"},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:{clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}},{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Hasło",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await oe.user.findUnique({where:{email:e.email}});return t&&t.password&&await Object(function(){var e=Error("Cannot find module 'bcrypt'");throw e.code="MODULE_NOT_FOUND",e}())(e.password,t.password)?{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role||"user",subscription:t.subscription||"free"}:null}}}],callbacks:{session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.role=t.role,e.user.subscription=t.subscription),e),jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.role=t.role,e.subscription=t.subscription),e)}},or=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return aZ(r),aG(aY(t),r)};return{handlers:{GET:t,POST:t},auth:a2(e,e=>aZ(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return aZ(i),a3(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return aZ(r),a4(t,r)},unstable_update:async t=>{let r=await e(void 0);return aZ(r),a9(t,r)}}}aZ(e);let t=t=>aG(aY(t),e);return{handlers:{GET:t,POST:t},auth:a2(e),signIn:(t,r,n)=>a3(t,r,n,e),signOut:t=>a4(t,e),unstable_update:t=>a9(t,e)}}(ot),on=new u.AppRouteRouteModule({definition:{kind:d.x.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/home/<USER>/Dokumenty/moleculab/src/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:l}),{requestAsyncStorage:oi,staticGenerationAsyncStorage:oa,serverHooks:oo}=on,os="/api/auth/[...nextauth]/route";function oc(){return(0,p.patchFetch)({serverHooks:oo,staticGenerationAsyncStorage:oa})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[276,54,972],()=>r(837));module.exports=n})();