"use strict";(()=>{var e={};e.id=533,e.ids=[533],e.modules={3524:e=>{e.exports=require("@prisma/client")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2417:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>c,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>u});var n=r(9303),i=r(8716),o=r(670),a=r(7070);let p=new(r(3524)).PrismaClient;async function u(e){try{let{name:t,description:r,userId:s,moleculeId:n,settings:i}=await e.json();if(!t||!s||!n)return a.NextResponse.json({message:"Brakujące dane: nazwa, userId lub moleculeId"},{status:400});let o=await p.user.findUnique({where:{id:s}});if(!o)return a.NextResponse.json({message:"Użytkownik nie istnieje"},{status:404});if("free"===o.subscription&&await p.project.count({where:{userId:s}})>=3)return a.NextResponse.json({message:"Osiągnięto limit projekt\xf3w dla darmowego planu (3). Uaktualnij do Pro, aby zapisać więcej projekt\xf3w."},{status:403});let u=await p.project.create({data:{name:t,description:r,userId:s,molecules:[n],settings:i||{}}});return a.NextResponse.json({message:"Projekt został zapisany",project:u},{status:201})}catch(e){return console.error("Błąd zapisywania projektu:",e),a.NextResponse.json({message:"Wystąpił błąd podczas zapisywania projektu"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/projects/save/route",pathname:"/api/projects/save",filename:"route",bundlePath:"app/api/projects/save/route"},resolvedPagePath:"/home/<USER>/Dokumenty/moleculab/src/app/api/projects/save/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:m}=l,h="/api/projects/save/route";function f(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}},9925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,i={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),s=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?s:`${s}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[s,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(s,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function p(e){var t,r;if(!e)return;let[[s,n],...i]=a(e),{domain:o,expires:p,httponly:c,maxage:d,path:m,samesite:h,secure:f,partitioned:g,priority:y}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:s,value:decodeURIComponent(n),domain:o,...p&&{expires:new Date(p)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:m,...h&&{sameSite:u.includes(t=(t=h).toLowerCase())?t:void 0},...f&&{secure:!0},...y&&{priority:l.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var s in r)t(e,s,{get:r[s],enumerable:!0})})(i,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>p,stringifyCookie:()=>o}),e.exports=((e,i,o,a)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let o of s(i))n.call(e,o)||void 0===o||t(e,o,{get:()=>i[o],enumerable:!(a=r(i,o))||a.enumerable});return e})(t({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],l=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===s).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,s=this._parsed;return s.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(s).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,s;this._parsed=new Map,this._headers=e;let n=null!=(s=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?s:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,s,n,i,o=[],a=0;function p(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,i=!1;p();)if(","===(r=e.charAt(a))){for(s=a,a+=1,p(),n=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(i=!0,a=n,o.push(e.substring(t,s)),t=a):a=s+1}else a+=1;(!i||a>=e.length)&&o.push(e.substring(t,e.length))}return o}(n)){let t=p(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===s)}has(e){return this._parsed.has(e)}set(...e){let[t,r,s]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...s})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,s]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:s,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},2044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return s.RequestCookies},ResponseCookies:function(){return s.ResponseCookies},stringifyCookie:function(){return s.stringifyCookie}});let s=r(9925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,972],()=>r(2417));module.exports=s})();