(()=>{var e={};e.id=271,e.ids=[271],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},259:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>m,routeModule:()=>p,tree:()=>l}),t(3722),t(2029),t(5866);var a=t(3191),s=t(8716),n=t(7922),i=t.n(n),o=t(5231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3722)),"/home/<USER>/Dokumenty/moleculab/src/app/auth/signup/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],m=["/home/<USER>/Dokumenty/moleculab/src/app/auth/signup/page.tsx"],u="/auth/signup/page",c={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5162:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},3907:()=>{},7786:(e,r,t)=>{Promise.resolve().then(t.bind(t,2058))},434:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var a=t(9404),s=t.n(a)},5047:(e,r,t)=>{"use strict";var a=t(7389);t.o(a,"permanentRedirect")&&t.d(r,{permanentRedirect:function(){return a.permanentRedirect}}),t.o(a,"redirect")&&t.d(r,{redirect:function(){return a.redirect}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}})},2058:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var a=t(326),s=t(7577),n=t(434),i=t(5047);function o(){let e=(0,i.useRouter)(),[r,t]=(0,s.useState)(""),[o,d]=(0,s.useState)(""),[l,m]=(0,s.useState)(""),[u,c]=(0,s.useState)(""),[p,x]=(0,s.useState)(!1),[h,b]=(0,s.useState)(""),f=async t=>{if(t.preventDefault(),x(!0),b(""),l!==u){b("Hasła nie są identyczne"),x(!1);return}try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r,email:o,password:l})}),a=await t.json();if(!t.ok)throw Error(a.message||"Wystąpił błąd podczas rejestracji");e.push("/auth/signin?registered=true")}catch(e){b(e.message||"Wystąpił błąd podczas rejestracji"),console.error("Błąd rejestracji:",e)}finally{x(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-dark-800 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[a.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Zarejestruj się w MolecuLab Pro"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Lub"," ",a.jsx(n.default,{href:"/auth/signin",className:"font-medium text-primary-500 hover:text-primary-400",children:"zaloguj się do istniejącego konta"})]})]}),a.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-dark-700 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[h&&a.jsx("div",{className:"mb-4 bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md",children:a.jsx("p",{children:h})}),(0,a.jsxs)("form",{className:"space-y-6",onSubmit:f,children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-300",children:"Imię i nazwisko"}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:r,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-500 bg-dark-600 text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:"Adres email"}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:o,onChange:e=>d(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-500 bg-dark-600 text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:"Hasło"}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:l,onChange:e=>m(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-500 bg-dark-600 text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-300",children:"Potwierdź hasło"}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,value:u,onChange:e=>c(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-500 bg-dark-600 text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"})})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-dark-500 rounded bg-dark-600"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-300",children:["Akceptuję"," ",a.jsx(n.default,{href:"/terms",className:"font-medium text-primary-500 hover:text-primary-400",children:"regulamin"})," ","i"," ",a.jsx(n.default,{href:"/privacy",className:"font-medium text-primary-500 hover:text-primary-400",children:"politykę prywatności"})]})]}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:p,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Rejestracja...":"Zarejestruj się"})})]})]})})]})}},3722:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/auth/signup/page.tsx#default`)},2029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var a=t(9510),s=t(5036),n=t.n(s);t(4315);let i={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function o({children:e}){return a.jsx("html",{lang:"pl",className:`${n().variable}`,children:a.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},4315:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[276,984,404],()=>t(259));module.exports=a})();