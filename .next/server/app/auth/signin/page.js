(()=>{var e={};e.id=98,e.ids=[98],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>m,routeModule:()=>x,tree:()=>d}),t(2061),t(2029),t(5866);var s=t(3191),a=t(8716),i=t(7922),n=t.n(i),l=t(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2061)),"/home/<USER>/Dokumenty/moleculab/src/app/auth/signin/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],m=["/home/<USER>/Dokumenty/moleculab/src/app/auth/signin/page.tsx"],c="/auth/signin/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5162:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},3907:()=>{},1067:(e,r,t)=>{Promise.resolve().then(t.bind(t,7186))},5047:(e,r,t)=>{"use strict";var s=t(7389);t.o(s,"permanentRedirect")&&t.d(r,{permanentRedirect:function(){return s.permanentRedirect}}),t.o(s,"redirect")&&t.d(r,{redirect:function(){return s.redirect}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},7186:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(326),a=t(7577),i=t(434),n=t(8618),l=t(5047);function o(){let e=(0,l.useRouter)(),[r,t]=(0,a.useState)(""),[o,d]=(0,a.useState)(""),[m,c]=(0,a.useState)(!1),[u,x]=(0,a.useState)(""),p=async t=>{t.preventDefault(),c(!0),x("");try{let t=await (0,n.zB)("credentials",{redirect:!1,email:r,password:o});t?.error?x("Nieprawidłowy email lub hasło"):e.push("/dashboard")}catch(e){x("Wystąpił błąd podczas logowania"),console.error("Błąd logowania:",e)}finally{c(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-dark-800 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[s.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Zaloguj się do MolecuLab Pro"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Lub"," ",s.jsx(i.default,{href:"/auth/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"zarejestruj nowe konto"})]})]}),s.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-dark-700 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[u&&s.jsx("div",{className:"mb-4 bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md",children:s.jsx("p",{children:u})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:p,children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:"Adres email"}),s.jsx("div",{className:"mt-1",children:s.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:r,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-500 bg-dark-600 text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"})})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:"Hasło"}),s.jsx("div",{className:"mt-1",children:s.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:o,onChange:e=>d(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-500 bg-dark-600 text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-dark-500 rounded bg-dark-600"}),s.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-300",children:"Zapamiętaj mnie"})]}),s.jsx("div",{className:"text-sm",children:s.jsx(i.default,{href:"/auth/forgot-password",className:"font-medium text-primary-500 hover:text-primary-400",children:"Zapomniałeś hasła?"})})]}),s.jsx("div",{children:s.jsx("button",{type:"submit",disabled:m,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Logowanie...":"Zaloguj się"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-dark-500"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:"px-2 bg-dark-700 text-gray-400",children:"Lub kontynuuj z"})})]}),s.jsx("div",{className:"mt-6",children:(0,s.jsxs)("button",{onClick:()=>{(0,n.zB)("google",{callbackUrl:"/dashboard"})},className:"w-full flex justify-center py-2 px-4 border border-dark-500 rounded-md shadow-sm text-sm font-medium text-white bg-dark-600 hover:bg-dark-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:[(0,s.jsxs)("svg",{className:"h-5 w-5 mr-2",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),s.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),s.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),s.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google"]})})]})]})})]})}},2061:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/auth/signin/page.tsx#default`)},2029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>n});var s=t(9510),a=t(5036),i=t.n(a);t(4315);let n={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function l({children:e}){return s.jsx("html",{lang:"pl",className:`${i().variable}`,children:s.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},4315:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,984,404,996],()=>t(8931));module.exports=s})();