(()=>{var e={};e.id=631,e.ids=[631],e.modules={4505:(e,a,s)=>{var t={"./de.json":[5387,387],"./en.json":[7065,65],"./es.json":[5077,175],"./fr.json":[4352,352],"./pl.json":[1618,618],"./zh.json":[6805,616]};function r(e){if(!s.o(t,e))return Promise.resolve().then(()=>{var a=Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a});var a=t[e],r=a[0];return s.e(a[1]).then(()=>s.t(r,19))}r.keys=()=>Object.keys(t),r.id=4505,e.exports=r},3524:e=>{"use strict";e.exports=require("@prisma/client")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6571:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),s(6652),s(5949),s(2029),s(5866);var t=s(3191),r=s(8716),l=s(7922),n=s.n(l),i=s(5231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(a,o);let d=["",{children:["[locale]",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6652)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5949)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/home/<USER>/Dokumenty/moleculab/src/app/[locale]/dashboard/page.tsx"],m="/[locale]/dashboard/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/dashboard/page",pathname:"/[locale]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8090:(e,a,s)=>{Promise.resolve().then(s.bind(s,2356)),Promise.resolve().then(s.bind(s,5269))},4048:(e,a,s)=>{Promise.resolve().then(s.bind(s,2873))},2873:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>g});var t=s(326),r=s(6162),l=s(7577),n=s(8618),i=s(3493),o=s(5853),d=s(8139),c=s(2348),m=s(5362),x=s(8074),u=s(7290),p=s(8937),h=s(9072);function g(){let e=(0,r.T_)(),{data:a,status:s}=(0,n.kP)(),[g,b]=(0,l.useState)(null),[j,v]=(0,l.useState)("ball-stick"),[y,f]=(0,l.useState)(!1),[k,N]=(0,l.useState)({rotation:{enabled:!1,speed:1,axis:"y"},transitions:{enabled:!1,currentConformation:0,speed:1}});return(0,t.jsxs)("div",{className:"min-h-screen bg-dark-800 text-white",children:[t.jsx("header",{className:"bg-dark-700 border-b border-dark-600",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[t.jsx(i.rU,{href:"/",className:"text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 text-transparent bg-clip-text",children:e("app.title")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(p.Z,{}),"authenticated"===s?(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("span",{className:"text-gray-300",children:a?.user?.name}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${y?"bg-primary-900 text-primary-300":"bg-dark-600 text-gray-300"}`,children:e(`subscription.${y?"pro":"free"}`)}),t.jsx(i.rU,{href:"/api/auth/signout",className:"text-gray-300 hover:text-white",children:e("app.navigation.logout")})]}):t.jsx(i.rU,{href:"/auth/signin",className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md",children:e("app.navigation.login")})]})]})}),t.jsx("main",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[t.jsx(d.Z,{molecules:h.zu,onSelect:b,selectedMoleculeId:g?.id}),"authenticated"===s&&t.jsx(u.Z,{molecule:g,userId:a?.user?.id,isPro:y})]}),(0,t.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h2",{className:"text-lg font-medium",children:e("molecules.viewer.title")}),(0,t.jsxs)("div",{className:"flex space-x-2 flex-wrap",children:[t.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"ball-stick"===j?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>v("ball-stick"),children:e("molecules.viewer.modes.ball-stick")}),t.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"space-filling"===j?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>v("space-filling"),children:e("molecules.viewer.modes.space-filling")}),t.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"wireframe"===j?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>v("wireframe"),children:e("molecules.viewer.modes.wireframe")}),t.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"van-der-waals"===j?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>v("van-der-waals"),children:"Van der Waals"}),t.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"licorice"===j?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>v("licorice"),children:"Licorice"}),t.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"cartoon"===j?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>v("cartoon"),children:"Cartoon"})]})]}),(0,t.jsxs)("div",{className:"border-t border-dark-600 pt-4",children:[t.jsx("h3",{className:"text-sm font-medium mb-3",children:e("molecules.viewer.animations.title")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 flex-wrap",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",checked:k.rotation.enabled,onChange:e=>N(a=>({...a,rotation:{...a.rotation,enabled:e.target.checked}})),className:"rounded bg-dark-600 border-dark-500 text-primary-500 focus:ring-primary-500"}),t.jsx("span",{className:"text-sm",children:e("molecules.viewer.animations.rotation")})]}),k.rotation.enabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("label",{className:"text-sm",children:[e("molecules.viewer.animations.speed"),":"]}),t.jsx("input",{type:"range",min:"0.1",max:"3",step:"0.1",value:k.rotation.speed,onChange:e=>N(a=>({...a,rotation:{...a.rotation,speed:parseFloat(e.target.value)}})),className:"w-20"}),(0,t.jsxs)("span",{className:"text-xs text-gray-400",children:[k.rotation.speed.toFixed(1),"x"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("label",{className:"text-sm",children:[e("molecules.viewer.animations.axis"),":"]}),(0,t.jsxs)("select",{value:k.rotation.axis,onChange:e=>N(a=>({...a,rotation:{...a.rotation,axis:e.target.value}})),className:"bg-dark-600 border-dark-500 rounded text-sm px-2 py-1",children:[t.jsx("option",{value:"x",children:"X"}),t.jsx("option",{value:"y",children:"Y"}),t.jsx("option",{value:"z",children:"Z"})]})]})]})]})]})]}),t.jsx(o.Z,{molecule:g,renderMode:j,animationSettings:k,onAnimationSettingsChange:N}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[t.jsx("div",{className:"md:col-span-1",children:t.jsx(c.Z,{molecule:g})}),t.jsx("div",{className:"md:col-span-1",children:t.jsx(m.Z,{molecule:g})}),t.jsx("div",{className:"md:col-span-1",children:t.jsx(x.Z,{molecule:g,isPro:y})})]})]})]})}),t.jsx("footer",{className:"bg-dark-700 border-t border-dark-600 mt-12",children:t.jsx("div",{className:"container mx-auto px-4 py-6",children:t.jsx("p",{className:"text-center text-gray-400 text-sm",children:"MolecuLab Pro \xa9 2025"})})})]})}},8937:(e,a,s)=>{"use strict";s.d(a,{Z:()=>n});var t=s(326),r=s(6162),l=s(3493);function n(){let e=(0,r.T_)("language"),a=(0,l.jD)(),s=(0,l.tv)();return(0,t.jsxs)("div",{className:"relative",children:[t.jsx("select",{onChange:e=>{let t=e.target.value;s.replace(a,{locale:t})},className:"appearance-none bg-dark-600 border border-dark-500 rounded-md py-1 pl-3 pr-8 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500",defaultValue:a.split("/")[1],children:l.DI.locales.map(a=>t.jsx("option",{value:a,children:e(a)},a))}),t.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400",children:t.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})}},7290:(e,a,s)=>{"use strict";s.d(a,{Z:()=>n});var t=s(326),r=s(6162),l=s(7577);let n=({molecule:e,userId:a,isPro:s})=>{let n=(0,r.T_)("projects"),[i,o]=(0,l.useState)(!1),[d,c]=(0,l.useState)(""),[m,x]=(0,l.useState)(""),[u,p]=(0,l.useState)(!1),[h,g]=(0,l.useState)(0);(0,l.useState)(()=>{a&&g(2)});let b=async t=>{if(t.preventDefault(),e&&a){if(!s&&h>=3){alert(n("limit"));return}p(!0);try{await new Promise(e=>setTimeout(e,1e3)),g(e=>e+1),o(!1),c(""),x(""),alert("Projekt zapisany pomyślnie!")}catch(e){console.error("Błąd podczas zapisywania projektu:",e),alert("Wystąpił błąd podczas zapisywania projektu.")}finally{p(!1)}}};return a?(0,t.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[t.jsx("h2",{className:"text-lg font-medium mb-4",children:n("title")}),i?(0,t.jsxs)("form",{onSubmit:b,children:[(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-sm text-gray-400 mb-1",children:n("form.name")}),t.jsx("input",{type:"text",className:"w-full bg-dark-600 border border-dark-500 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500",value:d,onChange:e=>c(e.target.value),required:!0})]}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-sm text-gray-400 mb-1",children:n("form.description")}),t.jsx("textarea",{className:"w-full bg-dark-600 border border-dark-500 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500",rows:3,value:m,onChange:e=>x(e.target.value)})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[t.jsx("button",{type:"submit",className:"flex-1 bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-md font-medium",disabled:u,children:u?n("form.saving"):n("form.save")}),t.jsx("button",{type:"button",className:"flex-1 bg-dark-600 hover:bg-dark-500 text-white py-2 px-4 rounded-md font-medium",onClick:()=>o(!1),disabled:u,children:n("form.cancel")})]})]}):t.jsx("button",{className:`w-full py-2 px-4 rounded-md text-white font-medium ${e?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 cursor-not-allowed"}`,onClick:()=>e&&o(!0),disabled:!e,children:n("save")}),!s&&(0,t.jsxs)("div",{className:"mt-4 text-xs text-gray-400",children:[n("my"),": ",h,"/3"]})]}):(0,t.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[t.jsx("h2",{className:"text-lg font-medium mb-2",children:n("title")}),t.jsx("p",{className:"text-gray-400 text-sm mb-4",children:n("login")})]})}},3493:(e,a,s)=>{"use strict";s.d(a,{DI:()=>r,jD:()=>i,rU:()=>l,tv:()=>o});var t=s(2822);let r=(0,s(4491).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:l,redirect:n,usePathname:i,useRouter:o}=(0,t.Z)(r)},6652:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/[locale]/dashboard/page.tsx#default`)},5949:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>c,generateStaticParams:()=>d});var t=s(9510),r=s(3800),l=s.n(r),n=s(4588),i=s(8585);s(4315);var o=s(4289);function d(){return o.DI.locales.map(e=>({locale:e}))}async function c({children:e,params:{locale:a}}){let r;o.DI.locales.includes(a)||(0,i.notFound)();try{r=(await s(4505)(`./${a}.json`)).default}catch(e){(0,i.notFound)()}return t.jsx("html",{lang:a,children:t.jsx("body",{className:`${l().className} bg-dark-800 text-white`,children:t.jsx(n.Z,{locale:a,messages:r,children:e})})})}},2226:(e,a,s)=>{"use strict";s.d(a,{Z:()=>l});var t=s(9375),r=s(4289);let l=(0,t.Z)(async({locale:e})=>r.DI.locales.includes(e)?{locale:e,messages:(await s(4505)(`./${e}.json`)).default}:{messages:{}})},4289:(e,a,s)=>{"use strict";s.d(a,{DI:()=>r});var t=s(1806);let r=(0,s(9458).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:l,redirect:n,usePathname:i,useRouter:o}=(0,t.Z)(r)}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[276,984,404,162,54,996,751,530,678],()=>s(6571));module.exports=t})();