/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/test/page";
exports.ids = ["app/[locale]/test/page"];
exports.modules = {

/***/ "(rsc)/./src/i18n/messages lazy recursive ^\\.\\/.*\\.json$":
/*!*****************************************************************!*\
  !*** ./src/i18n/messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./de.json": [
		"(rsc)/./src/i18n/messages/de.json",
		"_rsc_src_i18n_messages_de_json"
	],
	"./en.json": [
		"(rsc)/./src/i18n/messages/en.json",
		"_rsc_src_i18n_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./src/i18n/messages/es.json",
		"_rsc_src_i18n_messages_es_json"
	],
	"./fr.json": [
		"(rsc)/./src/i18n/messages/fr.json",
		"_rsc_src_i18n_messages_fr_json"
	],
	"./pl.json": [
		"(rsc)/./src/i18n/messages/pl.json",
		"_rsc_src_i18n_messages_pl_json"
	],
	"./zh.json": [
		"(rsc)/./src/i18n/messages/zh.json",
		"_rsc_src_i18n_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./src/i18n/messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest%2Fpage&page=%2F%5Blocale%5D%2Ftest%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest%2Fpage.tsx&appDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest%2Fpage&page=%2F%5Blocale%5D%2Ftest%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest%2Fpage.tsx&appDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/test/page.tsx */ \"(rsc)/./src/app/[locale]/test/page.tsx\")), \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/test/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/test/page\",\n        pathname: \"/[locale]/test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkYlNUJsb2NhbGUlNUQlMkZ0ZXN0JTJGcGFnZSZwYWdlPSUyRiU1QmxvY2FsZSU1RCUyRnRlc3QlMkZwYWdlJmFwcFBhdGhzPSUyRiU1QmxvY2FsZSU1RCUyRnRlc3QlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGJTVCbG9jYWxlJTVEJTJGdGVzdCUyRnBhZ2UudHN4JmFwcERpcj0lMkZob21lJTJGYWRhbSUyRkRva3VtZW50eSUyRm1vbGVjdWxhYiUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGaG9tZSUyRmFkYW0lMkZEb2t1bWVudHklMkZtb2xlY3VsYWImaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLDRLQUFrRztBQUN6SDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixzS0FBK0Y7QUFDeEg7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUFzRjtBQUMvRyxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8/OGNlYSJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdbbG9jYWxlXScsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ3Rlc3QnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9hZGFtL0Rva3VtZW50eS9tb2xlY3VsYWIvc3JjL2FwcC9bbG9jYWxlXS90ZXN0L3BhZ2UudHN4XCIpLCBcIi9ob21lL2FkYW0vRG9rdW1lbnR5L21vbGVjdWxhYi9zcmMvYXBwL1tsb2NhbGVdL3Rlc3QvcGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2FkYW0vRG9rdW1lbnR5L21vbGVjdWxhYi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3hcIiksIFwiL2hvbWUvYWRhbS9Eb2t1bWVudHkvbW9sZWN1bGFiL3NyYy9hcHAvW2xvY2FsZV0vbGF5b3V0LnRzeFwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9hZGFtL0Rva3VtZW50eS9tb2xlY3VsYWIvc3JjL2FwcC9sYXlvdXQudHN4XCIpLCBcIi9ob21lL2FkYW0vRG9rdW1lbnR5L21vbGVjdWxhYi9zcmMvYXBwL2xheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIi9ob21lL2FkYW0vRG9rdW1lbnR5L21vbGVjdWxhYi9zcmMvYXBwL1tsb2NhbGVdL3Rlc3QvcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9bbG9jYWxlXS90ZXN0L3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvW2xvY2FsZV0vdGVzdC9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9bbG9jYWxlXS90ZXN0XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest%2Fpage&page=%2F%5Blocale%5D%2Ftest%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest%2Fpage.tsx&appDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZhZGFtJTJGRG9rdW1lbnR5JTJGbW9sZWN1bGFiJTJGbm9kZV9tb2R1bGVzJTJGbmV4dC1pbnRsJTJGZGlzdCUyRmVzbSUyRmRldmVsb3BtZW50JTJGc2hhcmVkJTJGTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRmFkYW0lMkZEb2t1bWVudHklMkZtb2xlY3VsYWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTJGYXBwJTJGJTVCbG9jYWxlJTVEJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdRQUE2SyIsInNvdXJjZXMiOlsid2VicGFjazovL21vbGVjdWxhYi1wcm8vPzkzYTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL2hvbWUvYWRhbS9Eb2t1bWVudHkvbW9sZWN1bGFiL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp%2F%5Blocale%5D%2Ftest%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp%2F%5Blocale%5D%2Ftest%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/test/page.tsx */ \"(ssr)/./src/app/[locale]/test/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZhZGFtJTJGRG9rdW1lbnR5JTJGbW9sZWN1bGFiJTJGc3JjJTJGYXBwJTJGJTVCbG9jYWxlJTVEJTJGdGVzdCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBa0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2xlY3VsYWItcHJvLz82NGI2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvYWRhbS9Eb2t1bWVudHkvbW9sZWN1bGFiL3NyYy9hcHAvW2xvY2FsZV0vdGVzdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp%2F%5Blocale%5D%2Ftest%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/test/page.tsx":
/*!****************************************!*\
  !*** ./src/app/[locale]/test/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _i18n_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/i18n/navigation */ \"(ssr)/./src/i18n/navigation.ts\");\n/* harmony import */ var _components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LanguageSwitcher */ \"(ssr)/./src/components/LanguageSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction TestPage() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-900 text-white p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-6 text-primary-400\",\n                    children: \"next-intl Configuration Test\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Current Configuration Status\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Current Locale:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400\",\n                                            children: locale\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 49\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"App Title:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: t(\"app.title\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 44\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"App Subtitle:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: t(\"app.subtitle\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 47\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Translation Test\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Navigation\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Dashboard: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"nav.dashboard\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 37,\n                                                            columnNumber: 32\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Molecules: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"nav.molecules\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 38,\n                                                            columnNumber: 32\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Sign In: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"nav.signin\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 39,\n                                                            columnNumber: 30\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"3D Viewer\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Title: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"molecules.viewer.title\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 28\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Ball-Stick: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"molecules.viewer.modes.ball-stick\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Van der Waals: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"molecules.viewer.modes.van-der-waals\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"Animations: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: t(\"molecules.viewer.animations.title\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-dark-800 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Navigation Test\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_navigation__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                    href: \"/test\",\n                                    className: \"bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md\",\n                                    children: \"Current Page (Test)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_navigation__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                    href: \"/\",\n                                    className: \"bg-secondary-600 hover:bg-secondary-700 px-4 py-2 rounded-md\",\n                                    children: \"Home Page\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_navigation__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                    href: \"/auth/signin\",\n                                    className: \"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-md\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-900 border border-green-700 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4 text-green-300\",\n                            children: \"✅ Success!\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200\",\n                            children: \"The next-intl configuration is working correctly! The locale is being detected, translations are loading, and navigation is functioning properly.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-green-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Fixed Issues:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 16\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: '✅ \"No locale was returned from getRequestConfig\" error resolved'\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"✅ Middleware properly configured with new routing API\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"✅ Navigation APIs working with createNavigation\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"✅ Locale detection and validation working\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"✅ Translation loading from JSON files working\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"✅ All supported locales functional\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/test/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageSwitcher.tsx":
/*!*********************************************!*\
  !*** ./src/components/LanguageSwitcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _i18n_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/i18n/navigation */ \"(ssr)/./src/i18n/navigation.ts\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(ssr)/./src/i18n/routing.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LanguageSwitcher() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"language\");\n    const pathname = (0,_i18n_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const router = (0,_i18n_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleLanguageChange = (e)=>{\n        const newLocale = e.target.value;\n        router.replace(pathname, {\n            locale: newLocale\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                onChange: handleLanguageChange,\n                className: \"appearance-none bg-dark-600 border border-dark-500 rounded-md py-1 pl-3 pr-8 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                defaultValue: pathname.split(\"/\")[1],\n                children: _i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.map((locale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                        value: locale,\n                        children: t(locale)\n                    }, locale, false, {\n                        fileName: \"/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-4 w-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M19 9l-7 7-7-7\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/i18n/navigation.ts":
/*!********************************!*\
  !*** ./src/i18n/navigation.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   getPathname: () => (/* binding */ getPathname),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(ssr)/./src/i18n/routing.ts\");\n\n\n// Lightweight wrappers around Next.js' navigation\n// APIs that consider the routing configuration\nconst { Link, redirect, usePathname, useRouter, getPathname } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_routing__WEBPACK_IMPORTED_MODULE_0__.routing);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaTE4bi9uYXZpZ2F0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBd0Q7QUFDcEI7QUFFcEMsa0RBQWtEO0FBQ2xELCtDQUErQztBQUN4QyxNQUFNLEVBQUVFLElBQUksRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRUMsV0FBVyxFQUFFLEdBQUdOLGdFQUFnQkEsQ0FBQ0MsNkNBQU9BLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2xlY3VsYWItcHJvLy4vc3JjL2kxOG4vbmF2aWdhdGlvbi50cz84ZjMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZU5hdmlnYXRpb24gfSBmcm9tICduZXh0LWludGwvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyByb3V0aW5nIH0gZnJvbSAnLi9yb3V0aW5nJztcblxuLy8gTGlnaHR3ZWlnaHQgd3JhcHBlcnMgYXJvdW5kIE5leHQuanMnIG5hdmlnYXRpb25cbi8vIEFQSXMgdGhhdCBjb25zaWRlciB0aGUgcm91dGluZyBjb25maWd1cmF0aW9uXG5leHBvcnQgY29uc3QgeyBMaW5rLCByZWRpcmVjdCwgdXNlUGF0aG5hbWUsIHVzZVJvdXRlciwgZ2V0UGF0aG5hbWUgfSA9IGNyZWF0ZU5hdmlnYXRpb24ocm91dGluZyk7XG4iXSwibmFtZXMiOlsiY3JlYXRlTmF2aWdhdGlvbiIsInJvdXRpbmciLCJMaW5rIiwicmVkaXJlY3QiLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciIsImdldFBhdGhuYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/i18n/navigation.ts\n");

/***/ }),

/***/ "(ssr)/./src/i18n/routing.ts":
/*!*****************************!*\
  !*** ./src/i18n/routing.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\");\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: [\n        \"en\",\n        \"pl\",\n        \"de\",\n        \"fr\",\n        \"es\",\n        \"zh\"\n    ],\n    // Used when no locale matches\n    defaultLocale: \"en\"\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaTE4bi9yb3V0aW5nLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEO0FBRTNDLE1BQU1DLFVBQVVELDZEQUFhQSxDQUFDO0lBQ25DLDJDQUEyQztJQUMzQ0UsU0FBUztRQUFDO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtLQUFLO0lBRTdDLDhCQUE4QjtJQUM5QkMsZUFBZTtBQUNqQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL3NyYy9pMThuL3JvdXRpbmcudHM/MDY4MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWZpbmVSb3V0aW5nIH0gZnJvbSAnbmV4dC1pbnRsL3JvdXRpbmcnO1xuXG5leHBvcnQgY29uc3Qgcm91dGluZyA9IGRlZmluZVJvdXRpbmcoe1xuICAvLyBBIGxpc3Qgb2YgYWxsIGxvY2FsZXMgdGhhdCBhcmUgc3VwcG9ydGVkXG4gIGxvY2FsZXM6IFsnZW4nLCAncGwnLCAnZGUnLCAnZnInLCAnZXMnLCAnemgnXSxcbiAgXG4gIC8vIFVzZWQgd2hlbiBubyBsb2NhbGUgbWF0Y2hlc1xuICBkZWZhdWx0TG9jYWxlOiAnZW4nXG59KTtcbiJdLCJuYW1lcyI6WyJkZWZpbmVSb3V0aW5nIiwicm91dGluZyIsImxvY2FsZXMiLCJkZWZhdWx0TG9jYWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/i18n/routing.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"afe1174a48e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/NTI3NCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFmZTExNzRhNDhlOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/i18n/routing */ \"(rsc)/./src/i18n/routing.ts\");\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n_routing__WEBPACK_IMPORTED_MODULE_3__.routing.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // Validate that the incoming `locale` parameter is valid\n    if (!(0,next_intl__WEBPACK_IMPORTED_MODULE_4__.hasLocale)(_i18n_routing__WEBPACK_IMPORTED_MODULE_3__.routing.locales, locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    let messages;\n    try {\n        messages = (await __webpack_require__(\"(rsc)/./src/i18n/messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n    } catch (error) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} bg-dark-800 text-white`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                locale: locale,\n                messages: messages,\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/test/page.tsx":
/*!****************************************!*\
  !*** ./src/app/[locale]/test/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/[locale]/test/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"MolecuLab Pro\",\n    description: \"Webowa platforma do wizualizacji molekuł\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pl\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-dark-800 text-white min-h-screen\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGeUI7QUFJeEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFXLENBQUMsRUFBRVIsa0xBQWMsQ0FBQyxDQUFDO2tCQUM1Qyw0RUFBQ1U7WUFBS0YsV0FBVTtzQkFDYkg7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2xlY3VsYWItcHJvLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSwgdmFyaWFibGU6ICctLWZvbnQtaW50ZXInIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ01vbGVjdUxhYiBQcm8nLFxuICBkZXNjcmlwdGlvbjogJ1dlYm93YSBwbGF0Zm9ybWEgZG8gd2l6dWFsaXphY2ppIG1vbGVrdcWCJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwicGxcIiBjbGFzc05hbWU9e2Ake2ludGVyLnZhcmlhYmxlfWB9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYmctZGFyay04MDAgdGV4dC13aGl0ZSBtaW4taC1zY3JlZW5cIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.ts":
/*!*****************************!*\
  !*** ./src/i18n/request.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.ts\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // Typically corresponds to the `[locale]` segment\n    const requested = await requestLocale;\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.hasLocale)(_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales, requested) ? requested : _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./src/i18n/messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yZXF1ZXN0LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0Q7QUFDZDtBQUNGO0FBRXBDLGlFQUFlQSw0REFBZ0JBLENBQUMsT0FBTyxFQUFFRyxhQUFhLEVBQUU7SUFDdEQsa0RBQWtEO0lBQ2xELE1BQU1DLFlBQVksTUFBTUQ7SUFDeEIsTUFBTUUsU0FBU0osb0RBQVNBLENBQUNDLDZDQUFPQSxDQUFDSSxPQUFPLEVBQUVGLGFBQ3RDQSxZQUNBRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUV6QixPQUFPO1FBQ0xGO1FBQ0FHLFVBQVUsQ0FBQyxNQUFNLGtGQUFPLEdBQVksRUFBRUgsT0FBTyxNQUFNLEdBQUdJLE9BQU87SUFDL0Q7QUFDRixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2xlY3VsYWItcHJvLy4vc3JjL2kxOG4vcmVxdWVzdC50cz9lYmUzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFJlcXVlc3RDb25maWcgfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcbmltcG9ydCB7IGhhc0xvY2FsZSB9IGZyb20gJ25leHQtaW50bCc7XG5pbXBvcnQgeyByb3V0aW5nIH0gZnJvbSAnLi9yb3V0aW5nJztcblxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoeyByZXF1ZXN0TG9jYWxlIH0pID0+IHtcbiAgLy8gVHlwaWNhbGx5IGNvcnJlc3BvbmRzIHRvIHRoZSBgW2xvY2FsZV1gIHNlZ21lbnRcbiAgY29uc3QgcmVxdWVzdGVkID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcbiAgY29uc3QgbG9jYWxlID0gaGFzTG9jYWxlKHJvdXRpbmcubG9jYWxlcywgcmVxdWVzdGVkKVxuICAgID8gcmVxdWVzdGVkXG4gICAgOiByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XG5cbiAgcmV0dXJuIHtcbiAgICBsb2NhbGUsXG4gICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4vbWVzc2FnZXMvJHtsb2NhbGV9Lmpzb25gKSkuZGVmYXVsdFxuICB9O1xufSk7XG4iXSwibmFtZXMiOlsiZ2V0UmVxdWVzdENvbmZpZyIsImhhc0xvY2FsZSIsInJvdXRpbmciLCJyZXF1ZXN0TG9jYWxlIiwicmVxdWVzdGVkIiwibG9jYWxlIiwibG9jYWxlcyIsImRlZmF1bHRMb2NhbGUiLCJtZXNzYWdlcyIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.ts":
/*!*****************************!*\
  !*** ./src/i18n/routing.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\");\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: [\n        \"en\",\n        \"pl\",\n        \"de\",\n        \"fr\",\n        \"es\",\n        \"zh\"\n    ],\n    // Used when no locale matches\n    defaultLocale: \"en\"\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yb3V0aW5nLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEO0FBRTNDLE1BQU1DLFVBQVVELDZEQUFhQSxDQUFDO0lBQ25DLDJDQUEyQztJQUMzQ0UsU0FBUztRQUFDO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtLQUFLO0lBRTdDLDhCQUE4QjtJQUM5QkMsZUFBZTtBQUNqQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9sZWN1bGFiLXByby8uL3NyYy9pMThuL3JvdXRpbmcudHM/MDY4MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWZpbmVSb3V0aW5nIH0gZnJvbSAnbmV4dC1pbnRsL3JvdXRpbmcnO1xuXG5leHBvcnQgY29uc3Qgcm91dGluZyA9IGRlZmluZVJvdXRpbmcoe1xuICAvLyBBIGxpc3Qgb2YgYWxsIGxvY2FsZXMgdGhhdCBhcmUgc3VwcG9ydGVkXG4gIGxvY2FsZXM6IFsnZW4nLCAncGwnLCAnZGUnLCAnZnInLCAnZXMnLCAnemgnXSxcbiAgXG4gIC8vIFVzZWQgd2hlbiBubyBsb2NhbGUgbWF0Y2hlc1xuICBkZWZhdWx0TG9jYWxlOiAnZW4nXG59KTtcbiJdLCJuYW1lcyI6WyJkZWZpbmVSb3V0aW5nIiwicm91dGluZyIsImxvY2FsZXMiLCJkZWZhdWx0TG9jYWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@formatjs","vendor-chunks/use-intl","vendor-chunks/next-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest%2Fpage&page=%2F%5Blocale%5D%2Ftest%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest%2Fpage.tsx&appDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fadam%2FDokumenty%2Fmoleculab&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();