(()=>{var e={};e.id=546,e.ids=[546],e.modules={4505:(e,s,r)=>{var t={"./de.json":[5387,387],"./en.json":[7065,65],"./es.json":[5077,175],"./fr.json":[4352,352],"./pl.json":[1618,618],"./zh.json":[6805,616]};function a(e){if(!r.o(t,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=t[e],a=s[0];return r.e(s[1]).then(()=>r.t(a,19))}a.keys=()=>Object.keys(t),a.id=4505,e.exports=a},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5943:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(3823),r(5949),r(2029),r(5866);var t=r(3191),a=r(8716),l=r(7922),n=r.n(l),o=r(5231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(s,i);let d=["",{children:["[locale]",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3823)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signin/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,5949)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signin/page.tsx"],m="/[locale]/auth/signin/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/auth/signin/page",pathname:"/[locale]/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5162:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},3907:()=>{},8090:(e,s,r)=>{Promise.resolve().then(r.bind(r,2356)),Promise.resolve().then(r.bind(r,5269))},8650:(e,s,r)=>{Promise.resolve().then(r.bind(r,2478))},2478:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(326),a=r(6162),l=r(7577),n=r(8618),o=r(3493),i=r(8937);function d(){let e=(0,a.T_)("auth.signin"),[s,r]=(0,l.useState)(""),[d,c]=(0,l.useState)(""),[m,u]=(0,l.useState)(!1),[h,p]=(0,l.useState)(""),x=async r=>{r.preventDefault(),u(!0),p("");try{let r=await (0,n.zB)("credentials",{redirect:!1,email:s,password:d});r?.error&&p(e("error"))}catch(s){p(e("error"))}finally{u(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-dark-800 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[t.jsx("div",{className:"absolute top-4 right-4",children:t.jsx(i.Z,{})}),(0,t.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[t.jsx("h2",{className:"text-center text-3xl font-extrabold text-white",children:e("title")}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-400",children:[e("register")," ",t.jsx(o.rU,{href:"/auth/signup",className:"font-medium text-primary-400 hover:text-primary-300",children:"→"})]})]}),t.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsxs)("div",{className:"bg-dark-700 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,t.jsxs)("form",{className:"space-y-6",onSubmit:x,children:[h&&t.jsx("div",{className:"bg-red-900/30 border border-red-500 text-red-300 px-4 py-3 rounded-md text-sm",children:h}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:e("email")}),t.jsx("div",{className:"mt-1",children:t.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:s,onChange:e=>r(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-dark-600 text-white sm:text-sm"})})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:e("password")}),t.jsx("div",{className:"mt-1",children:t.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:d,onChange:e=>c(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-dark-600 text-white sm:text-sm"})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-dark-500 rounded bg-dark-600"}),t.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-300",children:e("remember")})]}),t.jsx("div",{className:"text-sm",children:t.jsx("a",{href:"#",className:"font-medium text-primary-400 hover:text-primary-300",children:e("forgot")})})]}),t.jsx("div",{children:t.jsx("button",{type:"submit",disabled:m,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-dark-700 disabled:opacity-50 disabled:cursor-not-allowed",children:m?e("loading"):e("button")})})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-0 flex items-center",children:t.jsx("div",{className:"w-full border-t border-dark-500"})}),t.jsx("div",{className:"relative flex justify-center text-sm",children:t.jsx("span",{className:"px-2 bg-dark-700 text-gray-400",children:e("continue")})})]}),t.jsx("div",{className:"mt-6",children:(0,t.jsxs)("button",{onClick:()=>(0,n.zB)("google"),className:"w-full flex justify-center py-2 px-4 border border-dark-500 rounded-md shadow-sm text-sm font-medium text-white bg-dark-600 hover:bg-dark-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-dark-700",children:[(0,t.jsxs)("svg",{className:"h-5 w-5 mr-2",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),t.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),t.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),t.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),e("google")]})})]})]})})]})}},8937:(e,s,r)=>{"use strict";r.d(s,{Z:()=>n});var t=r(326),a=r(6162),l=r(3493);function n(){let e=(0,a.T_)("language"),s=(0,l.jD)(),r=(0,l.tv)();return(0,t.jsxs)("div",{className:"relative",children:[t.jsx("select",{onChange:e=>{let t=e.target.value;r.replace(s,{locale:t})},className:"appearance-none bg-dark-600 border border-dark-500 rounded-md py-1 pl-3 pr-8 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500",defaultValue:s.split("/")[1],children:l.DI.locales.map(s=>t.jsx("option",{value:s,children:e(s)},s))}),t.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400",children:t.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})}},3493:(e,s,r)=>{"use strict";r.d(s,{DI:()=>a,jD:()=>o,rU:()=>l,tv:()=>i});var t=r(2822);let a=(0,r(4491).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:l,redirect:n,usePathname:o,useRouter:i}=(0,t.Z)(a)},3823:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signin/page.tsx#default`)},5949:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c,generateStaticParams:()=>d});var t=r(9510),a=r(3800),l=r.n(a),n=r(4588),o=r(8585);r(4315);var i=r(4289);function d(){return i.DI.locales.map(e=>({locale:e}))}async function c({children:e,params:{locale:s}}){let a;i.DI.locales.includes(s)||(0,o.notFound)();try{a=(await r(4505)(`./${s}.json`)).default}catch(e){(0,o.notFound)()}return t.jsx("html",{lang:s,children:t.jsx("body",{className:`${l().className} bg-dark-800 text-white`,children:t.jsx(n.Z,{locale:s,messages:a,children:e})})})}},2029:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o,metadata:()=>n});var t=r(9510),a=r(5036),l=r.n(a);r(4315);let n={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function o({children:e}){return t.jsx("html",{lang:"pl",className:`${l().variable}`,children:t.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},2226:(e,s,r)=>{"use strict";r.d(s,{Z:()=>l});var t=r(9375),a=r(4289);let l=(0,t.Z)(async({locale:e})=>a.DI.locales.includes(e)?{locale:e,messages:(await r(4505)(`./${e}.json`)).default}:{messages:{}})},4289:(e,s,r)=>{"use strict";r.d(s,{DI:()=>a});var t=r(1806);let a=(0,r(9458).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:l,redirect:n,usePathname:o,useRouter:i}=(0,t.Z)(a)},4315:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[276,984,404,162,54,996,751],()=>r(5943));module.exports=t})();