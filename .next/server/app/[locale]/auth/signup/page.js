(()=>{var e={};e.id=928,e.ids=[928],e.modules={4505:(e,r,t)=>{var a={"./de.json":[5387,387],"./en.json":[7065,65],"./es.json":[5077,175],"./fr.json":[4352,352],"./pl.json":[1618,618],"./zh.json":[6805,616]};function s(e){if(!t.o(a,e))return Promise.resolve().then(()=>{var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r});var r=a[e],s=r[0];return t.e(r[1]).then(()=>t.t(s,19))}s.keys=()=>Object.keys(a),s.id=4505,e.exports=s},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9066:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(6590),t(5949),t(2029),t(5866);var a=t(3191),s=t(8716),n=t(7922),o=t.n(n),l=t(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d=["",{children:["[locale]",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6590)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signup/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5949)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signup/page.tsx"],m="/[locale]/auth/signup/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/auth/signup/page",pathname:"/[locale]/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5162:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},3907:()=>{},8090:(e,r,t)=>{Promise.resolve().then(t.bind(t,2356)),Promise.resolve().then(t.bind(t,5269))},1050:(e,r,t)=>{Promise.resolve().then(t.bind(t,1918))},434:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var a=t(9404),s=t.n(a)},1918:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var a=t(326),s=t(6162),n=t(7577),o=t(3493),l=t(8937);function i(){let e=(0,s.T_)("auth.signup"),[r,t]=(0,n.useState)(""),[i,d]=(0,n.useState)(""),[c,m]=(0,n.useState)(""),[u,p]=(0,n.useState)(""),[h,x]=(0,n.useState)(!1),[g,b]=(0,n.useState)(!1),[f,y]=(0,n.useState)(""),v=async t=>{if(t.preventDefault(),c!==u){y("Hasła nie są zgodne");return}b(!0),y("");try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r,email:i,password:c})}),a=await t.json();if(!t.ok)throw Error(a.message||e("error"));window.location.href="/auth/signin"}catch(r){y(e("error"))}finally{b(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-dark-800 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[a.jsx("div",{className:"absolute top-4 right-4",children:a.jsx(l.Z,{})}),(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[a.jsx("h2",{className:"text-center text-3xl font-extrabold text-white",children:e("title")}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-400",children:[e("login")," ",a.jsx(o.rU,{href:"/auth/signin",className:"font-medium text-primary-400 hover:text-primary-300",children:"→"})]})]}),a.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:a.jsx("div",{className:"bg-dark-700 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,a.jsxs)("form",{className:"space-y-6",onSubmit:v,children:[f&&a.jsx("div",{className:"bg-red-900/30 border border-red-500 text-red-300 px-4 py-3 rounded-md text-sm",children:f}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-300",children:e("name")}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:r,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-dark-600 text-white sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:e("email")}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:i,onChange:e=>d(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-dark-600 text-white sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:e("password")}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:c,onChange:e=>m(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-dark-600 text-white sm:text-sm"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-300",children:e("confirm")}),a.jsx("div",{className:"mt-1",children:a.jsx("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,value:u,onChange:e=>p(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-dark-500 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-dark-600 text-white sm:text-sm"})})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{id:"terms",name:"terms",type:"checkbox",required:!0,checked:h,onChange:e=>x(e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-dark-500 rounded bg-dark-600"}),a.jsx("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-300",children:e("terms")})]}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:g||!h,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-dark-700 disabled:opacity-50 disabled:cursor-not-allowed",children:g?e("loading"):e("button")})})]})})})]})}},8937:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var a=t(326),s=t(6162),n=t(3493);function o(){let e=(0,s.T_)("language"),r=(0,n.jD)(),t=(0,n.tv)();return(0,a.jsxs)("div",{className:"relative",children:[a.jsx("select",{onChange:e=>{let a=e.target.value;t.replace(r,{locale:a})},className:"appearance-none bg-dark-600 border border-dark-500 rounded-md py-1 pl-3 pr-8 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500",defaultValue:r.split("/")[1],children:n.DI.locales.map(r=>a.jsx("option",{value:r,children:e(r)},r))}),a.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400",children:a.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})}},3493:(e,r,t)=>{"use strict";t.d(r,{DI:()=>s,jD:()=>l,rU:()=>n,tv:()=>i});var a=t(2822);let s=(0,t(4491).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:n,redirect:o,usePathname:l,useRouter:i}=(0,a.Z)(s)},6590:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signup/page.tsx#default`)},5949:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,generateStaticParams:()=>d});var a=t(9510),s=t(3800),n=t.n(s),o=t(4588),l=t(8585);t(4315);var i=t(4289);function d(){return i.DI.locales.map(e=>({locale:e}))}async function c({children:e,params:{locale:r}}){let s;i.DI.locales.includes(r)||(0,l.notFound)();try{s=(await t(4505)(`./${r}.json`)).default}catch(e){(0,l.notFound)()}return a.jsx("html",{lang:r,children:a.jsx("body",{className:`${n().className} bg-dark-800 text-white`,children:a.jsx(o.Z,{locale:r,messages:s,children:e})})})}},2029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>o});var a=t(9510),s=t(5036),n=t.n(s);t(4315);let o={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function l({children:e}){return a.jsx("html",{lang:"pl",className:`${n().variable}`,children:a.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},2226:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var a=t(9375),s=t(4289);let n=(0,a.Z)(async({locale:e})=>s.DI.locales.includes(e)?{locale:e,messages:(await t(4505)(`./${e}.json`)).default}:{messages:{}})},4289:(e,r,t)=>{"use strict";t.d(r,{DI:()=>s});var a=t(1806);let s=(0,t(9458).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:n,redirect:o,usePathname:l,useRouter:i}=(0,a.Z)(s)},4315:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[276,984,404,162,54,751],()=>t(9066));module.exports=a})();