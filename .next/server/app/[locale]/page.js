(()=>{var e={};e.id=61,e.ids=[61],e.modules={4505:(e,t,r)=>{var a={"./de.json":[5387,387],"./en.json":[7065,65],"./es.json":[5077,175],"./fr.json":[4352,352],"./pl.json":[1618,618],"./zh.json":[6805,616]};function s(e){if(!r.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],s=t[0];return r.e(t[1]).then(()=>r.t(s,19))}s.keys=()=>Object.keys(a),s.id=4505,e.exports=s},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(5875),r(5949),r(2029),r(5866);var a=r(3191),s=r(8716),n=r(7922),l=r.n(n),o=r(5231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let d=["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5875)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,5949)),"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/home/<USER>/Dokumenty/moleculab/src/app/[locale]/page.tsx"],u="/[locale]/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5162:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},3907:()=>{},8090:(e,t,r)=>{Promise.resolve().then(r.bind(r,2356)),Promise.resolve().then(r.bind(r,5269))},9075:(e,t,r)=>{Promise.resolve().then(r.bind(r,8412))},434:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(9404),s=r.n(a)},8412:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(326),s=r(6162),n=r(434),l=r(8937),o=r(3493);function i(){let e=(0,s.T_)();return(0,a.jsxs)("div",{className:"min-h-screen bg-dark-800 text-white",children:[a.jsx("header",{className:"bg-dark-700 border-b border-dark-600",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[a.jsx(n.default,{href:"/",className:"text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 text-transparent bg-clip-text",children:e("app.title")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(l.Z,{}),a.jsx(o.rU,{href:"/auth/signin",className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md",children:e("app.navigation.login")})]})]})}),(0,a.jsxs)("main",{className:"flex flex-col items-center justify-center min-h-[80vh] text-center px-4",children:[a.jsx("h1",{className:"text-5xl md:text-7xl font-bold text-primary-300 mb-6",children:e("app.title")}),a.jsx("p",{className:"text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl",children:e("app.subtitle")}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[a.jsx(o.rU,{href:"/molecules",className:"bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-md text-lg font-medium",children:e("app.navigation.browse")}),a.jsx(o.rU,{href:"/auth/signin",className:"bg-dark-600 hover:bg-dark-500 text-white px-6 py-3 rounded-md text-lg font-medium",children:e("app.navigation.login")})]})]}),a.jsx("footer",{className:"bg-dark-700 border-t border-dark-600 mt-auto",children:a.jsx("div",{className:"container mx-auto px-4 py-6",children:a.jsx("p",{className:"text-center text-gray-400 text-sm",children:"MolecuLab Pro \xa9 2025"})})})]})}},8937:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(326),s=r(6162),n=r(3493);function l(){let e=(0,s.T_)("language"),t=(0,n.jD)(),r=(0,n.tv)();return(0,a.jsxs)("div",{className:"relative",children:[a.jsx("select",{onChange:e=>{let a=e.target.value;r.replace(t,{locale:a})},className:"appearance-none bg-dark-600 border border-dark-500 rounded-md py-1 pl-3 pr-8 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500",defaultValue:t.split("/")[1],children:n.DI.locales.map(t=>a.jsx("option",{value:t,children:e(t)},t))}),a.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400",children:a.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})}},3493:(e,t,r)=>{"use strict";r.d(t,{DI:()=>s,jD:()=>o,rU:()=>n,tv:()=>i});var a=r(2822);let s=(0,r(4491).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:n,redirect:l,usePathname:o,useRouter:i}=(0,a.Z)(s)},5949:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,generateStaticParams:()=>d});var a=r(9510),s=r(3800),n=r.n(s),l=r(4588),o=r(8585);r(4315);var i=r(4289);function d(){return i.DI.locales.map(e=>({locale:e}))}async function c({children:e,params:{locale:t}}){let s;i.DI.locales.includes(t)||(0,o.notFound)();try{s=(await r(4505)(`./${t}.json`)).default}catch(e){(0,o.notFound)()}return a.jsx("html",{lang:t,children:a.jsx("body",{className:`${n().className} bg-dark-800 text-white`,children:a.jsx(l.Z,{locale:t,messages:s,children:e})})})}},5875:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/[locale]/page.tsx#default`)},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>l});var a=r(9510),s=r(5036),n=r.n(s);r(4315);let l={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function o({children:e}){return a.jsx("html",{lang:"pl",className:`${n().variable}`,children:a.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},2226:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var a=r(9375),s=r(4289);let n=(0,a.Z)(async({locale:e})=>s.DI.locales.includes(e)?{locale:e,messages:(await r(4505)(`./${e}.json`)).default}:{messages:{}})},4289:(e,t,r)=>{"use strict";r.d(t,{DI:()=>s});var a=r(1806);let s=(0,r(9458).Z)({locales:["en","pl","de","fr","es","zh"],defaultLocale:"en",pathnames:{"/":"/","/molecules":"/molecules","/dashboard":"/dashboard","/auth/signin":"/auth/signin","/auth/signup":"/auth/signup","/projects":"/projects"}}),{Link:n,redirect:l,usePathname:o,useRouter:i}=(0,a.Z)(s)},4315:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[276,984,404,162,54,751],()=>r(3061));module.exports=a})();