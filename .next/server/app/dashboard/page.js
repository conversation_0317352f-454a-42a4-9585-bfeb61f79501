(()=>{var e={};e.id=702,e.ids=[702],e.modules={3524:e=>{"use strict";e.exports=require("@prisma/client")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5720:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),t(8256),t(2029),t(5866);var s=t(3191),r=t(8716),l=t(7922),i=t.n(l),n=t(5231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let o=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8256)),"/home/<USER>/Dokumenty/moleculab/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/home/<USER>/Dokumenty/moleculab/src/app/dashboard/page.tsx"],m="/dashboard/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4982:(e,a,t)=>{Promise.resolve().then(t.bind(t,9530))},9530:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var s=t(326),r=t(7577),l=t(8618),i=t(434),n=t(5853),d=t(8139),o=t(2348),c=t(5362),m=t(8074),x=t(7290),u=t(9072);function p(){let{data:e,status:a}=(0,l.kP)(),[t,p]=(0,r.useState)(null),[h,g]=(0,r.useState)("ball-stick"),[b,j]=(0,r.useState)(!1),[y,f]=(0,r.useState)({rotation:{enabled:!1,speed:1,axis:"y"},transitions:{enabled:!1,currentConformation:0,speed:1}});return(0,s.jsxs)("div",{className:"min-h-screen bg-dark-800 text-white",children:[s.jsx("header",{className:"bg-dark-700 border-b border-dark-600",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[s.jsx(i.default,{href:"/",className:"text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 text-transparent bg-clip-text",children:"MolecuLab Pro"}),s.jsx("div",{className:"flex items-center space-x-4",children:"authenticated"===a?(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("span",{className:"text-gray-300",children:e?.user?.name}),s.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${b?"bg-primary-900 text-primary-300":"bg-dark-600 text-gray-300"}`,children:b?"Pro":"Free"}),s.jsx(i.default,{href:"/api/auth/signout",className:"text-gray-300 hover:text-white",children:"Wyloguj"})]}):s.jsx(i.default,{href:"/auth/signin",className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md",children:"Zaloguj się"})})]})}),s.jsx("main",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[s.jsx(d.Z,{molecules:u.zu,onSelect:p,selectedMoleculeId:t?.id}),"authenticated"===a&&s.jsx(x.Z,{molecule:t,userId:e?.user?.id,isPro:b})]}),(0,s.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,s.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h2",{className:"text-lg font-medium",children:"Wizualizator 3D"}),(0,s.jsxs)("div",{className:"flex space-x-2 flex-wrap",children:[s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"ball-stick"===h?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>g("ball-stick"),children:"Ball-Stick"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"space-filling"===h?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>g("space-filling"),children:"Space-Filling"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"wireframe"===h?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>g("wireframe"),children:"Wireframe"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"van-der-waals"===h?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>g("van-der-waals"),children:"Van der Waals"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"licorice"===h?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>g("licorice"),children:"Licorice"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"cartoon"===h?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>g("cartoon"),children:"Cartoon"})]})]}),(0,s.jsxs)("div",{className:"border-t border-dark-600 pt-4",children:[s.jsx("h3",{className:"text-sm font-medium mb-3",children:"Animacje"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 flex-wrap",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",checked:y.rotation.enabled,onChange:e=>f(a=>({...a,rotation:{...a.rotation,enabled:e.target.checked}})),className:"rounded bg-dark-600 border-dark-500 text-primary-500 focus:ring-primary-500"}),s.jsx("span",{className:"text-sm",children:"Rotacja"})]}),y.rotation.enabled&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm",children:"Prędkość:"}),s.jsx("input",{type:"range",min:"0.1",max:"3",step:"0.1",value:y.rotation.speed,onChange:e=>f(a=>({...a,rotation:{...a.rotation,speed:parseFloat(e.target.value)}})),className:"w-20"}),(0,s.jsxs)("span",{className:"text-xs text-gray-400",children:[y.rotation.speed.toFixed(1),"x"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm",children:"Oś:"}),(0,s.jsxs)("select",{value:y.rotation.axis,onChange:e=>f(a=>({...a,rotation:{...a.rotation,axis:e.target.value}})),className:"bg-dark-600 border-dark-500 rounded text-sm px-2 py-1",children:[s.jsx("option",{value:"x",children:"X"}),s.jsx("option",{value:"y",children:"Y"}),s.jsx("option",{value:"z",children:"Z"})]})]})]})]})]})]}),s.jsx(n.Z,{molecule:t,renderMode:h,animationSettings:y,onAnimationSettingsChange:f}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsx("div",{className:"md:col-span-1",children:s.jsx(o.Z,{molecule:t})}),s.jsx("div",{className:"md:col-span-1",children:s.jsx(c.Z,{molecule:t})}),s.jsx("div",{className:"md:col-span-1",children:s.jsx(m.Z,{molecule:t,isPro:b})})]})]})]})}),s.jsx("footer",{className:"bg-dark-700 border-t border-dark-600 mt-12",children:s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx("p",{className:"text-center text-gray-400 text-sm",children:"MolecuLab Pro \xa9 2025 - Zaawansowana platforma do wizualizacji molekuł"})})})]})}},7290:(e,a,t)=>{"use strict";t.d(a,{Z:()=>i});var s=t(326),r=t(6162),l=t(7577);let i=({molecule:e,userId:a,isPro:t})=>{let i=(0,r.T_)("projects"),[n,d]=(0,l.useState)(!1),[o,c]=(0,l.useState)(""),[m,x]=(0,l.useState)(""),[u,p]=(0,l.useState)(!1),[h,g]=(0,l.useState)(0);(0,l.useState)(()=>{a&&g(2)});let b=async s=>{if(s.preventDefault(),e&&a){if(!t&&h>=3){alert(i("limit"));return}p(!0);try{await new Promise(e=>setTimeout(e,1e3)),g(e=>e+1),d(!1),c(""),x(""),alert("Projekt zapisany pomyślnie!")}catch(e){console.error("Błąd podczas zapisywania projektu:",e),alert("Wystąpił błąd podczas zapisywania projektu.")}finally{p(!1)}}};return a?(0,s.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[s.jsx("h2",{className:"text-lg font-medium mb-4",children:i("title")}),n?(0,s.jsxs)("form",{onSubmit:b,children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm text-gray-400 mb-1",children:i("form.name")}),s.jsx("input",{type:"text",className:"w-full bg-dark-600 border border-dark-500 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500",value:o,onChange:e=>c(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm text-gray-400 mb-1",children:i("form.description")}),s.jsx("textarea",{className:"w-full bg-dark-600 border border-dark-500 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500",rows:3,value:m,onChange:e=>x(e.target.value)})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[s.jsx("button",{type:"submit",className:"flex-1 bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-md font-medium",disabled:u,children:u?i("form.saving"):i("form.save")}),s.jsx("button",{type:"button",className:"flex-1 bg-dark-600 hover:bg-dark-500 text-white py-2 px-4 rounded-md font-medium",onClick:()=>d(!1),disabled:u,children:i("form.cancel")})]})]}):s.jsx("button",{className:`w-full py-2 px-4 rounded-md text-white font-medium ${e?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 cursor-not-allowed"}`,onClick:()=>e&&d(!0),disabled:!e,children:i("save")}),!t&&(0,s.jsxs)("div",{className:"mt-4 text-xs text-gray-400",children:[i("my"),": ",h,"/3"]})]}):(0,s.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[s.jsx("h2",{className:"text-lg font-medium mb-2",children:i("title")}),s.jsx("p",{className:"text-gray-400 text-sm mb-4",children:i("login")})]})}},8256:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/dashboard/page.tsx#default`)}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[276,984,404,162,996,530,678],()=>t(5720));module.exports=s})();