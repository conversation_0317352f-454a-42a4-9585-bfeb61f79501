(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7516:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>d}),r(4182),r(2029),r(5866);var s=r(3191),a=r(8716),n=r(7922),o=r.n(n),l=r(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4182)),"/home/<USER>/Dokumenty/moleculab/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],m=["/home/<USER>/Dokumenty/moleculab/src/app/page.tsx"],c="/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5162:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},3243:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9404,23))},3907:()=>{},1812:(e,t,r)=>{"use strict";let{createProxy:s}=r(8570);e.exports=s("/home/<USER>/Dokumenty/moleculab/node_modules/next/dist/client/link.js")},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var s=r(9510),a=r(5036),n=r.n(a);r(4315);let o={title:"MolecuLab Pro",description:"Webowa platforma do wizualizacji molekuł"};function l({children:e}){return s.jsx("html",{lang:"pl",className:`${n().variable}`,children:s.jsx("body",{className:"bg-dark-800 text-white min-h-screen",children:e})})}},4182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(9510),a=r(1812),n=r.n(a);function o(){return s.jsx("main",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"max-w-5xl w-full text-center",children:[s.jsx("h1",{className:"text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 text-transparent bg-clip-text mb-6",children:"MolecuLab Pro"}),s.jsx("p",{className:"text-xl mb-8",children:"Zaawansowana platforma do wizualizacji molekuł"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx(n(),{href:"/molecules",className:"btn-primary",children:"Przeglądaj molekuły"}),s.jsx(n(),{href:"/auth/signin",className:"btn-secondary",children:"Zaloguj się"})]})]})})}},4315:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,984,404],()=>r(7516));module.exports=s})();