(()=>{var e={};e.id=466,e.ids=[466],e.modules={3524:e=>{"use strict";e.exports=require("@prisma/client")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9344:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(697),t(2029),t(5866);var s=t(3191),r=t(8716),l=t(7922),n=t.n(l),i=t(5231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(a,o);let c=["",{children:["molecules",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,697)),"/home/<USER>/Dokumenty/moleculab/src/app/molecules/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/home/<USER>/Dokumenty/moleculab/src/app/molecules/page.tsx"],m="/molecules/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/molecules/page",pathname:"/molecules",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5856:(e,a,t)=>{Promise.resolve().then(t.bind(t,5349))},5349:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>m});var s=t(326),r=t(7577),l=t(5853),n=t(8139),i=t(2348),o=t(5362),c=t(8074),d=t(9072);function m(){let[e,a]=(0,r.useState)(null),[t,m]=(0,r.useState)("ball-stick"),[x]=(0,r.useState)(!1),[p,u]=(0,r.useState)({rotation:{enabled:!1,speed:1,axis:"y"},transitions:{enabled:!1,currentConformation:0,speed:1}});return(0,s.jsxs)("div",{className:"min-h-screen bg-dark-800 text-white",children:[s.jsx("header",{className:"bg-dark-700 border-b border-dark-600",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[s.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 text-transparent bg-clip-text",children:"MolecuLab Pro"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{className:"text-gray-300 hover:text-white",children:"Dokumentacja"}),s.jsx("button",{className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md",children:"Zaloguj się"})]})]})}),s.jsx("main",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[s.jsx("div",{className:"lg:col-span-1 space-y-6",children:s.jsx(n.Z,{molecules:d.zu,onSelect:a,selectedMoleculeId:e?.id})}),(0,s.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,s.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h2",{className:"text-lg font-medium",children:"Wizualizator 3D"}),(0,s.jsxs)("div",{className:"flex space-x-2 flex-wrap",children:[s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"ball-stick"===t?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>m("ball-stick"),children:"Ball-Stick"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"space-filling"===t?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>m("space-filling"),children:"Space-Filling"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"wireframe"===t?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>m("wireframe"),children:"Wireframe"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"van-der-waals"===t?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>m("van-der-waals"),children:"Van der Waals"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"licorice"===t?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>m("licorice"),children:"Licorice"}),s.jsx("button",{className:`px-3 py-1 rounded-md text-sm ${"cartoon"===t?"bg-primary-500 text-white":"bg-dark-600 text-gray-300 hover:bg-dark-500"}`,onClick:()=>m("cartoon"),children:"Cartoon"})]})]}),(0,s.jsxs)("div",{className:"border-t border-dark-600 pt-4",children:[s.jsx("h3",{className:"text-sm font-medium mb-3",children:"Animacje"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 flex-wrap",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",checked:p.rotation.enabled,onChange:e=>u(a=>({...a,rotation:{...a.rotation,enabled:e.target.checked}})),className:"rounded bg-dark-600 border-dark-500 text-primary-500 focus:ring-primary-500"}),s.jsx("span",{className:"text-sm",children:"Rotacja"})]}),p.rotation.enabled&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm",children:"Prędkość:"}),s.jsx("input",{type:"range",min:"0.1",max:"3",step:"0.1",value:p.rotation.speed,onChange:e=>u(a=>({...a,rotation:{...a.rotation,speed:parseFloat(e.target.value)}})),className:"w-20"}),(0,s.jsxs)("span",{className:"text-xs text-gray-400",children:[p.rotation.speed.toFixed(1),"x"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm",children:"Oś:"}),(0,s.jsxs)("select",{value:p.rotation.axis,onChange:e=>u(a=>({...a,rotation:{...a.rotation,axis:e.target.value}})),className:"bg-dark-600 border-dark-500 rounded text-sm px-2 py-1",children:[s.jsx("option",{value:"x",children:"X"}),s.jsx("option",{value:"y",children:"Y"}),s.jsx("option",{value:"z",children:"Z"})]})]})]})]})]})]}),s.jsx(l.Z,{molecule:e,renderMode:t,animationSettings:p,onAnimationSettingsChange:u}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsx("div",{className:"md:col-span-1",children:s.jsx(i.Z,{molecule:e})}),s.jsx("div",{className:"md:col-span-1",children:s.jsx(o.Z,{molecule:e})}),s.jsx("div",{className:"md:col-span-1",children:s.jsx(c.Z,{molecule:e,isPro:x})})]})]})]})}),s.jsx("footer",{className:"bg-dark-700 border-t border-dark-600 mt-12",children:s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx("p",{className:"text-center text-gray-400 text-sm",children:"MolecuLab Pro \xa9 2025 - Zaawansowana platforma do wizualizacji molekuł"})})})]})}},697:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(8570).createProxy)(String.raw`/home/<USER>/Dokumenty/moleculab/src/app/molecules/page.tsx#default`)}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[276,984,162,530,678],()=>t(9344));module.exports=s})();