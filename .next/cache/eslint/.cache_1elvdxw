[{"/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signin/page.tsx": "1", "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signup/page.tsx": "2", "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/dashboard/page.tsx": "3", "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx": "4", "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/page.tsx": "5", "/home/<USER>/Dokumenty/moleculab/src/app/api/auth/[...nextauth]/route.ts": "6", "/home/<USER>/Dokumenty/moleculab/src/app/api/auth/register/route.ts": "7", "/home/<USER>/Dokumenty/moleculab/src/app/api/projects/save/route.ts": "8", "/home/<USER>/Dokumenty/moleculab/src/app/api/subscription/create/route.ts": "9", "/home/<USER>/Dokumenty/moleculab/src/app/auth/signin/page.tsx": "10", "/home/<USER>/Dokumenty/moleculab/src/app/auth/signup/page.tsx": "11", "/home/<USER>/Dokumenty/moleculab/src/app/dashboard/page.tsx": "12", "/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx": "13", "/home/<USER>/Dokumenty/moleculab/src/app/molecules/page.tsx": "14", "/home/<USER>/Dokumenty/moleculab/src/app/page.tsx": "15", "/home/<USER>/Dokumenty/moleculab/src/components/AIAnalysis.tsx": "16", "/home/<USER>/Dokumenty/moleculab/src/components/ExportTools.tsx": "17", "/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx": "18", "/home/<USER>/Dokumenty/moleculab/src/components/MoleculeSelector.tsx": "19", "/home/<USER>/Dokumenty/moleculab/src/components/MoleculeViewer.tsx": "20", "/home/<USER>/Dokumenty/moleculab/src/components/ProjectSave.tsx": "21", "/home/<USER>/Dokumenty/moleculab/src/components/PropertyPanel.tsx": "22", "/home/<USER>/Dokumenty/moleculab/src/i18n/config.ts": "23", "/home/<USER>/Dokumenty/moleculab/src/i18n/navigation.ts": "24", "/home/<USER>/Dokumenty/moleculab/src/lib/ai-analysis.ts": "25", "/home/<USER>/Dokumenty/moleculab/src/lib/molecules.ts": "26", "/home/<USER>/Dokumenty/moleculab/src/lib/three-utils.ts": "27", "/home/<USER>/Dokumenty/moleculab/src/middleware.ts": "28", "/home/<USER>/Dokumenty/moleculab/src/tests/3d-visualizations.test.ts": "29", "/home/<USER>/Dokumenty/moleculab/src/types/molecule.ts": "30", "/home/<USER>/Dokumenty/moleculab/src/types/next-auth.d.ts": "31"}, {"size": 6737, "mtime": 1749203896000, "results": "32", "hashOfConfig": "33"}, {"size": 6924, "mtime": 1749203896000, "results": "34", "hashOfConfig": "33"}, {"size": 10906, "mtime": 1749235858999, "results": "35", "hashOfConfig": "33"}, {"size": 1043, "mtime": 1749235734005, "results": "36", "hashOfConfig": "33"}, {"size": 2221, "mtime": 1749203896000, "results": "37", "hashOfConfig": "33"}, {"size": 2185, "mtime": 1749236030581, "results": "38", "hashOfConfig": "33"}, {"size": 1533, "mtime": 1749203896000, "results": "39", "hashOfConfig": "33"}, {"size": 1749, "mtime": 1749203896000, "results": "40", "hashOfConfig": "33"}, {"size": 2902, "mtime": 1749203896000, "results": "41", "hashOfConfig": "33"}, {"size": 6699, "mtime": 1749203896000, "results": "42", "hashOfConfig": "33"}, {"size": 7039, "mtime": 1749203896000, "results": "43", "hashOfConfig": "33"}, {"size": 10405, "mtime": 1749235168869, "results": "44", "hashOfConfig": "33"}, {"size": 576, "mtime": 1749203896000, "results": "45", "hashOfConfig": "33"}, {"size": 9197, "mtime": 1749235218183, "results": "46", "hashOfConfig": "33"}, {"size": 835, "mtime": 1749203896000, "results": "47", "hashOfConfig": "33"}, {"size": 6680, "mtime": 1749203896000, "results": "48", "hashOfConfig": "33"}, {"size": 3969, "mtime": 1749203896000, "results": "49", "hashOfConfig": "33"}, {"size": 1351, "mtime": 1749235782229, "results": "50", "hashOfConfig": "33"}, {"size": 5067, "mtime": 1749203896000, "results": "51", "hashOfConfig": "33"}, {"size": 16809, "mtime": 1749235096214, "results": "52", "hashOfConfig": "33"}, {"size": 4434, "mtime": 1749203896000, "results": "53", "hashOfConfig": "33"}, {"size": 7953, "mtime": 1749203896000, "results": "54", "hashOfConfig": "33"}, {"size": 392, "mtime": 1749235700935, "results": "55", "hashOfConfig": "33"}, {"size": 638, "mtime": 1749235678129, "results": "56", "hashOfConfig": "33"}, {"size": 5676, "mtime": 1749203897000, "results": "57", "hashOfConfig": "33"}, {"size": 7691, "mtime": 1749203897000, "results": "58", "hashOfConfig": "33"}, {"size": 9118, "mtime": 1749234950370, "results": "59", "hashOfConfig": "33"}, {"size": 313, "mtime": 1749235689634, "results": "60", "hashOfConfig": "33"}, {"size": 3420, "mtime": 1749235243432, "results": "61", "hashOfConfig": "33"}, {"size": 911, "mtime": 1749234907390, "results": "62", "hashOfConfig": "33"}, {"size": 528, "mtime": 1749235890656, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "oy6o6z", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signin/page.tsx", ["157"], [], "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/auth/signup/page.tsx", ["158"], [], "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/dashboard/page.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/layout.tsx", ["159", "160"], [], "/home/<USER>/Dokumenty/moleculab/src/app/[locale]/page.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/api/auth/[...nextauth]/route.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/api/auth/register/route.ts", ["161"], [], "/home/<USER>/Dokumenty/moleculab/src/app/api/projects/save/route.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/api/subscription/create/route.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/auth/signin/page.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/auth/signup/page.tsx", ["162"], [], "/home/<USER>/Dokumenty/moleculab/src/app/dashboard/page.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/layout.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/molecules/page.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/app/page.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/components/AIAnalysis.tsx", ["163"], [], "/home/<USER>/Dokumenty/moleculab/src/components/ExportTools.tsx", ["164"], [], "/home/<USER>/Dokumenty/moleculab/src/components/LanguageSwitcher.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/components/MoleculeSelector.tsx", ["165", "166"], [], "/home/<USER>/Dokumenty/moleculab/src/components/MoleculeViewer.tsx", ["167", "168", "169", "170", "171", "172", "173", "174", "175"], [], "/home/<USER>/Dokumenty/moleculab/src/components/ProjectSave.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/components/PropertyPanel.tsx", [], [], "/home/<USER>/Dokumenty/moleculab/src/i18n/config.ts", ["176"], [], "/home/<USER>/Dokumenty/moleculab/src/i18n/navigation.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/lib/ai-analysis.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/lib/molecules.ts", ["177", "178", "179", "180", "181"], [], "/home/<USER>/Dokumenty/moleculab/src/lib/three-utils.ts", ["182"], [], "/home/<USER>/Dokumenty/moleculab/src/middleware.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/tests/3d-visualizations.test.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/types/molecule.ts", [], [], "/home/<USER>/Dokumenty/moleculab/src/types/next-auth.d.ts", ["183"], [], {"ruleId": "184", "severity": 2, "message": "185", "line": 31, "column": 14, "nodeType": null, "messageId": "186", "endLine": 31, "endColumn": 19}, {"ruleId": "184", "severity": 2, "message": "185", "line": 50, "column": 14, "nodeType": null, "messageId": "186", "endLine": 50, "endColumn": 19}, {"ruleId": "187", "severity": 2, "message": "188", "line": 21, "column": 43, "nodeType": "189", "messageId": "190", "endLine": 21, "endColumn": 46, "suggestions": "191"}, {"ruleId": "184", "severity": 2, "message": "185", "line": 28, "column": 12, "nodeType": null, "messageId": "186", "endLine": 28, "endColumn": 17}, {"ruleId": "184", "severity": 2, "message": "192", "line": 46, "column": 23, "nodeType": null, "messageId": "186", "endLine": 46, "endColumn": 24}, {"ruleId": "187", "severity": 2, "message": "188", "line": 49, "column": 21, "nodeType": "189", "messageId": "190", "endLine": 49, "endColumn": 24, "suggestions": "193"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 15, "column": 52, "nodeType": "189", "messageId": "190", "endLine": 15, "endColumn": 55, "suggestions": "194"}, {"ruleId": "184", "severity": 2, "message": "195", "line": 17, "column": 9, "nodeType": null, "messageId": "186", "endLine": 17, "endColumn": 18}, {"ruleId": "184", "severity": 2, "message": "196", "line": 6, "column": 10, "nodeType": null, "messageId": "186", "endLine": 6, "endColumn": 19}, {"ruleId": "197", "severity": 1, "message": "198", "line": 47, "column": 6, "nodeType": "199", "endLine": 47, "endColumn": 41, "suggestions": "200"}, {"ruleId": "184", "severity": 2, "message": "201", "line": 12, "column": 3, "nodeType": null, "messageId": "186", "endLine": 12, "endColumn": 24}, {"ruleId": "184", "severity": 2, "message": "202", "line": 13, "column": 3, "nodeType": null, "messageId": "186", "endLine": 13, "endColumn": 27}, {"ruleId": "187", "severity": 2, "message": "188", "line": 228, "column": 27, "nodeType": "189", "messageId": "190", "endLine": 228, "endColumn": 30, "suggestions": "203"}, {"ruleId": "184", "severity": 2, "message": "204", "line": 274, "column": 9, "nodeType": null, "messageId": "186", "endLine": 274, "endColumn": 16}, {"ruleId": "184", "severity": 2, "message": "205", "line": 283, "column": 11, "nodeType": null, "messageId": "186", "endLine": 283, "endColumn": 21}, {"ruleId": "184", "severity": 2, "message": "206", "line": 288, "column": 11, "nodeType": null, "messageId": "186", "endLine": 288, "endColumn": 21}, {"ruleId": "184", "severity": 2, "message": "207", "line": 293, "column": 11, "nodeType": null, "messageId": "186", "endLine": 293, "endColumn": 20}, {"ruleId": "184", "severity": 2, "message": "208", "line": 428, "column": 3, "nodeType": null, "messageId": "186", "endLine": 428, "endColumn": 28}, {"ruleId": "184", "severity": 2, "message": "209", "line": 430, "column": 21, "nodeType": null, "messageId": "186", "endLine": 430, "endColumn": 33}, {"ruleId": "187", "severity": 2, "message": "188", "line": 6, "column": 43, "nodeType": "189", "messageId": "190", "endLine": 6, "endColumn": 46, "suggestions": "210"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 11, "column": 47, "nodeType": "189", "messageId": "190", "endLine": 11, "endColumn": 50, "suggestions": "211"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 12, "column": 45, "nodeType": "189", "messageId": "190", "endLine": 12, "endColumn": 48, "suggestions": "212"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 99, "column": 33, "nodeType": "189", "messageId": "190", "endLine": 99, "endColumn": 36, "suggestions": "213"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 114, "column": 23, "nodeType": "189", "messageId": "190", "endLine": 114, "endColumn": 26, "suggestions": "214"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 125, "column": 62, "nodeType": "189", "messageId": "190", "endLine": 125, "endColumn": 65, "suggestions": "215"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 79, "column": 13, "nodeType": "189", "messageId": "190", "endLine": 79, "endColumn": 16, "suggestions": "216"}, {"ruleId": "184", "severity": 2, "message": "217", "line": 1, "column": 8, "nodeType": null, "messageId": "186", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["218", "219"], "'_' is assigned a value but never used.", ["220", "221"], ["222", "223"], "'canvasRef' is assigned a value but never used.", "'molecules' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", "ArrayExpression", ["224"], "'createCartoonGeometry' is defined but never used.", "'interpolateConformations' is defined but never used.", ["225", "226"], "'meshRef' is assigned a value but never used.", "'helixAtoms' is assigned a value but never used.", "'sheetAtoms' is assigned a value but never used.", "'loopAtoms' is assigned a value but never used.", "'onAnimationSettingsChange' is defined but never used.", "'setIsLoading' is assigned a value but never used.", ["227", "228"], ["229", "230"], ["231", "232"], ["233", "234"], ["235", "236"], ["237", "238"], ["239", "240"], "'NextAuth' is defined but never used.", {"messageId": "241", "fix": "242", "desc": "243"}, {"messageId": "244", "fix": "245", "desc": "246"}, {"messageId": "241", "fix": "247", "desc": "243"}, {"messageId": "244", "fix": "248", "desc": "246"}, {"messageId": "241", "fix": "249", "desc": "243"}, {"messageId": "244", "fix": "250", "desc": "246"}, {"desc": "251", "fix": "252"}, {"messageId": "241", "fix": "253", "desc": "243"}, {"messageId": "244", "fix": "254", "desc": "246"}, {"messageId": "241", "fix": "255", "desc": "243"}, {"messageId": "244", "fix": "256", "desc": "246"}, {"messageId": "241", "fix": "257", "desc": "243"}, {"messageId": "244", "fix": "258", "desc": "246"}, {"messageId": "241", "fix": "259", "desc": "243"}, {"messageId": "244", "fix": "260", "desc": "246"}, {"messageId": "241", "fix": "261", "desc": "243"}, {"messageId": "244", "fix": "262", "desc": "246"}, {"messageId": "241", "fix": "263", "desc": "243"}, {"messageId": "244", "fix": "264", "desc": "246"}, {"messageId": "241", "fix": "265", "desc": "243"}, {"messageId": "244", "fix": "266", "desc": "246"}, {"messageId": "241", "fix": "267", "desc": "243"}, {"messageId": "244", "fix": "268", "desc": "246"}, "suggestUnknown", {"range": "269", "text": "270"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "271", "text": "272"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "273", "text": "270"}, {"range": "274", "text": "272"}, {"range": "275", "text": "270"}, {"range": "276", "text": "272"}, "Update the dependencies array to be: [searchTerm, filterType, molecules, categories]", {"range": "277", "text": "278"}, {"range": "279", "text": "270"}, {"range": "280", "text": "272"}, {"range": "281", "text": "270"}, {"range": "282", "text": "272"}, {"range": "283", "text": "270"}, {"range": "284", "text": "272"}, {"range": "285", "text": "270"}, {"range": "286", "text": "272"}, {"range": "287", "text": "270"}, {"range": "288", "text": "272"}, {"range": "289", "text": "270"}, {"range": "290", "text": "272"}, {"range": "291", "text": "270"}, {"range": "292", "text": "272"}, {"range": "293", "text": "270"}, {"range": "294", "text": "272"}, [614, 617], "unknown", [614, 617], "never", [1317, 1320], [1317, 1320], [509, 512], [509, 512], [1909, 1944], "[searchTerm, filterType, molecules, categories]", [6470, 6473], [6470, 6473], [252, 255], [252, 255], [324, 327], [324, 327], [388, 391], [388, 391], [2838, 2841], [2838, 2841], [3221, 3224], [3221, 3224], [3584, 3587], [3584, 3587], [1880, 1883], [1880, 1883]]