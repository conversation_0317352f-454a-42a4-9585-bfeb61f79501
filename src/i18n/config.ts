import { getRequestConfig } from 'next-intl/server';
import { routing } from './navigation';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!routing.locales.includes(locale as (typeof routing.locales)[number])) {
    return { messages: {} };
  }

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
