'use client';

import { useTranslations, useLocale } from 'next-intl';
import { Link } from '@/i18n/navigation';

export default function TestPage() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="min-h-screen bg-dark-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        
        <h1 className="text-4xl font-bold mb-6 text-primary-400">
          next-intl Configuration Test
        </h1>
        
        <div className="bg-dark-800 rounded-lg p-6 mb-6">
          <h2 className="text-2xl font-semibold mb-4">Current Configuration Status</h2>
          <div className="space-y-2">
            <p><strong>Current Locale:</strong> <span className="text-green-400">{locale}</span></p>
            <p><strong>App Title:</strong> <span className="text-blue-400">{t('app.title')}</span></p>
            <p><strong>App Subtitle:</strong> <span className="text-blue-400">{t('app.subtitle')}</span></p>
          </div>
        </div>

        <div className="bg-dark-800 rounded-lg p-6 mb-6">
          <h2 className="text-2xl font-semibold mb-4">Translation Test</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Navigation</h3>
              <ul className="space-y-1 text-sm">
                <li>Dashboard: <span className="text-green-400">{t('nav.dashboard')}</span></li>
                <li>Molecules: <span className="text-green-400">{t('nav.molecules')}</span></li>
                <li>Sign In: <span className="text-green-400">{t('nav.signin')}</span></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">3D Viewer</h3>
              <ul className="space-y-1 text-sm">
                <li>Title: <span className="text-green-400">{t('molecules.viewer.title')}</span></li>
                <li>Ball-Stick: <span className="text-green-400">{t('molecules.viewer.modes.ball-stick')}</span></li>
                <li>Van der Waals: <span className="text-green-400">{t('molecules.viewer.modes.van-der-waals')}</span></li>
                <li>Animations: <span className="text-green-400">{t('molecules.viewer.animations.title')}</span></li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-dark-800 rounded-lg p-6 mb-6">
          <h2 className="text-2xl font-semibold mb-4">Navigation Test</h2>
          <div className="flex flex-wrap gap-4">
            <Link 
              href="/test" 
              className="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md"
            >
              Current Page (Test)
            </Link>
            <Link 
              href="/" 
              className="bg-secondary-600 hover:bg-secondary-700 px-4 py-2 rounded-md"
            >
              Home Page
            </Link>
            <Link 
              href="/auth/signin" 
              className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-md"
            >
              Sign In
            </Link>
          </div>
        </div>

        <div className="bg-green-900 border border-green-700 rounded-lg p-6">
          <h2 className="text-2xl font-semibold mb-4 text-green-300">✅ Success!</h2>
          <p className="text-green-200">
            The next-intl configuration is working correctly! The locale is being detected, 
            translations are loading, and navigation is functioning properly.
          </p>
          <div className="mt-4 text-sm text-green-300">
            <p><strong>Fixed Issues:</strong></p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>✅ "No locale was returned from getRequestConfig" error resolved</li>
              <li>✅ Middleware properly configured with new routing API</li>
              <li>✅ Navigation APIs working with createNavigation</li>
              <li>✅ Locale detection and validation working</li>
              <li>✅ Translation loading from JSON files working</li>
              <li>✅ All supported locales functional</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
