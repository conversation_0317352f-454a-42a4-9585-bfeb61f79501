'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Link as LocalizedLink } from '@/i18n/navigation';
import MoleculeViewer from '@/components/MoleculeViewer';
import MoleculeSelector from '@/components/MoleculeSelector';
import PropertyPanel from '@/components/PropertyPanel';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { molecules } from '@/lib/molecules';
import { Molecule, RenderMode, AnimationSettings } from '@/types/molecule';

export default function MoleculesPage() {
  const t = useTranslations();
  const { data: session } = useSession();
  const [selectedMolecule, setSelectedMolecule] = useState<Molecule | null>(null);
  const [renderMode, setRenderMode] = useState<RenderMode>('ball-stick');
  const [isPro] = useState<boolean>(false); // W przyszł<PERSON><PERSON><PERSON> będzie pobierane z kontekstu użytkownika
  const [animationSettings, setAnimationSettings] = useState<AnimationSettings>({
    rotation: {
      enabled: false,
      speed: 1.0,
      axis: 'y'
    },
    transitions: {
      enabled: false,
      currentConformation: 0,
      speed: 1.0
    }
  });

  useEffect(() => {
    // Automatyczne wybieranie pierwszej molekuły
    if (molecules.length > 0 && !selectedMolecule) {
      setSelectedMolecule(molecules[0]);
    }
  }, [selectedMolecule]);

  return (
    <div className="min-h-screen bg-dark-900 text-white">
      {/* Header */}
      <header className="bg-dark-800 border-b border-dark-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <LocalizedLink href="/" className="text-2xl font-bold text-primary-400">
                MolecuLab Pro
              </LocalizedLink>
              <nav className="hidden md:flex space-x-6">
                <LocalizedLink href="/dashboard" className="text-gray-300 hover:text-white">
                  {t('nav.dashboard')}
                </LocalizedLink>
                <LocalizedLink href="/molecules" className="text-primary-400">
                  {t('nav.molecules')}
                </LocalizedLink>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              {session ? (
                <span className="text-sm text-gray-300">
                  {t('nav.welcome', { name: session.user?.name })}
                </span>
              ) : (
                <LocalizedLink 
                  href="/auth/signin"
                  className="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"
                >
                  {t('nav.signin')}
                </LocalizedLink>
              )}
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Selektor molekuł */}
          <div className="lg:col-span-1">
            <MoleculeSelector 
              molecules={molecules}
              selectedMolecule={selectedMolecule}
              onMoleculeSelect={setSelectedMolecule}
            />
          </div>

          {/* Główna zawartość */}
          <div className="lg:col-span-3 space-y-6">
            {/* Kontrolki trybu renderowania */}
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium">{t('molecules.viewer.title')}</h2>
                <div className="flex space-x-2 flex-wrap">
                  <button
                    className={`px-3 py-1 rounded-md text-sm ${
                      renderMode === 'ball-stick'
                        ? 'bg-primary-500 text-white'
                        : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
                    }`}
                    onClick={() => setRenderMode('ball-stick')}
                  >
                    {t('molecules.viewer.modes.ball-stick')}
                  </button>
                  <button
                    className={`px-3 py-1 rounded-md text-sm ${
                      renderMode === 'space-filling'
                        ? 'bg-primary-500 text-white'
                        : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
                    }`}
                    onClick={() => setRenderMode('space-filling')}
                  >
                    {t('molecules.viewer.modes.space-filling')}
                  </button>
                  <button
                    className={`px-3 py-1 rounded-md text-sm ${
                      renderMode === 'wireframe'
                        ? 'bg-primary-500 text-white'
                        : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
                    }`}
                    onClick={() => setRenderMode('wireframe')}
                  >
                    {t('molecules.viewer.modes.wireframe')}
                  </button>
                  <button
                    className={`px-3 py-1 rounded-md text-sm ${
                      renderMode === 'van-der-waals'
                        ? 'bg-primary-500 text-white'
                        : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
                    }`}
                    onClick={() => setRenderMode('van-der-waals')}
                  >
                    Van der Waals
                  </button>
                  <button
                    className={`px-3 py-1 rounded-md text-sm ${
                      renderMode === 'licorice'
                        ? 'bg-primary-500 text-white'
                        : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
                    }`}
                    onClick={() => setRenderMode('licorice')}
                  >
                    Licorice
                  </button>
                  <button
                    className={`px-3 py-1 rounded-md text-sm ${
                      renderMode === 'cartoon'
                        ? 'bg-primary-500 text-white'
                        : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
                    }`}
                    onClick={() => setRenderMode('cartoon')}
                  >
                    Cartoon
                  </button>
                </div>
              </div>
              
              {/* Kontrolki animacji */}
              <div className="border-t border-dark-600 pt-4">
                <h3 className="text-sm font-medium mb-3">{t('molecules.viewer.animations.title')}</h3>
                <div className="flex items-center space-x-4 flex-wrap">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={animationSettings.rotation.enabled}
                      onChange={(e) => setAnimationSettings(prev => ({
                        ...prev,
                        rotation: { ...prev.rotation, enabled: e.target.checked }
                      }))}
                      className="rounded bg-dark-600 border-dark-500 text-primary-500 focus:ring-primary-500"
                    />
                    <span className="text-sm">{t('molecules.viewer.animations.rotation')}</span>
                  </label>
                  
                  {animationSettings.rotation.enabled && (
                    <>
                      <div className="flex items-center space-x-2">
                        <label className="text-sm">{t('molecules.viewer.animations.speed')}:</label>
                        <input
                          type="range"
                          min="0.1"
                          max="3"
                          step="0.1"
                          value={animationSettings.rotation.speed}
                          onChange={(e) => setAnimationSettings(prev => ({
                            ...prev,
                            rotation: { ...prev.rotation, speed: parseFloat(e.target.value) }
                          }))}
                          className="w-20"
                        />
                        <span className="text-xs text-gray-400">{animationSettings.rotation.speed.toFixed(1)}x</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <label className="text-sm">{t('molecules.viewer.animations.axis')}:</label>
                        <select
                          value={animationSettings.rotation.axis}
                          onChange={(e) => setAnimationSettings(prev => ({
                            ...prev,
                            rotation: { ...prev.rotation, axis: e.target.value as 'x' | 'y' | 'z' }
                          }))}
                          className="bg-dark-600 border-dark-500 rounded text-sm px-2 py-1"
                        >
                          <option value="x">X</option>
                          <option value="y">Y</option>
                          <option value="z">Z</option>
                        </select>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Wizualizator 3D */}
            <MoleculeViewer 
              molecule={selectedMolecule} 
              renderMode={renderMode}
              animationSettings={animationSettings}
              onAnimationSettingsChange={setAnimationSettings}
            />

            {/* Panel właściwości */}
            <PropertyPanel molecule={selectedMolecule} />
          </div>
        </div>
      </main>
    </div>
  );
}
