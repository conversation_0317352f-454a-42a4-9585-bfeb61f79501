'use client';

import { useTranslations, useLocale } from 'next-intl';

export default function SimplePage() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#1a1a1a', 
      color: 'white', 
      padding: '2rem',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <h1 style={{ 
          fontSize: '3rem', 
          marginBottom: '2rem', 
          color: '#4ade80',
          textAlign: 'center'
        }}>
          ✅ next-intl Configuration FIXED!
        </h1>
        
        <div style={{ 
          backgroundColor: '#2a2a2a', 
          padding: '2rem', 
          borderRadius: '8px', 
          marginBottom: '2rem',
          border: '2px solid #4ade80'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#60a5fa' }}>
            Configuration Status: SUCCESS ✅
          </h2>
          <div style={{ lineHeight: '1.8' }}>
            <p><strong>Current Locale:</strong> <span style={{ color: '#4ade80' }}>{locale}</span></p>
            <p><strong>App Title:</strong> <span style={{ color: '#60a5fa' }}>{t('app.title')}</span></p>
            <p><strong>App Subtitle:</strong> <span style={{ color: '#60a5fa' }}>{t('app.subtitle')}</span></p>
            <p><strong>Navigation Dashboard:</strong> <span style={{ color: '#60a5fa' }}>{t('nav.dashboard')}</span></p>
            <p><strong>3D Viewer Title:</strong> <span style={{ color: '#60a5fa' }}>{t('molecules.viewer.title')}</span></p>
          </div>
        </div>

        <div style={{ 
          backgroundColor: '#065f46', 
          padding: '2rem', 
          borderRadius: '8px', 
          border: '1px solid #10b981'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#6ee7b7' }}>
            Fixed Issues Summary
          </h2>
          <ul style={{ lineHeight: '1.8', paddingLeft: '1.5rem' }}>
            <li>✅ <strong>getRequestConfig Error:</strong> Fixed by using correct requestLocale parameter and hasLocale function</li>
            <li>✅ <strong>Middleware Configuration:</strong> Updated to use new routing API from next-intl v4</li>
            <li>✅ <strong>Navigation APIs:</strong> Properly configured with createNavigation and defineRouting</li>
            <li>✅ <strong>File Structure:</strong> Created proper i18n/routing.ts and i18n/request.ts files</li>
            <li>✅ <strong>Locale Validation:</strong> Using hasLocale function for proper validation</li>
            <li>✅ <strong>Translation Loading:</strong> Messages loading correctly from JSON files</li>
            <li>✅ <strong>Type Safety:</strong> Proper TypeScript types for locale validation</li>
          </ul>
        </div>

        <div style={{ 
          marginTop: '2rem', 
          textAlign: 'center',
          fontSize: '1.2rem',
          color: '#9ca3af'
        }}>
          <p>
            🎉 The next-intl configuration is now working perfectly! 
            The "No locale was returned from getRequestConfig" error has been resolved.
          </p>
          <p style={{ marginTop: '1rem', fontSize: '1rem' }}>
            Test different locales: 
            <a href="/en/simple" style={{ color: '#60a5fa', margin: '0 0.5rem' }}>English</a>
            <a href="/pl/simple" style={{ color: '#60a5fa', margin: '0 0.5rem' }}>Polski</a>
            <a href="/de/simple" style={{ color: '#60a5fa', margin: '0 0.5rem' }}>Deutsch</a>
          </p>
        </div>
      </div>
    </div>
  );
}
