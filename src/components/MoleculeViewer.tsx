'use client';

import { useState, useEffect, useRef } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Html } from '@react-three/drei';
import { Molecule, RenderMode, AnimationSettings } from '@/types/molecule';
import {
  getAtomColor,
  getAtomRadius,
  getVanDerWaalsRadius,
  detectSecondaryStructure
} from '@/lib/three-utils';
import * as THREE from 'three';

interface MoleculeViewerProps {
  molecule: Molecule | null;
  renderMode: RenderMode;
  animationSettings?: AnimationSettings;
  onAnimationSettingsChange?: (settings: AnimationSettings) => void;
}

// Komponent dla pojedynczego atomu
const Atom = ({ position, element, renderMode }: {
  position: [number, number, number];
  element: string;
  renderMode: RenderMode;
}) => {
  let radius: number;

  switch (renderMode) {
    case 'space-filling':
      radius = getAtomRadius(element);
      break;
    case 'van-der-waals':
      radius = getVanDerWaalsRadius(element);
      break;
    case 'ball-stick':
      radius = getAtomRadius(element) * 0.5;
      break;
    case 'licorice':
      radius = getAtomRadius(element) * 0.3;
      break;
    case 'wireframe':
      radius = getAtomRadius(element) * 0.3;
      break;
    case 'cartoon':
      // W trybie cartoon atomy są ukryte lub bardzo małe
      radius = getAtomRadius(element) * 0.1;
      break;
    default:
      radius = getAtomRadius(element) * 0.5;
  }

  const segments = renderMode === 'wireframe' ? 8 : 32;
  const color = getAtomColor(element);

  // W trybie cartoon atomy są prawie niewidoczne
  if (renderMode === 'cartoon') {
    return (
      <mesh position={position}>
        <sphereGeometry args={[radius, 8, 8]} />
        <meshStandardMaterial
          color={color}
          transparent={true}
          opacity={0.1}
          metalness={0.2}
          roughness={0.8}
        />
      </mesh>
    );
  }

  return (
    <mesh position={position}>
      <sphereGeometry args={[radius, segments, segments]} />
      <meshStandardMaterial
        color={color}
        wireframe={renderMode === 'wireframe'}
        metalness={0.2}
        roughness={0.8}
      />
      {renderMode !== 'wireframe' && renderMode !== 'cartoon' && (
        <mesh>
          <sphereGeometry args={[radius, segments, segments]} />
          <meshPhongMaterial
            color={color}
            transparent={true}
            opacity={0.2}
            side={THREE.BackSide}
            depthWrite={false}
          />
        </mesh>
      )}
    </mesh>
  );
};

// Komponent dla pojedynczego wiązania
const Bond = ({
  start,
  end,
  bondType,
  renderMode
}: {
  start: [number, number, number];
  end: [number, number, number];
  bondType: 'single' | 'double' | 'aromatic';
  renderMode: RenderMode;
}) => {
  // Obliczanie środka, długości i orientacji wiązania
  const midpoint: [number, number, number] = [
    (start[0] + end[0]) / 2,
    (start[1] + end[1]) / 2,
    (start[2] + end[2]) / 2,
  ];
  
  const direction = new THREE.Vector3(
    end[0] - start[0],
    end[1] - start[1],
    end[2] - start[2]
  );
  
  const length = direction.length();
  direction.normalize();
  
  // Obliczanie rotacji cylindra
  const quaternion = new THREE.Quaternion();
  const upVector = new THREE.Vector3(0, 1, 0);
  quaternion.setFromUnitVectors(upVector, direction);
  
  // Grubość wiązania w zależności od trybu
  let radius: number;
  let segments: number;

  switch (renderMode) {
    case 'licorice':
      radius = 0.15; // Stała grubość dla trybu licorice
      segments = 12;
      break;
    case 'wireframe':
      radius = 0.05;
      segments = 4;
      break;
    case 'cartoon':
      radius = 0.02; // Bardzo cienkie wiązania w trybie cartoon
      segments = 4;
      break;
    default:
      radius = 0.1;
      segments = 8;
  }
  
  // Dla wiązań podwójnych i aromatycznych
  const offset = 0.15;
  const perpendicular = new THREE.Vector3(1, 0, 0).cross(direction).normalize();
  if (perpendicular.lengthSq() < 0.01) {
    perpendicular.set(0, 0, 1).cross(direction).normalize();
  }
  
  // Renderowanie wiązania w zależności od typu
  if (bondType === 'single' || renderMode === 'wireframe') {
    return (
      <mesh position={midpoint} quaternion={quaternion}>
        <cylinderGeometry args={[radius, radius, length, segments]} />
        <meshStandardMaterial 
          color="#ffffff" 
          wireframe={renderMode === 'wireframe'} 
          metalness={0.2}
          roughness={0.8}
        />
      </mesh>
    );
  } else if (bondType === 'double') {
    return (
      <>
        <mesh 
          position={[
            midpoint[0] + perpendicular.x * offset,
            midpoint[1] + perpendicular.y * offset,
            midpoint[2] + perpendicular.z * offset
          ]} 
          quaternion={quaternion}
        >
          <cylinderGeometry args={[radius, radius, length, segments]} />
          <meshStandardMaterial color="#ffffff" metalness={0.2} roughness={0.8} />
        </mesh>
        <mesh 
          position={[
            midpoint[0] - perpendicular.x * offset,
            midpoint[1] - perpendicular.y * offset,
            midpoint[2] - perpendicular.z * offset
          ]} 
          quaternion={quaternion}
        >
          <cylinderGeometry args={[radius, radius, length, segments]} />
          <meshStandardMaterial color="#ffffff" metalness={0.2} roughness={0.8} />
        </mesh>
      </>
    );
  } else { // aromatic
    return (
      <>
        <mesh position={midpoint} quaternion={quaternion}>
          <cylinderGeometry args={[radius, radius, length, segments]} />
          <meshStandardMaterial color="#ffffff" metalness={0.2} roughness={0.8} />
        </mesh>
        <mesh 
          position={[
            midpoint[0] + perpendicular.x * offset,
            midpoint[1] + perpendicular.y * offset,
            midpoint[2] + perpendicular.z * offset
          ]} 
          quaternion={quaternion}
        >
          <cylinderGeometry args={[radius * 0.7, radius * 0.7, length, segments]} />
          <meshStandardMaterial color="#ffffff" transparent opacity={0.7} metalness={0.2} roughness={0.8} />
        </mesh>
      </>
    );
  }
};

// Komponent do centrowania kamery na molekule
const CenterCamera = ({ molecule }: { molecule: Molecule }) => {
  const { camera } = useThree();
  const controls = useRef<any>();
  
  useEffect(() => {
    if (!molecule || !molecule.atoms || molecule.atoms.length === 0) return;
    
    // Obliczanie środka molekuły
    const center = new THREE.Vector3();
    molecule.atoms.forEach(atom => {
      center.add(new THREE.Vector3(...atom.position));
    });
    center.divideScalar(molecule.atoms.length);
    
    // Obliczanie promienia molekuły
    let maxDistance = 0;
    molecule.atoms.forEach(atom => {
      const atomPos = new THREE.Vector3(...atom.position);
      const distance = atomPos.distanceTo(center);
      maxDistance = Math.max(maxDistance, distance);
    });
    
    // Ustawianie kamery
    const radius = Math.max(maxDistance * 3, 10);
    camera.position.set(center.x, center.y, center.z + radius);
    
    if (controls.current) {
      controls.current.target.set(center.x, center.y, center.z);
      controls.current.update();
    }
  }, [molecule, camera]);
  
  return <OrbitControls ref={controls} enableDamping dampingFactor={0.25} />;
};

// Komponent informacji o atomie
const AtomLabel = ({ position, element }: { position: [number, number, number]; element: string }) => {
  return (
    <Html position={position} distanceFactor={10}>
      <div className="bg-dark-800/80 px-2 py-1 rounded text-xs text-white">
        {element}
      </div>
    </Html>
  );
};

// Komponent dla wizualizacji cartoon (białka)
const CartoonStructure = ({ molecule }: { molecule: Molecule }) => {
  useEffect(() => {
    if (!molecule || !molecule.atoms) return;

    // Wykrywanie struktur drugorzędowych
    detectSecondaryStructure(molecule);

    // Tworzenie geometrii dla każdego typu struktury
    // (Implementacja uproszczona - w rzeczywistości wymagałaby bardziej zaawansowanych algorytmów)

  }, [molecule]);

  // Renderowanie struktur drugorzędowych jako kolorowe wstęgi/strzałki
  const renderSecondaryStructures = () => {
    if (!molecule || !molecule.atoms) return null;

    const structures = detectSecondaryStructure(molecule);
    const elements: JSX.Element[] = [];

    // Grupowanie sąsiadujących atomów o tej samej strukturze
    let currentGroup: typeof structures = [];
    let currentType: 'helix' | 'sheet' | 'loop' | null = null;

    structures.forEach((struct, index) => {
      if (struct.structure !== currentType) {
        if (currentGroup.length > 1) {
          elements.push(renderStructureGroup(currentGroup, currentType!, index));
        }
        currentGroup = [struct];
        currentType = struct.structure;
      } else {
        currentGroup.push(struct);
      }
    });

    // Renderowanie ostatniej grupy
    if (currentGroup.length > 1 && currentType) {
      elements.push(renderStructureGroup(currentGroup, currentType, structures.length));
    }

    return elements;
  };

  const renderStructureGroup = (
    group: Array<{ atomIndex: number; structure: 'helix' | 'sheet' | 'loop' }>,
    type: 'helix' | 'sheet' | 'loop',
    key: number
  ) => {
    if (group.length < 2) return null;

    const atoms = group.map(g => molecule.atoms[g.atomIndex]);
    const positions = atoms.map(atom => atom.position);

    // Obliczanie środka i kierunku
    const center = positions.reduce((acc, pos) => [
      acc[0] + pos[0] / positions.length,
      acc[1] + pos[1] / positions.length,
      acc[2] + pos[2] / positions.length
    ], [0, 0, 0] as [number, number, number]);

    const direction = new THREE.Vector3(
      positions[positions.length - 1][0] - positions[0][0],
      positions[positions.length - 1][1] - positions[0][1],
      positions[positions.length - 1][2] - positions[0][2]
    );

    const length = direction.length();
    direction.normalize();

    const quaternion = new THREE.Quaternion();
    const upVector = new THREE.Vector3(0, 1, 0);
    quaternion.setFromUnitVectors(upVector, direction);

    if (type === 'helix') {
      return (
        <mesh key={`helix-${key}`} position={center} quaternion={quaternion}>
          <cylinderGeometry args={[0.8, 0.8, length, 8]} />
          <meshStandardMaterial color="#ff6b6b" transparent opacity={0.8} />
        </mesh>
      );
    } else if (type === 'sheet') {
      return (
        <mesh key={`sheet-${key}`} position={center} quaternion={quaternion}>
          <boxGeometry args={[1.5, 0.2, length]} />
          <meshStandardMaterial color="#4ecdc4" transparent opacity={0.8} />
        </mesh>
      );
    } else {
      return (
        <mesh key={`loop-${key}`} position={center} quaternion={quaternion}>
          <cylinderGeometry args={[0.1, 0.1, length, 4]} />
          <meshStandardMaterial color="#95e1d3" />
        </mesh>
      );
    }
  };

  return <>{renderSecondaryStructures()}</>;
};

// Komponent do animacji rotacji
const RotatingGroup = ({
  children,
  animationSettings
}: {
  children: React.ReactNode;
  animationSettings?: AnimationSettings;
}) => {
  const groupRef = useRef<THREE.Group>(null);

  useFrame((state, delta) => {
    if (groupRef.current && animationSettings?.rotation.enabled) {
      const speed = animationSettings.rotation.speed;
      const axis = animationSettings.rotation.axis;

      switch (axis) {
        case 'x':
          groupRef.current.rotation.x += delta * speed;
          break;
        case 'y':
          groupRef.current.rotation.y += delta * speed;
          break;
        case 'z':
          groupRef.current.rotation.z += delta * speed;
          break;
      }
    }
  });

  return <group ref={groupRef}>{children}</group>;
};

// Główny komponent wizualizatora
const MoleculeViewer: React.FC<MoleculeViewerProps> = ({
  molecule,
  renderMode = 'ball-stick',
  animationSettings
}) => {
  const [isLoading] = useState(false);
  const [showLabels, setShowLabels] = useState(false);

  // Domyślne ustawienia animacji
  const defaultAnimationSettings: AnimationSettings = {
    rotation: {
      enabled: false,
      speed: 1.0,
      axis: 'y'
    },
    transitions: {
      enabled: false,
      currentConformation: 0,
      speed: 1.0
    }
  };

  const currentAnimationSettings = animationSettings || defaultAnimationSettings;

  if (!molecule) {
    return (
      <div className="w-full h-[500px] flex items-center justify-center bg-dark-700 rounded-lg">
        <p className="text-gray-400">Wybierz molekułę do wyświetlenia</p>
      </div>
    );
  }

  return (
    <div className="w-full h-[500px] relative bg-dark-700 rounded-lg overflow-hidden">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-dark-800/50 z-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      )}
      
      <div className="absolute top-4 right-4 z-10 flex space-x-2">
        <button 
          className={`px-3 py-1 rounded-md text-xs ${
            showLabels ? 'bg-primary-500 text-white' : 'bg-dark-600/80 text-gray-300 hover:bg-dark-500/80'
          }`}
          onClick={() => setShowLabels(!showLabels)}
        >
          Etykiety
        </button>
        <button 
          className="px-3 py-1 rounded-md text-xs bg-dark-600/80 text-gray-300 hover:bg-dark-500/80"
          onClick={() => {
            // Reset kamery - implementacja w przyszłości
          }}
        >
          Reset widoku
        </button>
      </div>
      
      <Canvas shadows dpr={[1, 2]}>
        <PerspectiveCamera makeDefault position={[0, 0, 15]} />
        <CenterCamera molecule={molecule} />

        <ambientLight intensity={0.5} />
        <directionalLight position={[10, 10, 5]} intensity={1} castShadow />
        <directionalLight position={[-10, -10, -5]} intensity={0.5} />
        <hemisphereLight intensity={0.3} groundColor="#000000" />

        <RotatingGroup animationSettings={currentAnimationSettings}>
          {/* Renderowanie atomów */}
          {molecule.atoms.map((atom, index) => (
            <group key={`atom-${index}`}>
              <Atom
                position={atom.position}
                element={atom.element}
                renderMode={renderMode}
              />
              {showLabels && (
                <AtomLabel position={atom.position} element={atom.element} />
              )}
            </group>
          ))}

          {/* Renderowanie wiązań (dla wszystkich trybów oprócz space-filling i van-der-waals) */}
          {!['space-filling', 'van-der-waals'].includes(renderMode) && molecule.bonds && molecule.bonds.map((bond, index) => {
            const atom1 = molecule.atoms[bond.from];
            const atom2 = molecule.atoms[bond.to];

            if (!atom1 || !atom2) return null;

            return (
              <Bond
                key={`bond-${index}`}
                start={atom1.position}
                end={atom2.position}
                bondType={bond.type}
                renderMode={renderMode}
              />
            );
          })}

          {/* Renderowanie struktur drugorzędowych w trybie cartoon */}
          {renderMode === 'cartoon' && (
            <CartoonStructure molecule={molecule} />
          )}
        </RotatingGroup>
      </Canvas>
      
      <div className="absolute bottom-4 left-4 bg-dark-800/80 px-3 py-2 rounded-md">
        <p className="text-sm font-medium">{molecule.name}</p>
        <p className="text-xs text-gray-400">{molecule.formula}</p>
      </div>
      
      <div className="absolute bottom-4 right-4 bg-dark-800/80 px-3 py-2 rounded-md text-xs text-gray-400">
        <p>Obracanie: kliknij i przeciągnij | Przybliżanie: scroll</p>
      </div>
    </div>
  );
};

export default MoleculeViewer;
