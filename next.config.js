const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin('./src/i18n/config.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Enable experimental features if needed
  },
  images: {
    domains: ['localhost'],
  },
  // Ensure proper handling of ES modules
  transpilePackages: ['three', '@react-three/fiber', '@react-three/drei'],
};

module.exports = withNextIntl(nextConfig);
